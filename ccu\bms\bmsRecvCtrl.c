/**
 ******************************************************************************
 * @file      bmsRecvCtrl.c
 * @brief     C Source file of bmsRecvCtrl.c.
 * @details   This file including all API functions's
 *            implement of bmsRecvCtrl.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <types.h>
#include <string.h>
#include <stdio.h>
#include <sxlib.h>
#include <taskLib.h>
#include <stdlib.h>
#include <trace.h>
#include "ccu\bsn\io.h"
#include <bms.h>
#include "bmsDll.h"
#include "bmsMain.h"
#include "bmsSendCtrl.h"
#include "bmsRecvCtrl.h"
#include "ccu\bsn\deviceState.h"
#include "ccu\charge\ccuChargeMain.h"
#include "ccu\para\para.h"
#include "ccu\ccuLog\ccuLog.h"
/*-----------------------------------------------------------------------------
 Section: Type Definitions
----------------------------------------------------------------------------*/
#define CHECK_BMS_LEN(offset)                           \
    {                                                   \
        if ((index >= len) || ((len - index) < offset)) \
            return;                                     \
    }
#define BEM_TimeOut 5000  // BEM超时处理时间
uint8 isBHMFirstData = 0; // 第一次接收BHM报文标记

typedef void (*PDecodeFunc)(uint8 *pInBuf, uint32 len);
typedef bool_e (*PRecvDealFunc)(void);

typedef struct BMS_RECV_DEAL_STRU
{
    uint32 pgn;
    uint8 minLen;
    uint8 prio;
    PDecodeFunc pDecodeFunc;
    PRecvDealFunc pDealFunc;
} BMS_RECV_DEAL;

typedef struct BMS_RECV_TIME_INFO_STRU
{
    uint32 pgn;
    uint32 overTime;
} BMS_RECV_TIME;

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
extern BMS_DATA bmsData;
extern BMS_CTRL bmsCtrl;

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
/* NONE */

static uint32 bms_over_pgn = 0;
static uint8 bms_enable = 0;
static uint32 lastBEMTime = 0; // 最后一次接收到BEM报文的时间
static uint8 first_bfc_recv = FALSE;
/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/**
 ******************************************************************************
 * @brief       Get_bms_over_pgn
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
uint32 Get_bms_over_pgn(void)
{
    return bms_over_pgn;
}

/**
 ******************************************************************************
 * @brief       Set_bms_over_pgn
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
void Set_bms_over_pgn(uint32 pgn)
{
    bms_over_pgn = pgn;
}

/**
 ******************************************************************************
 * @brief       Set_bms_enable
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
void Set_bms_enable(uint8 enable)
{
    bms_enable = enable;
}

/**
 ******************************************************************************
 * @brief       Get_bms_enable
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
uint32 Get_bms_enable(void)
{
    return bms_enable;
}

/**
 ******************************************************************************
 * @brief      Recv_BHM
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        车辆握手帧解码
 ******************************************************************************
 */
static void Recv_BHM(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;
    static uint8 CpStatus = 0;
    static uint8 CpNum[4] = {3, 0, 0, 0};
    CHARGE_PARA strChargePara;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
    uint16 highlestTotalVoltage = 0;
    if (Get_CcuCfgParaEuropeEnable())
    {
        memcpy(&highlestTotalVoltage, &pInBuf[index],
               sizeof(pBmsData->strBHM.highlestTotalVoltage));
        index += sizeof(pBmsData->strBHM.highlestTotalVoltage);

        highlestTotalVoltage = (highlestTotalVoltage > TwoUint8ToUint16(strChargePara.minOutputVoltage))
                                   ? highlestTotalVoltage
                                   : TwoUint8ToUint16(strChargePara.maxOutputVoltage);
        memcpy(pBmsData->strBHM.highlestTotalVoltage, &highlestTotalVoltage,
               sizeof(pBmsData->strBHM.highlestTotalVoltage));

        pBmsData->strBHM.plcAagVal = pInBuf[index++];
        CpStatus = pInBuf[index++] & 0x03;
        if (CpStatus != pBmsData->strBHM.plc_CpStatus.CpStatus)
        {
            //            printf("=======CP====[%d ---> %d]=[%d]=\n",CpStatus,pBmsData->strBHM.plc_CpStatus.CpStatus,CpNum[CpStatus]);
            CpNum[CpStatus]++;
            for (uint8 i = 0; i < 4; i++)
            {
                if (i != CpStatus)
                {
                    CpNum[i] = 0;
                }
            }
        }
        if (CpNum[CpStatus] > 3)
        {
            if (CpStatus != pBmsData->strBHM.plc_CpStatus.CpStatus)
            {
                printf("=======CP==CHANGE==[%d ---> %d]==\n", pBmsData->strBHM.plc_CpStatus.CpStatus, CpStatus);
                pBmsData->strBHM.plc_CpStatus.CpStatus = CpStatus;
            }
        }
    }
    else
    {
        // 只保存接收到的第一帧BHM报文的数据
        if (isBHMFirstData)
        {
            memcpy(pBmsData->strBHM.highlestTotalVoltage, &pInBuf[index],
                   sizeof(pBmsData->strBHM.highlestTotalVoltage));
            index += sizeof(pBmsData->strBHM.highlestTotalVoltage);
            isBHMFirstData = 0;
        }
    }
    return;
}

/**
******************************************************************************
* @brief       Deal_BHM
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note        车辆握手帧处理
******************************************************************************
*/
static bool_e Deal_BHM(void)
{
    return TRUE;
}

/**
 ******************************************************************************
 * @brief      Recv_BRM
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        车辆辨识帧解码
 ******************************************************************************
 */
static void Recv_BRM(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;
    if (Get_CcuCfgParaEuropeEnable())
    {
        pBmsData->strBRM.ec.plcVer = pInBuf[index++];

        memcpy(pBmsData->strBRM.ec.evccId, &pInBuf[index], // EVCC MAC地址
               sizeof(pBmsData->strBRM.ec.evccId));
        index += sizeof(pBmsData->strBRM.ec.evccId);
        trace(TR_BMS_PROCESS, "<rx 02, Recv_BRM> : ");
        trace_buf(TR_BMS_PROCESS, pInBuf, index);
    }
    else
    {
        memcpy(pBmsData->strBRM.gbt.bmsComProtocolVer, &pInBuf[index], // 3byte 车辆通信协议版本
               sizeof(pBmsData->strBRM.gbt.bmsComProtocolVer));
        index += sizeof(pBmsData->strBRM.gbt.bmsComProtocolVer);

        pBmsData->strBRM.gbt.batteryType = pInBuf[index++]; // 1byte 电池类型

        memcpy(pBmsData->strBRM.gbt.ratedCapacity, &pInBuf[index], // 2byte 动力蓄电池额定容量
               sizeof(pBmsData->strBRM.gbt.ratedCapacity));
        index += sizeof(pBmsData->strBRM.gbt.ratedCapacity);

        memcpy(pBmsData->strBRM.gbt.ratedVoltage, &pInBuf[index], // 2byte	动力蓄电池额定总电压
               sizeof(pBmsData->strBRM.gbt.ratedVoltage));
        index += sizeof(pBmsData->strBRM.gbt.ratedVoltage);

        CHECK_BMS_LEN(sizeof(pBmsData->strBRM.gbt.batteryFactory)); // 4byte	电池生产厂商名称
        memcpy(pBmsData->strBRM.gbt.batteryFactory, &pInBuf[index],
               sizeof(pBmsData->strBRM.gbt.batteryFactory));
        index += sizeof(pBmsData->strBRM.gbt.batteryFactory);

        CHECK_BMS_LEN(sizeof(pBmsData->strBRM.gbt.batteryGroupNO)); // 4byte	电池组序号
        memcpy(pBmsData->strBRM.gbt.batteryGroupNO, &pInBuf[index],
               sizeof(pBmsData->strBRM.gbt.batteryGroupNO));
        index += sizeof(pBmsData->strBRM.gbt.batteryGroupNO);

        CHECK_BMS_LEN(1);
        pBmsData->strBRM.gbt.producedYear = pInBuf[index++];

        CHECK_BMS_LEN(1);
        pBmsData->strBRM.gbt.producedMonth = pInBuf[index++];

        CHECK_BMS_LEN(1);
        pBmsData->strBRM.gbt.producedDay = pInBuf[index++];

        CHECK_BMS_LEN(sizeof(pBmsData->strBRM.gbt.chargeTimes));
        memcpy(pBmsData->strBRM.gbt.chargeTimes, &pInBuf[index],
               sizeof(pBmsData->strBRM.gbt.chargeTimes));
        index += sizeof(pBmsData->strBRM.gbt.chargeTimes);

        CHECK_BMS_LEN(1);
        pBmsData->strBRM.gbt.propertyFlag = pInBuf[index++];

        CHECK_BMS_LEN(1);
        pBmsData->strBRM.gbt.reserve = pInBuf[index++];

        CHECK_BMS_LEN(sizeof(pBmsData->strBRM.gbt.VIN));
        /*********************BAOHAOPENG****临时改动****************************/
        memcpy(pBmsData->strBRM.gbt.VIN, &pInBuf[index],
               sizeof(pBmsData->strBRM.gbt.VIN));
        //        memcpy(pBmsData->strBRM.gbt.VIN,"12345678912345677" ,
        //             sizeof(pBmsData->strBRM.gbt.VIN));
        index += sizeof(pBmsData->strBRM.gbt.VIN);
        /*小桔使用*/
        CHECK_BMS_LEN(sizeof(pBmsData->strBRM.gbt.Soft));
        memcpy(pBmsData->strBRM.gbt.Soft, &pInBuf[index],
               sizeof(pBmsData->strBRM.gbt.Soft));
        index += sizeof(pBmsData->strBRM.gbt.Soft);
    }
    return;
}

/**
******************************************************************************
* @brief       Deal_BRM
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note        车辆辨识帧处理
******************************************************************************
*/
static bool_e Deal_BRM(void)
{
    BMS_DATA *pBmsData = &bmsData;
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (Get_FaultMask(eErrType_BRMErr) || ((!is_bcd(pBmsData->strBRM.gbt.bmsComProtocolVer, 3)) && !Get_CcuCfgParaEuropeEnable()))
    {
        // BRM数据项异常故障
        Set_ErrType(eErrType_BRMErr);
        return FALSE;
    }

    if (TRUE == Is_DualCharge_IndependentMode(DUAL_CHARGE_CHANNEL_01) && TRUE == Is_DualCharge_Initiator(DUAL_CHARGE_CHANNEL_01))
    {
        if (Check_Is_Send2560(FALSE, TRUE)) // 假如重新超时
        {
            pBmsCtrl->spn_2560 = 0xAA;
            Check_Is_Send2560(TRUE, FALSE);
        }
    }

    else
    {
        pBmsCtrl->spn_2560 = 0xAA;
    }

    Set_BmsRecvTimerEnable(BMS_PGN_BRM, eTimerEnable_Off);

    if (eComState_Normal == Get_BmsComState())
    {
        pBmsCtrl->overTimePgnforTcu = 0x00;
    }

    /* BEM接收使能
     * 1.在发送CHM的时候打开，是为了第一次走正常流程的时候打开的；
     * 2.在处理BRM再次打开，因为充电机接收BMS超时的情况下，会关掉BEM的接收，重连成功的情况下
     *   可再次打开;
     * 3.在收到BEM后，会暂时关掉BEM的接收，因为需要等外部处理完成，故外部处理完成重连成功情况下
     *   再次打开BEM;
     * 4.因为存在刚刚关掉BEM，然后因为别的原因进入到了停止充电状态，所以在停止充电状态如果发现
     *   BEM被关掉了，应该再次打开
     */
    if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BEM))
    {
        Set_BmsRecvEnable(BMS_PGN_BEM, eRecvEnable_On);
        Set_BmsRecvFlag(BMS_PGN_BEM, eRecvFlag_No);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief      Recv_BRM
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        车辆辨识帧解码
 ******************************************************************************
 */
static void Recv_BFC(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;

    pBmsData->strBFC.current_offset = pInBuf[index++];

    pBmsData->strBFC.byte2 = pInBuf[index++];

    //    memcpy(pBmsData->strBFC.reserved, &pInBuf[index],      //2byte 动力蓄电池额定容量
    //           sizeof(pBmsData->strBFC.reserved));
    //    index += sizeof(pBmsData->strBFC.reserved);

    return;
}

static bool_e Deal_BFC(void)
{
    BMS_DATA *pBmsData = &bmsData;
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    static uint32 tick = 0;

    if (Get_BmsRecvFlag(BMS_PGN_BFC) != eRecvFlag_Yes)
    {
        tick = tickGet();
    }

    Set_BmsRecvFlag(BMS_PGN_BFC, eRecvFlag_Yes);

    if ((pBmsData->strCFC.gun_count != pBmsData->strBFC.gun_count))

    {
        pBmsCtrl->Dual_Cfc = DUAL_ACK_01;
        //        return TRUE;
    }

    if (DUAL_ACK_11 != pBmsData->strBFC.ack)
    {
        pBmsCtrl->Dual_Cfc = DUAL_ACK_01;

        if (DUAL_ACK_11 == pBmsCtrl->Dual_Bfc)
        {
            tick = tickGet() + abs(tickGet() - Get_BmsRecvTimer(BMS_PGN_BFC));
        }
        pBmsCtrl->Dual_Bfc = pBmsData->strBFC.ack;

        if (abs(tickGet() - tick) > 1000)
        {
            //             Set_BmsSendRemainTimer(BMS_PGN_CFC, 0);
            //             Set_BmsRecvEnable(BMS_PGN_BFC, eTimerEnable_Off);
            //             Set_BmsRecvTimerEnable(BMS_PGN_BFC, eTimerEnable_Off);
            Set_ErrType(eErrType_EVGunMismatch);
            if (0x00 == pBmsCtrl->spn_2560)
            {
                if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BRM))
                {
                    Set_BmsRecvFlag(BMS_PGN_BRM, eRecvFlag_No);
                    Set_BmsRecvTimer(BMS_PGN_BRM, 0x00);
                    Set_BmsRecvEnable(BMS_PGN_BRM, eRecvEnable_On);
                    Set_BmsRecvTimerEnable(BMS_PGN_BRM, eTimerEnable_On);
                    printf("===[%s]==============[%d]====================\n", __FUNCTION__, __LINE__);
                }
            }
            printf("===[%s]==============[%d]=========[%d]===[%d]========\n", __FUNCTION__,
                   __LINE__, pBmsCtrl->Dual_Cfc, pBmsCtrl->Dual_Bfc);
        }
    }
    if (DUAL_ACK_11 == pBmsData->strBFC.ack)
    {
        tick = tickGet();

        pBmsCtrl->Dual_Bfc = pBmsData->strBFC.ack;

        pBmsCtrl->Dual_Cfc = DUAL_ACK_11;

        pBmsCtrl->Dual_current_offset = pBmsData->strBFC.current_offset;

        printf("===[%s]==============[%d]=========[%d]===[%d]========\n", __FUNCTION__,
               __LINE__, pBmsCtrl->Dual_Cfc, pBmsCtrl->Dual_Bfc);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief      Recv_BCP
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        动力蓄电池充电参数报文解码
 ******************************************************************************
 */
static void Recv_BCP(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;

    memcpy(pBmsData->strBCP.singleHighestVoltage, &pInBuf[index], // 2byte 单体电池最高允许充电电压
           sizeof(pBmsData->strBCP.singleHighestVoltage));
    index += sizeof(pBmsData->strBCP.singleHighestVoltage);

    memcpy(pBmsData->strBCP.highestCurrent, &pInBuf[index], // 2byte 最高允许充电电流
           sizeof(pBmsData->strBCP.highestCurrent));
    index += sizeof(pBmsData->strBCP.highestCurrent);

    memcpy(pBmsData->strBCP.nominalEnergy, &pInBuf[index], // 2byte	动力电池标称总能量
           sizeof(pBmsData->strBCP.nominalEnergy));
    index += sizeof(pBmsData->strBCP.nominalEnergy);

    memcpy(pBmsData->strBCP.highestVoltage, &pInBuf[index], // 2byte 最高允许充电总电压
           sizeof(pBmsData->strBCP.highestVoltage));
    index += sizeof(pBmsData->strBCP.highestVoltage);

    pBmsData->strBCP.highestTemperature = pInBuf[index++]; // 1byte	单体电池最高允许温度

    memcpy(pBmsData->strBCP.SOC, &pInBuf[index], // 整车荷电状态
           sizeof(pBmsData->strBCP.SOC));
    index += sizeof(pBmsData->strBCP.SOC);

    memcpy(pBmsData->strBCP.totalVoltage, &pInBuf[index], // 车辆接口当前电压测量值
           sizeof(pBmsData->strBCP.totalVoltage));
    index += sizeof(pBmsData->strBCP.totalVoltage);
    if (Get_CcuCfgParaEuropeEnable())
    {
        pBmsData->strBCP.plcEvTransType = pInBuf[index++]; // 能量传输类型
    }
    return;
}

/**
******************************************************************************
* @brief       Deal_BCP
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note        动力蓄电池充电参数接收处理
******************************************************************************
*/
static bool_e Deal_BCP(void)
{
    Set_BmsSendRemainTimer(BMS_PGN_CRM, 0);
    Set_BmsSendFlg(BMS_PGN_CRM, eSendFlag_No);
    Set_BmsSendRemainTimer(BMS_PGN_CFC, 0);
    Set_BmsSendFlg(BMS_PGN_CFC, eSendFlag_No);
    Set_BmsSendFlg(BMS_PGN_CML, eSendFlag_No);
    Set_BmsRecvEnable(BMS_PGN_BRM, eRecvEnable_Off);
    Set_BmsRecvTimerEnable(BMS_PGN_BCP, eTimerEnable_Off);

    Set_BmsRecvEnable(BMS_PGN_BFC, eRecvEnable_Off);
    Set_BmsRecvTimerEnable(BMS_PGN_BFC, eTimerEnable_Off);
    if (Get_FaultMask(eErrType_BCPErr))
    {
        // BRM数据项异常故障
        Set_ErrType(eErrType_BCPErr);
        return FALSE;
    }

    if (Check_VehParams() != TRUE)
    {
        if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CML))
        {
            Set_BmsSendRemainTimer(BMS_PGN_CML, 0xFFFF);
            Set_BmsLastSendTimer(BMS_PGN_CML, 0xFFFF);
        }
        Set_BmsRecvEnable(BMS_PGN_BCP, eRecvEnable_Off);
    }
    else
    {
        if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CML))
        {
            Set_BmsSendRemainTimer(BMS_PGN_CML, 0xFFFF);
            Set_BmsLastSendTimer(BMS_PGN_CML, 0xFFFF);
        }

        if (TRUE == Get_EnableFlag(eParaFmt_CfgCTSSendEnable) &&
            0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CTS))
        {
            Set_BmsSendRemainTimer(BMS_PGN_CTS, 0xFFFF);
            Set_BmsLastSendTimer(BMS_PGN_CTS, 0xFFFF);
        }
    }

    if (BMS_STAGE_RECOGNIZE == Get_BmsStage())
    {
        Set_BmsStage(BMS_STAGE_PARACONFIG);
    }

    return TRUE;
}
/**
 ******************************************************************************
 * @brief      Recv_BRO
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        电池充电准备就绪报文解码
 ******************************************************************************
 */
static void Recv_BRO(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;

    pBmsData->strBRO.bmsPrepareState = pInBuf[index++]; // 车辆是否准备好

    return;
}

/**
******************************************************************************
* @brief       Deal_BRO
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note        动力蓄电池充电参数接收处理
******************************************************************************
*/
static bool_e Deal_BRO(void)
{
    BMS_DATA *pBmsData = &bmsData;
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    Set_BmsRecvEnable(BMS_PGN_BCP, eRecvEnable_Off);

    Set_BmsRecvTimer(BMS_PGN_BRO, 0x00);
    Set_BmsRecvFlag(BMS_PGN_BRO, eRecvFlag_Yes);
    if (0x00 == pBmsData->strBRO.bmsPrepareState)
    {
        if (Check_VehParams() != TRUE)
        {
            if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CML))
            {
                Set_BmsSendRemainTimer(BMS_PGN_CML, 0xFFFF);
                Set_BmsLastSendTimer(BMS_PGN_CML, 0xFFFF);
            }
            Set_BmsRecvEnable(BMS_PGN_BRO, eRecvEnable_Off);
            return FALSE;
        }
    }

    if (0xAA == pBmsCtrl->lastBROState)
    {
        if (0xAA != pBmsData->strBRO.bmsPrepareState)
        {
            Set_ErrType(eErrType_BROErr);

            // 根据协议一致性需要立即停止通信
            Set_BmsSendRemainTimer(BMS_PGN_CRO, 0x00);
            Stop_BMS(FALSE);
        }
    }

    if (0xAA == pBmsData->strBRO.bmsPrepareState)
    {
        pBmsCtrl->lastBROState = pBmsData->strBRO.bmsPrepareState;
        Set_BmsRecvTimerEnable(BMS_PGN_BRO, eTimerEnable_Off);

        Set_BmsRecvTimer(BMS_PGN_BRO_AA, (BMS_PGN_BRO_AA_OT - 5 * 1000));
        Set_BmsRecvFlag(BMS_PGN_BRO_AA, eRecvFlag_Yes);
        if ((eSwitchState_OFF == Get_SwitchState(SXIO_IN_K1) && eSwitchState_OFF == Get_SwitchState(SXIO_IN_K2))) /**k1k2断开完成才能发送CRO_00*/
        {
            if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CRO))
            {
                Set_BmsSendRemainTimer(BMS_PGN_CRO, 0xFFFF);
                Set_BmsLastSendTimer(BMS_PGN_CRO, 0xFFFF);
                pBmsCtrl->spn_2830 = 0x00;
            }

            Set_BmsSendRemainTimer(BMS_PGN_CTS, 0);
            Set_BmsSendRemainTimer(BMS_PGN_CML, 0);
        }
        chargeReconnectFlg(TRUE, enumAllowFlag_Allow); /**<重连时开启允许充电    在超时时禁止充电*/
                                                       //        trace(TR_CHARGE, "ChargeReconnectFlg9 = %d\n",chargeReconnectFlg(FALSE,FALSE));
    }

    return FALSE;
}

/**
 ******************************************************************************
 * @brief      Recv_BCL
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        电池充电需求报文解码
 ******************************************************************************
 */
static void Recv_BCL(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;
    //    uint16 Plc_Cur = 0;
    //    uint16 Ret_Cur = 0;
    memcpy(pBmsData->strBCL.voltageDemand, &pInBuf[index], // 2byte 充电电压需求
           sizeof(pBmsData->strBCL.voltageDemand));
    index += sizeof(pBmsData->strBCL.voltageDemand);

    //    if(Get_CcuCfgParaEuropeEnable())/**/
    //    {
    //
    //        memcpy(pBmsData->strBCL.currentDemand, &pInBuf[index],
    //                   sizeof(pBmsData->strBCL.currentDemand));
    //        Plc_Cur = TwoUint8ToUint16(pBmsData->strBCL.currentDemand);
    //        Ret_Cur = (BITS(Plc_Cur,15) ? (4000 + (Plc_Cur & 0x7fff)) :
    //                                  (4000 - Plc_Cur));
    //        Uint16ToTwoUint8(pBmsData->strBCL.currentDemand,Ret_Cur);
    //        index += sizeof(pBmsData->strBCL.currentDemand);
    //    }
    //    else
    {
        memcpy(pBmsData->strBCL.currentDemand, &pInBuf[index], // 2byte 充电电流需求
               sizeof(pBmsData->strBCL.currentDemand));
        index += sizeof(pBmsData->strBCL.currentDemand);
    }
    pBmsData->strBCL.bmsChargeMode = pInBuf[index++]; // 1byte	充电模式（0x01:恒压充电  0x02:恒流充电）
    if (Get_CcuCfgParaEuropeEnable())
    {
        pBmsData->strBCL.plcMaxVolLimit[0] = pInBuf[index++]; // 最高允许充电电压
        pBmsData->strBCL.plcMaxVolLimit[1] = pInBuf[index++];
        pBmsData->strBCL.plcPreChargeStatus = pInBuf[index++]; // 预充电状态
    }
    return;
}

/**
******************************************************************************
* @brief       Deal_BCL
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note        电池充电需求处理
******************************************************************************
*/
static bool_e Deal_BCL(void)
{
    // 关闭BRO的接收使能
    Set_BmsRecvEnable(BMS_PGN_BRO, eRecvEnable_Off);

    Set_BmsRecvEnable(BMS_PGN_BRO_AA, eRecvEnable_Off);
    Set_BmsRecvTimerEnable(BMS_PGN_BRO_AA, eTimerEnable_Off);

    Set_BmsSendRemainTimer(BMS_PGN_CRO, 0); // 2023版27930协议只要收到BCL报文就可以停止CRO报文的发送

    if (eSendFlag_Yes != Get_BmsSendFlg(BMS_PGN_CCS) &&
        0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CCS))
    {
        Set_BmsSendRemainTimer(BMS_PGN_CCS, 0xFFFF); // 2023版27930协议只要收到BCL报文（不管是否收到BCS报文），就可以开始CCS报文的发送
        Set_BmsLastSendTimer(BMS_PGN_CCS, 0xFFFF);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief      Recv_BCS
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        电池充电总状态报文解码
 ******************************************************************************
 */
static void Recv_BCS(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;

    memcpy(pBmsData->strBCS.voltageMeasuredValue, &pInBuf[index], // 2byte 车辆接口当前电压测量值
           sizeof(pBmsData->strBCS.voltageMeasuredValue));
    index += sizeof(pBmsData->strBCS.voltageMeasuredValue);

    memcpy(pBmsData->strBCS.currentMeasuredValue, &pInBuf[index], // 2byte	车辆接口当前电流测量值
           sizeof(pBmsData->strBCS.currentMeasuredValue));
    index += sizeof(pBmsData->strBCS.currentMeasuredValue);

    memcpy(pBmsData->strBCS.highestSingleVolAndNO, &pInBuf[index], // 2byte	最高单体蓄电池电压及其组号
           sizeof(pBmsData->strBCS.highestSingleVolAndNO));
    index += sizeof(pBmsData->strBCS.highestSingleVolAndNO);

    pBmsData->strBCS.curSOC = pInBuf[index++]; // 1byte 当前荷电状态

    memcpy(pBmsData->strBCS.remainTime, &pInBuf[index], // 2byte 估算剩余充电时间
           sizeof(pBmsData->strBCS.remainTime));
    index += sizeof(pBmsData->strBCS.remainTime);

    return;
}

/**
******************************************************************************
* @brief       Deal_BCS
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note        电池充电总状态处理
******************************************************************************
*/
static bool_e Deal_BCS(void)
{
    // 2023版27930协议的正常充电流程图中在没有接收到BCS时，不判超时
    if (Get_BmsRecvTimerEnable(BMS_PGN_BCS) != eTimerEnable_On)
    {
        Set_BmsRecvTimerEnable(BMS_PGN_BCS, eTimerEnable_On);
        Set_BmsRecvTimer(BMS_PGN_BCS, 0x00);
        Set_BmsRecvFlag(BMS_PGN_BCS, eRecvFlag_Yes);
    }

    if (eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BCL))
    {
        // Set_BmsSendRemainTimer(BMS_PGN_CRO, 0); //2023版27930协议只要收到BCL报文就停止CRO报文的发送，不论是否收到BCS保文

        if (eSendFlag_Yes != Get_BmsSendFlg(BMS_PGN_CCS) &&
            0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CCS))
        {
            Set_BmsSendRemainTimer(BMS_PGN_CCS, 0xFFFF);
            Set_BmsLastSendTimer(BMS_PGN_CCS, 0xFFFF);
        }
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief      Recv_BSM
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        蓄电池状态信息解码
 ******************************************************************************
 */
static void Recv_BSM(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;

    pBmsData->strBSM.highestSingleVoltageNO = pInBuf[index++]; // 1byte 单体蓄电池电压所在编号
    pBmsData->strBSM.highestTemperature = pInBuf[index++];     // 1byte	当前电池最高温度
    pBmsData->strBSM.highestTemperatureNO = pInBuf[index++];   // 1byte	最高电池温度检测点编号
    pBmsData->strBSM.lowestTemperature = pInBuf[index++];      // 1byte	当前电池最低温度
    pBmsData->strBSM.lowestTemperatureNO = pInBuf[index++];    // 1byte	最低电池温度检测点编号
    pBmsData->strBSM.strBatteryState.state = pInBuf[index++];
    pBmsData->strBSM.strBatteryCtrl.ctrlInfo = pInBuf[index++];

    return;
}

/**
******************************************************************************
* @brief       Deal_BSM
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note        蓄电池状态信息处理
******************************************************************************
*/
static bool_e Deal_BSM(void)
{
    BMS_DATA *pBmsData = &bmsData;

    /*2023版27930协议规定在充电机没有接收BSM报文之前，不应以BSM报文超时为由中止充电*/
    if (Get_BmsRecvTimerEnable(BMS_PGN_BSM) != eTimerEnable_On)
    {
        Set_BmsRecvTimerEnable(BMS_PGN_BSM, eTimerEnable_On);
        Set_BmsRecvTimer(BMS_PGN_BSM, 0x00);
        Set_BmsRecvFlag(BMS_PGN_BSM, eRecvFlag_Yes);
    }

    // 单体电池电压状态
    if ((!Get_FaultMask(eErrType_PhySingleVoltooHighBSM)) && 0x00 == pBmsData->strBSM.strBatteryState.singleVoltageState)
    {
        Clr_ErrType(eErrType_PhySingleVoltooHighBSM);
        Clr_ErrType(eErrType_PhySingleVoltooLowBSM);
    }
    else if ((!Get_FaultMask(eErrType_PhySingleVoltooLowBSM)) && 0x01 == pBmsData->strBSM.strBatteryState.singleVoltageState)
    {
        Clr_ErrType(eErrType_PhySingleVoltooLowBSM);
        Set_ErrType(eErrType_PhySingleVoltooHighBSM);
    }
    else if (0x02 == pBmsData->strBSM.strBatteryState.singleVoltageState || (Get_FaultMask(eErrType_PhySingleVoltooLowBSM)))
    {
        Set_ErrType(eErrType_PhySingleVoltooLowBSM);
        Clr_ErrType(eErrType_PhySingleVoltooHighBSM);
    }

    // SOC状态
    if ((!Get_FaultMask(eErrType_SOCTooLowBSM)) && 0x00 == pBmsData->strBSM.strBatteryState.batterySOCState)
    {
        Clr_ErrType(eErrType_SOCTooLowBSM);
        Clr_ErrType(eErrType_SOCTooHighBSM);
    }
    else if ((!Get_FaultMask(eErrType_SOCTooLowBSM)) && 0x01 == pBmsData->strBSM.strBatteryState.batterySOCState)
    {
        Clr_ErrType(eErrType_SOCTooLowBSM);
        Set_ErrType(eErrType_SOCTooHighBSM);
    }
    else if (0x02 == pBmsData->strBSM.strBatteryState.batterySOCState || (Get_FaultMask(eErrType_SOCTooLowBSM)))
    {
        Set_ErrType(eErrType_SOCTooLowBSM);
        Clr_ErrType(eErrType_SOCTooHighBSM);
    }

    // 过电流
    if (0x00 == pBmsData->strBSM.strBatteryState.chargeCurrentState)
    {
        Clr_ErrType(eErrType_OutputCurOverLimitBSM);
    }
    else if (0x01 == pBmsData->strBSM.strBatteryState.chargeCurrentState)
    {
        Set_ErrType(eErrType_OutputCurOverLimitBSM);
    }

    // 温度状态
    if (0x00 == pBmsData->strBSM.strBatteryState.batteryTemperatureState)
    {
        Clr_ErrType(eErrType_TempOverLimitBSM);
    }
    else if (0x01 == pBmsData->strBSM.strBatteryState.batteryTemperatureState)
    {
        Set_ErrType(eErrType_TempOverLimitBSM);
    }

    // 绝缘状态
    if (0x00 == pBmsData->strBSM.strBatteryCtrl.imdState)
    {
        Clr_ErrType(eErrType_ImdErrBSM);
    }
    else if (0x01 == pBmsData->strBSM.strBatteryCtrl.imdState)
    {
        Set_ErrType(eErrType_ImdErrBSM);
    }

    // 输出连接器状态
    if (0x00 == pBmsData->strBSM.strBatteryCtrl.connectState)
    {
        Clr_ErrType(eErrType_PhyConErrBSM);
    }
    else if (0x01 == pBmsData->strBSM.strBatteryCtrl.connectState)
    {
        Set_ErrType(eErrType_PhyConErrBSM);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief      Recv_BMV
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        单体动力蓄电池电压解码
 ******************************************************************************
 */
static void Recv_BMV(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint16 index = 0;

    for (index = 0; index < 256; index++)
    {
        CHECK_BMS_LEN(2);
        memcpy(pBmsData->strBMV.singleVoltage[index], &pInBuf[index * 2], 2);
    }

    return;
}

/**
******************************************************************************
* @brief       Deal_BMV
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note         单体动力蓄电池电压处理
******************************************************************************
*/
static bool_e Deal_BMV(void)
{
    // 无处理
    return TRUE;
}
/**
 ******************************************************************************
 * @brief      Recv_BMT
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        单体动力蓄电池温度解码
 ******************************************************************************
 */
static void Recv_BMT(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;

    for (index = 0; index < 128; index++)
    {
        CHECK_BMS_LEN(1);
        pBmsData->strBMT.batteryTemperature[index] = pInBuf[index];
    }

    return;
}

/**
******************************************************************************
* @brief       Deal_BMT
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note         单体动力蓄电池温度处理
******************************************************************************
*/
static bool_e Deal_BMT(void)
{
    // 无处理
    return TRUE;
}
/**
 ******************************************************************************
 * @brief      Recv_BSP
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        单体动力蓄电池预留报文解码
 ******************************************************************************
 */
static void Recv_BSP(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;

    for (index = 0; index < 16; index++)
    {
        CHECK_BMS_LEN(1);
        pBmsData->strBSP.batteryReserve[index] = pInBuf[index];
    }

    return;
}

/**
******************************************************************************
* @brief       Deal_BSP
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note         单体动力蓄电池温度处理
******************************************************************************
*/
static bool_e Deal_BSP(void)
{
    // 无处理
    return TRUE;
}

/**
 ******************************************************************************
 * @brief      Recv_BST
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        车辆停止充电报文解码
 ******************************************************************************
 */
static void Recv_BST(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;
    uint8 isoffk1k2 = FALSE;

    if (DEV_STD_GB == Get_CcuCfgParaEuropeEnable())
    {
        trace(TR_BMS_PROCESS, "==!! 接收到BST[%02X-%02X%02X-%02X]\n",
              pInBuf[index], pInBuf[index + 1], pInBuf[index + 2], pInBuf[index + 3]);

        pBmsData->strBST.gbt.stopCause = pInBuf[index++]; // 1byte 车辆中止充电原因

        memcpy(pBmsData->strBST.gbt.faultCause, &pInBuf[index], sizeof(pBmsData->strBST.gbt.faultCause)); // 2byte 车辆中止充电故障原因
        index += 2;

        pBmsData->strBST.gbt.erroCause = pInBuf[index++]; // 1byte 车辆中止充电错误原因

        trace(TR_BMS_PROCESS, "==!! 接收到BST[%02X-%02X%02X-%02X]\n",
              pBmsData->strBST.gbt.stopCause, pBmsData->strBST.gbt.faultCause[0],
              pBmsData->strBST.gbt.faultCause[1], pBmsData->strBST.gbt.erroCause);
        if ((pBmsData->strBST.gbt.faultCause[0] & 0x03) == 0x01)
            Set_ErrType(eErrType_BmsFaultStopErr);
        if ((pBmsData->strBST.gbt.faultCause[0] & 0x0C) == 0x04)
            Set_ErrType(eErrType_BmsFaultStopErr);
        if ((pBmsData->strBST.gbt.faultCause[0] & 0x30) == 0x10)
            Set_ErrType(eErrType_BmsFaultStopErr);
        if ((pBmsData->strBST.gbt.faultCause[0] & 0xC0) == 0x40)
            Set_ErrType(eErrType_BmsFaultStopErr);
        if ((pBmsData->strBST.gbt.faultCause[1] & 0x03) == 0x01)
            Set_ErrType(eErrType_BmsFaultStopErr);
        if ((pBmsData->strBST.gbt.faultCause[1] & 0x0C) == 0x04)
            Set_ErrType(eErrType_BmsFaultStopErr);
        if ((pBmsData->strBST.gbt.faultCause[1] & 0x30) == 0x10)
            Set_ErrType(eErrType_BmsFaultStopErr);
        if ((pBmsData->strBST.gbt.faultCause[1] & 0xC0) == 0x40)
            Set_ErrType(eErrType_BmsFaultStopErr);
        if (Get_CcuCfgParaEuropeEnable() && (pBmsData->strBST.gbt.erroCause & 0xC0))
        {
            if ((pBmsData->strBST.gbt.erroCause & 0x40)) /*车辆已经充满*/
            {
                pBmsData->strBST.gbt.stopCause |= 0x01;
                trace(TR_BMS_PROCESS, "==!! 接收到BST[%x]======车辆已充满\n",
                      pBmsData->strBST.gbt.stopCause);
            }
            else if ((pBmsData->strBST.gbt.erroCause & 0x80))
            {
                trace(TR_BMS_PROCESS, "==!! 接收到BST[%x]======桩请求结束\n",
                      pBmsData->strBST.gbt.erroCause);
            }
            else
            {
                trace(TR_BMS_PROCESS, "==!! 接收到BST[%x]======车辆原因停止\n",
                      pBmsData->strBST.gbt.erroCause);
            }
        }

        if (((pBmsData->strBST.gbt.erroCause & 0x03) == 0x01) ||
            ((pBmsData->strBST.gbt.erroCause & 0x03) == 0x02))
        {
            trace(TR_BMS_PROCESS, "==BST错误原因[%x]:电流过大======\n", pBmsData->strBST.gbt.erroCause);
            Set_ErrType(eErrType_BmsFaultStopErr);
        }

        if (((pBmsData->strBST.gbt.erroCause & 0x0C) == 0x04) ||
            ((pBmsData->strBST.gbt.erroCause & 0x0C) == 0x08))
        {
            trace(TR_BMS_PROCESS, "==BST错误原因[%x]:电压异常======\n", pBmsData->strBST.gbt.erroCause);
            Set_ErrType(eErrType_BmsFaultStopErr);
        }
        // 2023 A类系统中止充电原因扩充第5-6位  00：匹配  01：参数不匹配   10：不可信状态
        if (((pBmsData->strBST.gbt.erroCause & 0x30) == 0x10) ||
            ((pBmsData->strBST.gbt.erroCause & 0x30) == 0x20))
        {
            Set_ErrType(eErrType_BmsFaultStopErr);
        }
    }

    if (DEV_STD_RKN == Get_CcuCfgParaEuropeEnable())
    {
        pBmsData->strBST.ec_rkn.stopCauseByte1.byte = pInBuf[index++];
        pBmsData->strBST.ec_rkn.faultCauseByte2.byte = pInBuf[index++];
        pBmsData->strBST.ec_rkn.faultCauseByte3.byte = pInBuf[index++];
        pBmsData->strBST.ec_rkn.errorCauseByte4.byte = pInBuf[index++];
        pBmsData->strBST.ec_rkn.messageSequence = pInBuf[index++];
        pBmsData->strBST.ec_rkn.errorCodeII = pInBuf[index++];
        pBmsData->strBST.ec_rkn.errorCodeI = pInBuf[index++];
        pBmsData->strBST.ec_rkn.plcExtension.byte = pInBuf[index++];
        trace(TR_BMS_PROCESS, "==!! 接收到 瑞凯诺欧标BST [messageSequence:%02x]"
                              "[errorCodeII : %02x],[errorCodeI : %02x]"
                              "[plc_extension : %02x]\n",
              pBmsData->strBST.ec_rkn.messageSequence, pBmsData->strBST.ec_rkn.errorCodeII,
              pBmsData->strBST.ec_rkn.errorCodeI, pBmsData->strBST.ec_rkn.plcExtension.byte);

        if (pBmsData->strBEM.ec_rkn.plcExtension.bits.cpPpErrorCode > 0x02 || (pBmsData->strBEM.ec_rkn.plcExtension.bits.evErrorCode >= GQ_DETECT_CP_ZERO_II && pBmsData->strBEM.ec_rkn.errorCodeII <= GQ_DETECT_CP_STATE_EF_II))
        {
            isoffk1k2 = TRUE;
        }

        if ((pBmsData->strBEM.ec_rkn.errorCodeII >= GQ_SLAC_ERROR_INIT && pBmsData->strBEM.ec_rkn.errorCodeII <= GQ_SLAC_ERROR_LINK_DETECT_II))
        {
            isoffk1k2 = TRUE;
        }
    }
    else if (DEV_STD_GW == Get_CcuCfgParaEuropeEnable() && (CHARGE_MODE_AUTO == Get_ChargeMode()))
    {
        memcpy(pBmsData->strBEM.ec_gw.reserved, &pInBuf[index], sizeof(pBmsData->strBEM.ec_gw.reserved));
        index += sizeof(pBmsData->strBEM.ec_gw.reserved);
        pBmsData->strBEM.ec_gw.zrnStopReason = pInBuf[index++];
        memcpy(pBmsData->strBEM.ec_gw.zclStopReason, &pInBuf[index], sizeof(pBmsData->strBEM.ec_gw.zclStopReason));
        index += sizeof(pBmsData->strBEM.ec_gw.zclStopReason);
        pBmsData->strBEM.ec_gw.zrnCommonStopReason = pInBuf[index++];
        pBmsData->strBEM.ec_gw.zrnEvErrorStopReason = pInBuf[index++];

        trace(TR_BMS_PROCESS, "==!! 接收到 吉瓦特 欧标BEM [zrnStopReason:%02x]"
                              "[zclStopReasonL : %02x],[zclStopReasonH : %02x],[zrnCommonStopReason : %02x]"
                              "[zrnEvErrorStopReason : %02x]\n",
              pBmsData->strBEM.ec_gw.zrnStopReason, pBmsData->strBEM.ec_gw.zclStopReason[0], pBmsData->strBEM.ec_gw.zclStopReason[1],
              pBmsData->strBEM.ec_gw.zrnCommonStopReason, pBmsData->strBEM.ec_gw.zrnEvErrorStopReason);
        //        Set_InitPrintf(1,TRUE);
        if (RegDataToUint16(pBmsData->strBEM.ec_gw.zclStopReason) >= StopCause_CCUStop_Normal && RegDataToUint16(pBmsData->strBEM.ec_gw.zclStopReason) < StopCause_CCS2_ServiceCategoryFlag_Error)

        {
            if (!Check_ErrType(eErrType_BmsFaultStopErr))
            {
                Set_ErrType(eErrType_BmsFaultStopErr);
            }
        }
        else if (RegDataToUint16(pBmsData->strBEM.ec_gw.zclStopReason) >= StopCause_CCS2_ServiceCategoryFlag_Error)
        {
            isoffk1k2 = TRUE;
            K1_K2_OFF; /*紧急故障直接断*/
            Set_k1k2OffDelay(0);
            if (!Check_ErrType(eErrType_GunConnectErr))
            {
                Set_ErrType(eErrType_GunConnectErr);
            }
            trace(TR_CHARGE, "K1k2OFF160!!!!!!!!!!!!!!!!!!!!\n");
        }

        if ((pBmsData->strBEM.ec_gw.zrnCommonStopReason >= GW_NO_ERROR))
        {
            if (!Check_ErrType(eErrType_BmsFaultStopErr))
            {
                Set_ErrType(eErrType_BmsFaultStopErr);
            }
        }
    }
    if (isoffk1k2)
    {
        if ((eSwitchState_ON == Get_SwitchState(SXIO_IN_K2)) && (eSwitchState_ON == Get_SwitchState(SXIO_IN_K1)))
        {
            K1_K2_OFF; /*紧急故障直接断*/
            Set_k1k2OffDelay(0);
            trace(TR_CHARGE, "K1k2OFF160!!!!!!!!!!!!!!!!!!!!\n");
        }
        if (!Check_ErrType(eErrType_GunConnectErr))
        {
            Set_ErrType(eErrType_GunConnectErr);
        }
        isoffk1k2 = FALSE;
    }

    return;
}

/**
******************************************************************************
* @brief       Deal_BST
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note         车辆停止充电报文处理
******************************************************************************
*/
static bool_e Deal_BST(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CST))
    {
        if (eSendFlag_Yes != Get_BmsSendFlg(BMS_PGN_CST))
        {
            trace(TR_BMS_PROCESS, "BMS_PGN_CST 未发送过.\n");
            /*
             * 没发送过CST,说明车辆主动终止充电
             * 已经发送过CST，说明充电机主动终止，这个时候没必要再发送CST
             *
             * */
            Set_StopSrc(eChargeStopFlag_BMS);
            Set_BmsSendRemainTimer(BMS_PGN_CST, 10 * sysClkRateGet());
            Set_BmsLastSendTimer(BMS_PGN_CST, 0xFFFF);
            Set_BmsStartTimer(BMS_PGN_CST);
        }

        Set_BmsRecvEnable(BMS_PGN_BCL, eRecvEnable_Off);
        Set_BmsRecvTimerEnable(BMS_PGN_BCL, eTimerEnable_Off);

        Set_BmsRecvEnable(BMS_PGN_BCS, eRecvEnable_Off);
        Set_BmsRecvTimerEnable(BMS_PGN_BCS, eTimerEnable_Off);

        Set_BmsRecvEnable(BMS_PGN_BSM, eRecvEnable_Off);
        Set_BmsRecvEnable(BMS_PGN_BMV, eRecvEnable_Off);
        Set_BmsRecvEnable(BMS_PGN_BMT, eRecvEnable_Off);
        Set_BmsRecvEnable(BMS_PGN_BSP, eRecvEnable_Off);

        Set_BmsSendRemainTimer(BMS_PGN_CCS, 0);
    }

    Set_BmsRecvEnable(BMS_PGN_BSD, eRecvEnable_On);

    Set_BmsRecvEnable(BMS_PGN_BST, eRecvEnable_Off);
    Set_BmsRecvTimerEnable(BMS_PGN_BST, eTimerEnable_Off);

    if (BMS_STAGE_CHARGING == Get_BmsStage())
    {
        Set_BmsStage(BMS_STAGE_STOP);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief      Recv_BSD
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        BMS统计数据解码
 ******************************************************************************
 */
static void Recv_BSD(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;

    trace(TR_BMS_PROCESS, "接收到BSD,tick = %d\n", tickGet());

    pBmsData->strBSD.stopSOC = pInBuf[index++]; // 1byte 中止荷电状态

    memcpy(&pBmsData->strBSD.singleLowestVoltage, &pInBuf[index], // 2byte 单体电池最低电压
           sizeof(pBmsData->strBSD.singleLowestVoltage));
    index += sizeof(pBmsData->strBSD.singleLowestVoltage);

    memcpy(&pBmsData->strBSD.singleHighestestVoltage, &pInBuf[index], // 2byte	单体电池最高电压
           sizeof(pBmsData->strBSD.singleHighestestVoltage));
    index += sizeof(pBmsData->strBSD.singleHighestestVoltage);

    pBmsData->strBSD.lowestTemperature = pInBuf[index++]; // 1byte 动力蓄电池最低温度

    pBmsData->strBSD.highestTemperature = pInBuf[index++]; // 1byte	动力蓄电池最高温度

    if (Get_CcuCfgParaEuropeEnable())
    {
        pBmsData->strBSD.plcWeldDetectReq = pInBuf[index++]; // 粘连检测要求
    }

    return;
}

/**
******************************************************************************
* @brief       Deal_BSD
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note         BMS统计数据解码处理
******************************************************************************
*/
static bool_e Deal_BSD(void)
{
    Set_BmsSendRemainTimer(BMS_PGN_CST, 0x00);

    if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CSD))
    {
        Set_BmsSendRemainTimer(BMS_PGN_CSD, 0xFFFF);
        Set_BmsLastSendTimer(BMS_PGN_CSD, 0xFFFF);
    }

    return TRUE;
}
/**
 ******************************************************************************
 * @brief      Recv_BEM
 * @param[in]   uint8 *pInBuf                  输入的数据
 * @param[in]   uint32 len                     输入的数据长度
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        BMS错误报文解码
 ******************************************************************************
 */
static void Recv_BEM(uint8 *pInBuf, uint32 len)
{
    BMS_DATA *pBmsData = &bmsData;
    uint8 index = 0;
    uint8 isoffk1k2 = FALSE;

    if (DEV_STD_GB == Get_CcuCfgParaEuropeEnable())
    {
        pBmsData->strBEM.gbt.errorInfo1 = pInBuf[index++];

        pBmsData->strBEM.gbt.errorInfo2 = pInBuf[index++];

        pBmsData->strBEM.gbt.errorInfo3 = pInBuf[index++];

        pBmsData->strBEM.gbt.errorInfo4 = pInBuf[index++];
    }
    else if (DEV_STD_RKN == Get_CcuCfgParaEuropeEnable() && (CHARGE_MODE_AUTO == Get_ChargeMode()))
    {
        pBmsData->strBEM.ec_rkn.timeoutFlags1.byte = pInBuf[index++];
        pBmsData->strBEM.ec_rkn.timeoutFlags2.byte = pInBuf[index++];
        pBmsData->strBEM.ec_rkn.timeoutFlags3.byte = pInBuf[index++];
        pBmsData->strBEM.ec_rkn.timeoutFlags4.byte = pInBuf[index++];
        pBmsData->strBEM.ec_rkn.messageSequence = pInBuf[index++];
        pBmsData->strBEM.ec_rkn.errorCodeII = pInBuf[index++];
        pBmsData->strBEM.ec_rkn.errorCodeI = pInBuf[index++];
        pBmsData->strBEM.ec_rkn.plcExtension.byte = pInBuf[index++];

        trace(TR_BMS_PROCESS, "==!! 接收到 瑞凯诺欧标BEM [messageSequence:%02x]"
                              "[plcErrorcodeII : %02x],[plcErrorcodeI : %02x]"
                              "[plc_extension : %02x]\n",
              pBmsData->strBEM.ec_rkn.messageSequence, pBmsData->strBEM.ec_rkn.errorCodeII,
              pBmsData->strBEM.ec_rkn.errorCodeI, pBmsData->strBEM.ec_rkn.plcExtension);

        if (pBmsData->strBEM.ec_rkn.plcExtension.bits.cpPpErrorCode > 0x02 || (pBmsData->strBEM.ec_rkn.plcExtension.bits.evErrorCode >= GQ_DETECT_CP_ZERO_II && pBmsData->strBEM.ec_rkn.errorCodeII <= GQ_DETECT_CP_STATE_EF_II))
        {
            isoffk1k2 = TRUE;
        }

        if ((pBmsData->strBEM.ec_rkn.errorCodeII >= GQ_SLAC_ERROR_INIT && pBmsData->strBEM.ec_rkn.errorCodeII <= GQ_SLAC_ERROR_LINK_DETECT_II))
        {
            isoffk1k2 = TRUE;
        }
    }
    else if (DEV_STD_GW == Get_CcuCfgParaEuropeEnable() && (CHARGE_MODE_AUTO == Get_ChargeMode()))
    {
        memcpy(pBmsData->strBEM.ec_gw.reserved, &pInBuf[index], sizeof(pBmsData->strBEM.ec_gw.reserved));
        index += sizeof(pBmsData->strBEM.ec_gw.reserved);
        pBmsData->strBEM.ec_gw.zrnStopReason = pInBuf[index++];
        memcpy(pBmsData->strBEM.ec_gw.zclStopReason, &pInBuf[index], sizeof(pBmsData->strBEM.ec_gw.zclStopReason));
        index += sizeof(pBmsData->strBEM.ec_gw.zclStopReason);
        pBmsData->strBEM.ec_gw.zrnCommonStopReason = pInBuf[index++];
        pBmsData->strBEM.ec_gw.zrnEvErrorStopReason = pInBuf[index++];

        trace(TR_BMS_PROCESS, "==!! 接收到 吉瓦特 欧标BEM [zrnStopReason:%02x]"
                              "[zclStopReasonL : %02x],[zclStopReasonH : %02x],[zrnCommonStopReason : %02x]"
                              "[zrnEvErrorStopReason : %02x]\n",
              pBmsData->strBEM.ec_gw.zrnStopReason, pBmsData->strBEM.ec_gw.zclStopReason[0], pBmsData->strBEM.ec_gw.zclStopReason[1],
              pBmsData->strBEM.ec_gw.zrnCommonStopReason, pBmsData->strBEM.ec_gw.zrnEvErrorStopReason);
        //        Set_InitPrintf(1,TRUE);
        if (RegDataToUint16(pBmsData->strBEM.ec_gw.zclStopReason) >= StopCause_CCUStop_Normal && RegDataToUint16(pBmsData->strBEM.ec_gw.zclStopReason) < StopCause_CCS2_ServiceCategoryFlag_Error)

        {
            if (!Check_ErrType(eErrType_BmsFaultStopErr))
            {
                Set_ErrType(eErrType_BmsFaultStopErr);
            }
        }
        else if (RegDataToUint16(pBmsData->strBEM.ec_gw.zclStopReason) >= StopCause_CCS2_ServiceCategoryFlag_Error)
        {
            isoffk1k2 = TRUE;
            K1_K2_OFF; /*紧急故障直接断*/
            Set_k1k2OffDelay(0);
            if (!Check_ErrType(eErrType_GunConnectErr))
            {
                Set_ErrType(eErrType_GunConnectErr);
            }
            trace(TR_CHARGE, "K1k2OFF160!!!!!!!!!!!!!!!!!!!!\n");
        }

        if ((pBmsData->strBEM.ec_gw.zrnCommonStopReason >= GW_NO_ERROR))
        {
            if (!Check_ErrType(eErrType_BmsFaultStopErr))
            {
                Set_ErrType(eErrType_BmsFaultStopErr);
            }
        }
    }
    if (isoffk1k2)
    {
        if ((eSwitchState_ON == Get_SwitchState(SXIO_IN_K2)) && (eSwitchState_ON == Get_SwitchState(SXIO_IN_K1)))
        {
            K1_K2_OFF; /*紧急故障直接断*/
            Set_k1k2OffDelay(0);
            trace(TR_CHARGE, "K1k2OFF160!!!!!!!!!!!!!!!!!!!!\n");
        }
        if (!Check_ErrType(eErrType_GunConnectErr))
        {
            Set_ErrType(eErrType_GunConnectErr);
        }
        isoffk1k2 = FALSE;
    }

    return;
}

/**
******************************************************************************
* @brief       Deal_BEM
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note         BMS错误报文处理
******************************************************************************
*/
static bool_e Deal_BEM(void)
{
    BMS_DATA *pBmsData = &bmsData;
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    Set_BmsRecvFlag(BMS_PGN_BEM, eRecvFlag_Yes);

    lastBEMTime = tickGet();

    if (eComState_Normal == pBmsCtrl->comState)
    {
        if (DEV_STD_GB != Get_CcuCfgParaEuropeEnable())
        {
            if (BMS_STAGE_SHAKEHAND == Get_BmsStage())
            {
                pBmsCtrl->overTimePgnforBms = 0;
                return FALSE;
            }
        }

        if (pBmsCtrl->overTimePgnforBms == 0x00)
        {
            if (0 != (pBmsData->strBEM.gbt.errorInfo1 & 0x03) ||
                0 != ((pBmsData->strBEM.gbt.errorInfo1 >> 2) & 0x03))
            {
                pBmsCtrl->overTimePgnforBms = BMS_PGN_CRM;
            }
            else if (0 != (pBmsData->strBEM.gbt.errorInfo2 & 0x03))
            {
                pBmsCtrl->overTimePgnforBms = BMS_PGN_CTS;
            }
            else if (0 != ((pBmsData->strBEM.gbt.errorInfo2 >> 2) & 0x03))
            {
                pBmsCtrl->overTimePgnforBms = BMS_PGN_CRO;
            }
            else if (0 != (pBmsData->strBEM.gbt.errorInfo3 & 0x03))
            {
                pBmsCtrl->overTimePgnforBms = BMS_PGN_CCS;
            }
            else if (0 != ((pBmsData->strBEM.gbt.errorInfo3 >> 2) & 0x03))
            {
                pBmsCtrl->overTimePgnforBms = BMS_PGN_CST;
            }
            else if (0 != (pBmsData->strBEM.gbt.errorInfo4 & 0x03))
            {
                pBmsCtrl->overTimePgnforBms = BMS_PGN_CSD;
            }
        }
        if ((BMS_STAGE_SHAKEHAND == Get_BmsStage() && BMS_PGN_CRM == pBmsCtrl->overTimePgnforBms) ||
            BMS_PGN_CST == pBmsCtrl->overTimePgnforBms ||
            BMS_PGN_CSD == pBmsCtrl->overTimePgnforBms)
        {
            pBmsCtrl->overTimePgnforBms = 0;
            return FALSE;
        }

        pBmsCtrl->comState = eComState_TimeOut;
        pBmsCtrl->overTimeDealFlg = 0x00;
    }

    return TRUE;
}

/**
******************************************************************************
* @brief       检测是否可以设置BMS通信故障
* @param[in]
* @param[in]
* @param[out]
* @retval      FALSE-不可设置   TRUE-可设置
*
* @details     1.导引故障时不可置
*              2.在停止充电状态下，不可置
*              3.BSM报异常时不可置
*
* @note
******************************************************************************
*/
static bool_e IsCanSetBmsComErr(void)
{
    if (TRUE == Check_ErrType(eErrType_GunConnectErr))
    {
        return FALSE;
    }

    if (TRUE == Check_ErrType(eErrType_OutputCurOverLimitBSM) ||
        TRUE == Check_ErrType(eErrType_TempOverLimitBSM) ||
        TRUE == Check_ErrType(eErrType_SOCTooHighBSM) ||
        TRUE == Check_ErrType(eErrType_SOCTooLowBSM) ||
        TRUE == Check_ErrType(eErrType_ImdErrBSM) ||
        TRUE == Check_ErrType(eErrType_PhySingleVoltooHighBSM) ||
        TRUE == Check_ErrType(eErrType_PhySingleVoltooLowBSM) ||
        TRUE == Check_ErrType(eErrType_PhyConErrBSM))
    {
        return FALSE;
    }

    if (CCU_WORK_STATE_CHARGE_STOP == Get_WorkState() ||
        CCU_WORK_STATE_RELEASE_02 == Get_WorkState() ||
        CCU_WORK_STATE_STOP_FINISH == Get_WorkState() ||
        CCU_WORK_STATE_FREE == Get_WorkState())
    {
        return FALSE;
    }

    return TRUE;
}

/**
******************************************************************************
* @brief       ShakeHand_OverTimeDeal
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note         握手阶段超时处理
******************************************************************************
*/
static void ShakeHand_OverTimeDeal(void)
{
#if 1
    if (!Get_CcuCfgParaEuropeEnable())
    {
        Bms_Init();
        K3_K4_OFF; // ShakeHand_OverTimeDeal
        Set_BMS_Ver(0xA5);
    }
#else
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    pBmsCtrl->comState = eComState_Break;

    if (TRUE == IsCanSetBmsComErr())
    {
        Set_ErrType(eErrType_ComErrWithBMS);
    }

    Set_BmsSendRemainTimer(BMS_PGN_CHM, 0x00);
#endif
    return;
}

/**
******************************************************************************
* @brief        充电停止阶段的超时处理
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note
******************************************************************************
*/
static void ChargeStop_OverTimeDealForBMS(uint32 PGN)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (eSendFlag_Yes != Get_BmsSendFlg(BMS_PGN_CEM))
    {
        Set_BmsSendRemainTimer(BMS_PGN_CEM, 2000);
        Set_BmsStartTimer(BMS_PGN_CEM);
        Set_BmsLastSendTimer(BMS_PGN_CEM, 0xFFFF);
        Set_BmsSendRemainTimer(BMS_PGN_CST, 0x00);
    }
    else if (eResult_Succ == pBmsCtrl->overTimeDealFlg)
    {
        if (0 == Get_BmsSendRemainTimer(BMS_PGN_CEM))
        {
            Set_BmsRecvTimerEnable(PGN, eTimerEnable_Off);

            if (PGN == BMS_PGN_BSD || PGN == BMS_PGN_BST)
            {
                Set_BmsStage(BMS_STAGE_STOPFINISH);
                return;
            }

            pBmsCtrl->overTimeDealFlg = eResult_Fail;
            pBmsCtrl->overTimePgnforBms = 0;
            Set_BmsSendFlg(BMS_PGN_CEM, eSendFlag_No);
            pBmsCtrl->comState = eComState_Normal;
        }
    }

    if (PGN == BMS_PGN_BSD)
    {
        Set_BmsSendRemainTimer(BMS_PGN_CST, 0x00);
        Set_BmsStage(BMS_STAGE_STOPFINISH);
    }

    if (PGN == BMS_PGN_BST)
    {
        Set_BmsSendRemainTimer(BMS_PGN_CCS, 0);
    }

    return;
}

/**
******************************************************************************
* @brief       充电停止阶段的超时处理
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details     这个时候收到BEM中报CST或者CSD超时，可以当作没看见。
*
* @note
******************************************************************************
*/
static void ChargeStop_OverTimeDealForCharger(void)
{
    return;
}

/**
******************************************************************************
* @brief       辨识、配置、充电等阶段Bms报文的超时处理
* @param[in]   NONE
* @param[in]   NONE
* @param[out]  NONE
* @retval      NONE
*
* @details     超时PGN源于自己接收计时管理
*
* @note
******************************************************************************
*/
static void Charge_OverTimeDealForBMS(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    if (eSendFlag_Yes != Get_BmsSendFlg(BMS_PGN_CEM))
    {
        memset(pBmsCtrl->bmsSendCtrl, 0x00, sizeof(pBmsCtrl->bmsSendCtrl));
        memset(pBmsCtrl->bmsRecvCtrl, 0x00, sizeof(pBmsCtrl->bmsRecvCtrl));

        Set_BmsSendRemainTimer(BMS_PGN_CEM, 2000);
        Set_BmsStartTimer(BMS_PGN_CEM);
        Set_BmsLastSendTimer(BMS_PGN_CEM, 0xFFFF);
        Set_BmsSendRemainTimer(BMS_PGN_CST, 0x00);
    }
    else if (eResult_Succ == pBmsCtrl->overTimeDealFlg &&
             0 == Get_BmsSendRemainTimer(BMS_PGN_CEM))
    {
        if (BMS_STAGE_SHAKEHAND != Get_BmsStage() &&
            BMS_STAGE_STOP != Get_BmsStage() &&
            BMS_STAGE_STOPFINISH != Get_BmsStage())
        {
            pBmsCtrl->tryConnectCnt++;
            printf("Charge_OverTimeDealForBMS(3)\n");
            Set_BmsSendRemainTimer(BMS_PGN_CRM, 0xFFFF);
            Set_BmsLastSendTimer(BMS_PGN_CRM, 0xFFFF);
            pBmsCtrl->spn_2560 = 0x00;
            pBmsCtrl->spn_2830 = 0x00; /**<重连时先发00再发AA*/
            Set_BmsSendFlg(BMS_PGN_CEM, eSendFlag_No);

            pBmsCtrl->comState = eComState_Normal;
            pBmsCtrl->overTimePgnforBms = 0;
            pBmsCtrl->overTimeDealFlg = eResult_Fail;
            if (Get_CcuCfgParaEuropeEnable())
            {
                Set_BmsStage(BMS_STAGE_STOPFINISH);
            }
            else
            {
                Set_BmsStage(BMS_STAGE_RECOGNIZE);
                //            Set_ChargeReconnectFlg(enumAllowFlag_Forbid);
                chargeReconnectFlg(TRUE, enumAllowFlag_Forbid);
                //            trace(TR_CHARGE, "ChargeReconnectFlg2 = %d\n",chargeReconnectFlg(FALSE,FALSE));
                //            Bms_OverTimeTick(TRUE,tickGet());   /** 赋值当前重连时刻*/
            }
        }
    }
}

/**
******************************************************************************
* @brief       Charge_OverTimeDealForBMS
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details      超时PGN源自于BEM的接收
*
* @note         辨识、配置、充电等阶段充电机报文的超时处理
******************************************************************************
*/
static void Charge_OverTimeDealForCharger(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (eRecvEnable_On == Get_BmsRecvEnable(BMS_PGN_BEM) &&
        eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BEM))
    {
        Set_BmsRecvEnable(BMS_PGN_BEM, eRecvEnable_Off);
        Set_BmsRecvFlag(BMS_PGN_BEM, eRecvFlag_No);
    }
    else if (eResult_Succ == pBmsCtrl->overTimeDealFlg)
    {
        if (BMS_STAGE_SHAKEHAND != Get_BmsStage() &&
            BMS_STAGE_STOP != Get_BmsStage() &&
            BMS_STAGE_STOPFINISH != Get_BmsStage())
        {
            pBmsCtrl->tryConnectCnt++;

            memset(pBmsCtrl->bmsSendCtrl, 0x00, sizeof(pBmsCtrl->bmsSendCtrl));
            memset(pBmsCtrl->bmsRecvCtrl, 0x00, sizeof(pBmsCtrl->bmsRecvCtrl));

            Set_BmsSendRemainTimer(BMS_PGN_CRM, 0xFFFF);
            Set_BmsLastSendTimer(BMS_PGN_CRM, 0xFFFF);
            pBmsCtrl->spn_2560 = 0x00;
            pBmsCtrl->spn_2830 = 0x00; /**<重连时先发00再发AA*/
            pBmsCtrl->comState = eComState_Normal;
            pBmsCtrl->overTimePgnforBms = 0;
            pBmsCtrl->overTimeDealFlg = eResult_Fail;
            Set_BmsStage(BMS_STAGE_RECOGNIZE);
            //            Set_ChargeReconnectFlg(enumAllowFlag_Forbid);
            chargeReconnectFlg(TRUE, enumAllowFlag_Forbid);
            trace(TR_CHARGE, "ChargeReconnectFlg3 = %d\n", chargeReconnectFlg(FALSE, FALSE));
            //            Bms_OverTimeTick(TRUE,tickGet());   /** 赋值当前重连时刻*/
        }
    }
}

/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
const BMS_RECV_DEAL BMS_RECV_DEAL_TABLE[] =
    {
        {BMS_PGN_BHM, 2, 6, Recv_BHM, Deal_BHM},
        {BMS_PGN_BRM, 8, 7, Recv_BRM, Deal_BRM},
        {BMS_PGN_BFC, 2, 6, Recv_BFC, Deal_BFC},
        {BMS_PGN_BCP, 13, 7, Recv_BCP, Deal_BCP},
        {BMS_PGN_BRO, 1, 4, Recv_BRO, Deal_BRO},
        {BMS_PGN_BCL, 5, 6, Recv_BCL, Deal_BCL},
        {BMS_PGN_BCS, 9, 7, Recv_BCS, Deal_BCS},
        {BMS_PGN_BSM, 7, 6, Recv_BSM, Deal_BSM},
        {BMS_PGN_BMV, 0, 7, Recv_BMV, Deal_BMV},
        {BMS_PGN_BMT, 0, 7, Recv_BMT, Deal_BMT},
        {BMS_PGN_BSP, 0, 7, Recv_BSP, Deal_BSP},
        {BMS_PGN_BST, 4, 4, Recv_BST, Deal_BST},
        {BMS_PGN_BSD, 7, 6, Recv_BSD, Deal_BSD},
        {BMS_PGN_BEM, 4, 2, Recv_BEM, Deal_BEM}};

const BMS_RECV_TIME BMS_RECV_TIME_INFO[] =
    {
        {BMS_PGN_BHM, 10000}, // 2023版27930协议将BHM的接收超时时间由5秒更改为10秒
        {BMS_PGN_BFC, 1005},
        {BMS_PGN_BRM, 5005},
        {BMS_PGN_BCP, 6505},
        {BMS_PGN_BRO, 5005},
        {BMS_PGN_BRO_AA, BMS_PGN_BRO_AA_OT},
        {BMS_PGN_BCL, 1005},
        {BMS_PGN_BCS, 5005},
        {BMS_PGN_BSM, 5005}, // 2023版27930协议新增
        //    { BMS_PGN_BMV,      10000 },
        //    { BMS_PGN_BMT,      10000 },
        //    { BMS_PGN_BSP,      10000 },
        {BMS_PGN_BST, 5000},
        {BMS_PGN_BSD, 10000},
        //  { BMS_PGN_BEM,            }
};

const BMS_RECV_TIME BMS_EC_RECV_TIME_INFO[] =
    {
        {BMS_PGN_BHM, 5005},
        {BMS_PGN_BFC, 1005},
        {BMS_PGN_BRM, 50005},
        {BMS_PGN_BCP, BMS_PGN_BRO_AA_OT},
        {BMS_PGN_BRO, 5005},
        {BMS_PGN_BRO_AA, BMS_PGN_BRO_AA_OT},
        {BMS_PGN_BCL, 1005},
        {BMS_PGN_BCS, 5005},
        //  { BMS_PGN_BSM,            },
        //    { BMS_PGN_BMV,      10000 },
        //    { BMS_PGN_BMT,      10000 },
        //    { BMS_PGN_BSP,      10000 },
        {BMS_PGN_BST, 5000},
        {BMS_PGN_BSD, 10000},
        //  { BMS_PGN_BEM,            }
};

/**
****************************************************************************** * @brief      Get_BmsRecvCtrl
* @param[in]   uint32 Pgn  参数组编号
* @param[in]
* @param[out]
* @retval      BMS_RECV_CTRL *返回控制变量指针
*
* @details     获取对应PGN的控制变量
*
* @note
******************************************************************************
*/
BMS_RECV_CTRL *Get_BmsRecvCtrl(uint32 pgn)
{
    switch (pgn)
    {
    case BMS_PGN_BHM:
        return &bmsCtrl.bmsRecvCtrl[0];

    case BMS_PGN_BRM:
        return &bmsCtrl.bmsRecvCtrl[1];

    case BMS_PGN_BCP:
        return &bmsCtrl.bmsRecvCtrl[2];

    case BMS_PGN_BRO:
        return &bmsCtrl.bmsRecvCtrl[3];

    case BMS_PGN_BRO_AA:
        return &bmsCtrl.bmsRecvCtrl[4];

    case BMS_PGN_BCL:
        return &bmsCtrl.bmsRecvCtrl[5];

    case BMS_PGN_BCS:
        return &bmsCtrl.bmsRecvCtrl[6];

    case BMS_PGN_BSM:
        return &bmsCtrl.bmsRecvCtrl[7];

    case BMS_PGN_BMV:
        return &bmsCtrl.bmsRecvCtrl[8];

    case BMS_PGN_BMT:
        return &bmsCtrl.bmsRecvCtrl[9];

    case BMS_PGN_BSP:
        return &bmsCtrl.bmsRecvCtrl[10];

    case BMS_PGN_BST:
        return &bmsCtrl.bmsRecvCtrl[11];

    case BMS_PGN_BSD:
        return &bmsCtrl.bmsRecvCtrl[12];

    case BMS_PGN_BEM:
        return &bmsCtrl.bmsRecvCtrl[13];

    case BMS_PGN_BFC:
        return &bmsCtrl.bmsRecvCtrl[14];
    default:
        break;
    }

    return NULL;
}

/**
******************************************************************************
* @brief       Set_BmsRecvEnable
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]   RECV_ENABLE  enableFlg  使能标记
* @param[out]
* @retval
*
* @details     0x55-能接收、0x00-不能接收、 0xFF-无效
*
* @note        设置接收使能标记
******************************************************************************
*/
void Set_BmsRecvEnable(uint32 pgn, RECV_ENABLE enableFlg)
{
    BMS_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_BmsRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return;
    }

    pRecvCtrl->recvEnableFlg = enableFlg;

    return;
}

/**
******************************************************************************
* @brief       Get_BmsRecvEnable
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]
* @param[out]
* @retval      RECV_ENABLE  enableFlg  使能标记
*
* @details     0x55-已接收、0x00-未接收、 0xFF-无效
*
* @note        获取接收使能标记
******************************************************************************
*/
RECV_ENABLE Get_BmsRecvEnable(uint32 pgn)
{
    BMS_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_BmsRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return eRecvEnable_Null;
    }

    return pRecvCtrl->recvEnableFlg;
}

/**
******************************************************************************
* @brief       Set_BmsRecvFlag
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]   RECV_FLAG    RecvFlg    接收标记
* @param[out]
* @retval
*
* @details     0x55-已接收、0x00-未接收、 0xFF-无效
*
* @note        设置已接收标记
******************************************************************************
*/
void Set_BmsRecvFlag(uint32 pgn, RECV_FLAG RecvFlg)
{
    BMS_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_BmsRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return;
    }

    pRecvCtrl->recvFlg = RecvFlg;

    return;
}

/**
******************************************************************************
* @brief      Get_BmsRecvFlag
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]
* @param[out]
* @retval      RECV_FLAG    RecvFlg    接收标记
*
* @details     0x55-已接收、0x00-未接收、 0xFF-无效
*
* @note        获取已接收标记
******************************************************************************
*/
RECV_FLAG Get_BmsRecvFlag(uint32 pgn)
{
    BMS_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_BmsRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return eRecvFlag_No;
    }

    return pRecvCtrl->recvFlg;
}

/**
******************************************************************************
* @brief      Set_BmsRecvTimerEnable
* @param[in]   uint32 Pgn                     参数组编号
* @param[in]   TIMER_ENABLE enableFlg    接收计时使能标记
* @param[out]
* @retval
*
* @details     0x55-计时、0x00-不计时、 0xFF-无效
*
* @note        设置接收计时器使能
******************************************************************************
*/
void Set_BmsRecvTimerEnable(uint32 pgn, TIMER_ENABLE enableFlg)
{
    BMS_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_BmsRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return;
    }

    pRecvCtrl->recvTimerEnableFlg = enableFlg;

    return;
}

/**
******************************************************************************
* @brief      Get_BmsRecvTimerEnable
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]
* @param[out]
* @retval      TIMER_ENABLE           接收计时器使能标记
*
* @details     0x55-计时、0x00-未接收、 0xFF-无效
*
* @note        获取接收计时器使能标记
******************************************************************************
*/
TIMER_ENABLE Get_BmsRecvTimerEnable(uint32 pgn)
{
    BMS_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_BmsRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return eTimerEnable_Null;
    }

    return pRecvCtrl->recvTimerEnableFlg;
}

/**
******************************************************************************
* @brief       Set_BmsRecvTimer
* @param[in]   uint32 Pgn                     参数组编号
* @param[in]   int32  countValue              计时器赋值
* @param[out]
* @retval
*
* @details
*
* @note        设置接收计时器
******************************************************************************
*/
void Set_BmsRecvTimer(uint32 pgn, uint32 countValue)
{
    BMS_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_BmsRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return;
    }

    pRecvCtrl->recvTimer = tickGet() - countValue;
    return;
}

/**
******************************************************************************
* @brief       Get_BmsRecvTimer
* @param[in]   uint32 Pgn                     参数组编号
* @param[in]
* @param[out]
* @retval
*
* @details
*
* @note        获取接收计时器值
******************************************************************************
*/
int32 Get_BmsRecvTimer(uint32 pgn)
{
    BMS_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_BmsRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return 0;
    }

    return pRecvCtrl->recvTimer;
}

/**
******************************************************************************
* @brief       通讯超时初始化tick获取
* @param[in]
* @param[out]
* @retval
*
* @details   flag为1时表示设置 为0时表示反馈
*
* @note
******************************************************************************
*/
uint32 Bms_OverTimeTick(uint8 flag, uint32 tick)
{
    static uint32 overtime_tick = 0;
    if (flag == 1)
    {
        overtime_tick = tick;
    }
    return overtime_tick;
}

/**
******************************************************************************
* @brief       处理充电过程中因接收到BEM导致BMS一直卡在某个阶段
* @param[in]
* @param[out]
* @retval
*
* @details
*
* @note
******************************************************************************
*/
static void Deal_BEMFault()
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (Get_BmsRecvFlag(BMS_PGN_BEM) != eRecvFlag_Yes || !Get_CcuCfgParaEuropeEnable())
    {
        return;
    }

    if ((tickGet() - lastBEMTime) <= BEM_TimeOut)
    {
        return;
    }

    if (eSendFlag_Yes != Get_BmsSendFlg(BMS_PGN_CEM))
    {
        Set_BmsSendRemainTimer(BMS_PGN_CEM, 2000);
        Set_BmsStartTimer(BMS_PGN_CEM);
        Set_BmsLastSendTimer(BMS_PGN_CEM, 0xFFFF);
        Set_BmsSendRemainTimer(BMS_PGN_CST, 0x00);
    }
    else if (eResult_Succ == pBmsCtrl->overTimeDealFlg && 0 == Get_BmsSendRemainTimer(BMS_PGN_CEM))
    {
        if (BMS_STAGE_SHAKEHAND != Get_BmsStage() &&
            BMS_STAGE_STOPFINISH != Get_BmsStage())
        {
            printf("Received BEM exception handling.\n");
            Set_BmsSendFlg(BMS_PGN_CEM, eSendFlag_No);

            pBmsCtrl->comState = eComState_Normal;
            pBmsCtrl->overTimePgnforBms = 0;
            pBmsCtrl->overTimeDealFlg = eResult_Fail;

            Set_BmsStage(BMS_STAGE_STOPFINISH);
        }
    }
}

/**
******************************************************************************
* @brief       通信超时处理
* @param[in]
* @param[in]
* @param[out]
* @retval      bool_e
*
* @details
*
* @note
******************************************************************************
*/
void Bms_OverTimeDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (pBmsCtrl->tryConnectCnt >= 3 ||
        eComState_Break == pBmsCtrl->comState)
    {
        pBmsCtrl->comState = eComState_Break;

        /*
         * 1.因为BHM产生通信故障，没有问题
         * 2.3次重连分为收到BEM超时，或者充电机接收BMS报文超时，
         *   如果是3次重连后充电机接收BMS报文超时，根据要求需要继续发送CEM,
         *         考虑到要CEM发出来，所以stopBms延时2秒再停止BMS服务。
         *   然后所有通信中断情况下，一定要保证停止发送CRM，且不再接收BRM
         */
        if (pBmsCtrl->overTimePgnforBms != BMS_PGN_BHM &&
            pBmsCtrl->overTimePgnforBms != BMS_PGN_CST &&
            pBmsCtrl->overTimePgnforBms != BMS_PGN_CSD &&
            pBmsCtrl->overTimePgnforBms != BMS_PGN_CCS &&
            pBmsCtrl->overTimePgnforBms != BMS_PGN_CML &&
            pBmsCtrl->overTimePgnforBms != BMS_PGN_CRO &&
            pBmsCtrl->overTimePgnforBms != BMS_PGN_CTS &&
            pBmsCtrl->overTimePgnforBms != BMS_PGN_CRM)
        {
            if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CEM))
            {
                Set_BmsSendRemainTimer(BMS_PGN_CEM, 0xFFFF);
                Set_BmsLastSendTimer(BMS_PGN_CEM, 0xFFFF);
            }
        }

        Set_BmsSendRemainTimer(BMS_PGN_CRM, 0x00);

        Set_BmsSendRemainTimer(BMS_PGN_CST, 0x00);

        Set_BmsRecvEnable(BMS_PGN_BRM, eRecvEnable_Off);
        Set_BmsRecvTimerEnable(BMS_PGN_BRM, eTimerEnable_Off);
        //        Set_BmsRecvEnable(BMS_PGN_BST, eRecvEnable_On);
        //        Set_BmsRecvTimerEnable(BMS_PGN_BST, eTimerEnable_On);
        //        Set_BmsRecvTimer(BMS_PGN_BST, 0x00);
        //        Set_BmsRecvEnable(BMS_PGN_BSD, eRecvEnable_On);
        //        Set_BmsRecvTimerEnable(BMS_PGN_BSD, eTimerEnable_On);
        //        Set_BmsRecvTimer(BMS_PGN_BSD, 0x00);

        if (TRUE == IsCanSetBmsComErr())
        {
            chargeReconnectFinishFlg(TRUE, TRUE);
            trace(TR_CHARGE, "三次重连结束 ! tick = %d \n", tickGet());
            Set_ErrType(eErrType_ComErrWithBMS);
        }
        return;
    }

    switch (pBmsCtrl->overTimePgnforBms)
    {
    case BMS_PGN_BHM:
        ShakeHand_OverTimeDeal();
        break;

    case BMS_PGN_CST:
    case BMS_PGN_CSD:
        ChargeStop_OverTimeDealForCharger();
        break;

    case BMS_PGN_BST:
    case BMS_PGN_BSD:
        ChargeStop_OverTimeDealForBMS(pBmsCtrl->overTimePgnforBms);
        break;

    case BMS_PGN_CCS:
    case BMS_PGN_CML:
    case BMS_PGN_CRO:
    case BMS_PGN_CTS:
    case BMS_PGN_CRM:
        Charge_OverTimeDealForCharger();
        break;

    case BMS_PGN_BRM:
    case BMS_PGN_BCP:
    case BMS_PGN_BRO:
    case BMS_PGN_BRO_AA:
    case BMS_PGN_BCL:
    case BMS_PGN_BCS:
    case BMS_PGN_BSM:
    case BMS_PGN_BMV:
    case BMS_PGN_BMT:
    case BMS_PGN_BSP:
        Charge_OverTimeDealForBMS();
        break;

    default:
        Deal_BEMFault();
        break;
    }
}

/**
******************************************************************************
* @brief       Bms_Decode
* @param[in]   CAN_FRAME *pCanFrame
* @param[out]  None
* @retval
*
* @details     Bms数据解码
*              1.长度检测
*              2.接收使能检测
*              3.数据内容缓存
* @note
******************************************************************************
*/
void Bms_Decode(CAN_FRAME *pCanFrame)
{
    const BMS_RECV_DEAL *pRecvCtrl = NULL;
    uint8 index = 0;

    for (index = 0; index < FCNT(BMS_RECV_DEAL_TABLE); index++)
    {
        pRecvCtrl = &BMS_RECV_DEAL_TABLE[index];

        if (pRecvCtrl->pgn == ThreeUint8ToUint32(pCanFrame->msgPGN))
        {
            break;
        }
    }

    // 当列表中找不到该PGN时应该认为不合法
    if (index >= FCNT(BMS_RECV_DEAL_TABLE))
    {
        //      printf("Bms_Decode():没找到对应的PGN\n");
        return;
    }

    if (NULL == pRecvCtrl)
    {
        //      printf("Bms_Decode():没找到对应的函数\n");
        return;
    }

    if (pCanFrame->dataLen < pRecvCtrl->minLen)
    {
        //      printf("Bms_Decode():长度不对\n");
        return;
    }

    if (pCanFrame->canId.prio != pRecvCtrl->prio)
    {
        //      printf("Bms_Decode():优先级不对\n");
        return;
    }

    log_recv_pgn_data(pRecvCtrl->pgn, pCanFrame->dataBuf, pCanFrame->dataLen);

    if (eRecvEnable_On != Get_BmsRecvEnable(pRecvCtrl->pgn))
    {
        //      printf("Bms_Decode():不可以接收 PGN = %06X\n", pRecvCtrl->pgn);
        return;
    }

    if (NULL != pRecvCtrl->pDecodeFunc)
    {
        pRecvCtrl->pDecodeFunc(pCanFrame->dataBuf, pCanFrame->dataLen);
    }

    trace(TR_CH2, "<R2: %X>  ", pCanFrame->canId.canId);
    trace_buf(TR_CH2, pCanFrame->dataBuf, pCanFrame->dataLen);

    if (NULL != pRecvCtrl->pDealFunc && TRUE == pRecvCtrl->pDealFunc())
    {
        Set_BmsRecvFlag(pRecvCtrl->pgn, eRecvFlag_Yes);
        Set_BmsRecvTimer(pRecvCtrl->pgn, 0);
    }

    return;
}

/**
******************************************************************************
* @brief       Bms_RecvTimerManage
* @param[in]   None
* @param[out]  None
* @retval
*
* @details     Bms接收计时管理
*
*
* @note
******************************************************************************
*/
void Bms_RecvTimerManage(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    const BMS_RECV_TIME *pBmsRecvTimeInfo = NULL;
    uint8 index = 0;
    uint32 curTimer = 0;

    for (index = 0; index < FCNT(BMS_RECV_TIME_INFO); index++)
    {
        if (Get_CcuCfgParaEuropeEnable())
        {
            pBmsRecvTimeInfo = &BMS_EC_RECV_TIME_INFO[index];
            if (BMS_PGN_BHM == pBmsRecvTimeInfo->pgn)
            {
                continue;
            }
        }
        else
        {
            pBmsRecvTimeInfo = &BMS_RECV_TIME_INFO[index];
        }
        if (eRecvEnable_On != Get_BmsRecvEnable(pBmsRecvTimeInfo->pgn))
        {
            continue;
        }

        if (eTimerEnable_On != Get_BmsRecvTimerEnable(pBmsRecvTimeInfo->pgn))
        {
            continue;
        }
        curTimer = abs(tickGet() - Get_BmsRecvTimer(pBmsRecvTimeInfo->pgn));

        if (curTimer > pBmsRecvTimeInfo->overTime || (TRUE == Get_bms_enable() && pBmsRecvTimeInfo->pgn == Get_bms_over_pgn()))
        {
            if (eComState_Normal == pBmsCtrl->comState)
            {
                if (BMS_PGN_BFC == pBmsRecvTimeInfo->pgn)
                {
                    //                    Set_BmsSendRemainTimer(BMS_PGN_CFC, 0);
                    //                    Set_BmsRecvEnable(BMS_PGN_BFC, eTimerEnable_Off);
                    //                    Set_BmsRecvTimerEnable(BMS_PGN_BFC, eTimerEnable_Off);
                    if (0x00 == pBmsCtrl->spn_2560)
                    {
                        if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BRM))
                        {
                            Set_BmsRecvFlag(BMS_PGN_BRM, eRecvFlag_No);
                            Set_BmsRecvTimer(BMS_PGN_BRM, 0x00);
                            Set_BmsRecvEnable(BMS_PGN_BRM, eRecvEnable_On);
                            Set_BmsRecvTimerEnable(BMS_PGN_BRM, eTimerEnable_On);
                            printf("===[%s]==============[%d]====================\n", __FUNCTION__, __LINE__);
                        }
                    }
                    continue;
                }

                if (0x00 == pBmsCtrl->overTimePgnforTcu)
                {
                    pBmsCtrl->overTimePgnforTcu = pBmsRecvTimeInfo->pgn;
                }

                if (0x00 == pBmsCtrl->overTimePgnforBms)
                {
                    pBmsCtrl->overTimePgnforBms = pBmsRecvTimeInfo->pgn;
                }

                pBmsCtrl->comState = eComState_TimeOut;
                pBmsCtrl->overTimeDealFlg = eResult_Fail;
                chargeReconnectFlg(TRUE, enumAllowFlag_Forbid);
                trace(TR_BMS_PROCESS, "~~~~~充电机接收BMS报文超时，PGN = %06x,"
                                      "~~~~~充电机上传TCU--BMS报文超时，PGN = %06x\n",
                      pBmsRecvTimeInfo->pgn,
                      pBmsCtrl->overTimePgnforTcu);
            }
        }
    }
}

/*----------------------------bmsRecvCtrl.c--------------------------------*/
