/**
 ******************************************************************************
 * @file       pcuMain.h
 * @brief      API include file of pcuMain.h.
 * @details    This file including all API functions's declare of pcuMain.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef __PCU_MAIN_H__
#define __PCU_MAIN_H__
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <types.h>
#include <ccu\lib\ccuLib.h>
#include <can.h>

/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/
/*-----------------------------------------------------------------------------
 Section: PCU通信阶段定义
 ----------------------------------------------------------------------------*/
#define PCU_STAGE_FREE 0x00		  /**< 空闲阶段 */
#define PCU_STAGE_READY 0x01	  /**< 充电准备阶段 */
#define PCU_STAGE_CHARGING 0x02	  /**< 充电阶段 */
#define PCU_STAGE_CHARGESTOP 0x03 /**< 充电停止阶段  */
#define PCU_STAGE_STOPFINISH 0x04 /**< 停止完成阶段  */

#define PCU_STAGE_NOT_TIMEOUT 0xFFFFFFFF /**< 不计超时 */

#define PCU_STOP_CMD_REMAIN_TIME 3000  /**< 发送2S停止关机释放模块*/
#define PCU_CSTOP_CMD_REMAIN_TIME 3000 /**< 发送1S停止关机保留模块*/

/*-----------------------------------------------------------------------------
 Section: PCU-PGN定义
 ----------------------------------------------------------------------------*/
#define PGN_YK_QSTART 0x000101		   /**< 遥控-快速启动帧 */
#define PGN_YK_QSTART_ACK 0x000201	   /**< 遥控-快速启动应答帧*/
#define PGN_YK_CSTOP 0x000102		   /**< 遥控-停止充电帧  保存模块 */
#define PGN_YK_CSTOP_ACK 0x000202	   /**< 遥控-停止充电应答帧  保存模块 */
#define PGN_YK_SSTART 0x000103		   /**< 遥控-软启动帧 */
#define PGN_YK_SSTART_ACK 0x000203	   /**< 遥控-软启动应答帧 */
#define PGN_YK_SADDR 0x000104		   /**< 遥控-显示地址帧 */
#define PGN_YK_SADDR_ACK 0x000204	   /**< 遥控-显示地址应答帧 */
#define PGN_YK_OPARA 0x000105		   /**< 遥控-调整参数帧 */
#define PGN_YK_OPARA_ACK 0x000205	   /**< 遥控-调整参数应答帧 */
#define PGN_YK_STOP 0x000106		   /**< 遥控-停止充电帧    释放模块 */
#define PGN_YK_STOP_ACK 0x000206	   /**< 遥控-停止充电应答帧    释放模块 */
#define PGN_PCU_FIX_SET 0x008000	   /**< 定值设置帧 */
#define PGN_PCU_FIX_SET_ACK 0x008100   /**< 定值设置应答帧*/
#define PGN_PCU_FIX_QUERY 0x008200	   /**< 定值查询帧 */
#define PGN_PCU_FIX_QUERY_ACK 0x008300 /**< 定值查询应答帧 */
#define PGN_CCU_YXYC1 0x002000		   /**< CCU遥信遥测帧1 */
#define PGN_CCU_YXYC2 0x002100		   /**< CCU遥信遥测帧2 */
#define PGN_PCU_WS 0x002200			   /**< PCU工作状态及故障 */
#define PGN_PCU_YXYC 0x002300		   /**< PCU遥信遥测帧 */
#define PGN_PCU_ALARM 0x002400		   /**< PCU告警信息帧 */
#define PGN_PCU_YC2 0x002500		   /**< PCU遥信遥测帧2 */
#define PGN_HEART_CCU 0x004000		   /**< CCU心跳帧 */
#define PGN_HEART_PCU 0x004100		   /**< PCU心跳帧 */
#define PGN_PCU_EXYC 0x00C100		   /**< PCU扩展数据 */

#define CCU_PGN_CNT 11
#define PCU_PGN_CNT 14

#define PCU_ADDR_QUERY_SEND_EN 0	/**地址显示帧使能 1使能 0 非使能*/
#define PCU_ADDR_BROADCAST_EN 0		/**上电广播地址使能       1使能  0非使能*/
#define PCU_REC_TIMEOUT_EN 1		/**PCU超时检测       1使能  0非使能*/
#define PCU_REC_HERT_SEND_YXYC_EN 1 /**收到PCU新跳后再发送YXYC帧使能 1使能 0非使能*/
#define PCU_YK_CMD_ACK_EN 0			/**遥控命令帧应答使能  1-使能，0-不使能*/
#define PCU_YK_CMD_VOL_RANGE_EN 1	/**遥控命令帧电压范围赋值使能  1-使能，0-不使能*/
/*-----------------------------------------------------------------------------
 Section: PCU任务调用周期
 ----------------------------------------------------------------------------*/
#define PCU_CALL_CYCLE 10

/*-----------------------------------------------------------------------------
 Section: PCU与CCU节点地址
 ----------------------------------------------------------------------------*/
#define ADDR_CCU 0x30		   /**< CCU地址 */
#define ADDR_PCU_MIN 0x10	   /**< PCU最小地址 */
#define ADDR_PCU_MAX 0x2E	   /**< PCU最大地址 */
#define ADDR_PCU_BOARD 0x2F	   /**< PCU广播地址 */
#define ADDR_CCU_BOARD 0x4F	   /**< CCU广播地址 */
#define ADDR_MOUDLE_BOARD 0x9F /**< 充电模块广播地址 */
#define ADDR_SW_BOARD 0xAF	   /**< 开关模块广播地址 */
/*-----------------------------------------------------------------------------
 Section: CAN通道
 ----------------------------------------------------------------------------*/
#define PCU_CHAN CAN_3

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
typedef enum
{
	ePcuMulFrame_NotFinish = 0, /***<PCU多帧未完成*/
	ePcuMulFrame_Check_ERR = 1, /***<PCU多帧校验错误 */
	ePcuMulFrame_Check_OK = 2,	/***<PCU多帧校验成功 */
};
/*-----------------------------------------------------------------------------
 Section: PCU发送控制结构
 ----------------------------------------------------------------------------*/
typedef struct PCU_SEND_CTRL_STRU
{
	uint32 sendRemainTimer; /**< 发送剩余时间 */
	uint8 sendFlg;			/**< 已发送标记 */
	int32 sendTimer;		/**< 发送周期计时 */
} PCU_SEND_CTRL;

/*-----------------------------------------------------------------------------
 Section: PCU接收控制结构
 ----------------------------------------------------------------------------*/
typedef struct PCU_RECV_CTRL_STRU
{
	uint8 recvEnableFlg;	  /**< 接收使能标记 */
	uint8 recvTimerEnableFlg; /**< 接收计时器使能标记 */
	uint8 recvFlg;			  /**< 接收标记 */
	int32 recvTimer;		  /**< 接收超时计时 */

} PCU_RECV_CTRL;

/*-----------------------------------------------------------------------------
 Section: PCU定值列表
 ----------------------------------------------------------------------------*/
typedef enum ENUM_CONST_DEV_TYPE
{
	eDevType_CCU = 0x01,	/**< 充电控制器 */
	eDevType_PCU = 0x02,	/**< 功率控制模块 */
	eDevType_Module = 0x03, /**< 充电模块 */
	eDevType_Swcu = 0x04,	/**< 开关模块 */
} CONST_DEV_TYPE;

typedef enum ENUM_PCU_CONST_LIST
{
	ePcuList_R_DevModel = 1,			/**< 设备型号只读 32字节*/
	ePcuList_R_Res = 2,					/**< 预留 2字节*/
	ePcuList_R_DevType = 3,				/**< 设备类型只读 1字节 ，只读项  1一体式单充直流充电桩，2一体式双充直流充电桩，3分体式单充直流桩，4分体式双充直流充电桩，5分体式整流柜*/
	ePcuList_R_DevNum = 4,				/**< 设备序列号只读*/
	ePcuList_R_ManNum = 5,				/**< 厂商编码 4字节 1034*/
	ePcuList_R_HardVer = 6,				/**< 硬件版本（可选）2字节*/
	ePcuList_R_SoftVer = 7,				/**< 软件版本 3字节*/
	ePcuList_R_SoftDate = 8,			/**< 软件日期 4字节*/
	ePcuList_R_SoftCrc = 9,				/**< 软件CRC（可选） 16字节*/
	ePcuList_R_RunMode = 10,			/**< 设备运行模式 1字节  0正常模式，1调试模式*/
	ePcuList_RW_PcuComAddr = 11,		/**< 有拨码开关，只读模式 1字节 */
	ePcuList_RW_PcuOvertime = 12,		/**< 超时时间 1字节，默认10S*/
	ePcuList_RW_PcuProtoVer = 13,		/**< 功率控制器协议版本2字节 */
	ePcuList_RW_ChargeComAddr = 14,		/**< 功率控制器与充电模块地址有拨码开关，只读模式 1字节 */
	ePcuList_RW_ChargeOvertime = 15,	/**< 功率控制器与充电模块超时时间 1字节，默认5S*/
	ePcuList_RW_ChargeProtoVer = 16,	/**< 功率控制器与充电模块协议版本2字节 */
	ePcuList_RW_SwComAddr = 17,			/**< 功率控制器与开关模块地址有拨码开关，只读模式 1字节 */
	ePcuList_RW_SwOvertime = 18,		/**< 功率控制器与开关超时时间 1字节，默认5S*/
	ePcuList_RW_SwProtoVer = 19,		/**< 功率控制器与开关模块协议版本2字节 */
	ePcuList_RW_CcuCnt = 20,			/**< 充电主控模块数目1字节 */
	ePcuList_RW_PortCnt = 21,			/**< 充电接口数目 1字节 */
	ePcuList_RW_ModuleCnt = 22,			/**< 充电机模块数目1字节  */
	ePcuList_RW_ModuleGroupCnt = 23,	/**< 充电机模块分组数目1字节  */
	ePcuList_RW_ModuleGroupMode = 24,	/**< 充电机模块分组模式1字节  */
	ePcuList_RW_VolRangeBreak = 25,		/**< 输出电压分断1字节  */
	ePcuList_RW_ModuleNomVol = 26,		/**< 充电机模块额定电压2字节 */
	ePcuList_RW_ModuleNomCur = 27,		/**< 充电机模块额定电流 2字节*/
	ePcuList_RW_ModuleMaxVol = 28,		/**< 充电机模块最高输出电压 2字节*/
	ePcuList_RW_ModuleMinVol = 29,		/**< 充电机模块最低输出电压2字节 */
	ePcuList_RW_ModuleMaxCur = 30,		/**< 充电机模块最高输出电流 2字节*/
	ePcuList_RW_ModuleMinCur = 31,		/**< 充电机模块最低输出电流2字节*/
	ePcuList_RW_ASwCnt = 32,			/**< A型开关数目 1字节  */
	ePcuList_RW_BSwCnt = 33,			/**< B型开关数目 1字节  */
	ePcuList_RW_CSwCnt = 34,			/**< C型开关数目 1字节  */
	ePcuList_RW_PowerAllotEnable = 35,	/**< 功率分配使能  1字节*/
	ePcuList_RW_ExpandEnable = 36,		/**< 是否扩容使用  1字节*/
	ePcuList_RW_YXCycle = 37,			/**< 遥信报文周期 （可选项）默认1S*/
	ePcuList_RW_YCCycle = 38,			/**< 遥测报文周期（可选项）默认1S */
	ePcuList_RW_HighMaxVol = 39,		/**< 高压段最高输出电压 2字节*/
	ePcuList_RW_HighMaxVolCur = 40,		/**< 高压段最高电压点电流输出点2字节*/
	ePcuList_RW_HighConsMinVol = 41,	/**< 高压段恒功率下限电压点 2字节*/
	ePcuList_RW_HighConsMinVolCur = 42, /**< 高压段恒功率下限电压点电流 2字节*/
	ePcuList_RW_HighMaxCurMinVol = 43,	/**< 高压段恒功率最大电流电压点 2字节*/
	ePcuList_RW_HighMaxCur = 44,		/**< 高压段最大电流输出值        2字节*/
	ePcuList_RW_HighMinVol = 45,		/**< 高压段电压输出下限点        2字节*/
	ePcuList_RW_HighMinVolCur = 46,		/**< 高压段电压输出下限点电流值        2字节*/
	ePcuList_RW_LowMaxVol = 47,			/**< 低压段最高输出电压 2字节*/
	ePcuList_RW_LowMaxVolCur = 48,		/**< 低压段电流输出点 2字节*/
	ePcuList_RW_LowConsMinVol = 49,		/**< 低压段恒功率下限电压点 2字节*/
	ePcuList_RW_LowConsMinVolCur = 50,	/**< 低压段恒功率下限电压点电流 2字节*/
	ePcuList_RW_LowMaxCurMinVol = 51,	/**< 低压段恒功率最大电流最低电压 2字节*/
	ePcuList_RW_LowMaxCur = 52,			/**< 高压段最大电流输出值        2字节*/
	ePcuList_RW_LowMinVol = 53,			/**< 高压段电压输出下限点        2字节*/
	ePcuList_RW_LowMinVolCur = 54,		/**< 高压段电压输出下限点电流值        2字节*/
	ePcuList_RW_PcuTopology = 55,		/**< PCU开关物理拓扑                               1字节*/
	ePcuList_RW_PcuStrategy = 56,		/**< PCU功率分配策略                             1字节*/
	ePcuList_RW_IsContactorFault = 57,	/**< 接触器故障是否允许充电                            1字节*/
	ePcuList_RW_LightOpen = 58,			/**< 照明灯                          1字节*/
} PCU_CONST_LIST;

typedef enum ENUM_OPERATE_TYPE
{
	eOperateType_Set = 0x01,   /**< 设置 */
	eOperateType_Query = 0x02, /**< 查询 */
} CONST_OPERATE_TYPE;

typedef struct PARA_OPERATE_STRU
{
	CONST_DEV_TYPE devType;
	CONST_OPERATE_TYPE operateType;
	uint8 paraType;
} PARA_OPERATE;
/**<PCU定值类型结构体*/
typedef struct
{
	uint8 devName[32];		 /**<设备名称*/
	uint8 res[2];			 /**<预留*/
	uint8 devType;			 /**<设备类型*/
	uint8 devNum[32];		 /**<设备序列号*/
	uint8 manNum[4];		 /**< 厂商编码 4字节 1034*/
	uint8 hardVer[2];		 /**< 硬件版本（可选）*/
	uint8 softVer[3];		 /**< 软件版本*/
	uint8 softDate[4];		 /**< 软件日期*/
	uint8 softCrc[16];		 /**< 软件CRC（可选）*/
	uint8 runMode;			 /**< 设备运行模式 1字节  0正常模式，1调试模式*/
	uint8 pcuToCcuComAddr;	 /**< 有拨码开关，只读模式 1字节 */
	uint8 pcuAndCcuOvertime; /**< 默认10S */
	uint8 pcuAndCcuProtoVer[2];
	uint8 pcuToMoudleComAddr;	/**< 有拨码开关，只读模式 1字节 */
	uint8 pcuAndMoudleOvertime; /**< 默认5S */
	uint8 pcuAndMoudleProtoVer[2];
	uint8 pcuToSwComAddr;	/**< 有拨码开关，只读模式 1字节 */
	uint8 pcuAndSwOvertime; /**< 默认10S */
	uint8 pcuAndSwProtoVer[2];
	uint8 ccuNum;		   /**< 充电主控模块数目 */
	uint8 chargePortNum;   /**< 充电机接口数目 */
	uint8 moduleNum;	   /**< 充电机模块数目 */
	uint8 moduleGroupNum;  /**< 充电机模块组数 */
	uint8 moduleGroupMode; /**< 充电机模块分组模式 */
	uint8 volRangeBreak;
	uint8 moduleRatedVol[2]; /**< 充电机模块额定电压 */
	uint8 moduleRatedCur[2]; /**< 充电机模块额定电流*/
	uint8 moduleMaxVol[2];	 /**< 充电机模块最高输出电压 */
	uint8 moduleMinVol[2];	 /**< 充电机模块最低输出电压 */
	uint8 moduleMaxCur[2];	 /**< 充电机模块最高输出电流 */
	uint8 moduleMinCur[2];	 /**< 充电机模块最低输出电流*/
	uint8 aSwNum;
	uint8 bSwNum;
	uint8 cSwNum;
	uint8 powerAllotEnable; /**< 功率分配使能 */
	uint8 expandEnable;
	uint8 yxCycle;				/**< 遥信报文周期 （可选项）*/
	uint8 ycCycle;				/**< 遥信报文周期 （可选项）*/
	uint8 highMaxVol[2];		/**< 高压段最高输出电压点*/
	uint8 highMaxVolCur[2];		/**< 高压段最高输出电压点-输出电流值*/
	uint8 highConsMinVol[2];	/**< 高压段恒功率输出下限电压点*/
	uint8 highConsMinVolCul[2]; /**< 高压段恒功率输出下限电压点-输出电流值*/
	uint8 highMaxCurMinVol[2];	/**< 高压段最大电流输出电压下限值*/
	uint8 highMaxCur[2];		/**< 高压段最大电流输出值*/
	uint8 highMinVol[2];		/**< 高压段电压输出下限点*/
	uint8 highMinVolCur[2];		/**< 高压段电压输出下限点-输出电流值*/
	uint8 lowMaxVol[2];			/**< 低压段最高输出电压点*/
	uint8 lowMaxVolCur[2];		/**< 低压段最高输出电压点-输出电流值*/
	uint8 lowConsMinVol[2];		/**< 低压段恒功率输出下限电压点*/
	uint8 lowConsMinVolCul[2];	/**< 低压段恒功率输出下限电压点-输出电流值*/
	uint8 lowMaxCurMinVol[2];	/**< 低压段最大电流输出电压下限值*/
	uint8 lowMaxCur[2];			/**< 低压段最大电流输出值*/
	uint8 lowMinVol[2];			/**< 低压段电压输出下限点*/
	uint8 lowMinVolCur[2];		/**< 低压段电压输出下限点-输出电流值*/
	uint8 PcuTopology;			/**< PCU开关物理拓扑                               1字节*/
	uint8 PcuStrategy;			/**< PCU开关物理拓扑                               1字节*/
	uint8 IsContactorFault;
	uint8 LightOpen; /**< 照明灯开启（必选项）*/
} PCU_FIX_PARA;
/**<TCU定值类型结构体*/
typedef struct
{
	uint8 devName[32];		 /**<设备名称*/
	uint8 devModel[2];		 /**<设备型号*/
	uint8 devType;			 /**<设备类型*/
	uint8 devNum[32];		 /**<设备序列号*/
	uint8 manNum[4];		 /**< 厂商编码 4字节 1034*/
	uint8 hardVer[2];		 /**< 硬件版本（可选）*/
	uint8 softVer[3];		 /**< 软件版本*/
	uint8 softDate[4];		 /**< 软件日期*/
	uint8 softCrc[16];		 /**< 软件CRC（可选）*/
	uint8 runMode;			 /**< 设备运行模式 1字节  0正常模式，1调试模式*/
	uint8 ccuToTcuComAddr;	 /**< 有拨码开关，只读模式 1字节 */
	uint8 ccuAndTcuOvertime; /**< 默认3S */
	uint8 ccuAndTcuProtoVer[2];
	uint8 ccuToPcuComAddr;	 /**< 有拨码开关，只读模式 1字节 */
	uint8 ccuAndPcuOvertime; /**< 默认10S */
	uint8 ccuAndPcuProtoVer[2];
	uint8 pileRatedVol[2]; /**< 充电机桩额定电压 */
	uint8 pileRatedCur[2]; /**< 充电机桩额定电流*/
	uint8 pileMaxVol[2];   /**< 充电机桩最高输出电压 */
	uint8 pileMinVol[2];   /**< 充电机桩最低输出电压 */
	uint8 pileMaxCur[2];   /**< 充电机模块最高输出电流 */
	uint8 pileMinCur[2];   /**< 充电机模块最低输出电流*/
	uint8 bmsVer;		   /**<BMS版本 0x01-2011;0x02-2015；0x03-兼容*/
	uint8 yxCycle;		   /**< 遥信报文周期 （可选项）*/
	uint8 ycCycle;		   /**< 遥信报文周期 （可选项）*/
	uint8 PlugAndPlay;	   /**< 控制器启动即插即充标识（必选项）*/
	uint8 LightOpen;	   /**< 照明灯开启（必选项）*/
} TCU_FIX_PARA;

/*-----------------------------------------------------------------------------
 Section: 遥控命令表
 ----------------------------------------------------------------------------*/
typedef enum ENUM_YK_LIST
{
	eYKList_QuickStart = 0x01,	/**< 快速开机 */
	eYKList_ChargeCStop = 0x02, /**< 停机保留模块 */
	eYKList_SoftStart = 0x03,	/**< 软起开机 */
	eYKList_ShowAddr = 0x04,	/**< 显示地址 */
	eYKList_OutputPara = 0x05,	/**< 输出参数 */
	eYKList_ChargeStop = 0x06,	/**< 停止充电 */
} YK_LIST;

typedef enum ENUM_PCU_WORK_STAE
{
	ePcuWorkState_Free = 0x01,		/**< 待机状态 */
	ePcuWorkState_Busy = 0x02,		/**< 工作状态 */
	ePcuWorkState_SelfCheck = 0x03, /**< 自检状态 */
} PCU_WORK_STAE;

/**< PCU告警总状态*/
typedef enum
{
	ePcuAlarmState_None = 0, /**< 正常状态 */
	ePcuAlarmState_Yes = 1,	 /**< 告警状态 */
} PCU_ALARM_STATE;

/**< PCU告警总状态*/
typedef enum
{
	ePcuFaultState_None = 0, /**< 正常状态 */
	ePcuFaultState_Yes = 1,	 /**< 故障状态 */
} PCU_FAULT_STATE;

/**< PCU告警总状态*/
typedef enum
{
	ePcuServeState_Forbid = 0, /**< 禁止服务状态 */
	ePcuServeState_Allow = 1,  /**< 允许服务状态 */
	ePcuServeState_Wait = 2,   /**< 等待状态 */
} PCU_SERVE_STATE;

typedef enum
{
	ePcuCtrlStage_Free = 0x00,	/**< 待机状态 */
	ePcuCtrlStage_QStart,		/**< 快速开机  */
	ePcuCtrlStage_QStartWait,	/**< 快速开机 完成 */
	ePcuCtrlStage_WaitK1K2On_1, /**< 等待K1K2第一次闭合 */
	ePcuCtrlStage_ImdAdjPara,	/**< 绝缘检测阶段调整参数 */
	ePcuCtrlStage_MidStop,		/**< 绝缘检测完成停机 */
	ePcuCtrlStage_SStart,		/**< 软启开机 */
	ePcuCtrlStage_SStartWs,		/**< 软启开机 */
	ePcuCtrlStage_WaitK1K2On_2, /**< 等待K1K2第二次闭合 */
	ePcuCtrlStage_AdjPara,		/**< 充电阶段调整参数 */
	ePcuCtrlStage_Pause,		/**< 充电暂停阶段 */
	ePcuCtrlStage_Stop,			/**< 充电停止阶段 */
	ePcuCtrlStage_Max,			/**<  */
} pcu_ctrl_stage;				/***<控制PCU阶段*/

/*-----------------------------------------------------------------------------
 Section: bit故障映射
 ----------------------------------------------------------------------------*/
typedef struct
{
	uint8 *errAddr;
	uint8 errbit;
	uint16 errType;
} YX_ERR_MATCH;
#pragma pack(push)
#pragma pack(1) // 设定为1字节对齐
/**< pcu遥控应答控制字 */
typedef struct
{
	union
	{
		uint8 ctrlWord; /**< 命令控制字*/
		struct
		{
			uint8 opCmd : 4;		/**< 操作指令 */
			uint8 outPutRange : 1;	/**< 输出范围 */
			uint8 resBit : 1;		/**< 预留 */
			uint8 k1k2State : 1;	/**< 接触器状态   */
			uint8 successState : 1; /**< 成功标识   */
		};
	};
	uint8 resByte; /**<预留字节*/
	uint16 setVol; /**<设定输出电压 保留1位小数 无偏移量  0~1000V*/
	uint16 setCur; /**<设定输出电流 保留2位小数 无偏移量  0~600A */
	uint16 batVol; /**<电池电压 保留1位小数   无偏移量          0~1000V*/
} PCU_YK_ACK_DATA;

/**< pcu工作状态及故障信息帧 */
typedef struct
{
	union
	{
		uint8 pcuState; /**< 工作状态*/
		struct
		{
			uint8 res1 : 4;		   /**< 预留 */
			uint8 errState : 1;	   /**< 故障状态 */
			uint8 allarmState : 1; /**< 告警状态 */
			uint8 workState : 2;   /**< 工作状态    */
		};
	};
	PCU_SERVE_STATE serveState; /**< 服务状态 */
	union
	{
		uint16 YxState; /**< 遥信状态*/
		struct
		{
			uint16 res2 : 4;		/**< 预留 */
			uint16 heaterState : 1; /**< 加热部件状态 */
			uint16 res3 : 1;		/**< 预留*/
			uint16 fsState : 1;		/**< 散热风机状态 */
			uint16 res4 : 7;		/**< 预留 */
			uint16 acJCQState : 1;	/**< 交流接触器状态 */
			uint16 acDLQState : 1;	/**< 断路器状态 */
		};
	};
	union
	{
		uint32 ErrState; /**< 故障状态*/
		struct
		{
			uint32 res5 : 7; /**< 预留 */
			uint32 peErr : 1;
			uint32 PcuJt : 1; /**< 网外160Kw pcuJt */
			uint32 ComErrWithmod : 1;
			uint32 pcuAndEcuTimeOut : 1;			   /**< 功率控制器之间通讯超时告警   */
			uint32 pcuAndPcuTimeOut : 1;			   /**< 功率控制器之间通讯超时告警（并柜扩容）   */
			uint32 pcuAndSwcuTimeOut : 1;			   /**< 功率控制模块开关模块通讯超时告警   */
			uint32 pcuAndCcuTimeOut : 1;			   /**< 功率控制模块与充电控制模块通讯超时   */
			uint32 noModFlt : 1;					   /**< 无可用模块     */
			uint32 hjsdAlm : 1;						   /**< 环境湿度告警      */
			uint32 blqFlt : 1;						   /**< 避雷器告警      */
			uint32 acJCQnl : 1; /**< 交流接触器黏连 */ // todo 潘焱220111 协议更新
			uint32 pcuRecvCcuTimeOut : 1;			   /**< PCU接受CCU遥信遥控报文超时 */
			uint32 tempFlt : 1;						   /**< 柜体过温故障 */
			uint32 acSWFlt : 1;						   /**< 开关模块故障 */
			uint32 modFlt : 1;						   /**< 电源模块故障  */
			uint32 inOpenPhaseFlt : 1;				   /**< 充电模块输入缺相 故障*/
			uint32 inVolLessnFlt : 1;				   /**< 模块交流输入欠压故障*/
			uint32 inVolOvernFlt : 1;				   /**< 模块交流输入过压故障 */
			uint32 pcuSJFlt : 1;					   /**< pcu水浸故障 （可选）*/
			uint32 pcuDoorFlt : 1;					   /**< pcu门禁故障 */
			uint32 heaterFlt : 1;					   /**< 加热部件故障 */
			uint32 fsFlt : 1;						   /**< 散热风机故障 */
			uint32 smokeFlt : 1;					   /**< 烟雾告警故障（可选） */
			uint32 acJCQFlt : 1;					   /**< 交流/桥接接触器故障 */
			uint32 acDLQFlt : 1;					   /**< 断路器故障 */
		};
	};
} PCU_WS_DATA;

/**< 遥信遥测 */
typedef struct
{
	uint8 modGroupCnt;	   /**< 充电模块总组数 */
	uint8 totalPower[2];   /**< 总功率 */
	uint8 freeModGroupCnt; /**< 空闲功率模块组数 */
	uint8 freePower[2];	   /**< 空闲功率 */
} PCU_YXYC_DATA;

/**< 遥信遥测 */
typedef struct
{
	uint8 databuf[8]; /**< 通道输出电流 */
} PCU_EXYC_DATA;

/**< 功率控制模块告警数据帧 */
typedef struct
{
	union
	{
		uint32 AlarmState; /**< 告警状态*/
		struct
		{
			uint32 res : 12;			  /**< 预留 */
			uint32 modXfAlm : 1;		  /**< 个别模块泄放故障*/
			uint32 modComAlm : 1;		  /**< 个别模块通讯故障 */
			uint32 modInOpenPhaseAlm : 1; /**< 个别模块输入缺相 故障*/
			uint32 modInVolLessnAlm : 1;  /**< 个别模块交流输入欠压故障*/
			uint32 modInVolOvernAlm : 1;  /**< 个别模块交流输入过压故障 */
			uint32 modOutVolLessnAlm : 1; /**< 个别模块输出欠压故障*/
			uint32 modOutVolOvernAlm : 1; /**< 个别模块输出过压故障 */
			uint32 modOutCurOvernAlm : 1; /**< 个别模块输出过流故障 */
			uint32 modOutShortAlm : 1;	  /**< 个别模块输出短路故障 */
			uint32 modInAlm : 1;		  /**< 个别模块交流输入故障 */
			uint32 modTempAlm : 1;		  /**< 模块过温告警 */
			uint32 modFsAlm : 1;		  /**< 模块风扇风机故障 */
			uint32 cabTempAlm : 1;		  /**< 柜体过温告警 */
			uint32 acSWAlm : 1;			  /**< 开关模块告警 */
			uint32 cabInOpenPhaseAlm : 1; /**< 柜体输入缺相 故障*/
			uint32 cabInVolLessnAlm : 1;  /**< 柜体输入欠压故障*/
			uint32 cabInVolOvernAlm : 1;  /**< 柜体输入过压故障 */
			uint32 cabHeaterAlm : 1;	  /**< 柜体加热部件告警 */
			uint32 cabFsAlm : 1;		  /**< 散热风机告警 */
			uint32 cabBLQErr : 1;		  /**< 柜体避雷器告警 */
		};
	};
} PCU_ALARM_DATA;

/**< 遥遥测2 */
typedef struct
{
	uint8 inFanTemp;  /**< 进风口温度 -50偏移 -50~200 */
	uint8 outFanTemp; /**< 进风口温度 -50偏移 -50~200 */
	uint8 humidity;	  /**< 环境湿度 */
	uint8 res[5];	  /**< 空闲功率 */
} PCU_YC2_DATA;

typedef struct
{
	pcu_ctrl_stage pcuStage;	 /**<PCU阶段*/
	PCU_YK_ACK_DATA pcuYkCmdAck; /**<PCU遥控应答*/
	PCU_WS_DATA pcuWs;			 /**<PCU工作状态*/
	PCU_YXYC_DATA pcuYxYc;		 /**<PCU遥信遥测数据*/
	PCU_ALARM_DATA pcuAlarm;	 /**<PCU告警数据*/
	PCU_YC2_DATA pcuYc2;
	uint8 timeOutFlag; /**<超时标记*/
	uint8 timeOutEn;   /**<超时使能*/
	uint8 tickFlag;	   /**<更新tick标记，为0表示更新超时tick，为1不用*/
	uint8 startUp;	   /**<启动完成在快速启动和软启动完成后都置1，开始启动时为0，*/
	uint16 dmnVol;	   /**<需求电压*/
	uint32 dmnCur;	   /**<需求电流*/
	uint32 tick;	   /**<计超时用的tick*/
} PCU_STATE;

#pragma pack(pop)

typedef void (*PdealFunc)(PCU_STATE *pPcuState);

typedef struct
{
	pcu_ctrl_stage pcuStage;
	uint32 timeOut;
	PdealFunc funState; /**状态函数*/
	PdealFunc func;
} PCU_WS_CONFIG;

extern PCU_WS_DATA *pPcuWsData;		  /**< pcu工作状态及故障信息帧数据指针 */
extern PCU_YXYC_DATA *pPcuYXYCData;	  /**< PCU遥信遥测数据指针 */
extern PCU_ALARM_DATA *pPcuAlarmData; /**< PCU告警数据 指针*/
extern PCU_YK_ACK_DATA *pPcuYkCmdAck; /**< pcu遥控应答 指针*/
extern PCU_EXYC_DATA *pPcuExYcData;	  /**< pcu遥控应答 指针*/
extern PCU_YC2_DATA *pPcuYc2Data;	  /**<  PCU遥信遥测2*/
/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/

void Pcu_Task(void);

bool_e Get_PcuAddrRecvSuccFlag(void);

void Set_PcuAddrRecvSuccFlag(bool_e flag);

uint8 Get_PcuAddr(void);

uint8 Get_CcuAddr(void);

int32 Get_FdCan(void);
uint16 Get_PcuCurr(void);
void Set_PcuAddr(uint8 addr);

SWITCH_STATE Get_ACJCQState(void);

SWITCH_STATE Get_ACDLQState(void);

uint8 Get_FsState(void);

uint8 Get_HeaterState(void);

PCU_WORK_STAE Get_PcuWorkState(void);

PCU_ALARM_STATE Get_PcuAlarmState(void);

PCU_FAULT_STATE Get_PcuFaultState(void);

PCU_SERVE_STATE Get_PcuServeState(void);

RESULT Get_SelfcheckStartFlag(void);

RESULT moudleQStartFlag(uint8 wr, uint8 flag);

RESULT moudleCStopFlag(uint8 wr, uint8 flag);

RESULT moudleSStartFlag(uint8 wr, uint8 flag);

RESULT moudleStopFlag(uint8 wr, uint8 flag);

// PCU_WORK_STATE pcuWorkState(uint8 wr,PCU_WORK_STATE state);
//
// PCU_ALARM_STATE pcuAlarmState(uint8 wr,PCU_ALARM_STATE state);
//
// PCU_FAULT_STATE pcuFaultState(uint8 wr,PCU_FAULT_STATE state);

void Set_SelfcheckStartFlag(RESULT flag);

uint8 Get_PcuWorkState(void);

PARA_OPERATE Get_CurParaOperateType(void);

void Set_CurParaOperateType(PARA_OPERATE type);

void Clr_CurParaOperateType(void);

PARA_OPERATE *Get_ParaOperateType(void);

void Del_ParaNeedOperateType(void);

void Add_ParaNeedSetType(CONST_DEV_TYPE dev, CONST_OPERATE_TYPE operateType, uint8 paraType);

void pcuSetStageTick(uint8 flag);

void pcu_FixPara_Init();

pcu_ctrl_stage Get_PcuStage(void);

void Clr_ChargeMoudleStartUp(void);

uint16 Get_PcuCurr(void);

uint8 Get_Humidity(void);

uint8 Get_OutFanTemp(void);

uint8 Get_InFanTemp(void);

#endif //__PCU_MAIN_H__
/*--------------------------End of pcuMain.h----------------------------*/
