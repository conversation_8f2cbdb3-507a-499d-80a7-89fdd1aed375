/**
 ******************************************************************************
 * @file       bmsSendCtrl.h
 * @brief      API include file of bmsSendCtrl.h.
 * @details    This file including all API functions's declare of bmsSendCtrl.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef __BMS_SEND_CTRL_H__
#define __BMS_SEND_CTRL_H__
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <ccu\lib\ccuLib.h>
#include <types.h>
#include "bmsMain.h"

/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
void Set_BmsSendRemainTimer(uint32 pgn, uint32 countValue);

uint32 Get_BmsSendRemainTimer(uint32 pgn);

void Set_BmsStartTimer(uint32 pgn);

void Set_BmsSendFlg(uint32 pgn, SEND_FLAG sendFlg);

SEND_FLAG Get_BmsSendFlg(uint32 pgn);

void Set_BmsLastSendTimer(uint32 pgn, uint32 countVal);

void Bms_SendServer(void);

#endif //__BMS_SEND_CTRL_H__
/*--------------------------End of bmsSendCtrl.h----------------------------*/
