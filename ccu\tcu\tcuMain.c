/**
 ******************************************************************************
 * @file      tcuMain.c
 * @brief     C Source file of tcuMain.c.
 * @details   This file including all API functions's
 *            implement of tcuMain.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <can.h>
#include <string.h>
#include <taskLib.h>
#include <dmnLib.h>
#include <stdlib.h>
#include <board\SXDC_4_0_1\SXDC_4_0_1.h>
#include <stdio.h>
#include <sxlib.h>
#include <gpio.h>
#include <trace.h>
#include <maths.h>
#include "tcuMain.h"
#include "tcuRecvCtrl.h"
#include "tcuSendCtrl.h"
#include "ccu\charge\ccuChargeMain.h"
#include "ccu\bsn\deviceState.h"
#include "ccu\bsn\io.h"
#include "ccu\para\para.h"
#include "ccu\bms\bmsRecvCtrl.h"
#include <bms.h>
#include <test.h>

static void Deal_MatchVer(void);

static void Deal_MatchPara(void);

static void Deal_RunFree(void);

static void Deal_RunStarting(void);

static void Deal_RunCharging(void);

static void Deal_RunStopping(void);

static void Deal_RunStopFinish(void);

static void Init_Tcu(void);

void Clr_TcuErrType(void);
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
typedef void (*PdealFunc)(void);
typedef struct TCU_STAGE_DEAL_STRU
{
    uint8 stage;
    PdealFunc func;
} TCU_STAGE_DEAL;

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
static uint8 tcuStage = TCU_STAGE_MATCH_VER;
static uint8 ccuHeartState = 0x00;
static uint8 tcuHeartState = 0x00;
static uint8 ccurstcmd = 0x00;
static uint8 MultiframeFlag = 0;
static uint8 MulFrameCnt[5] = {0};
static uint8 VIN_Start = 0;
TCU_CTRL tcuCtrl;
uint8 tcuErroInfo[7] = {0};
TCU_DEBUG tcuDebug;
const TCU_STAGE_DEAL TCU_STAGE_DEAL_TABLE[] =
    {
        {TCU_STAGE_MATCH_VER, Deal_MatchVer},
        {TCU_STAGE_MATCH_PARA, Deal_MatchPara},
        {TCU_STAGE_RUN_FREE, Deal_RunFree},
        {TCU_STAGE_RUN_STARTING, Deal_RunStarting},
        {TCU_STAGE_RUN_CHARGING, Deal_RunCharging},
        {TCU_STAGE_RUN_STOPPING, Deal_RunStopping},
        {TCU_STAGE_RUN_STOP_FINISH, Deal_RunStopFinish},
};
const Multiframe_Flag Multiframe[] =
    {
        {PGN_FIX_SET, 1, 0},
        {PGN_FIX_QUERY, 2, 1},
        {PGN_CAR_VIN_CONFIRM, 4, 2},
        {PGN_DEBUG_CMD, 8, 3},
        {PGN_RST_CMD, 16, 4},

};
/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/**
 ******************************************************************************
 * @brief      Check_StopChargeFailReason.
 * @param[in]  None
 * @param[out] None
 * @retval     处理TCU充电失败原因，包括启动完成帧或者停止完成帧的停机原因.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void Check_StopChargeFailReason(void)
{
    Check_StartFinishResaon(); /**< TODO- xg 北京入网检测,检测故障产生时 停机原因,防止故障清除后不能正确上传TCU*/
    Check_StopFinishReason();  /**< TODO- xg 北京入网检测,检测故障产生时 停机原因,防止故障清除后不能正确上传TCU*/
}

void MatchVer_Listen(void)
{
    if (eTimerEnable_On == Get_TcuRecvTimerEnable(PGN_TCU_YC))
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---TCU遥测帧\n",
              PGN_TCU_YC, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_TCU_YC, eTimerEnable_Off);
        Set_TcuRecvTimer(PGN_TCU_YC, 0);

        if (Check_ErrType(eErrType_ComErrWithTCU))
        {
            Clr_ErrType(eErrType_ComErrWithTCU);
        }
    }

    if (0x00 != Get_TcuSendRemainTimer(PGN_SET_PARA_ACK))
    {
        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---充电参数应答帧\n", PGN_SET_PARA_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_SET_PARA_ACK, 0);
    }

    if (0x00 != Get_TcuSendRemainTimer(PGN_CCU_YC))
    {
        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---CCU遥测帧\n", PGN_CCU_YC, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CCU_YC, 0);
    }

    if (0x00 != Get_TcuSendRemainTimer(PGN_CCU_YX1))
    {
        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---CCU遥信1帧\n", PGN_CCU_YX1, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CCU_YX1, 0);
    }

    if (0x00 != Get_TcuSendRemainTimer(PGN_CCU_YX2))
    {

        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---CCU遥信2帧\n", PGN_CCU_YX2, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CCU_YX2, 0);
    }

    if (0x00 != Get_TcuSendRemainTimer(PGN_PILE_STATE))
    {

        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---充电状态帧\n", PGN_PILE_STATE, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_PILE_STATE, 0);
    }
}

void MatchPara_Listen(void)
{
    if (eTimerEnable_On == Get_TcuRecvTimerEnable(PGN_TCU_YC))
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---TCU遥测帧\n",
              PGN_TCU_YC, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_TCU_YC, eTimerEnable_Off);
        Set_TcuRecvTimer(PGN_TCU_YC, 0);

        if (Check_ErrType(eErrType_ComErrWithTCU))
        {
            Clr_ErrType(eErrType_ComErrWithTCU);
        }
    }

    if (0x00 != Get_TcuSendRemainTimer(PGN_CCU_YC))
    {
        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---CCU遥测帧\n", PGN_CCU_YC, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CCU_YC, 0);
    }
    if (0x00 != Get_TcuSendRemainTimer(PGN_CCU_YX1))
    {
        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---CCU遥信1帧\n", PGN_CCU_YX1, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CCU_YX1, 0);
    }
    if (0x00 != Get_TcuSendRemainTimer(PGN_CCU_YX2))
    {
        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---CCU遥信2帧\n", PGN_CCU_YX2, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CCU_YX2, 0);
    }
    if (0x00 != Get_TcuSendRemainTimer(PGN_PILE_STATE))
    {
        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---充电状态帧\n", PGN_PILE_STATE, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_PILE_STATE, 0);
    }

    //    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    //
    //    if ((0x00 != pTcuCtrl->chargeStartFinishResult)
    //            && (eSwitchState_OFF == Get_SwitchState(SXIO_IN_DCS)))
    //    {
    //        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---启动完成帧\n",
    //        PGN_START_FINISH, Get_TcuStage());
    //
    //        Set_TcuSendRemainTimer(PGN_START_FINISH, 5000);
    //        Set_TcuStartTimer(PGN_START_FINISH);
    //        Set_TcuLastSendTimer(PGN_START_FINISH, 0xffff);
    //    }
}

/**
 ******************************************************************************
 * @brief       Deal_MatchVer
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details     匹配-版本校验阶段下:
 *              1.打开心跳帧的接收计时
 *              2.打开心跳帧的发送使能
 *
 * @note        上电的时候检测心跳超时5分钟（自定义，暂无标准规定）
 ******************************************************************************
 */
static void
Deal_MatchVer(void)
{
#if 1 /**< 没有接收到TCU心跳帧之前，不判断心跳帧超时 */
      //    if (eRecvFlag_Yes == Get_TcuRecvFlag(PGN_TCU_HEART))
    {
        if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_TCU_HEART))
        {
            trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---心跳帧\n",
                  PGN_TCU_HEART, Get_TcuStage());

            Set_TcuRecvTimerEnable(PGN_TCU_HEART, eTimerEnable_On);
            Set_TcuRecvTimer(PGN_TCU_HEART, 0);
        }
    }
#else
    if (eRecvFlag_Yes != Get_TcuRecvFlag(PGN_TCU_HEART))
    {
        if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_TCU_HEART))
        {
            trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---心跳帧\n",
                  PGN_TCU_HEART, Get_TcuStage());

            Set_TcuRecvTimerEnable(PGN_TCU_HEART, eTimerEnable_On);
            //            Set_TcuRecvTimer(PGN_TCU_HEART, (-5 * 60 * 1000 + 3) / TCU_CALL_CYCLE);
            Set_TcuRecvTimer(PGN_TCU_HEART, 0);
        }
    }
#endif

    if (0xFFFF != Get_TcuSendRemainTimer(PGN_CCU_HEART))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---心跳帧\n",
              PGN_CCU_HEART, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CCU_HEART, 0xFFFF);
        Set_TcuLastSendTimer(PGN_CCU_HEART, 0xFFFF);
    }
    if (eTimerEnable_On == Get_TcuRecvTimerEnable(PGN_TCU_YC))
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---遥测帧\n",
              PGN_TCU_YC, Get_TcuStage());
        Set_TcuRecvTimerEnable(PGN_TCU_YC, eTimerEnable_Off);
        Set_TcuRecvTimer(PGN_TCU_YC, 0);
    }

    return;
}

/**
 ******************************************************************************
 * @brief       Deal_MatchPara
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     匹配-参数配置阶段下：
 *              1.检测校验应答帧是否已经发送
 *              2.检测是否收到下发充电参数
 *              3.检测下发充电参数的接收超时计时是否打开
 *              4.打开下发充电参数的接收超时计时器
 *              5.初始化超时计时器
 *              6.打开遥信1帧的发送使能，置立即发送
 *              7.打开遥信2帧的发送使能，置立即发送
 *              8.打开遥测帧的发送使能，置立即发送
 * @note
 ******************************************************************************
 */
static void
Deal_MatchPara(void)
{
    MatchPara_Listen(); /**< xg 增加 用于入网测试*/

    //    if (eRecvFlag_Yes == Get_TcuRecvFlag(PGN_TCU_HEART))
    {
        if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_TCU_HEART))
        {
            trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---心跳帧\n",
                  PGN_TCU_HEART, Get_TcuStage());

            Set_TcuRecvTimerEnable(PGN_TCU_HEART, eTimerEnable_On);
            Set_TcuRecvTimer(PGN_TCU_HEART, 0);
        }
    }

    if (eSendFlag_Yes == Get_TcuSendFlg(PGN_VER_CHECK_ACK))
    {
        if (eRecvFlag_Yes != Get_TcuRecvFlag(PGN_SET_PARA))
        {
            if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_SET_PARA))
            {
                trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---下发充电参数帧\n",
                      PGN_SET_PARA, Get_TcuStage());

                Set_TcuRecvTimerEnable(PGN_SET_PARA, eTimerEnable_On);
                Set_TcuRecvTimer(PGN_SET_PARA, 0x00);
            }
        }
    }

    /**
     *  接收到参数配置帧且发送应答超过1秒,进入运行阶段.
     */
    if (eRecvFlag_Yes == Get_TcuRecvFlag(PGN_SET_PARA))
    {
        if (abs(tickGet() - Get_TcuStartTimer(PGN_SET_PARA_ACK)) > sysClkRateGet())
        {
            Set_TcuStage(TCU_STAGE_RUN_FREE);
        }
    }
}

/**
 ******************************************************************************
 * @brief       运行-空闲阶段周期处理
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
static void
Deal_RunFree(void)
{
    if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_TCU_HEART))
    {
        trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---心跳帧\n",
              PGN_TCU_HEART, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_TCU_HEART, eTimerEnable_On);
        Set_TcuRecvTimer(PGN_TCU_HEART, 0);
    }

    if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_TCU_YC))
    {
        trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---遥测帧\n",
              PGN_TCU_YC, Get_TcuStage());
        Set_TcuRecvTimerEnable(PGN_TCU_YC, eTimerEnable_On);
        Set_TcuRecvTimer(PGN_TCU_YC, 0);
    }

    if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_RST_CMD))
    {
        trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---远程复位\n",
              PGN_RST_CMD, Get_TcuStage());
        Set_TcuRecvTimerEnable(PGN_RST_CMD, eTimerEnable_On);
        Set_TcuRecvTimer(PGN_RST_CMD, 0);
    }

    if (0xffff != Get_TcuSendRemainTimer(PGN_CCU_YX1))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---YX1帧\n",
              PGN_CCU_YX1, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CCU_YX1, 0XFFFF);
        Set_TcuLastSendTimer(PGN_CCU_YX1, 0xFFFF);
    }

    if (0xffff != Get_TcuSendRemainTimer(PGN_CCU_YX2))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---YX2帧\n",
              PGN_CCU_YX2, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CCU_YX2, 0XFFFF);
        Set_TcuLastSendTimer(PGN_CCU_YX2, 0xFFFF);
    }

    if (0xffff != Get_TcuSendRemainTimer(PGN_CCU_YC))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---YC帧\n",
              PGN_CCU_YC, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CCU_YC, 0XFFFF);
        Set_TcuLastSendTimer(PGN_CCU_YC, 0xFFFF);
    }

    if (0xffff != Get_TcuSendRemainTimer(PGN_PILE_STATE))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---充电状态帧\n",
              PGN_PILE_STATE, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_PILE_STATE, 0XFFFF);
        Set_TcuLastSendTimer(PGN_PILE_STATE, 0xFFFF);
    }

    /**
     *  TODO-问题
     *  (1)描述
     *  PCU通信超时故障在启动前，启动时报故障
     *  (2)分析
     *  启动前出现故障导致故障没有机会清除
     *  (3)处理
     *  启动前清除故障标志
     */
    //    Init_Tcu();
    Clr_TcuErrType();
}

// static bool_e Check_StartFinishResaon(void)
bool_e Check_StartFinishResaon(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    typedef struct
    {
        uint16 errType;
        uint8 reason;
    } START_FINISH_FAIL_ERR;

    START_FINISH_FAIL_ERR *pErrTable;

    const START_FINISH_FAIL_ERR START_FINISH_FAIL_ERR_MAP[] =
        {
            {eErrType_GunConnectErr, 0x01},            /**< 控制导引故障*/
            {eErrType_ComErrWithTCU, 0x02},            /**< 与TCU通讯超时故障 */
            {eErrType_StartFinishAckTimeOutTcu, 0x02}, /**< 启动完成帧确认超时*/
            {eErrType_EmergencyStop, 0x03},            /**< 紧急停止故障 */
            {eErrType_CcuDoorOpenErr, 0x04},           /**< CCU门禁故障 */
            {eErrType_BlqErr, 0x05},                   /**< 避雷器故障 */
            {eErrType_SmokeErr, 0x06},                 /**< 烟雾报警故障 */
            {eErrType_ACBreakerErr, 0x07},             /**< 交流断路器故障*/
            {eErrType_AcJCQErr, 0x08},                 /**< 交流接触器故障 */
            {eErrType_InputVolOverLimit, 0x0A},        /**< 输入过压 */
            {eErrType_InputVolLessLimit, 0x0A},        /**< 输入欠压 */
            {eErrType_InputOpenphase, 0x0A},           /**< 输入缺相*/
            {eErrType_CabTempOverErr, 0x0B},           /**< 充电柜过温故障 */
            {eErrType_PileTempOverLimitErr, 0x0B},     /**< 桩过温故障 */
            {eErrType_GunTempOverLimitErr, 0x0C},      /**< 枪过温故障 */
            {eErrType_ElecLockErr, 0x0D},              /**< 充电接口电子锁故障 */
            {eErrType_ImdErr, 0x0E},                   /**< 绝缘监测故障 */
            {eErrType_QStartTimeOut, 0x0E},            /**< 快速启动超时*/
            {eErrType_ImdTimeOut, 0x0E},               /**< 绝缘检测超时*/
            {eErrType_ComErrWithIMD, 0x0E},            /**< 绝缘检测仪通信超时 */
            {eErrType_BatteryReverseConnect, 0x0F},    /**< 电池反接故障 */
            {eErrType_K1Err, 0x10},                    /**< 直流输出接触器K1故障 */
            {eErrType_K2Err, 0x10},                    /**< 直流输出接触器K2故障 */
            {eErrType_FuseProtectorErr, 0x12},         /**< 直流母线输出熔断器故障 */
            {eErrType_ReleaseErr, 0x15},               /**< 泄放回路告警 */
            {eErrType_AssistPowerErr, 0x16},           /**< 辅助电源故障 */
            {eErrType_PowerModuleErr, 0x17},           /**< 充电模块故障 */
            {eErrType_OutputVolOverLimit, 0x18},       /**< 直流母线输出电压过压故障 */
            {eErrType_OutputVolLessLimit, 0x19},       /**< 直流母线输出电压欠压故障 */
            {eErrType_OutputCurOverLimit, 0x1A},       /**< 直流母线输出电流过流故障 */
            {eErrType_OutputShortCut, 0x1B},           /**< 直流输出短路故障 */
            {eErrType_BHMVolErr, 0x1C},                /**< 最高允许充电电压小于充电机最小输出电压 */
            {eErrType_K1K2OutsideVolErr1, 0x1D},       /**< K1K2外侧电压>= 10V */
            {eErrType_BCPVolErr1, 0x1E},               /**< 启动充电前K1K2外侧电压与BCP中电压值误差>= 5% */
            {eErrType_K1K2OutsideVolErr2, 0x1F},       /**< 外侧电压小于充电机最小输出电压 */
            {eErrType_K1K2OutsideVolErr3, 0x20},       /**< 外侧电压大于充电机最大输出电压 */
            {eErrType_BatteryVolErr, 0x21},            /**< 电池端电压大于最高允许充电总电压 */
            {eErrType_BRMErr, 0x23},                   /**< BRM数据项异常 */
            {eErrType_BCPErr, 0x25},                   /**< BCP数据项异常 */
            {eErrType_ChargeParaNoMatch, 0x25},        /**< BCP中电池电压不在充电机输出能力范围内*/
            {eErrType_SStartTimeOut, 0x3D},            /**< BMS异常停机错误*/
            {eErrType_BROErr, 0x3E},                   /**< BRO准备就绪后取消 */
            {eErrType_PEErr, 0x38},                    /**< PE断线故障 */
            {eErrType_SwErr, 0x45},                    /**< 开关故障*/
            {eErrType_K1K2OutsideVolErr4, 0x46},       /**< 绝缘检测时，外侧电压小于200V*/
            {eErrType_DropDownErr1, 0x47},             /**< CCU倾倒故障*/
            {eErrType_PcuBusyTimeout, 0x4A},           /**< PCU工作状态转换超时*/
            {eErrType_PCUOtherErr, 0x4B},              /**< PCU其他故障*/
            {eErrType_CcuYxYcTimeoutErr, 0x4C},        /**< CCU遥信遥测超时故障*/
            {eErrType_PcuDoorOpenErr, 0x4D},           /**< PCU门禁故障 */
        };

    if (0x00 != pTcuCtrl->chargeStartFinishFailReason) // TODO -xg 已判断了故障，不能被刷了.
    {
        return FALSE;
    }

    if (DEVICE_STATE_FAULT == Get_DeviceState() ||
        DEVICE_STATE_MAJOR_FAULT == Get_DeviceState())
    {
        for (int index = 0; index < FCNT(START_FINISH_FAIL_ERR_MAP); index++)
        {
            pErrTable = (START_FINISH_FAIL_ERR *)&START_FINISH_FAIL_ERR_MAP[index];

            if (TRUE == Check_ErrType(pErrTable->errType))
            {
                //                pTcuCtrl->chargeStartFinishFailReason = pErrTable->reason;
                if (Get_FaultMask(eErrType_DCMainContactorSynechia) && (Check_ErrType(eErrType_K2Err) || Check_ErrType(eErrType_K1Err)))
                {
                    pTcuCtrl->chargeStartFinishFailReason = 0x11;
                }
                else
                {
                    pTcuCtrl->chargeStartFinishFailReason = pErrTable->reason;
                }
            }
        }

        if (0x00 == pTcuCtrl->chargeStartFinishFailReason || (eErrType_GunConnectErr == Check_ErrType(pErrTable->errType) && eActFlag_On == Get_BMS_StartFlg())) //
        {
            if (TRUE == Check_ErrType(eErrType_ComErrWithBMS))
            {
                if (BMS_PGN_BRM == Get_BMS_OverTimePgn())
                {
                    pTcuCtrl->chargeStartFinishFailReason = 0x22; // BRM超时
                }
                else if (BMS_PGN_BCP == Get_BMS_OverTimePgn())
                {
                    pTcuCtrl->chargeStartFinishFailReason = 0x24; // BCP超时
                }
                else if (BMS_PGN_BRO == Get_BMS_OverTimePgn())
                {
                    pTcuCtrl->chargeStartFinishFailReason = 0x26; // BRO超时
                }
                else if (BMS_PGN_BRO_AA == Get_BMS_OverTimePgn())
                {
                    pTcuCtrl->chargeStartFinishFailReason = 0x27; // BRO_AA超时
                }
            }
            else if (pTcuCtrl->tcuProtocolVer > TCU_PROTOCOL_VER) /**<协议版本在V1.1以后*/
            {
                if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120))
                {
                    if (eTcuPlugAndPlay_Enable == pTcuCtrl->plugAndPlay)
                    {
                        if (TRUE == Check_ErrType(eErrType_PlugAndPlayAckTimeout)) /**< 车辆验证超时 */
                        {
                            pTcuCtrl->chargeStartFinishFailReason = 0x29;
                        }
                        else if (TRUE == Check_ErrType(eErrType_PlugAndPlayAckErr)) /**< 车辆鉴权不通过 */
                        {
                            pTcuCtrl->chargeStartFinishFailReason = 0x2A;
                        }
                    }
                }
                else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120)
                {
                    if (TRUE == Check_ErrType(eErrType_ComErrWithBMS))
                    {
                        if (BMS_PGN_BCL == Get_BMS_OverTimePgn()) // TODO-xg 北京入网检测新增
                        {
                            pTcuCtrl->chargeStartFinishFailReason = 0x29; // BCL超时
                        }
                        else if (BMS_PGN_BCS == Get_BMS_OverTimePgn()) // TODO-xg 北京入网检测新增
                        {
                            pTcuCtrl->chargeStartFinishFailReason = 0x2A; // BCS超时
                        }
                    }
                    else if (TRUE == Check_ErrType(eErrType_WaterLoggingErr))
                    {
                        pTcuCtrl->chargeStartFinishFailReason = 0x2C;
                    }
                    else if (TRUE == Check_ErrType(eErrType_PcuWaterFault))
                    {
                        pTcuCtrl->chargeStartFinishFailReason = 0x2D;
                    }
                    else if (TRUE == Check_ErrType(eErrType_ComErrWithPCU))
                    {
                        pTcuCtrl->chargeStartFinishFailReason = 0x2E;
                    }
                    else if (TRUE == Check_ErrType(eErrType_SwErr))
                    {
                        pTcuCtrl->chargeStartFinishFailReason = 0x30;
                    }
                    else
                    {
                        if (eTcuPlugAndPlay_Enable == pTcuCtrl->plugAndPlay)
                        {
                            if (TRUE == Check_ErrType(eErrType_TcuCarVinIllegalityErr)) /**< 非法VIN*/
                            {
                                pTcuCtrl->chargeStartFinishFailReason = 0x32;
                            }
                            else if (TRUE == Check_ErrType(eErrType_TcuCarVinConfirmFailErr)) /**< 即插即充鉴权失败*/
                            {
                                pTcuCtrl->chargeStartFinishFailReason = 0x33;
                            }
                            else if (TRUE == Check_ErrType(eErrType_TcuCarVinConfirmTimeoutErr)) /**< 即插即充鉴权超时*/
                            {
                                pTcuCtrl->chargeStartFinishFailReason = 0x34;
                            }
                            else if (TRUE == Check_ErrType(eErrType_CarVinConfirmTimeoutErr)) /**< CCU接收鉴权帧超时*/
                            {
                                pTcuCtrl->chargeStartFinishFailReason = 0x35;
                            }
                            else if (TRUE == Check_ErrType(eErrType_VinInconformityErr)) /**< 鉴权VIN码与车辆VIN不一致*/
                            {
                                pTcuCtrl->chargeStartFinishFailReason = 0x36;
                            }
                        }
                    }
                }
            }
        }

        /**
         *  如果找不到故障原因，返回其他故障
         */
        if (0x00 == pTcuCtrl->chargeStartFinishFailReason)
        {
            if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120))
            {
                pTcuCtrl->chargeStartFinishFailReason = 0x28; // 充电机其它故障
            }
            else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120)
            {
                pTcuCtrl->chargeStartFinishFailReason = 0xFF; // 充电机其它故障
            }
            else
            {
                pTcuCtrl->chargeStartFinishFailReason = 0xFF; // 充电机其它故障
            }
        }
    }

    trace(TR_ALARM, "启动完成帧---停机原因---<%02X>\n",
          pTcuCtrl->chargeStartFinishFailReason);
    return TRUE;
}

/**
 ******************************************************************************
 * @brief       运行-启动中阶段周期处理
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     运行-启动中阶段下：
 *              1.检测预充是否成功且直流接触器已经闭合或是否不能启动，打开启动完成帧的发送使能
 *              2.启动完成帧发送之后，打开启动完成应答帧的接收超时
 *
 * @note
 ******************************************************************************
 */
static void
Deal_RunStarting(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    if (Platform_protocol_Xj == Get_CcuCfgParaPlatform_convert() && ((eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BCL)) || (eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BCS))) && eSwitchState_ON == Get_SwitchState(SXIO_IN_K1) && eSwitchState_ON == Get_SwitchState(SXIO_IN_K2))
    //    if (CCU_WORK_STATE_CHARGING == Get_WorkState()
    //            && eSwitchState_ON == Get_SwitchState(SXIO_IN_K1)
    //            && eSwitchState_ON == Get_SwitchState(SXIO_IN_K2))//TODO-xg 入网检测.
    {
        if (eSendFlag_Yes != Get_TcuSendFlg(PGN_START_FINISH))
        {
            trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---充电启动完成帧1\n",
                  PGN_START_FINISH, Get_TcuStage());

            pTcuCtrl->chargeStopFinishEn = TRUE; // TODO-开普修改

            Set_TcuSendRemainTimer(PGN_START_FINISH, 5000);
            Set_TcuStartTimer(PGN_START_FINISH);
            Set_TcuLastSendTimer(PGN_START_FINISH, 0xffff);
        }
        else
        {
            if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_START_FINISH_ACK))
            {
                trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---充电启动完成应答帧1\n",
                      PGN_START_FINISH_ACK, Get_TcuStage());

                Set_TcuRecvTimerEnable(PGN_START_FINISH_ACK, eTimerEnable_On);
                Set_TcuRecvTimer(PGN_START_FINISH_ACK, 0x00);
            }
        }
    }
    else if (Platform_protocol_Xj != Get_CcuCfgParaPlatform_convert() && (eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BCL)) && (eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BCS)) && eSwitchState_ON == Get_SwitchState(SXIO_IN_K1) && eSwitchState_ON == Get_SwitchState(SXIO_IN_K2))
    {
        if (eSendFlag_Yes != Get_TcuSendFlg(PGN_START_FINISH))
        {
            trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---充电启动完成帧1\n",
                  PGN_START_FINISH, Get_TcuStage());

            pTcuCtrl->chargeStopFinishEn = TRUE; // TODO-开普修改

            Set_TcuSendRemainTimer(PGN_START_FINISH, 5000);
            Set_TcuStartTimer(PGN_START_FINISH);
            Set_TcuLastSendTimer(PGN_START_FINISH, 0xffff);
        }
        else
        {
            if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_START_FINISH_ACK))
            {
                trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---充电启动完成应答帧1\n",
                      PGN_START_FINISH_ACK, Get_TcuStage());

                Set_TcuRecvTimerEnable(PGN_START_FINISH_ACK, eTimerEnable_On);
                Set_TcuRecvTimer(PGN_START_FINISH_ACK, 0x00);
            }
        }
    }
    else
    {
        if (eSendFlag_Yes != Get_TcuSendFlg(PGN_START_FINISH))
        {
            TCU_CTRL *pTcuCtrl = &tcuCtrl;

            //            if ((0x00 != pTcuCtrl->chargeStartFinishResult)
            //                    && (eSwitchState_OFF == Get_SwitchState(SXIO_IN_DCS))) //TODO-入网检测
            if ((0x00 != pTcuCtrl->chargeStartFinishFailReason) && (eSwitchState_OFF == Get_SwitchState(SXIO_IN_DCS))) // TODO-入网检测
            {
                if (0x00 == Get_TcuSendRemainTimer(PGN_START_FINISH))
                {
                    trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---充电启动完成帧2\n",
                          PGN_START_FINISH, Get_TcuStage());

                    Set_TcuSendRemainTimer(PGN_START_FINISH, 5000);
                    Set_TcuStartTimer(PGN_START_FINISH);
                    Set_TcuLastSendTimer(PGN_START_FINISH, 0xffff);
                }

                if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_START_FINISH_ACK)) // TODO-入网检测
                {
                    trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---充电启动完成应答帧2\n",
                          PGN_START_FINISH_ACK, Get_TcuStage());

                    Set_TcuRecvTimerEnable(PGN_START_FINISH_ACK, eTimerEnable_On);
                    Set_TcuRecvTimer(PGN_START_FINISH_ACK, 0x00);
                }
            }
        }
    }

#if VEHICLE_VALIDATE_ENABLE
    if ((eTcuPlugAndPlay_Enable == pTcuCtrl->plugAndPlay) && (Get_BmsRecvFlag(BMS_PGN_BRM) == eRecvFlag_Yes)) // BRM以后发送VIN
    {
        if (Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
        {
            if (eSendFlag_Yes != Get_TcuSendFlg(PGN_TCU_VIN_REP))
            {
                trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---VIN码上报帧\n",
                      PGN_TCU_VIN_REP, Get_TcuStage());
                Set_TcuSendRemainTimer(PGN_TCU_VIN_REP, 30000); // 5000
                Set_TcuStartTimer(PGN_TCU_VIN_REP);
                Set_TcuLastSendTimer(PGN_TCU_VIN_REP, 0xffff);

                trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---VIN码上报应答帧\n",
                      PGN_TCU_VIN_REP_ACK, Get_TcuStage());
                Set_TcuRecvTimerEnable(PGN_TCU_VIN_REP_ACK, eTimerEnable_On);
                Set_TcuRecvTimer(PGN_TCU_VIN_REP_ACK, 0x00);
            }
        }
        else if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120)) // 支持TCU协议版本[V1.14, V1.20)
        {
            if (eSendFlag_Yes != Get_TcuSendFlg(PGN_CAR_CHECK_INFO))
            {
                trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---车辆验证数据帧\n",
                      PGN_CAR_CHECK_INFO, Get_TcuStage());
                Set_TcuSendRemainTimer(PGN_CAR_CHECK_INFO, 5000);
                Set_TcuStartTimer(PGN_CAR_CHECK_INFO);
                Set_TcuLastSendTimer(PGN_CAR_CHECK_INFO, 0xffff);

                trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---车辆验证确认帧\n",
                      PGN_CAR_CHECK_INFO_ACK, Get_TcuStage());
                Set_TcuRecvTimerEnable(PGN_CAR_CHECK_INFO_ACK, eTimerEnable_On);
                Set_TcuRecvTimer(PGN_CAR_CHECK_INFO_ACK, 0x00);
            }
        }
        //        else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0121) //支持TCU协议版本[V1.21)
        else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120) // 支持TCU协议版本[V1.20)
        {
            if (eSendFlag_Yes != Get_TcuSendFlg(PGN_CAR_VIN_IDENTIFY))
            {
                trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---车辆识别帧\n",
                      PGN_CAR_VIN_IDENTIFY, Get_TcuStage());
                Set_TcuSendRemainTimer(PGN_CAR_VIN_IDENTIFY, 5000);
                Set_TcuStartTimer(PGN_CAR_VIN_IDENTIFY);
                Set_TcuLastSendTimer(PGN_CAR_VIN_IDENTIFY, 0xffff);

                trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---车辆识别应答帧\n",
                      PGN_CAR_VIN_IDENTIFY_ACK, Get_TcuStage());
                Set_TcuRecvTimerEnable(PGN_CAR_VIN_IDENTIFY_ACK, eTimerEnable_On);
                Set_TcuRecvTimer(PGN_CAR_VIN_IDENTIFY_ACK, 0x00);
            }
        }
    }
#endif

    // 一定是故障触发的，tcu触发的在接收停止充电的地方去置状态
    if (eActFlag_On != Get_ChargeActFlag())
    {
        if (eRecvFlag_Yes == Get_TcuRecvFlag(PGN_START_FINISH_ACK) ||
            TRUE == Check_ErrType(eErrType_StartFinishAckTimeOutTcu))
        {
            if (TRUE == pTcuCtrl->chargeStopFinishEn)
            {
                Set_TcuStage(TCU_STAGE_RUN_STOPPING); // TODO-开普修改
            }
            else
            {
                Set_TcuStage(TCU_STAGE_RUN_STOP_FINISH);
            }
        }
    }

    return;
}

/**
 ******************************************************************************
 * @brief      Deal_RunCharging.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details    1.处理充电中
 *
 * @note
 ******************************************************************************
 */
static void Deal_RunCharging(void)
{
    if (eActFlag_On != Get_ChargeActFlag())
    {
        Set_TcuStage(TCU_STAGE_RUN_STOPPING);
    }
#if 0
    if(eRecvFlag_Yes == Get_TcuRecvFlag(PGN_CHARGE_STOP))
    {
    trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---启动完成帧\n",
    PGN_START_FINISH, Get_TcuStage());

    Set_TcuSendRemainTimer(PGN_START_FINISH, 5000);
    Set_TcuStartTimer(PGN_START_FINISH);
    Set_TcuLastSendTimer(PGN_START_FINISH, 0xffff);
    }
#endif
    return;
}

/**
 ******************************************************************************
 * @brief      Check_StopFinishReason.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details    1.检测关机原因
 *
 * @note
 ******************************************************************************
 */
void Check_StopFinishReason(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    typedef struct
    {
        uint16 errType;
        uint8 reason;
    } STOP_FINISH_FAIL_ERR;

    STOP_FINISH_FAIL_ERR *pErrTable;

    const STOP_FINISH_FAIL_ERR STOP_FINISH_FAIL_ERR_MAP[] =
        {
            {eErrType_GunConnectErr, 0x05},               /**< 控制导引故障*/
            {eErrType_ComErrWithTCU, 0x06},               /**< 与TCU通讯超时故障 */
            {eErrType_ChargePauseTimeout, 0x07},          /**< 充电暂停超时 */
            {eErrType_EmergencyStop, 0x08},               /**< 紧急停止故障 */
            {eErrType_CcuDoorOpenErr, 0x09},              /**< CCU门禁故障 */
            {eErrType_BlqErr, 0x0A},                      /**< 避雷器故障 */
            {eErrType_SmokeErr, 0x0B},                    /**< 烟雾报警故障 */
            {eErrType_ACBreakerErr, 0x0C},                /**< 交流断路器故障*/
            {eErrType_AcJCQErr, 0x0D},                    /**< 交流接触器故障 */
            {eErrType_InputVolOverLimit, 0x0F},           /**< 输入过压 */
            {eErrType_InputVolLessLimit, 0x0F},           /**< 输入欠压 */
            {eErrType_InputOpenphase, 0x0F},              /**< 输入缺相*/
            {eErrType_CabTempOverErr, 0x10},              /**< 充电柜过温故障 */
            {eErrType_PileTempOverLimitErr, 0x10},        /**< 桩过温故障 */
            {eErrType_GunTempOverLimitErr, 0x11},         /**< 枪过温故障 */
            {eErrType_ElecLockErr, 0x12},                 /**< 充电接口电子锁故障 */
            {eErrType_K1Err, 0x13},                       /**< 直流输出接触器K1故障 */
            {eErrType_K2Err, 0x13},                       /**< 直流输出接触器K2故障 */
            {eErrType_FuseProtectorErr, 0x15},            /**< 直流母线输出熔断器故障 */
            {eErrType_MultipleContactorSynechia, 0x17},   /**< 并联接触器粘连故障 */
            {eErrType_ReleaseErr, 0x18},                  /**< 泄放回路告警 */
            {eErrType_AssistPowerErr, 0x19},              /**< 辅助电源故障 */
            {eErrType_PowerModuleErr, 0x1A},              /**< 充电模块故障 */
            {eErrType_OutputVolOverLimit, 0x1B},          /**< 直流母线输出电压过压故障 */
            {eErrType_OutputVolLessLimit, 0x1C},          /**< 直流母线输出电压欠压故障 */
            {eErrType_OutputCurOverLimit, 0x1D},          /**< 直流母线输出电流过流故障 */
            {eErrType_OutputShortCut, 0x1E},              /**< 直流输出短路故障 */
            {eErrType_PhySingleVoltooHighBSM, 0x22},      /**< BSM中电池单体电压过高 */
            {eErrType_PhySingleVoltooLowBSM, 0x23},       /**< BSM中电池单体电压过低 */
            {eErrType_SOCTooHighBSM, 0x24},               /**< BSM中SOC过高*/
            {eErrType_SOCTooLowBSM, 0x25},                /**< BSM中SOC过低*/
            {eErrType_OutputCurOverLimitBSM, 0x26},       /**< BSM中过电流故障 */
            {eErrType_TempOverLimitBSM, 0x27},            /**< BSM中过温故障 */
            {eErrType_ImdErrBSM, 0x28},                   /**< BSM中绝缘异常*/
            {eErrType_PhyConErrBSM, 0x29},                /**< BSM中导引异常*/
            {eErrType_BmsFaultStopErr, 0x2B},             /**< BMS异常停机错误*/
            {eErrType_PEErr, 0x2F},                       /**< PE检测故障  */
            {eErrType_SStartTimeOut, 0x34},               /**< 预充阶段调压失败*/
            {eErrType_OutputOverMaxAllowChargeVol, 0x36}, /**< 输出电压大于车辆最高允许充电电压*/
            {eErrType_DemandOverMaxAllowChargeCur, 0x37}, /**< 需求电流超过车辆最高允许充电电流*/
            {eErrType_DemandOverMaxAllowChargeVol, 0x38}, /**< 需求电压超过车辆最高允许充电电压*/
            {eErrType_ComErrWithPCU, 0x39},               /**< 充电控制模块与功率控制模块通信超时*/
            {eErrType_PcuAndSwcuTimeout, 0x3A},           /**< 功率控制模块与开关模块通信超时*/
            {eErrType_PcuAndPcuTimeout, 0x3C},            /**< 功率控制器间通信超时*/
            {eErrType_WaterLoggingErr, 0x3E},             /**< 充电桩水浸故障*/
            {eErrType_PcuWaterFault, 0x3F},               /**< 充电机柜水浸故障*/
            {eErrType_SwErr, 0x43},                       /**< 开关故障*/
            {eErrType_OutputOverMaxChargeCur, 0x44},      /**< 输出电流大于最大允许充电电流*/
            {eErrType_DemandLowMinAllowOutputVol, 0x45},  /**< 需求电压小于充电机最小输出电压*/
            {eErrType_PcuDoorOpenErr, 0x46},              /**< PCU门禁故障*/
            {eErrType_PCUOtherErr, 0x47},                 /**< PCU其他故障*/
            {eErrType_CcuYxYcTimeoutErr, 0x48},           /**< CCU遥信遥测超时故障*/
            {eErrType_OutputOverMaxAllowOutputVol, 0x49}, /**< 输出电压大于充电机最大允许输出电压*/
            {eErrType_DropDownErr1, 0x4A},                /**< CCU倾倒故障*/
            {eErrType_BusCurOverCarLimite, 0x4B},         /**< 输出电流大于车辆需求总电流*/
            {eErrType_OutputOverMaxAllowOutputCur, 0x4C}, /**< 输出电流大于最大允许输出电流*/
        };

    // TODO xg-入网检测增加,首次故障视为停机原因;
    if (0x00 != pTcuCtrl->chargeStopFinishReason)
    {
        return;
    }

    if (TRUE == Check_ErrType(eErrType_StartFinishAckTimeOutTcu))
    {
        pTcuCtrl->chargeStopFinishReason = 0x04;
    }
    else if (DEVICE_STATE_FAULT == Get_DeviceState() ||
             DEVICE_STATE_MAJOR_FAULT == Get_DeviceState())
    {
        for (int index = 0; index < ARRAY_SIZE(STOP_FINISH_FAIL_ERR_MAP); index++)
        {
            pErrTable = (STOP_FINISH_FAIL_ERR *)&STOP_FINISH_FAIL_ERR_MAP[index];

            if (TRUE == Check_ErrType(pErrTable->errType))
            {
                //                pTcuCtrl->chargeStopFinishReason = pErrTable->reason;
                if (Get_FaultMask(eErrType_DCMainContactorSynechia) && (Check_ErrType(eErrType_K2Err) || Check_ErrType(eErrType_K1Err)))
                {
                    pTcuCtrl->chargeStopFinishReason = 0x11;
                }
                else
                {
                    pTcuCtrl->chargeStopFinishReason = pErrTable->reason;
                }
                break;
            }
        }

        if ((0x00 == pTcuCtrl->chargeStopFinishReason) || (eErrType_GunConnectErr == Check_ErrType(pErrTable->errType) && (Get_WorkState() == CCU_WORK_STATE_STOP_FINISH))) /*暂改0413*/
        {
            if (TRUE == Check_ErrType(eErrType_ComErrWithBMS))
            {
                if (BMS_PGN_BCL == Get_BMS_OverTimePgn())
                {
                    pTcuCtrl->chargeStopFinishReason = 0x1F;
                }
                else if (BMS_PGN_BCS == Get_BMS_OverTimePgn())
                {
                    pTcuCtrl->chargeStopFinishReason = 0x20;
                }
                else if (BMS_PGN_BSM == Get_BMS_OverTimePgn())
                {
                    pTcuCtrl->chargeStopFinishReason = 0x21;
                }
                else
                {
                    pTcuCtrl->chargeStopFinishReason = 0x2C;
                }
            }
        }

        /**< 其他故障!!*/
        if (0x00 == pTcuCtrl->chargeStopFinishReason)
        {
            if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120))
            {
                pTcuCtrl->chargeStopFinishReason = 0x2D; // TODO -xg 北京入网检测增加
            }
            else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120)
            {
                pTcuCtrl->chargeStopFinishReason = 0xFF; // TODO -xg 北京入网检测增加
            }
            else
            {
                pTcuCtrl->chargeStopFinishReason = 0xFF; // 充电机其它故障
            }
        }
    }

    trace(TR_ALARM, "停止完成帧---停机原因---<%02X>\n",
          pTcuCtrl->chargeStopFinishReason);
}

/**
 ******************************************************************************
 * @brief      Get_StopFinishReason.
 * @param[in]  None
 * @param[out] None
 * @retval     查询停机原因(停止完成帧处理)
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 Get_StopFinishReason(void)
{
    uint8 resl = 0;
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    switch (pTcuCtrl->TcuChargeStopReason)
    {
    case 0x01: /**< 计费控制单元正常停止*/
    case 0x02: /**< 计费控制单元判自身故障停止*/
    case 0x03: /**< 计费控制单元判充电控制器故障停止*/
    {
        if (0 != pTcuCtrl->chargeStopFinishReason)
        {
            resl = pTcuCtrl->chargeStopFinishReason;
        }
        else
        {
            resl = pTcuCtrl->TcuChargeStopReason;
        }
    }
    break;
    default: /**< 处理充电控制器故障停止或BSM正常停止*/
    {
        if (0 != pTcuCtrl->chargeStopFinishReason)
        {
            resl = pTcuCtrl->chargeStopFinishReason;
        }
        else if (eChargeStopFlag_BMS == Get_StopSrc())
        {
            resl = 0x2A; // BMS正常中止充电
        }
        else
        {
            if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120))
            {
                resl = 0x2D; // TODO -xg 北京入网检测增加
            }
            else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120)
            {
                resl = 0xFF;
            }
        }
    }
    break;
    }

    trace(TR_TCU_PROCESS, "停机原因---TCU---<%X>---CCU---<%X>\n",
          pTcuCtrl->TcuChargeStopReason, resl);
    return resl;
}
/**
 ******************************************************************************
 * @brief      Deal_RunStopping.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details    1.处理停止中相关业务
 *
 * @note
 ******************************************************************************
 */
static void Deal_RunStopping(void)
{
    //	printf("F1: %d, F2: %d, F3: %d\n",
    //			Get_Release02FinishFlag(),
    //			Get_SwitchState(SXIO_IN_FY),
    //			Check_ErrType(eErrType_AssistPowerErr));
    //    if (TRUE == Get_Release02FinishFlag() &&
    //       (eSwitchState_OFF == Get_SwitchState(SXIO_IN_FY) ||
    if (TRUE == Check_ErrType(eErrType_ComErrWithTCU)) /**<通讯超时就不发送停止完成帧以及不打开接受停止完成帧*/
    {
        Set_TcuStage(TCU_STAGE_RUN_STOP_FINISH);
        return;
    }
    if ((Get_CcuCfgParaEuropeEnable() || (TRUE == Get_K3K4FinishFlag())) && (Get_WorkState() == CCU_WORK_STATE_STOP_FINISH))
    {
        taskDelay(10);
        if (eSendFlag_Yes != Get_TcuSendFlg(PGN_STOP_FINISH))
        {
            if (0x00 != Get_TcuSendRemainTimer(PGN_CHARGE_STOP_ACK))
            {
                Set_TcuSendRemainTimer(PGN_CHARGE_STOP_ACK, 0);
                trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---停止应答帧\n",
                      PGN_CHARGE_STOP_ACK, Get_TcuStage());
            }

            trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---停止完成帧\n",
                  PGN_STOP_FINISH, Get_TcuStage());
            //            Check_StopFinishReason();
            Set_TcuSendRemainTimer(PGN_STOP_FINISH, 5000);
            Set_TcuStartTimer(PGN_STOP_FINISH);
            Set_TcuLastSendTimer(PGN_STOP_FINISH, 0xffff);
        }
        else
        {
            if (eTimerEnable_On != Get_TcuRecvTimerEnable(PGN_STOP_FINISH_ACK))
            {
                trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---停止完成应答帧\n",
                      PGN_STOP_FINISH_ACK, Get_TcuStage());

                Set_TcuRecvTimerEnable(PGN_STOP_FINISH_ACK, eTimerEnable_On);
                Set_TcuRecvTimer(PGN_STOP_FINISH_ACK, 0x00);
            }
        }
    }

    if (eRecvFlag_Yes == Get_TcuRecvFlag(PGN_STOP_FINISH_ACK) ||
        TRUE == Check_ErrType(eErrType_StopFinishAckTimeOutTcu))
    {
        Set_TcuStage(TCU_STAGE_RUN_STOP_FINISH);
    }

    return;
}

/**
 ******************************************************************************
 * @brief      Deal_RunStopFinish.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details    1.处理停止完成
 *
 * @note
 ******************************************************************************
 */
static void Deal_RunStopFinish(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    pTcuCtrl->stopFinishFlag = TRUE;

    if (TRUE == Get_StopFinishFlag())
    {
        Set_TcuStage(TCU_STAGE_RUN_FREE);

        Init_Tcu();
    }

    return;
}

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void Clr_TcuErrType(void)
{
    /**
     *  TODO-问题2020-11-05
     *  (1)描述：
     *  手动模拟测试即插即充时，即插即充故障标志清除不了
     *  (2)分析
     *  手动模拟时，充电任务先清除故障标志，TCU任务才产生故障
     *  (3)处理
     *  在TCU任务结束时，增加清除动作.
     */
    const PILE_ERROR_TYPE CLR_TCU_ERRTYPE_MAP[] =
        {
            eErrType_TcuCarVinIllegalityErr,
            eErrType_TcuCarVinConfirmFailErr,
            eErrType_TcuCarVinConfirmTimeoutErr,
            eErrType_VinInconformityErr,
            eErrType_TcuCarVinOtherErr,
            eErrType_CarVinConfirmTimeoutErr,
        };

    for (int index = 0; index < FCNT(CLR_TCU_ERRTYPE_MAP); index++)
    {
        Clr_ErrType(CLR_TCU_ERRTYPE_MAP[index]);
    }
}

/**
 ******************************************************************************
 * @brief      Get_Fy_State
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details		重卡BMS辅源24V/12V
 *
 * @note
 ******************************************************************************
 */
uint8 Get_Fy_State(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    return pTcuCtrl->fy_Switch;
}
/**
 ******************************************************************************
 * @brief      Init_Tcu.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details    1.TCU初始化
 *
 * @note
 ******************************************************************************
 */
static void Init_Tcu(void)
{
    uint16 TmpPtlVer = 0;
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    Init_TcuCutFrame();

    TmpPtlVer = pTcuCtrl->tcuProtocolVer;

    memset(pTcuCtrl, 0x00, MOFFSET(TCU_CTRL, tcuSendCtrl)); // TODO-xg 此处会导致心跳帧超时被清除

    pTcuCtrl->tcuProtocolVer = TmpPtlVer; // TODO -xg 北京入网检测，恢复协议版本号

    // 上电默认为服务启用状态
    pTcuCtrl->chargeServeState = 0x02;
    pTcuCtrl->stopFinishFlag = TRUE;

    tcu_FixPara_Init();

    Clr_TcuErrType();
    /***********************************/
    Clr_MultiframeFlag();
    Clr_MulFrameCnt();
    /***************************************/
    return;
}

/**
 ******************************************************************************
 * @brief      Tcu_StageManage.
 * @param[in]  None
 * @param[out] uint8
 * @retval
 *
 * @details    1.TCU阶段状态管理
 *
 * @note
 ******************************************************************************
 */
static void Tcu_StageManage(void)
{
    const TCU_STAGE_DEAL *pStageDeal = NULL;
    uint8 index = 0;

    for (index = 0; index < FCNT(TCU_STAGE_DEAL_TABLE); index++)
    {
        pStageDeal = &TCU_STAGE_DEAL_TABLE[index];

        if (Get_TcuStage() == pStageDeal->stage)
        {
            if (NULL != pStageDeal->func)
            {
                pStageDeal->func();
            }
        }
    }
}

/**
 ******************************************************************************
 * @brief      Get_TcuStage.
 * @param[in]  None
 * @param[out] uint8
 * @retval
 *
 * @details    1.获取TCU阶段状态
 *
 * @note
 ******************************************************************************
 */
uint8 Get_TcuStage(void)
{
    return tcuStage;
}

/**
 ******************************************************************************
 * @brief      Set_TcuStage.
 * @param[in]  uint8
 * @param[out] None
 * @retval
 *
 * @details    1.设置TCU阶段状态
 *
 * @note
 ******************************************************************************
 */
void Set_TcuStage(uint8 stage)
{
    if (stage != tcuStage)
    {
        trace(TR_TCU_PROCESS, "TCU 状态转换  %d -- > %d \n", tcuStage, stage);
    }

    tcuStage = stage;

    return;
}

/**
 ******************************************************************************
 * @brief      Set_CcuHeartState.
 * @param[in]  uint8
 * @param[out] None
 * @retval
 *
 * @details   1.设置CCU心跳状态
 *
 * @note
 ******************************************************************************
 */
void Set_CcuHeartState(uint8 state)
{
    ccuHeartState = state;
    return;
}

/**
 ******************************************************************************
 * @brief      Get_RstCmd.
 * @param[in]  None
 * @param[out] uint8
 * @retval
 *
 * @details    1.获取CCU心跳状态
 *
 * @note
 ******************************************************************************
 */
uint8 Get_RstCmd(void)
{
    return ccurstcmd;
}

/**
 ******************************************************************************
 * @brief      Set_CcuHeartState.
 * @param[in]  uint8
 * @param[out] None
 * @retval
 *
 * @details   1.设置CCU心跳状态
 *
 * @note
 ******************************************************************************
 */
void Set_RstCmd(uint8 state)
{
    ccurstcmd = state;
}
/**
 ******************************************************************************
 * @brief      Get_CcuHeartState.
 * @param[in]  None
 * @param[out] uint8
 * @retval
 *
 * @details    1.获取CCU心跳状态
 *
 * @note
 ******************************************************************************
 */
uint8 Get_CcuHeartState(void)
{
    return ccuHeartState;
}
/**
 ******************************************************************************
 * @brief      Set_TcuHeartState.
 * @param[in]  uint8
 * @param[out] None
 * @retval
 *
 * @details    1.设置TCU心跳状态
 *
 * @note
 ******************************************************************************
 */
void Set_TcuHeartState(uint8 state)
{
    tcuHeartState = state;
    return;
}

/**
 ******************************************************************************
 * @brief      Get_TcuHeartState.
 * @param[in]  None
 * @param[out] uint8
 * @retval
 *
 * @details    获取TCU心跳状态
 *
 * @note
 ******************************************************************************
 */
uint8 Get_TcuHeartState(void)
{
    return tcuHeartState;
}

uint8 Get_TcuPlugAndPlay(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    return pTcuCtrl->plugAndPlay;
}
/**
 ******************************************************************************
 * @brief      Get_TcuHeartState.
 * @param[in]  None
 * @param[out] uint8
 * @retval
 *
 * @details    获取TCU心跳状态
 *
 * @note
 ******************************************************************************
 */
void TcuRst_Check(void)
{
    extern void reboot();
    if (Rst_Start == Get_RstCmd() && eSendFlag_Yes == Get_TcuSendFlg(PGN_RST_CMD_ACK))
    {
        Set_RstCmd(Rst_Normal);
        printf(" REST START SUCCESS!!!\n");
        //       DELAY_RST_ON;
        reboot();
    }
}
/**
 ******************************************************************************
 * @brief      Get_TcuStopFinishFlag.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details    获取TCU停止完成状态
 *
 * @note
 ******************************************************************************
 */
bool_e Get_TcuStopFinishFlag(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    return pTcuCtrl->stopFinishFlag;
}

/**
 ******************************************************************************
 * @brief      Get_TcuChargeTime.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details    获取TCU充电时长
 *
 * @note
 ******************************************************************************
 */
uint16 Get_TcuChargeTime(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    return pTcuCtrl->chargeTime;
}
/**
 ******************************************************************************
 * @brief      Get_CcuToTcuAddr.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details    获取发送给TCU帧ID原地址
 *
 * @note
 ******************************************************************************
 */
uint8 Get_CcuToTcuAddr(void)
{
    CONFIG_PARA strConfigPara;
    OPERATE_PARA strOperatePara;

    Get_PilePara((void *)&strConfigPara, eParaType_ConfigPara);
    Get_PilePara((void *)&strOperatePara, eParaType_OperatePara);
    uint8 addr = 0;
    if (Platform_protocol_Hn == Get_CcuCfgParaPlatform_convert())
    {
        addr = CCU_SINGLE_CHARGER_ADDR;
    }
    else
    {
        addr = (strConfigPara.onePiLeMulCharge == TRUE) ? CCU_MUL_CHARGER_ADDR : CCU_SINGLE_CHARGER_ADDR;
    }

    return addr;
}
/**
 ******************************************************************************
 * @brief      Check_TCUaddr.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note  检测TCU的枪号是否与ccu地址一致
 ******************************************************************************
 */
uint8 Check_TcuGun(CAN_DATA *pCanData)
{
    CAN_DATA *PData = pCanData;
    if (Get_CcuCfgParaPlatform_convert() < Platform_protocol_Hn)
    {
        return TRUE;
    }
    if (PData->dataBuf[0] == Get_TcuJunctorId())
    {
        return TRUE;
    }
    else if (0xAA == PData->dataBuf[0])
    {
        return TRUE;
    }
    return FALSE;
}
/**
 ******************************************************************************
 * @brief      Set_MultiframeFlag.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note  每次只能发送一个多帧
 ******************************************************************************
 */
void Set_MultiframeFlag(CAN_DATA *pCanData, uint8 flag)
{
    CAN_ID *ID = (CAN_ID *)&pCanData->canId;
    for (uint8 i = 0; i < ARRAY_SIZE(Multiframe); i++)
    {
        if (Multiframe[i].pgn == ID->pf)
        {
            if (flag)
            {
                MultiframeFlag |= (0x01 << i);
            }
            else
            {
                MultiframeFlag &= ~(0x01 << i);
            }
        }
    }
}
/**
 ******************************************************************************
 * @brief      Check_MultiframeFlag.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 Check_MultiframeFlag(CAN_DATA *pCanData)
{
    CAN_ID *ID = (CAN_ID *)&pCanData->canId;
    for (uint8 i = 0; i < ARRAY_SIZE(Multiframe); i++)
    {
        if (Multiframe[i].pgn == ID->pf)
        {
            if (Multiframe[i].Class == (MultiframeFlag & (0x01 << i)))
            {
                return TRUE;
            }
            else
            {
                return FALSE;
            }
        }
    }
    return FALSE;
}
uint8 Get_MultiframeFlag(void)
{
    return MultiframeFlag;
}
void Clr_MultiframeFlag(void)
{
    MultiframeFlag = 0x00;
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void Set_MulFrameCnt(CAN_DATA *pCanData, uint8 flag)
{
    CAN_ID *pId = (CAN_ID *)&pCanData->canId;
    for (uint8 i = 0; i < ARRAY_SIZE(Multiframe); i++)
    {
        if (Multiframe[i].pgn == pId->pf)
        {
            if (flag)
            {
                MulFrameCnt[Multiframe[i].Counter]++;
            }
            else
            {
                MulFrameCnt[Multiframe[i].Counter] = 0;
            }
        }
    }
}
uint8 Get_MulFrameCnt(CAN_DATA *pCanData)
{
    CAN_ID *pId = (CAN_ID *)&pCanData->canId;
    for (uint8 i = 0; i < ARRAY_SIZE(Multiframe); i++)
    {
        if (Multiframe[i].pgn == pId->pf)
        {
            return MulFrameCnt[Multiframe[i].Counter];
        }
    }
    return 0;
}
void Clr_MulFrameCnt(void)
{
    for (uint8 i = 0; i < ARRAY_SIZE(Multiframe); i++)
    {
        MulFrameCnt[Multiframe[i].Counter] = 0;
    }
}

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void Set_VIN_StartFlag(uint8 flag)
{
    if (VIN_Start != flag)
    {
        VIN_Start = flag;
    }
}
uint8 Get_VIN_StartFlag(void)
{
    return VIN_Start;
}
/**
 ******************************************************************************
 * @brief      Get_TcuChargeEnerge.
 * @param[in]  None
 * @param[out] uint16
 * @retval
 *
 * @details    获取TCU充电功率
 *
 * @note
 ******************************************************************************
 */
uint16 Get_TcuChargeEnerge(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    return pTcuCtrl->chargeEnerge;
}

/**
 ******************************************************************************
 * @brief      Check_YxData.
 * @param[in]  None
 * @param[out] bool_e
 * @retval
 *
 * @details    1.检测遥信状态，返回是否变化
 *
 * @note
 ******************************************************************************
 */
static bool_e Check_YxData(void)
{
    static uint8 lastData[16] = {0};
    uint8 curData[16] = {0};
#if 1
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    Get_Yx_Date();
    curData[0] = pTcuCtrl->tcuSendYx1.yx1State;
    curData[1] = pTcuCtrl->tcuSendYx1.yx1ErrData2;
    curData[2] = pTcuCtrl->tcuSendYx1.yx1ErrData3;
    curData[3] = pTcuCtrl->tcuSendYx1.yx1ErrData4;
    curData[4] = pTcuCtrl->tcuSendYx1.yx1ErrData5;
    curData[5] = pTcuCtrl->tcuSendYx1.errBackUpId;
    curData[6] = pTcuCtrl->tcuSendYx1.yx1ErrData7;
    curData[7] = pTcuCtrl->tcuSendYx2.yx2ErrData1;
    curData[8] = pTcuCtrl->tcuSendYx2.yx2ErrData2;
    curData[9] = pTcuCtrl->tcuSendYx2.yx2ErrData3;
    curData[10] = pTcuCtrl->tcuSendYx2.yx2ErrData4;
    curData[11] = pTcuCtrl->tcuSendYx2.yx2Data5;
    curData[12] = pTcuCtrl->tcuSendYx2.yx2Data6;
    curData[13] = pTcuCtrl->tcuSendYx2.yx2Data7;

#else
    Get_TcuReportYx(curData, 16);
#endif
    if (0 == memcmp(curData, lastData, sizeof(curData)))
    {
        return FALSE;
    }
    else
    {
        memcpy(lastData, curData, sizeof(lastData));
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief      Tcu_CheckChange.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details    1.检测遥信1状态是否改变，
 * 			   2.如果发送变化，立即发送
 *
 * @note
 ******************************************************************************
 */
static void Tcu_CheckChange(void)
{
    if (TRUE == Check_YxData())
    {
        if (0 != Get_TcuSendRemainTimer(PGN_CCU_YX1))
        {
            trace(TR_TCU_PROCESS, "立即发送TCU遥信帧\n");
            Set_TcuLastSendTimer(PGN_CCU_YX1, 0xFFFF);
            Set_TcuLastSendTimer(PGN_CCU_YX2, 0xFFFF);
        }
    }
}

/**
 ******************************************************************************
 * @brief      TCU版本支持即插即充
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
bool_e Get_TcuVerPlugAndPaySupport(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    if (pTcuCtrl->tcuProtocolVer == TCU_PLUGANDPAY_SUPPORT_VER)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}
/**
 ******************************************************************************
 * @brief     即插 即充车辆确认 .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
bool_e Tcu_GetVehicleValidate(void)
{
#if VEHICLE_VALIDATE_ENABLE
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    if (eTcuPlugAndPlay_Enable == pTcuCtrl->plugAndPlay)
    {
        if (eTcuVehicleCalibrate_OK == pTcuCtrl->vehicleValidateFailReason)
        {
            return TRUE;
        }
        else
        {
            return FALSE;
        }
    }
    else
    {
        return TRUE;
    }
#else
    return TRUE;
#endif
}

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint16 Get_TcuProtocolVer(void)
{
    return tcuCtrl.tcuProtocolVer;
}

void Set_TcuProtocolVer(uint16 tcuProtocolVer)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    pTcuCtrl->tcuProtocolVer = tcuProtocolVer;
}
/**
 ******************************************************************************
 * @brief      Tcu_Task.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details   1.TCU主任务
 *
 * @note
 ******************************************************************************
 */
void Tcu_Task(void)
{
    uint32 tick = tickGet();
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    can_init(TCU_CHAN, CAN_BAD_125);

    memset(pTcuCtrl, 0x00, MOFFSET(TCU_CTRL, tcuSendCtrl));

    taskDelay(100);

    Init_Tcu();

    taskDelay(20 * sysClkRateGet());

    dmnTaskRegister();

    FOREVER
    {
        if (abs(tick - tickGet()) >= 10)
        {
            dmnTaskSigned();
            tick = tickGet();

            Tcu_CheckChange();
        }

        taskDelay(TCU_CALL_CYCLE);

        TESTLISTEN; /**< 板级测试模式监听 */

        Tcu_StageManage(); /**< TCU主任务状态机 */

        Tcu_RecvTimerManage(); /**< TCU接收超时任务 */

        Tcu_SendServer(); /**< TCU发送任务 */

        UPD_SendServer(); /**< TCU升级任务 */

        Tcu_RecvServer(); /**< TCU接收任务 */

        TcuRst_Check(); /*复位重启任务*/
    }

    dmnTaskUnRegister();
    taskDelete(NULL);
}

/*----------------------------tcuMain.c--------------------------------*/
