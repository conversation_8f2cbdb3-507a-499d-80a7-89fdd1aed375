/**
 ******************************************************************************
 * @file      pcuSendCtrl.c
 * @brief     C Source file of pcuSendCtrl.c.
 * @details   This file including all API functions's
 *            implement of pcuSendCtrl.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <ttylib.h>
#include <stdio.h>
#include <sxlib.h>
#include "trace.h"
#include <gpio.h>
#include <taskLib.h>

#include "pcuMain.h"
#include "pcuRecvCtrl.h"
#include "pcuSendCtrl.h"
#include <ccu\lib\ccuLib.h>
#include <ccu\charge\ccuChargeMain.h>
#include <ccu\tcu\tcuMain.h>
#include <ccu\bsn\deviceState.h>
#include <ccu/bsn/io.h>
#include <ccu\bsn\sample.h>
#include <bms.h>
#include <maths.h>
#include <ccu\para\para.h>
#include <ccu\lib\ccuCanFrame.h>
#include "../lib/ccuFix.h"
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
typedef uint16 (*PsendCycle)(void);
typedef void (*PsendFunc)(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
typedef void (*PsendDealFunc)(uint32 pgn);

typedef struct PCU_SEND_DEAL_STRU
{
    uint32 pgn;
    uint8 prio;
    PsendCycle pSendCycle;
    PsendFunc pSendFunc;
    PsendDealFunc pSendDealFunc;
} PCU_SEND_DEAL;

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
extern uint8 g_PcuTaskDelay;

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
static PCU_SEND_CTRL s_PcuSendCtrl[PCU_PGN_CNT];

static void Send_Heart(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YXYC1(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YXYC2(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
// static void Send_YK(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YK_QStart(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YK_CStop(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YK_SStart(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YK_SSadr(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YK_Opara(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YK_Stop(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
static void Send_PucFixSet(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);
static void Send_PucFixQuery(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen);

static void Succ_Heart(uint32 pgn);
static void Succ_YXYC1(uint32 pgn);
static void Succ_YXYC2(uint32 pgn);
// static void Succ_YK(uint32 pgn);
static void Succ_YK_QStart(uint32 pgn);
static void Succ_YK_CStop(uint32 pgn);
static void Succ_YK_SStart(uint32 pgn);
static void Succ_YK_SAddr(uint32 pgn);
static void Succ_YK_Opara(uint32 pgn);
static void Succ_YK_Stop(uint32 pgn);
static void Succ_PucFixSet(uint32 pgn);
static void Succ_PucFixQuery(uint32 pgn);
static uint16 Pcu_Cycle_250ms(void);
static uint16 Pcu_Cycle_500ms(void);
static uint16 Pcu_Cycle_1000ms(void);
static uint16 Pcu_Cycle_2000ms(void);
const PCU_SEND_DEAL PCU_SEND_DEAL_TABLE[] =
    {
        {PGN_YK_QSTART, 6, Pcu_Cycle_250ms, Send_YK_QStart, Succ_YK_QStart},
        {PGN_YK_CSTOP, 6, Pcu_Cycle_250ms, Send_YK_CStop, Succ_YK_CStop},
        {PGN_YK_SSTART, 6, Pcu_Cycle_250ms, Send_YK_SStart, Succ_YK_SStart},
        {PGN_YK_SADDR, 6, Pcu_Cycle_250ms, Send_YK_SSadr, Succ_YK_SAddr},
        {PGN_YK_OPARA, 6, Pcu_Cycle_250ms, Send_YK_Opara, Succ_YK_Opara},
        {PGN_YK_STOP, 6, Pcu_Cycle_250ms, Send_YK_Stop, Succ_YK_Stop},
        {PGN_PCU_FIX_SET, 6, Pcu_Cycle_500ms, Send_PucFixSet, Succ_PucFixSet},
        {PGN_PCU_FIX_QUERY, 6, Pcu_Cycle_500ms, Send_PucFixQuery, Succ_PucFixQuery},
        {PGN_HEART_CCU, 6, Pcu_Cycle_2000ms, Send_Heart, Succ_Heart},
        {PGN_CCU_YXYC1, 6, Pcu_Cycle_1000ms, Send_YXYC1, Succ_YXYC1},
        {PGN_CCU_YXYC2, 6, Pcu_Cycle_1000ms, Send_YXYC2, Succ_YXYC2},
};

static uint16 Pcu_Cycle_250ms(void)
{
    return 250 / PCU_CALL_CYCLE;
}

static uint16 Pcu_Cycle_500ms(void)
{
    return 500 / PCU_CALL_CYCLE;
}

static uint16 Pcu_Cycle_1000ms(void)
{
    return 1000 / PCU_CALL_CYCLE;
}

static uint16 Pcu_Cycle_2000ms(void)
{
    return 2000 / PCU_CALL_CYCLE;
}
/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/**
 ******************************************************************************
 * @brief      GetPcuSendCtrl
 * @param[in]   uint32 Pgn     参数组编号
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      PCU_SEND_CTRL *返回控制变量指针
 *
 * @details     获取对应PGN的控制变量
 *
 * @note
 ******************************************************************************
 */
static PCU_SEND_CTRL *
Get_PcuSendCtrl(uint32 pgn)
{
    switch (pgn)
    {
    case PGN_YK_QSTART:
        return &s_PcuSendCtrl[0];

    case PGN_YK_CSTOP:
        return &s_PcuSendCtrl[1];

    case PGN_YK_SSTART:
        return &s_PcuSendCtrl[2];

    case PGN_YK_SADDR:
        return &s_PcuSendCtrl[3];

    case PGN_YK_OPARA:
        return &s_PcuSendCtrl[4];

    case PGN_YK_STOP:
        return &s_PcuSendCtrl[5];

    case PGN_PCU_FIX_SET:
        return &s_PcuSendCtrl[6];

    case PGN_PCU_FIX_QUERY:
        return &s_PcuSendCtrl[7];

    case PGN_CCU_YXYC1:
        return &s_PcuSendCtrl[8];

    case PGN_CCU_YXYC2:
        return &s_PcuSendCtrl[9];

    case PGN_HEART_CCU:
        return &s_PcuSendCtrl[10];

    default:
        break;
    }

    return NULL;
}

/**
 ******************************************************************************
 * @brief       CCU心跳帧发送组帧
 * @param[in]   uint32 pgn       参数组编号
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void Send_Heart(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];

    memset(&pOutBuf[index], 0x00, 8);
    index += 8;

    pOutLen[0] = index;
}

/**
 ******************************************************************************
 * @brief       CCU心跳帧发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     (1)打开心跳帧的接收计时
 *              (3)初始化心跳应答帧接收超时计时器
 * @note
 ******************************************************************************
 */
static void Succ_Heart(uint32 pgn)
{
    if (eTimerEnable_On != Get_PcuRecvTimerEnable(PGN_HEART_PCU))
    {
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---PCU心跳帧\n", PGN_HEART_PCU);
        Set_PcuRecvTimerEnable(PGN_HEART_PCU, eTimerEnable_On);
        Set_PcuRecvTimer(PGN_HEART_PCU, 0x00);
    }
}
/**
 ******************************************************************************
 * @brief       获取电压档位
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static bool_e Get_DemandVol_Gear(uint16 demandVol)
{
    static uint8 last_gear = 0;
    uint8 gear = 0;
    if (last_gear)
    {
        if (demandVol < 4800)
        {
            gear = 0;
        }
        else
        {
            gear = 1;
        }
    }
    else
    {
        if (demandVol >= 5000)
        {
            gear = 1;
        }
        else
        {
            gear = 0;
        }
    }
    last_gear = gear;
    return gear;
}
/**
 ******************************************************************************
 * @brief       CCU心跳帧发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     (1)打开心跳帧的接收计时
 *              (3)初始化心跳应答帧接收超时计时器
 * @note
 ******************************************************************************
 */
uint8 Get_K1K2_State(void)
{
    if ((eSwitchState_ON == Get_SwitchState(SXIO_IN_K1)) && (eSwitchState_ON == Get_SwitchState(SXIO_IN_K2)))
    {
        return eSwitchState_ON;
    }
    else
    {
        return eSwitchState_OFF;
    }
}

/**
 ******************************************************************************
 * @brief       CCU遥信遥测帧1发送组帧
 * @param[in]   uint32 pgn       参数组编号
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void Send_YXYC1(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 buf[8] = {0};

    Get_PcuYxYc1Data(buf);

    memcpy(pOutBuf + index, buf, 8);
    index += 8;

    pOutLen[0] = index;
}

/**
 ******************************************************************************
 * @brief       CCU遥信遥测帧1发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     (1)打开PCU遥信遥测帧1的接收计时
 *              (2)初始化PCU遥信遥测帧1接收超时计时器
 * @note
 ******************************************************************************
 */
static void Succ_YXYC1(uint32 pgn)
{
    if (eTimerEnable_On != Get_PcuRecvTimerEnable(PGN_PCU_WS))
    {
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---<%d>---PCU工作状态帧\n", PGN_PCU_WS);
        Set_PcuRecvTimerEnable(PGN_PCU_WS, eTimerEnable_On);
        Set_PcuRecvTimer(PGN_PCU_WS, 0x00);
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---<%d>---PCU遥信遥测帧\n", PGN_PCU_YXYC);
        Set_PcuRecvTimerEnable(PGN_PCU_YXYC, eTimerEnable_On);
        Set_PcuRecvTimer(PGN_PCU_YXYC, 0x00);
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---<%d>---PCU告警数据帧\n", PGN_PCU_ALARM);
        Set_PcuRecvTimerEnable(PGN_PCU_ALARM, eTimerEnable_On);
        Set_PcuRecvTimer(PGN_PCU_ALARM, 0x00);
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---<%d>---PCU扩展数据\n", PGN_PCU_EXYC);
        Set_PcuRecvTimerEnable(PGN_PCU_EXYC, eTimerEnable_On);
        Set_PcuRecvTimer(PGN_PCU_EXYC, 0x00);
    }
}

/**
 ******************************************************************************
 * @brief       CCU遥信遥测帧2发送组帧
 * @param[in]   uint32 pgn       参数组编号
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void Send_YXYC2(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];

    Uint16ToTwoUint8(pOutBuf + index, Get_K1K2InsideVol());
    index += 2;

    Uint16ToTwoUint8(pOutBuf + index, Get_K1K2Current() / 10);
    index += 2;

    Uint16ToTwoUint8(pOutBuf + index, Get_K1K2Current() * Get_K1K2InsideVol() / 1000000);
    index += 2;

    Uint16ToTwoUint8(pOutBuf + index, Get_ChargeTime());
    index += 2;

    pOutLen[0] = index;
}

/**
 ******************************************************************************
 * @brief       CCU遥信遥测帧2发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     (1)打开PCU遥信遥测帧2的接收计时
 *              (2)初始化PCU遥信遥测帧2接收超时计时器
 * @note
 ******************************************************************************
 */
static void Succ_YXYC2(uint32 pgn)
{
    //    if (eTimerEnable_On != Get_PcuRecvTimerEnable(PGN_PCU_YXYC))
    //    {
    //        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---<%d>---CCU遥信遥测帧2\n", PGN_PCU_YXYC);
    //        Set_PcuRecvTimerEnable(PGN_PCU_YXYC, eTimerEnable_On);
    //        Set_PcuRecvTimer(PGN_PCU_YXYC, 0x00);
    //    }
}

/**
 ******************************************************************************
 * @brief       CCU快速启动帧发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */

static void Send_YK_QStart(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    BCP_DATA strBCP;
    CHARGE_PARA strChargePara;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
    Get_BMS_Data(BMS_PGN_BCP, (void *)&strBCP);
    uint16 demondVol = Get_DemondVol();

    //    if(Get_CcuCfgParaEuropeEnable())
    //    {
    //        demondVol = *(uint16 *)strBCP.highestVoltage;
    //    }
    //    else
    //    {

    //    }
    uint16 batVol = demondVol;
    if (demondVol > 55)
    {
        if (demondVol - 2000 >= 55)
        {
            demondVol -= 55;
        }
    }
    // printf("Send_YK_QStart %d,%d\n",*(uint16 *)strBCP.highestVoltage,demondVol);
    uint32 Cur = FourUint8ToUint32(strChargePara.minOutputCurrent);
    uint8 k1k2 = Get_K1K2_State() ? 0x40 : 0x00;

    if (CHARGE_MODE_AUTO == Get_ChargeMode())
    {
        Cur = FourUint8ToUint32(strChargePara.minOutputCurrent);
    }
    else
    {
        Cur = Get_ManualCur();
    }

#if PCU_YK_CMD_VOL_RANGE_EN
    uint8 volGrader = Get_DemandVol_Gear(demondVol) ? 0x10 : 0x00;
#else
    uint8 volGrader = 0x10;
#endif

    uint8 index = pOutLen[0];

    pOutBuf[index++] = (uint8)pgn | k1k2 | volGrader;
    uint8 chargeMode = (Get_ChargeMode() == CHARGE_MODE_MANUAL) ? 0x80 : 0x00;
    pOutBuf[index++] = chargeMode;

    Uint16ToTwoUint8(pOutBuf + index, demondVol);
    index += 2;

    Uint16ToTwoUint8(pOutBuf + index, Cur / 10);
    index += 2;
    Uint16ToTwoUint8(pOutBuf + index, batVol);

    index += 2;

    pOutLen[0] = index;
    // print_buf(0,pOutBuf,index);
}

/**
 ******************************************************************************
 * @brief       CCU保留模块停止帧发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */

static void Send_YK_CStop(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 k1k2 = Get_K1K2_State() ? 0x40 : 0x00;

    pOutBuf[index++] = (uint8)pgn | k1k2;
    uint8 chargeMode = (Get_ChargeMode() == CHARGE_MODE_MANUAL) ? 0x80 : 0x00;
    pOutBuf[index++] = chargeMode;

    Uint16ToTwoUint8(pOutBuf + index, 0);
    index += 2;

    Uint16ToTwoUint8(pOutBuf + index, 0);
    index += 2;
    Uint16ToTwoUint8(pOutBuf + index, 0);

    index += 2;

    pOutLen[0] = index;
}
/**
 ******************************************************************************
 * @brief       CCU软起动帧发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */

static void Send_YK_SStart(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    CHARGE_PARA strChargePara;
    uint8 k1k2 = Get_K1K2_State() ? 0x40 : 0x00;
    uint16 demondVol = Get_K1K2OutsideVol();
    if (Get_CcuCfgParaEuropeEnable()) // 欧标增加
    {
        demondVol = Get_DemondVol();
    }

    uint16 batVol = demondVol;
#if 0
    if(demondVol > 55)
        demondVol -= 55;
#else
    if (demondVol > 55)
    {
        if (demondVol - 2000 >= 55)
        {
            demondVol -= 55;
        }
    }
#endif
    // printf("Send_YK_SStart %d\n",demondVol);
#if PCU_YK_CMD_VOL_RANGE_EN
    uint8 volGrader = Get_DemandVol_Gear(demondVol) ? 0x10 : 0x00;
#else
    uint8 volGrader = 0x10;
#endif
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
    uint32 Cur = FourUint8ToUint32(strChargePara.minOutputCurrent);
    pOutBuf[index++] = (uint8)pgn | k1k2 | volGrader;
    uint8 chargeMode = (Get_ChargeMode() == CHARGE_MODE_MANUAL) ? 0x80 : 0x00;
    pOutBuf[index++] = chargeMode;

    Uint16ToTwoUint8(pOutBuf + index, demondVol);
    index += 2;

    Uint16ToTwoUint8(pOutBuf + index, Cur / 10);
    index += 2;
    Uint16ToTwoUint8(pOutBuf + index, batVol);

    index += 2;

    pOutLen[0] = index;
    // print_buf(0,pOutBuf,index);
}

/**
 ******************************************************************************
 * @brief       CCU显示地址帧发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */

static void Send_YK_SSadr(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 k1k2 = Get_K1K2_State() ? 0x40 : 0x00;
    uint16 demondVol = Get_DemondVol();
    uint16 batVol = Get_K1K2OutsideVol();
#if PCU_YK_CMD_VOL_RANGE_EN
    uint8 volGrader = Get_DemandVol_Gear(demondVol) ? 0x10 : 0x00;
#else
    uint8 volGrader = 0x10;
#endif
    pOutBuf[index++] = (uint8)pgn | k1k2 | volGrader;
    uint8 chargeMode = (Get_ChargeMode() == CHARGE_MODE_MANUAL) ? 0x80 : 0x00;
    pOutBuf[index++] = chargeMode;

    Uint16ToTwoUint8(pOutBuf + index, demondVol);
    index += 2;

    Uint16ToTwoUint8(pOutBuf + index, Get_K1K2Current() / 10);
    index += 2;
    Uint16ToTwoUint8(pOutBuf + index, batVol);

    index += 2;

    pOutLen[0] = index;
}

/**
 ******************************************************************************
 * @brief       CCU参数调整帧
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     (1)打开PCU遥信遥测帧2的接收计时
 *              (2)初始化PCU遥信遥测帧2接收超时计时器
 * @note
 ******************************************************************************
 */

static void Send_YK_Opara(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    CHARGE_PARA strChargePara;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
    static uint16 last_demondvol = 0;
    uint8 index = pOutLen[0];
    uint16 demondVol = Get_DemondVol();
    uint32 demondCur = Get_DemondCur();

    //     uint16 batVol =  Get_K1K2OutsideVol();
    uint16 batVol = MIN(Get_K1K2OutsideVol(), demondVol);
    if (Get_CcuCfgParaEuropeEnable())
    {
        batVol = Get_DemondVol() - 55;
    }
    uint8 k1k2 = Get_K1K2_State() ? 0x40 : 0x00;
    if (demondVol == 0)
        demondVol = last_demondvol;
//    if(demondCur == 0)            /*充电暂停阶段，无电流输出（不需要按照最小输出电流） 开普 20231017*/
//    	demondCur = FourUint8ToUint32(strChargePara.minOutputCurrent);
#if PCU_YK_CMD_VOL_RANGE_EN
    uint8 volGrader = Get_DemandVol_Gear(demondVol) ? 0x10 : 0x00;
#else
    uint8 volGrader = 0x10;
#endif

    if ((batVol <= 7550) && (batVol > 7500)) // TODO -xg 电池电压调整
    {
        batVol = 7500;
    }
    else if ((batVol < 2000) && (batVol >= 1990)) // TODO -xg 电池电压调整
    {
        batVol = 2000;
    }
    pOutBuf[index++] = (uint8)pgn | k1k2 | volGrader;
    uint8 chargeMode = (Get_ChargeMode() == CHARGE_MODE_MANUAL) ? 0x80 : 0x00;
    pOutBuf[index++] = chargeMode;

    Uint16ToTwoUint8(pOutBuf + index, demondVol);
    index += 2;

    Uint16ToTwoUint8(pOutBuf + index, demondCur / 10);
    index += 2;
    Uint16ToTwoUint8(pOutBuf + index, batVol); // Get_BatteryVol()

    index += 2;

    pOutLen[0] = index;
    last_demondvol = demondVol;
}

/**
 ******************************************************************************
 * @brief       CCU遥信遥测帧2发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     (1)打开PCU遥信遥测帧2的接收计时
 *              (2)初始化PCU遥信遥测帧2接收超时计时器
 * @note
 ******************************************************************************
 */

static void Send_YK_Stop(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];

    pOutBuf[index++] = (uint8)pgn;
    uint8 chargeMode = (Get_ChargeMode() == CHARGE_MODE_MANUAL) ? 0x80 : 0x00;
    pOutBuf[index++] = chargeMode;

    Uint16ToTwoUint8(pOutBuf + index, 0);
    index += 2;

    Uint16ToTwoUint8(pOutBuf + index, 0);
    index += 2;
    Uint16ToTwoUint8(pOutBuf + index, 0);

    index += 2;

    pOutLen[0] = index;
}

/**
 ******************************************************************************
 * @brief       CCU遥信遥测帧2发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     (1)打开PCU遥信遥测帧2的接收计时
 *              (2)初始化PCU遥信遥测帧2接收超时计时器
 * @note
 ******************************************************************************
 */
static void Succ_YK_QStart(uint32 pgn)
{
    if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_QSTART_ACK))
    {
        trace(TR_PCU_PROCESS, "打开---接收使能---%06X---快速启动应答帧\n", PGN_YK_QSTART_ACK);
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---快速启动应答帧\n", PGN_YK_QSTART_ACK);
        Set_PcuRecvEnable(PGN_YK_QSTART_ACK, eRecvEnable_On);
        Set_PcuRecvTimerEnable(PGN_YK_QSTART_ACK, eTimerEnable_On);
        Set_PcuRecvTimer(PGN_YK_QSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_CSTOP_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SADDR_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_OPARA_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_STOP_ACK, 0x00);
    }
    pcuSetStageTick(TRUE);

    //    if(0 == Get_PcuSendRemainTimer(PGN_YK_QSTART))    /**<表示该帧已经发松完了  不用等待应答*/
    //    {
    //        Set_PcuRecvEnable(PGN_YK_QSTART_ACK, eRecvEnable_Off);
    //        Set_PcuRecvTimerEnable(PGN_YK_QSTART_ACK, eTimerEnable_Off);
    //        Set_PcuRecvTimer(PGN_YK_QSTART_ACK, 0x00);
    //    }
}
/**
 ******************************************************************************
 * @brief       CCU遥信遥测帧2发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     (1)打开PCU遥信遥测帧2的接收计时
 *              (2)初始化PCU遥信遥测帧2接收超时计时器
 * @note
 ******************************************************************************
 */
static void Succ_YK_CStop(uint32 pgn)
{
    if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_CSTOP_ACK))
    {
        trace(TR_PCU_PROCESS, "打开---接收使能---%06X---停止充电保留模块应答帧\n", PGN_YK_CSTOP_ACK);
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---停止充电保留模块应答帧\n", PGN_YK_CSTOP_ACK);
        Set_PcuRecvEnable(PGN_YK_QSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_QSTART_ACK, eTimerEnable_Off);
        Set_PcuRecvTimer(PGN_YK_QSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_CSTOP_ACK, eRecvEnable_On);
        Set_PcuRecvTimerEnable(PGN_YK_CSTOP_ACK, eRecvEnable_On);
        Set_PcuRecvTimer(PGN_YK_CSTOP_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SADDR_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_OPARA_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_STOP_ACK, 0x00);
    }
    pcuSetStageTick(TRUE);
    //    if(0 == Get_PcuSendRemainTimer(PGN_YK_CSTOP))    /**<表示该帧已经发松完了  不用等待应答*/
    //    {
    //        Set_PcuRecvEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
    //        Set_PcuRecvTimerEnable(PGN_YK_CSTOP_ACK, eTimerEnable_Off);
    //        Set_PcuRecvTimer(PGN_YK_CSTOP_ACK, 0x00);
    //    }
}
/**
 ******************************************************************************
 * @brief       èí???ˉ??·￠?í3é1|′|àí
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Succ_YK_SStart(uint32 pgn)
{
    if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_SSTART_ACK))
    {
        trace(TR_PCU_PROCESS, "打开---接收使能---%06X---软启动应答帧\n", PGN_YK_SSTART_ACK);
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---软启动应答帧\n", PGN_YK_SSTART_ACK);
        Set_PcuRecvEnable(PGN_YK_QSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_QSTART_ACK, eTimerEnable_Off);
        Set_PcuRecvTimer(PGN_YK_QSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_CSTOP_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SSTART_ACK, eRecvEnable_On);
        Set_PcuRecvTimerEnable(PGN_YK_SSTART_ACK, eRecvEnable_On);
        Set_PcuRecvTimer(PGN_YK_SSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SADDR_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_OPARA_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_STOP_ACK, 0x00);
    }
    pcuSetStageTick(TRUE);
    //    if(0 == Get_PcuSendRemainTimer(PGN_YK_SSTART))    /**<表示该帧已经发松完了  不用等待应答*/
    //    {
    //        Set_PcuRecvEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
    //        Set_PcuRecvTimerEnable(PGN_YK_SSTART_ACK, eTimerEnable_Off);
    //        Set_PcuRecvTimer(PGN_YK_SSTART_ACK, 0x00);
    //    }
}

/**
 ******************************************************************************
 * @brief       ??ê?μ??·??·￠?í3é1|′|àí
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Succ_YK_SAddr(uint32 pgn)
{
    if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_SADDR_ACK))
    {
        trace(TR_PCU_PROCESS, "打开---接收使能---%06X---显示地址应答帧\n", PGN_YK_SADDR_ACK);
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---显示地址应答帧\n", PGN_YK_SADDR_ACK);
        Set_PcuRecvEnable(PGN_YK_QSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_QSTART_ACK, eTimerEnable_Off);
        Set_PcuRecvTimer(PGN_YK_QSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_CSTOP_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SADDR_ACK, eRecvEnable_On);
        Set_PcuRecvTimerEnable(PGN_YK_SADDR_ACK, eRecvEnable_On);
        Set_PcuRecvTimer(PGN_YK_SADDR_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_OPARA_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_STOP_ACK, 0x00);
    }
}
/**
 ******************************************************************************
 * @brief       2?êyμ÷??·￠?í3é1|
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Succ_YK_Opara(uint32 pgn)
{
    if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_OPARA_ACK))
    {
        trace(TR_PCU_PROCESS, "打开---接收使能---%06X---参数调整应答帧\n", PGN_YK_OPARA_ACK);
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---参数调整应答帧\n", PGN_YK_OPARA_ACK);
        Set_PcuRecvEnable(PGN_YK_QSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_QSTART_ACK, eTimerEnable_Off);
        Set_PcuRecvTimer(PGN_YK_QSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_CSTOP_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SADDR_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_OPARA_ACK, eRecvEnable_On);
        Set_PcuRecvTimerEnable(PGN_YK_OPARA_ACK, eRecvEnable_On);
        Set_PcuRecvTimer(PGN_YK_OPARA_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_STOP_ACK, 0x00);
    }
}

/**
 ******************************************************************************
 * @brief       í￡?13?μ???·￠?í3é1|′|àí
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Succ_YK_Stop(uint32 pgn)
{
    if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_STOP_ACK))
    {
        trace(TR_PCU_PROCESS, "打开---接收使能---%06X---停止充电应答帧\n", PGN_YK_STOP_ACK);
        trace(TR_PCU_PROCESS, "打开---接收计时---%06X---停止充电应答帧\n", PGN_YK_STOP_ACK);
        Set_PcuRecvEnable(PGN_YK_QSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_QSTART_ACK, eTimerEnable_Off);
        Set_PcuRecvTimer(PGN_YK_QSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_CSTOP_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SSTART_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SADDR_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_OPARA_ACK, 0x00);

        Set_PcuRecvEnable(PGN_YK_STOP_ACK, eRecvEnable_On);
        Set_PcuRecvTimerEnable(PGN_YK_STOP_ACK, eRecvEnable_On);
        Set_PcuRecvTimer(PGN_YK_STOP_ACK, 0x00);
    }
    pcuSetStageTick(TRUE);
}

/**
 ******************************************************************************
 * @brief       定值设置帧发送组帧
 * @param[in]   uint32 pgn       参数组编号
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void SendAck_Close(uint32 pgn)
{
    if ((PGN_YK_QSTART == pgn) || (PGN_YK_CSTOP == pgn) || (PGN_YK_SSTART == pgn) ||
        (PGN_YK_SADDR == pgn) || (PGN_YK_OPARA == pgn) || (PGN_YK_STOP == pgn) ||
        (PGN_PCU_FIX_SET == pgn) || (PGN_PCU_FIX_QUERY == pgn))
    {
        pgn += 0x000100;
        Set_PcuRecvEnable(pgn, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(pgn, eTimerEnable_Off);
        Set_PcuRecvTimer(pgn, 0x00);
    }
}
///**
// ******************************************************************************
// * @brief       ò￡???üá???·￠?í′|àí
// * @param[in]   NONE
// * @param[out]  NONE
// * @retval      NONE
// *
// * @details
// *
// * @note
// ******************************************************************************
// */
// static void Succ_YK(uint32 pgn)
//{
//    uint8 cmd = (uint8)pgn;
//
//    if (eYKList_QuickStart == cmd)
//    {
//        if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_QSTART_ACK))
//        {
//            trace(TR_PCU_PROCESS, "打开---接收使能---%06X---快速启动应答帧\n", PGN_YK_QSTART_ACK);
//            trace(TR_PCU_PROCESS, "打开---接收计时---%06X---快速启动应答帧\n", PGN_YK_QSTART_ACK);
//
//            Set_PcuRecvEnable(PGN_YK_QSTART_ACK, eRecvEnable_On);
//            Set_PcuRecvTimerEnable(PGN_YK_QSTART_ACK, eTimerEnable_On);
//            Set_PcuRecvTimer(PGN_YK_QSTART_ACK, 0x00);
//        }
//    }
//    else if (eYKList_ChargeStop == cmd)
//    {
//        if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_STOP_ACK))
//        {
//            trace(TR_PCU_PROCESS, "打开---接收使能---%06X---充电停止应答帧\n", PGN_YK_STOP_ACK);
//            trace(TR_PCU_PROCESS, "打开---接收计时---%06X---充电停止应答帧\n", PGN_YK_STOP_ACK);
//
//            Set_PcuRecvEnable(PGN_YK_STOP_ACK, eRecvEnable_On);
//            Set_PcuRecvTimerEnable(PGN_YK_STOP_ACK, eTimerEnable_On);
//            Set_PcuRecvTimer(PGN_YK_STOP_ACK, 0x00);
//        }
//    }
//    else if (eYKList_SoftStart == cmd)
//    {
//        if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_SSTART_ACK))
//        {
//            trace(TR_PCU_PROCESS, "打开---接收使能---%06X---软启动应答帧\n", PGN_YK_SSTART_ACK);
//            trace(TR_PCU_PROCESS, "打开---接收计时---%06X---软启动应答帧\n", PGN_YK_SSTART_ACK);
//
//            Set_PcuRecvEnable(PGN_YK_SSTART_ACK, eRecvEnable_On);
//            Set_PcuRecvTimerEnable(PGN_YK_SSTART_ACK, eTimerEnable_On);
//            Set_PcuRecvTimer(PGN_YK_SSTART_ACK, 0x00);
//        }
//    }
//    else if (eYKList_ShowAddr == cmd)
//    {
//        if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_SADDR_ACK))
//        {
//            trace(TR_PCU_PROCESS, "打开---接收使能---%06X---显示地址应答帧\n", PGN_YK_SADDR_ACK);
//            trace(TR_PCU_PROCESS, "打开---接收计时---%06X---显示地址应答帧\n", PGN_YK_SADDR_ACK);
//
//            Set_PcuRecvEnable(PGN_YK_SADDR_ACK, eRecvEnable_On);
//            Set_PcuRecvTimerEnable(PGN_YK_SADDR_ACK, eTimerEnable_On);
//            Set_PcuRecvTimer(PGN_YK_SADDR_ACK, 0x00);
//        }
//    }
//    else if (eYKList_OutputPara == cmd)
//    {
//        if (eRecvEnable_On != Get_PcuRecvEnable(PGN_YK_OPARA_ACK))
//        {
//
//            //trace(TR_PCU_PROCESS, "打开---接收计时---%06X---参数修改应答帧\n", PGN_YK_OPARA_ACK);
//
//            Set_PcuRecvEnable(PGN_YK_OPARA_ACK, eRecvEnable_On);
//            Set_PcuRecvTimerEnable(PGN_YK_OPARA_ACK, eTimerEnable_On);
//            Set_PcuRecvTimer(PGN_YK_OPARA_ACK, 0x00);
//        }
//    }
//
//}

//
// PARA_FORMAT CONST_PCU_PARA_ALLOT_MAP[] =
//{
//    { ePcuList_R_HardVer,            g_StrPcuConstPara.HardVer,            sizeof(g_StrPcuConstPara.HardVer)          },
//    { ePcuList_R_SoftDate,           g_StrPcuConstPara.softDate,           sizeof(g_StrPcuConstPara.softDate)         },
//    { ePcuList_R_SoftVer,            g_StrPcuConstPara.softVer,            sizeof(g_StrPcuConstPara.softVer)          },
//    { ePcuList_R_SoftCrc,            g_StrPcuConstPara.softCrc,            sizeof(g_StrPcuConstPara.softCrc)          },
//    { ePcuList_RW_ComAddr,           &g_StrPcuConstPara.comAddr,           sizeof(g_StrPcuConstPara.comAddr)          },
//    { ePcuList_RW_CcuCnt,            &g_StrPcuConstPara.ccuCnt,            sizeof(g_StrPcuConstPara.ccuCnt)           },
//    { ePcuList_RW_Overtime,          &g_StrPcuConstPara.overtime,          sizeof(g_StrPcuConstPara.overtime)         },
//    { ePcuList_RW_ModuleCnt,         g_StrPcuConstPara.moduleCnt,          sizeof(g_StrPcuConstPara.moduleCnt)        },
//    { ePcuList_RW_ModuleNomVol,      g_StrPcuConstPara.moduleNomVol,       sizeof(g_StrPcuConstPara.moduleNomVol)     },
//    { ePcuList_RW_ModuleMaxVol,      g_StrPcuConstPara.moduleMaxVol,       sizeof(g_StrPcuConstPara.moduleMaxVol)     },
//    { ePcuList_RW_ModuleMinVol,      g_StrPcuConstPara.moduleMinVol,       sizeof(g_StrPcuConstPara.moduleMinVol)     },
//    { ePcuList_RW_ModuleNomCur,      g_StrPcuConstPara.moduleNomCur,       sizeof(g_StrPcuConstPara.moduleNomCur)     },
//    { ePcuList_RW_ModuleMaxCur,      g_StrPcuConstPara.moduleMaxCur,       sizeof(g_StrPcuConstPara.moduleMaxCur)     },
//    { ePcuList_RW_ModuleMinCur,      g_StrPcuConstPara.moduleMinCur,       sizeof(g_StrPcuConstPara.moduleMinCur)     },
//    { ePcuList_RW_ModuleOvertime,    g_StrPcuConstPara.moduleOvertime,     sizeof(g_StrPcuConstPara.moduleOvertime)   },
//    { ePcuList_RW_PowerAllotEnable,  &g_StrPcuConstPara.powerAllotEnable,  sizeof(g_StrPcuConstPara.powerAllotEnable) },
//    { ePcuList_RW_PowerAllotType,    &g_StrPcuConstPara.powerAllotType,    sizeof(g_StrPcuConstPara.powerAllotType)   },
//    { ePcuList_RW_ModuleGroupCnt,    &g_StrPcuConstPara.moduleGroupCnt,    sizeof(g_StrPcuConstPara.moduleGroupCnt)   },
//    { ePcuList_RW_ChargePortCnt,     &g_StrPcuConstPara.chargePortCnt,     sizeof(g_StrPcuConstPara.chargePortCnt)    },
//    { ePcuList_RW_OutputType,        &g_StrPcuConstPara.outputType,        sizeof(g_StrPcuConstPara.outputType)       },
//    { ePcuList_RW_ModuleUseEnable,   &g_StrPcuConstPara.moduleUseEnable,   sizeof(g_StrPcuConstPara.moduleUseEnable)  },
//    { ePcuList_RW_YXCycle,           &g_StrPcuConstPara.yxCycle,           sizeof(g_StrPcuConstPara.yxCycle)          },
//    { ePcuList_RW_YCCycle,           &g_StrPcuConstPara.ycCycle,           sizeof(g_StrPcuConstPara.ycCycle)          },
//};

uint8 Get_ConstPara(CONST_DEV_TYPE devType, uint8 paraType, uint8 *pOutData)
{
    uint8 len = 0;
    fix_cfg_t *pFix = NULL;
    PARA_FORMAT *pParaFormat = NULL;
    if (paraType < ePcuList_R_DevModel || paraType > ePcuList_RW_LowMinVolCur)
    {
        trace(TR_PCU_PROCESS, "定值参数不在范围内\n");
        return len;
    }
    Get_pcu_FixPara_ptr(paraType, (void **)&pFix);
    if (NULL != pFix)
    {
        len = pFix->len;
        memcpy(pOutData, pFix->pDat, len);
    }
    return len;
}

void Set_ConstPara(CONST_DEV_TYPE devType, uint8 paraType, uint8 *para)
{
    const PARA_FORMAT *pParaFormat = NULL;

    if (paraType < ePcuList_R_DevModel || paraType > ePcuList_RW_LowMinVolCur)
    {
        trace(TR_PCU_PROCESS, "定值参数不在范围内\n");
        return;
    }

    //    pParaFormat = (eDevType_PCU == devType) ? &CONST_PCU_PARA_ALLOT_MAP[paraType - 1] : NULL;

    if (NULL != pParaFormat)
    {
        memcpy((uint8 *)pParaFormat->para, para, pParaFormat->len);
    }
}

static void Send_PucFixSet(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    PARA_OPERATE paraOperate = Get_CurParaOperateType();
    uint8 *para = NULL;
    uint8 paraLen = 0;

    if (pOutBuf == NULL)
    {

        return;
    }

#if 0
    Uint16ToTwoUint8(pOutBuf + index, paraOperate.paraType);
    index += 2;

    pOutBuf[index++] = paraOperate.devType;
    pOutBuf[index++] = 0x00;

    paraLen = Get_ConstPara(paraOperate.devType, paraOperate.paraType, para);

    memcpy(pOutBuf + index, para, paraLen);
    index += paraLen;

    index = (index < 12) ? 12 : index;

    pOutLen[0] = index;
#else
    pOutBuf[index++] = Get_TcuJunctorId();
    pOutBuf[index++] = paraOperate.devType;
    pOutBuf[index++] = (Get_PcuAddr() == 0) ? 0x10 : Get_PcuAddr();

    pOutBuf[index++] = 0x00;

    paraLen = Get_ConstPara(paraOperate.devType, paraOperate.paraType, para);

    if (para == NULL)
    {
        return;
    }
    memcpy(pOutBuf + index, para, paraLen);
    index += paraLen;

    index = (index < 12) ? 12 : index;

    pOutLen[0] = index;
#endif
}

/**
 ******************************************************************************
 * @brief       遥控命令帧发送处理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Succ_PucFixSet(uint32 pgn)
{
    if (eRecvEnable_On != Get_PcuRecvEnable(PGN_PCU_FIX_SET_ACK))
    {
        Set_PcuRecvEnable(PGN_PCU_FIX_SET_ACK, eRecvEnable_On);
        Set_PcuRecvTimerEnable(PGN_PCU_FIX_SET_ACK, eTimerEnable_On);
        Set_PcuRecvTimer(PGN_PCU_FIX_SET_ACK, 0x00);
    }
}

/**
 ******************************************************************************
 * @brief       设置发送标记
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]   SEND_FLAG  enableFlg  已发送标记
 * @param[out]
 * @retval
 *
 * @details     0x55-已发送、0x00-未发送、0xFF-无效
 *
 * @note
 ******************************************************************************
 */
static void Send_PucFixQuery(uint32 pgn, uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    PARA_OPERATE paraOperate = Get_CurParaOperateType();
    uint8 para[16] = {0};
    uint8 paraLen = 0;

    Uint16ToTwoUint8(pOutBuf + index, paraOperate.paraType);
    index += 2;

    pOutBuf[index++] = paraOperate.devType;
    pOutBuf[index++] = 0x00;

    paraLen = Get_ConstPara(paraOperate.devType, paraOperate.paraType, para);

    memcpy(pOutBuf + index, para, paraLen);
    index += paraLen;

    index = (index < 12) ? 12 : index;

    pOutLen[0] = index;
}

/**
 ******************************************************************************
 * @brief       定值查询
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Succ_PucFixQuery(uint32 pgn)
{
    if (eRecvEnable_On != Get_PcuRecvEnable(PGN_PCU_FIX_QUERY_ACK))
    {
        Set_PcuRecvEnable(PGN_PCU_FIX_QUERY_ACK, eRecvEnable_On);
        Set_PcuRecvTimerEnable(PGN_PCU_FIX_QUERY_ACK, eTimerEnable_On);
        Set_PcuRecvTimer(PGN_PCU_FIX_QUERY_ACK, 0x00);
    }
}
/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/

/**
 ******************************************************************************
 * @brief       设置发送标记
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]   SEND_FLAG  enableFlg  已发送标记
 * @param[out]
 * @retval
 *
 * @details     0x55-已发送、0x00-未发送、0xFF-无效
 *
 * @note
 ******************************************************************************
 */
void Set_PcuSendFlg(uint32 pgn, SEND_FLAG sendFlg)
{
    PCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_PcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->sendFlg = sendFlg;
}

/**
 ******************************************************************************
 * @brief      获取发送标记
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval      SEND_FLAG  enableFlg  已发送标记
 *
 * @details     0x55-已发送、0x00-未发送、0xFF-无效
 *
 * @note
 ******************************************************************************
 */
SEND_FLAG
Get_PcuSendFlg(uint32 pgn)
{
    PCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_PcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return eSendFlag_Null;
    }

    return pSendCtrl->sendFlg;
}

/**
 ******************************************************************************
 * @brief      初始化发送剩余时间计时器
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]   uint32 countValue          计时器初始值
 * @param[out]
 * @retval
 *
 * @details     0xFFFF表示永久发送
 *
 * @note
 ******************************************************************************
 */
void Set_PcuSendRemainTimer(uint32 pgn, uint32 countValue)
{
    PCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_PcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->sendRemainTimer = countValue;
    return;
}

/**
 ******************************************************************************
 * @brief       获取发送剩余时间
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval      返回发送剩余时间
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint32
Get_PcuSendRemainTimer(uint32 pgn)
{
    PCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_PcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return 0;
    }

    return pSendCtrl->sendRemainTimer;
}

/**
 ******************************************************************************
 * @brief      设置发送剩余时间计时器自减
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval      计数器自减
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void Set_PcuSendRemainTimerDec(uint32 pgn)
{
    PCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_PcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    if (pSendCtrl->sendRemainTimer > 0)
    {
        pSendCtrl->sendRemainTimer--;
    }

    return;
}

/**
 ******************************************************************************
 * @brief       初始化发送计时器
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]   uint32 countValue          计时器初始值
 * @param[out]
 * @retval
 *
 * @details     延时可通过给countValue赋负值
 *
 * @note
 ******************************************************************************
 */
void Set_PcuSendTimer(uint32 pgn, uint32 countValue)
{
    PCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_PcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->sendTimer = countValue;
    return;
}

/**
 ******************************************************************************
 * @brief      获取发送计时器时间
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint32
Get_PcuSendTimer(uint32 pgn)
{
    PCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_PcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return 0;
    }

    return pSendCtrl->sendTimer;
}

/**
 ******************************************************************************
 * @brief      设置发送计时器自增
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void Set_PcuSendTimerInc(uint32 pgn)
{
    PCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_PcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->sendTimer++;
    return;
}

/**
 ******************************************************************************
 * @brief       获取PCU遥信遥测数据
 * @param[in]   int fd
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
extern TCU_CTRL tcuCtrl;
void Get_PcuYxYc1Data(uint8 *pOutData)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = 0;

    // TODO-开普修改
    if (0x02 == pTcuCtrl->chargeServeState) // 0x01-充电服务停止;0x02-充电服务启用
    {
        if (Get_CcuCfgParaEuropeEnable())
        {
            pOutData[index++] = (Get_EcPlcCpStatus() != eCpStatus_Unplug) ? 0x01 : 0x00;
        }
        else
        {
            pOutData[index++] = (Get_PhyConVol() == enumPhyConVol_4V) ? 0x01 : 0x00;
            //            printf("==============%s-%d=====%d=========\n",__FILE__,__LINE__,(Get_PhyConVol() == enumPhyConVol_4V) );
        }
        // printf("%s-%d\n",__FILE__,__LINE__);
    }
    else
    {
        pOutData[index++] = 0x00;
    }

    pOutData[index++] = (Check_ErrType(eErrType_EmergencyStop)) ? 0x01 : 0x00;
    pOutData[index++] = (Check_ErrType(eErrType_CcuDoorOpenErr) | Check_ErrType(eErrType_PcuDoorOpenErr)) ? 0x01 : 0x00;
    pOutData[index++] = (Check_ErrType(eErrType_WaterLoggingErr) | Check_ErrType(eErrType_PcuWaterFault)) ? 0x01 : 0x00;
    pOutData[index++] = (Check_ErrType(eErrType_K1Err) || Check_ErrType(eErrType_K2Err)) ? 0x01 : 0x00; /*网外新增判断*/
    memset(&pOutData[index], 0x00, 3);

    index += 4;
}
/**
 ******************************************************************************
 * @brief       PCU发送服务
 * @param[in]   int fd
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void Pcu_SendServer(void)
{
    const PCU_SEND_DEAL *pSendDeal = NULL;

    uint32 canId = 0;
    uint8 len = 0;
    uint8 buf[12] = {0};
    uint8 index = 0;

#if PCU_ADDR_BROADCAST_EN
    uint8 pcuAddr = (Get_PcuAddrRecvSuccFlag()) ? Get_PcuAddr() : ADDR_PCU_BOARD;
#else
    uint8 pcuAddr = (Get_PcuAddr() == 0) ? 0x10 : Get_PcuAddr();
#endif
    for (index = 0; index < FCNT(PCU_SEND_DEAL_TABLE); index++)
    {
        pSendDeal = &PCU_SEND_DEAL_TABLE[index];

        if (0xFFFF != Get_PcuSendRemainTimer(pSendDeal->pgn)) /**<为0xFFFF时表示永久发送*/
        {
            Set_PcuSendRemainTimerDec(pSendDeal->pgn);

            if (0 == Get_PcuSendRemainTimer(pSendDeal->pgn))
            {
                SendAck_Close(pSendDeal->pgn);
                continue;
            }
        }

        Set_PcuSendTimerInc(pSendDeal->pgn); /**<该值赋值大于周期时表示立即发送*/

        if (Get_PcuSendTimer(pSendDeal->pgn) < pSendDeal->pSendCycle())
        {
            continue;
        }

        if (NULL == pSendDeal->pSendFunc)
        {
            continue;
        }

        bzero(buf, sizeof(buf));
        len = 4;

        pSendDeal->pSendFunc(pSendDeal->pgn, buf, &len);
        Deal_PcuLogPackage(pSendDeal->pgn, buf, len - 4); // 发送PCU数据log

        if ((len >= 4) && (len <= 12))
        {
            canId = GetCanID(pSendDeal->pgn & 0x00FF00, pSendDeal->prio,
                             Get_CcuAddr(), pcuAddr);
            if (MODE_CPCU == sysGetMode())
            {
                CAN_DATA tmp = {0};
                tmp.canId = canId;
                tmp.dataLen = len - 4;
                memcpy(tmp.dataBuf, &buf[4], len - 4);

                Cpcu_SendToPcu(&tmp, sizeof(CAN_DATA));
            }
            else
            {
                can_send(PCU_CHAN, canId, &buf[4], (len - 4));
            }
            trace(TR_CH3, "  [S3: %08X]: ", canId);
            trace_buf(TR_CH3, &buf[4], (len - 4));
        }
        Set_PcuSendFlg(pSendDeal->pgn, eSendFlag_Yes);
        Set_PcuSendTimer(pSendDeal->pgn, 0x00);

        if (NULL != pSendDeal->pSendDealFunc)
        {
            pSendDeal->pSendDealFunc(pSendDeal->pgn);
        }

        /* 担心发送太频繁，导致串口死掉*/
        taskDelay(1);
        g_PcuTaskDelay += 1;

        if (g_PcuTaskDelay >= (PCU_CALL_CYCLE - 1))
        {
            break;
        }
    }
}
/*----------------------------pcuSendCtrl.c--------------------------------*/
