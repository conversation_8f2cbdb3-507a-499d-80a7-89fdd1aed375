/**
 ******************************************************************************
 * @file       types.h
 * @brief      系统类型定义
 * @details    本文件定义系统中用到的所有使用的基本类型
 * @copyright  Copyright(C), 2005-2020,Sanxing Medical & Electric Co.,Ltd.
 *
 ******************************************************************************
 */

#ifndef _TYPES_H_
#define _TYPES_H_

/*------------------------------------------------------------------------------
Section: Includes
------------------------------------------------------------------------------*/

/*------------------------------------------------------------------------------
Section: Macro Definitions
------------------------------------------------------------------------------*/
#ifndef OK
#define OK 0
#endif
#ifndef ERROR
#define ERROR (-1)
#endif
#ifndef BOOL
#define BOOL int8
#endif
#ifndef bool
#define bool uint8
#endif
#ifndef IMPORT
#define IMPORT extern
#endif
#ifndef LOCAL
#define LOCAL static
#endif
#ifndef FAST
#define FAST register
#endif

#ifndef NULL
#define NULL 0
#endif

#define Swap_Byte
/**
 * IO definitions
 *
 * define access restrictions to peripheral registers
 */

#define __I volatile const /*!< defines 'read only' permissions      */
#define __O volatile       /*!< defines 'write only' permissions     */
#define __IO volatile      /*!< defines 'read / write' permissions   */

/*------------------------------------------------------------------------------
Section: Type Definitions
------------------------------------------------------------------------------*/
typedef char char_t;
typedef signed char int8_t;
typedef signed short int16_t;
typedef signed int int32_t;
typedef signed long long int64_t;
typedef unsigned char uint8_t;
typedef unsigned short uint16_t;
typedef unsigned int uint32_t;
typedef unsigned long long uint64_t;
typedef float float32_t;
typedef double float64_t;
typedef long double float128_t;
typedef enum boolean
{
    FALSE = 0,
    TRUE = 1,
} bool_e;
typedef int status_t;

typedef signed char int8;
typedef short int int16;
typedef int int32;
typedef long long int64;

typedef unsigned char uint8;
typedef unsigned short int uint16;
typedef unsigned int uint32;
typedef unsigned long long uint64;

typedef unsigned char byte;
typedef unsigned char bits;
typedef unsigned char uchar;
typedef float float32;

typedef uint32 u32;
typedef uint16 u16;
typedef uint8 u8;

typedef uint32 size_t;

typedef unsigned char tBoolean;

typedef enum Boolean
{
    false = 0,
    true = 1
} boolean;
typedef signed long time_t;

#define WAIT_FOREVER ((uint32)0)
#define Sx_portMAX_DELAY (uint32)0xffffffffUL
// #define FOREVER for (;;)

#define FAR

// #define max(x, y)   (((x) < (y)) ? (y) : (x))
// #define min(x, y)   (((x) < (y)) ? (x) : (y))
// #define isascii(c)  ((unsigned) (c) <= 0177)
// #define toascii(c)  ((c) & 0177)
// #define BITS(x,y) (((x)>>(y))&0x01u)   /* 判断某位是否为1 */
// #define SETBITS(x,y,n) (x) = (n) ? ((x)|(1 << (y))) : ((x) &(~(1 << (y))));

#ifdef __cplusplus
typedef void (*OSFUNCPTR)(void *); /* ptr to function returning int */
typedef int (*FUNCPTR)(...);       /* ptr to function returning int */
typedef void (*VOIDFUNCPTR)(...);  /* ptr to function returning void */
typedef double (*DBLFUNCPTR)(...); /* ptr to function returning double*/
typedef float (*FLTFUNCPTR)(...);  /* ptr to function returning float */
typedef void (*VOIDFUNCPTRBOOL)(boolean);

#else
typedef void (*OSFUNCPTR)(void *); /* ptr to function returning int */
typedef int (*FUNCPTR)();          /* ptr to function returning int */
typedef void (*VOIDFUNCPTR)();     /* ptr to function returning void */
typedef double (*DBLFUNCPTR)();    /* ptr to function returning double*/
typedef float (*FLTFUNCPTR)();     /* ptr to function returning float */
typedef void (*VOIDFUNCPTRBOOL)(boolean b);

#endif /* _cplusplus */

typedef union
{
    unsigned long longValue;
    unsigned char array[4];
    struct
    {
        unsigned short high, low;
    } shortValue;
    struct
    {
        unsigned char highest, higher, middle, low;
    } charValue;
} U_UINT32;

typedef union
{
    unsigned long LongValue;
    unsigned char Array[4];
    struct
    {
        unsigned short High, Low;
    } IntValue;
    struct
    {
        unsigned char Highest, Higher, Middle, Low;
    } CharValue;
} Long_Char;

typedef int STATUS;

typedef enum
{
    DI_IO_L = 0,
    DI_IO_H = 1
} DI_HL;
typedef enum
{
    DI_OPEN = 0,
    DI_CLOSE = 1
} DI_STATUS;
typedef enum
{
    DI_ERR_NONE,
    DI_ERR_EXIST = 1
} DI_ERR;

#define diInput(di) ((di == DI_IO_H) ? DI_OPEN : DI_CLOSE)
#define diOutput(di) ((di == DI_OPEN) ? DI_IO_L : DI_IO_H)
#define diStatus(di) ((di == DI_IO_H) ? DI_OPEN : DI_CLOSE)
/*------------------------------------------------------------------------------
Section: Globals
------------------------------------------------------------------------------*/
// 通用数据结构
#define BUFFLEN 256

typedef struct
{
    int iDataLen;
    uint8 ucDataBuf[BUFFLEN];
} STWORKBUF;

/* uint32 processing */
#define BREAK_UINT32(var, ByteNum) \
    (uint8)((uint32_t)(((var) >> ((ByteNum) * 8)) & 0x00FF))

#define BUILD_UINT32(Byte0, Byte1, Byte2, Byte3) \
    ((uint32_t)((uint32_t)((Byte0) & 0x00FF) + ((uint32_t)((Byte1) & 0x00FF) << 8) + ((uint32_t)((Byte2) & 0x00FF) << 16) + ((uint32_t)((Byte3) & 0x00FF) << 24)))

/* uint16 processing */
#define BREAK_UINT16(var, ByteNum) \
    (uint8)((uint16)(((var) >> ((ByteNum) * 8)) & 0x00FF))

#define BUILD_UINT16(Byte0, Byte1) \
    ((uint16)((uint16)((Byte0) & 0x00FF) + ((uint16)((Byte1) & 0x00FF) << 8)))

#ifdef Swap_Byte

#define Swap_Uint32(x)                 \
    ((((const uint8 *)(x))[0] << 24) | \
     (((const uint8 *)(x))[1] << 16) | \
     (((const uint8 *)(x))[2] << 8) |  \
     (((const uint8 *)(x))[3]))

#define Swap_Uint16(x)                \
    ((((const uint8 *)(x))[0] << 8) | \
     ((const uint8 *)(x))[1])

#endif
/*------------------------------------------------------------------------------
Section: Function Prototypes
------------------------------------------------------------------------------*/

#endif /*_TYPES_H_ */

/*-------------------------------End of types.h-------------------------------*/
