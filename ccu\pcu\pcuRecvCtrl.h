/**
 ******************************************************************************
 * @file       pcuRecvCtrl.h
 * @brief      API include file of pcuRecvCtrl.h.
 * @details    This file including all API functions's declare of pcuRecvCtrl.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef __PCU_RECV_CTRL_H__
#define __PCU_RECV_CTRL_H__

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <types.h>

/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

typedef uint32 (*PPcuOutTimeFunc)(void);
typedef bool_e (*PDecodeFunc)(uint8 *pInBuf, uint32 len);

typedef struct PCU_RECV_DEAL_STRU
{
    uint32 pgn;                /**< 参数组编号 */
    uint8 prio;                /**< 优先权 */
    uint8 mulFrameFlag;        /**< 多帧标记 多帧数据时为真，否则为0 */
                               //    uint32          overtime;                   /**< 超时时间 */
    uint8 stopRecvFlag;        /**< 超时停止接收标记 TRUE-接收 */
    PPcuOutTimeFunc pOverTime; /**< 超时时间 */
    PDecodeFunc pDecodeFunc;   /**< 接收处理函数 */
} PCU_RECV_DEAL;
#pragma pack(push)
#pragma pack(1) // 设定为1字节对齐
typedef struct
{
    uint32 canId;        /**<帧ID*/
    uint8 frameCnt;      /**<报文总帧数*/
    uint16 dataLen;      /**<报问总长度*/
    uint8 dataBuf[128];  /**<数据缓冲区*/
    uint16 dataCnt;      /**<当前接受数据长度*/
    uint16 sumCheck;     /**<累加和校验*/
    uint8 sumRecFlag;    /**<校验接受完成 0表示未接受，1表示接受一半，2表示接受完成*/
} PCU_CAN_MULFRAME_RECV; /**<PCU多帧数据结构*/
#pragma pack(pop)
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
void Pcu_RecvTimerManage(void);

void Pcu_RecvServer(void);

void Set_PcuRecvTimer(uint32 pgn, int32 countValue);

void Set_PcuRecvTimerEnable(uint32 pgn, TIMER_ENABLE enableFlg);

TIMER_ENABLE Get_PcuRecvTimerEnable(uint32 pgn);

void Set_PcuRecvEnable(uint32 pgn, RECV_ENABLE enableFlg);

RECV_ENABLE Get_PcuRecvEnable(uint32 pgn);

void Clr_PcuErrInfo(uint32 pgn);

#endif //__PCU_RECV_CTRL_H__

/*--------------------------End of pcuRecvCtrl.h----------------------------*/
