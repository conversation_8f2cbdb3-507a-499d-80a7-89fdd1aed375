/*
 * list.h
 *
 *  Created on: 2023年6月1日
 *      Author: baohaopeng
 */

#ifndef LIST_H_
#define LIST_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <types.h>
#include <stddef.h>
/**
 *
 */
typedef enum
{
    RET_ERR = -1,
    RET_OK = 0,
} Ret_State;
/*define list structure*/
typedef struct List_Head
{
    struct List_Head *prev, *next;
} list_head;
/*hash list struction*/
typedef struct Hlist_Node
{
    struct Hlist_Node *next, **pprev;
} hlist_node;

typedef struct Hlist_Head
{
    hlist_node *first;
} hlist_head;
/*初始化链表结构体*/
#define LIST_HEAD_INIT(name) {(&name), (&name)}

#define container_of(ptr, type, member)                   \
    ({                                                    \
        const typeof(((type *)0)->member) *_mptr = ptr;   \
        (type *)((char *)_mptr - offsetof(type, member)); \
    })
#define LIST_HEAD(name) \
    list_head name = LIST_HEAD_INIT(name)

static void INIT_LIST_HEAD(list_head *list)
{
    list->prev = list;
    list->next = list;
}

/**
 ******************************************************************************
 * @brief      _list_add.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *  在已知两个节点之间添加新节点
 * @note
 ******************************************************************************
 */
static void _list_add(list_head *new,
                      list_head *prev,
                      list_head *next)
{
    next->prev = new;
    new->next = next;
    new->prev = prev;
    prev->next = new;
}

/**
 ******************************************************************************
 * @brief      list_add.
 * @param[in]  @new:  新添加节点
 *             @head：链表头节点
 * @param[out] None
 * @retval
 *
 * @details
 *      在指定的头部之后插入一个新节点
 * @note  (前插法?)
 ******************************************************************************
 */
static void list_add(list_head *new, list_head *head)
{
    _list_add(new, head, head->next);
}

/**
 ******************************************************************************
 * @brief     list_add_tail .
 * @param[in]  @new:  新添加节点
 *             @head：链表头节点
 * @param[out] None
 * @retval
 *
 * @details
 *          在头节点之前插入链表（头节点保持不变）
 * @note (尾插法)
 ******************************************************************************
 */
static void list_add_tail(list_head *new, list_head *head)
{
    _list_add(new, head->prev, head);
}

/**
 ******************************************************************************
 * @brief      __list_del.
 * @param[in]  @prev : 前驱节点
 *             @next : 后继节点
 * @param[out] None
 * @retval
 *
 * @details
 *             通过该节点（条目）前驱/后继节点，删除该节点
 * @note
 ******************************************************************************
 */
static void __list_del(list_head *prev, list_head *next)
{
    prev->next = next;
    next->prev = prev;
}

/**
 ******************************************************************************
 * @brief      __list_del_entry.
 * @param[in]  @entry : 需要配删除的条目（节点）
 * @param[out] None
 * @retval
 *
 * @details
 *          删除该节点条目
 * @note
 ******************************************************************************
 */
static void __list_del_entry(list_head *entry)
{
    __list_del(entry->prev, entry->next);
}
static void list_del(list_head *entry)
{
    __list_del(entry->prev, entry->next);
    entry->next = NULL; // 防止野指针!!
    entry->prev = NULL;
}

static void list_del_init(list_head *entry)
{
    __list_del_entry(entry);
    INIT_LIST_HEAD(entry);
}

/**
 ******************************************************************************
 * @brief      list_replace.
 * @param[in]  @new : 需要替换的节点条目
 *             @old : 需要被替换的节点条目
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void list_replace(list_head *old, list_head *new)
{
    new->next = old->next;
    new->prev = old->prev;
#if 1
    old->next->prev = new;
    old->prev->next = new;
#else
    new->next->prev = new;
    new->prev->next = new;
#endif
}
static void list_replace_init(list_head *old,
                              list_head *new)
{
    list_replace(old, new);
    INIT_LIST_HEAD(old);
}

/**
 ******************************************************************************
 * @brief      list_move.
 * @param[in]  @list :需要被转移的节点条目
 *             @head :被转移至该头节点的后面
 * @param[out] None
 * @retval
 *
 * @details
 *            以list_add(以前插法)方式
 * @note
 ******************************************************************************
 */
static void list_move(list_head *list, list_head *head)
{
    __list_del_entry(list);
    list_add(list, head);
}

static void list_move_tail(list_head *list,
                           list_head *head)
{
    __list_del_entry(list);
    list_add_tail(list, head);
}

/**
 ******************************************************************************
 * @brief      list_bulk_move_tail(多节点转移).
 * @param[in]  @head : 链表头节点
 *             @first: 被转移的起始节点
 *             @last : 被转移的结束节点
 * @param[out] None
 * @retval
 *
 * @details
 *          将[first,last]之间的节点转移至head节点之前
 * @note
 ******************************************************************************
 */
static void list_bulk_move_tail(list_head *head,
                                list_head *first,
                                list_head *last)
{
    first->prev->next = last->next;
    last->next->prev = first->prev;

    head->prev->next = first;
    first->prev = head->prev;

    head->prev = last;
    last->next = head;
}

/**
 ******************************************************************************
 * @brief      list_is_first.
 * @param[in]  @list : 需要判断的链表
 *             @head ：链表的头节点
 * @param[out] None
 * @retval
 *
 * @details
 *          判断该链表是否为第一节点（head.next指向的节点）
 * @note
 ******************************************************************************
 */

static uint8_t list_is_first(list_head *list, list_head *head)
{
    return (list->prev == head);
}

static uint8_t list_is_last(list_head *list, list_head *head)
{
#if 1
    return (head->prev == list);
#else
    return (list->next == head);
#endif
}

/**
 ******************************************************************************
 * @brief      list_is_empty.
 * @param[in]  @head : 链表头节点
 * @param[out] None
 * @retval
 *
 * @details
 *             头节点是否指向自己（头节点初始化）
 * @note
 ******************************************************************************
 */
static uint8_t list_is_empty(list_head *head)
{
    return (head->next == head);
}

static uint8_t list_is_empty_careful(list_head *head)
{
    list_head *next = (*head).next;
    return (next == head) && (head->prev == next);
}

/**
 ******************************************************************************
 * @brief      list_rotate_left.
 * @param[in]  @head :链表头部节点
 * @param[out] None
 * @retval
 *
 * @details
 *              将第一节点转移到最后一个节点
 * @note
 ******************************************************************************
 */
static void list_rotate_left(list_head *head)
{
    list_head *first = NULL;
    if (!list_is_empty(head))
    {
        first = head->next;
        list_move_tail(first, head);
    }
    else
    {
        printf("head is empty!!\n");
    }
}

/**
 ******************************************************************************
 * @brief      list_is_singular.
 * @param[in]  @head :
 * @param[out] None
 * @retval
 *
 * @details
 *          检测链表是否只有一个节点
 * @note
 ******************************************************************************
 */

static uint8_t list_is_singular(list_head *head)
{
    return (!list_is_empty(head)) && (head->next == head->prev);
}

/**
 ******************************************************************************
 * @brief      __list_cut_position.
 * @param[in]  @list :新的链表头节点
 *             @head :本链表的头节点
 *             @entry：被截取的节点目录[head , entry]
 * @param[out] None
 * @retval
 *
 * @details
 *          ASCII 表达
 *
 *          ->[head]<-->[A]<-->[B]<-->[C]<-->[D]<-->[E]<-->[F]<-
 *
 *          ->[list]<-->[A]<-->[B]<-->[C]<-
 *
 *          ->[head]<-->[D]<-->[E]<-->[F]<-
 *
 * @note
 ******************************************************************************
 */
static void __list_cut_position(list_head *list,
                                list_head *head,
                                list_head *entry)
{
    /*第一步很重要，entry->next在过程中节点地址发生变化，因此后续使用过程中，需要首先保存正确的节点地址*/
    list_head *newfirst = entry->next;
    list->next = head->next;
    head->next->prev = list;

    list->prev = entry;
    entry->next = list;
#if 0
    head->next = entry->next;
    entry->next->prev = head;
#else
    head->next = newfirst;
    newfirst->prev = head;
#endif
}

/**
 ******************************************************************************
 * @brief      list_cut_position.
 * @param[in]  @list :新的链表头节点
 *             @head :本链表的头节点
 *             @entry：被截取的节点目录(head , entry]
 * @param[out] None
 * @retval
 *
 * @details
 *          ①：判断@head链表是否为空
 *          ②：若链表只有一个节点，且节点中不存在@entry
 *          ③：若被截取的节点@entry是链表@head
 * @note
 ******************************************************************************
 */

static void list_cut_position(list_head *list,
                              list_head *head,
                              list_head *entry)
{
    if (list_is_empty(head))
    {
        return;
    }
    if (list_is_singular(head) && (head->next != entry) && head != entry)
    {
        return;
    }
    if (head == entry)
    {
        INIT_LIST_HEAD(list);
    }
    else
    {
        __list_cut_position(list, head, entry);
    }
}

/**
 ******************************************************************************
 * @brief      list_cut_before.
 * @param[in]  @list :新的链表头节点
 *             @head :本链表的头节点
 *             @entry：被截取的节点目录[head , entry)
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */

static void list_cut_before(list_head *list,
                            list_head *head,
                            list_head *entry)
{
    if (list_is_singular(head) && (head->next != entry) && head != entry)
    {
        return;
    }
    if (head->next == entry)
    {
        INIT_LIST_HEAD(list);
        return;
    }
    list->next = head->next;
    head->next->prev = list;

    entry->prev->next = list;
    list->prev = entry->prev;

    head->next = entry;
    entry->prev = head;
}

/**
 ******************************************************************************
 * @brief      __list_splice.
 * @param[in]  @list: 将要插入的链表
 *             @prev: 被插入链表的前驱节点
 *             @next: 被插入链表的后继节点
 * @param[out] None
 * @retval
 *
 * @details
 *          将@list链表合并到@prev和@next节点之间
 * @note
 ******************************************************************************
 */
static void __list_splice(list_head *list,
                          list_head *prev,
                          list_head *next)
{

    /*list 将在外层函数中清除初始化，此处应该使用局部变量作为临时储存*/
    list_head *first = list->next;
    list_head *last = list->prev;
    first->prev = prev;
    prev->next = first;

    last->next = next;
    next->prev = last;
}
/**
 ******************************************************************************
 * @brief      list_splice.
 * @param[in]  @list: 将要插入的链表
 *             @head: 被插入链表头节点
 * @param[out] None
 * @retval
 *
 * @details
 *              利用头插法合并链表(@list不为空)
 * @note
 ******************************************************************************
 */

static void list_splice(list_head *list,
                        list_head *head)
{
    if (!list_is_empty(list))
    {
        __list_splice(list, head, head->next);
    }
}

/**
 ******************************************************************************
 * @brief      list_splice_tail.
 * @param[in]  @list: 将要插入的链表
 *             @head: 被插入链表头节点
 * @param[out] None
 * @retval
 *
 * @details
 *              利用尾插法合并链表(@list不为空)
 * @note
 ******************************************************************************
 */

static void list_splice_tail(list_head *list,
                             list_head *head)
{
    if (!list_is_empty(list))
    {
        __list_splice(list, head->prev, head);
    }
}

/**
 ******************************************************************************
 * @brief      list_splice_init.
 * @param[in]  @list: 将要插入的链表
 *             @head: 被插入链表头节点
 * @param[out] None
 * @retval
 *
 * @details
 *               利用头插法合并链表(@list不为空)
 *               同时将@list头结点初始化
 * @note
 ******************************************************************************
 */

static void list_splice_init(list_head *list,
                             list_head *head)
{
    if (!list_is_empty(list))
    {
        list_splice(list, head);
        INIT_LIST_HEAD(list);
    }
}

/**
 ******************************************************************************
 * @brief      list_splice_init.
 * @param[in]  @list: 将要插入的链表
 *             @head: 被插入链表头节点
 * @param[out] None
 * @retval
 *
 * @details
 *               利用尾插法合并链表(@list不为空)
 *               同时将@list头结点初始化
 * @note
 ******************************************************************************
 */

static void list_splice_tail_init(list_head *list,
                                  list_head *head)
{
    if (!list_is_empty(list))
    {
        list_splice_tail(list, head);
        INIT_LIST_HEAD(list);
    }
}

/**
 ******************************************************************************
 * @brief      list_entry.
 * @param[in]  @ptr : 结构体中list_head类
 *             @type：结构体的类型
 *             @member：结构体中的成员
 * @param[out] None
 * @retval
 *
 * @details
 *          从list_head类中获取所在结构体的起始位置
 * @note
 ******************************************************************************
 */
#define list_entry(ptr, type, member) \
    container_of(ptr, type, member)

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  @ptr : 结构体中list_head类的头结点
 *             @type：结构体的类型
 *             @member：结构体中的成员
 * @param[out] None
 * @retval
 *
 * @details
 *              从头节点中获取所在结构体的起始位置
 * @note
 ******************************************************************************
 */
#define list_first_entry(ptr, type, member) \
    list_entry((ptr)->next, type, member)

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  @ptr : 结构体中list_head类的头结点
 *             @type：结构体的类型
 *             @member：结构体中的成员
 * @param[out] None
 * @retval
 *
 * @details
 *            从最后节点中获取所在结构体的起始位置
 * @note
 ******************************************************************************
 */
#define list_last_entry(ptr, type, member) \
    list_entry((ptr)->prev, type, member)

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
#define list_first_entry_or_null(ptr, type, member) ({       \
    list_head *_head = ptr;                                  \
    list_head *_pos = (_head->next);                         \
    (_head != _pos) ? list_entry(_pos, type, member) : NULL; \
})

/**
 ******************************************************************************
 * @brief      list_next_entry.
 * @param[in]  @pos : 指针类型的包含@member的结构体
 *             @member : 结构体里面的成员
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
#define list_next_entry(pos, member) \
    list_entry((pos)->member.next, typeof(*(pos)), member)

/**
 ******************************************************************************
 * @brief      list_prev_entry.
 * @param[in]  @pos : 指针类型的包含@member的结构体
 *             @member : 结构体里面的成员
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
#define list_prev_entry(pos, member) \
    list_entry((pos)->member.prev, typeof(*(pos)), member)

/**
 ******************************************************************************
 * @brief      list_for_each.
 * @param[in]  @head :链表表头
 *             @pos  ：指针类型变量
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */

#define list_for_each(pos, head) \
    for (pos = head->next; pos != head; pos = pos->next)

#define list_for_each_prev(pos, head) \
    for (pos = head->prev; pos != head; pos = pos->prev)

/**
 ******************************************************************************
 * @brief      .
 * @param[in] * @pos:    the &struct list_head to use as a loop cursor.
 *              @n:      another &struct list_head to use as temporary storage
 *              @head:   the head for your list.
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */

#define list_for_each_safe(pos, n, head)    \
    for (pos = (head)->next, n = pos->next; \
         pos != head;                       \
         pos = n, n = pos->next)

#define list_for_each_prev_safe(pos, n, head) \
    for (pos = (head)->prev, n = pos->prev;   \
         pos != head;                         \
         pos = n, n = pos->prev)

/**
 ******************************************************************************
 * @brief      list_for_each_entry.
 * @param[in]  @pos : the type * to use as a loop cursor.
 *             @head: the head for your list.
 *             @member : the name of the list_head within the struct.
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */

#define list_for_each_entry(pos, head, member)               \
    for (pos = list_first_entry(head, typeof(*pos), member); \
         &(pos->member) != (head);                           \
         pos = list_next_entry(pos, member))

#define list_for_each_entry_safe(pos, n, head, member)       \
    for (pos = list_first_entry(head, typeof(*pos), member), \
        n = list_next_entry(pos, member);                    \
         &(pos->member) != (head);                           \
         pos = n, n = list_next_entry(pos, member))

#define list_for_each_entry_reverse(pos, head, member)      \
    for (pos = list_last_entry(head, typeof(*pos), member); \
         &pos->member != (head);                            \
         pos = list_prev_entry(pos, member))

#define list_for_each_entry_continue(pos, head, member) \
    for (pos = list_next_entry(pos, member);            \
         &pos->member != (head);                        \
         pos = list_next_entry(pos, member))
/******************************Hash List Define***********************************************/
#define HLIST_HEAD_INIT {.first = NULL}

#define HLIST_HEAD(name) struct Hlist_Head name = HLIST_HEAD_INIT

#define INIT_HLIST_HEAD(ptr) ((ptr)->first = NULL)

/**
 ******************************************************************************
 * @brief      INIT_HLIST_NODE.
 * @param[in]  @h : hlist_node
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void INIT_HLIST_NODE(hlist_node *n)
{
    n->next = NULL;
    n->pprev = NULL;
}
/**
 ******************************************************************************
 * @brief      hlist_empty.
 * @param[in]  @h : hlist_head
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static int hlist_empty(hlist_head *h)
{
    return !(h->first);
}

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void _hlist_del(hlist_node *n)
{
    hlist_node *next = n->next;
    hlist_node **pprev = n->pprev;
    *pprev = next;
    if (next)
    {
        next->pprev = pprev;
    }
}

static void hlist_del(hlist_node *n)
{
    _hlist_del(n);
    n->next = NULL;
    n->pprev = NULL;
}
/**
 ******************************************************************************
 * @brief      hlist_add_head.
 * @param[in]  @h : hlist_head
 *             @n : hlist_node
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void hlist_add_head(hlist_head *h, hlist_node *n)
{
    hlist_node *first = h->first;
    n->next = first;
    if (first)
    {
        first->pprev = &n->next;
    }
    h->first = n;
    n->pprev = &h->first;
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void hlist_add_before(hlist_node *n, hlist_node *next)
{
    n->pprev = next->pprev;
    n->next = next;
    next->pprev = &n->next;
    *(n->pprev) = n;
}
/**
 ******************************************************************************
 * @brief      hlist_add_behind.
 * @param[in]  @n :需要插入的节点
 *             @prev：在该节点后插入
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void hlist_add_behind(hlist_node *n,
                             hlist_node *prev)
{
    n->next = prev->next;
    prev->next = n;
    n->pprev = &prev->next;
    if (n->next)
    {
        n->next->pprev = &n->next;
    }
}
/**
 ******************************************************************************
 * @brief      hlist_move_list.
 * @param[in]  @old ：被替换的链表头
 *             @new ：替换的链表头
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void hlist_move_list(hlist_head *old,
                            hlist_head *new)
{
    new->first = old->first;
    if (new->first)
    {
        new->first->pprev = &new->first;
    }
    old->first = NULL;
}

/***********************************plist 优先级链表***********************************************/
/**
 *  plist Structure Define
 */
typedef struct Plist_Head
{
    list_head node_list;
} plist_head;

typedef struct Plist_Node
{
    int prio;
    list_head prio_list;
    list_head node_list;
} plist_node;

#define PLIST_HEAD_INIT(head) \
    {                         \
        .node_list = LIST_HEAD_INIT(head.node_list)}

#define PLIST_HEAD(head) \
    {                    \
        plist_head head = PLIST_HEAD_INIT(head)}

#define PLIST_NODE_INIT(node, __prio)                  \
    {                                                  \
        .prio = (__prio),                              \
        .prio_list = LIST_HEAD_INIT((node).prio_list), \
        .node_list = LIST_HEAD_INIT((node).node_list), \
    }

#define plist_for_each(pos, head) \
    list_for_each_entry(pos, &(head)->node_list, node_list)

#define plist_for_each_continue(pos, head) \
    list_for_each_entry_continue(pos, &(head)->node_list, node_list)

#define plist_for_each_entry_continue(pos, head, m) \
    list_for_each_entry_continue(pos, &(head)->node_list, m.node_list)

#define plist_for_each_safe(pos, n, head) \
    list_for_each_entry_safe(pos, n, &(head)->node_list, node_list)

#define plist_for_each_entry(pos, head, mem) \
    list_for_each_entry(pos, &(head)->node_list, mem.node_list)

#define plist_for_each_entry_safe(pos, n, head, m) \
    list_for_each_entry_safe(pos, n, &(head)->node_list, m.node_list)
/**
 * plist_next - get the next entry in list
 * @pos:    the type * to cursor
 */
#define plist_next(pos) \
    list_next_entry(pos, node_list)

/**
 * plist_prev - get the prev entry in list
 * @pos:    the type * to cursor
 */
#define plist_prev(pos) \
    list_prev_entry(pos, node_list)

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void plist_head_init(plist_head *head)
{
    INIT_LIST_HEAD(&head->node_list);
}

static void plist_node_init(plist_node *node, int prio)
{
    node->prio = prio;
    INIT_LIST_HEAD(&node->node_list);
    INIT_LIST_HEAD(&node->prio_list);
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static int plist_head_empty(plist_head *head)
{
    return list_is_empty(&head->node_list);
}

static int plist_node_empty(plist_node *node)
{
    return list_is_empty(&node->node_list);
}

static void plist_check_prev_next(list_head *t, list_head *p,
                                  list_head *n)
{
    if (n->prev != p || p->next != n)
    {
        printf("top: %p, n: %p, p: %p\n"
               "prev: %p, n: %p, p: %p\n"
               "next: %p, n: %p, p: %p\n",
               t, t->next, t->prev, p, p->next, p->prev, n, n->next, n->prev);
    }
}

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static plist_node *plist_first(plist_head *head)
{
    return list_entry(head->node_list.next, plist_node, node_list);
}

static plist_node *plist_last(plist_head *head)
{
    return list_entry(head->node_list.prev, plist_node, node_list);
}

static void plist_check_list(list_head *top)
{
    list_head *prev = top, *next = top->next;

    plist_check_prev_next(top, prev, next);
    while (next != top)
    {
        prev = next;
        next = prev->next;
        plist_check_prev_next(top, prev, next);
    }
}

static void plist_check_head(plist_head *head)
{
    if (!plist_head_empty(head))
    {
        plist_check_list(&plist_first(head)->prio_list);
    }

    plist_check_list(&head->node_list);
}
#if 0
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void plist_add(plist_node *node, plist_head *head)
{
    plist_node *first, *iter, *prev = NULL;
    list_head *node_next = &head->node_list;
    if (plist_head_empty(head))
    {
        list_add_tail(&node->node_list, node_next);
        /* 当队列为空时，新节点既是主列表的唯一节点，
         * 也是它自己优先级分组的头节点。必须将它添加到
         * 自己的 prio_list 中，形成一个单元素循环链表，
         * 否则后续的 list_entry 会因 prio_list 为空而崩溃。*/
        list_add(&node->prio_list, &node->prio_list);
        return;
    }
    iter = first = plist_first(head);
    do
    {
        if (node->prio < iter->prio)
        {
            node_next = &iter->node_list;
            break;
        }
        prev = iter;
        iter = list_entry(iter->prio_list.next, plist_node, prio_list);
    } while (iter != first);
    if (!prev || prev->prio != node->prio) /**/
    {
        list_add_tail(&node->prio_list, &iter->prio_list);
    }
    list_add_tail(&node->node_list, node_next);
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void plist_del(plist_node *node, plist_head *head)
{
    if (!list_is_empty(&node->prio_list))
    {
        if (node->node_list.next != &head->node_list)
        {
            plist_node *next = list_entry(node->node_list.next, plist_node, node_list);
            if (list_is_empty(&next->prio_list))
            {
                /* 将被删除节点(node)的prio_list中的所有元素
                 * 移动到下一个节点(next)的prio_list中，
                 * 使next成为该优先级分组的新头节点。*/
                list_splice_init(&node->prio_list, &next->prio_list);
            }
        }
        list_del_init(&node->prio_list);
    }
    list_del_init(&node->node_list);
}

static void plist_requeue(plist_node *node, plist_head *head)
{
    plist_node *iter;
    list_head *node_next;

    /* 如果它已经是其优先级组的最后一个节点，则无需执行任何操作。
     * 这包括以下情况：
     * 1. 它是整个列表的最后一个节点。
     * 2. 下一个节点的优先级不同。*/
    if (node == plist_last(head))
    {
        return;
    }

    iter = list_entry(node->node_list.next, plist_node, node_list);
    if (node->prio != iter->prio)
    {
        return;
    }

    /* 从当前位置找到其优先级组的末尾，该末尾就是
     * 下一个更高优先级组的头部（或整个列表的头部）。*/
    node_next = &head->node_list; /* 默认为列表末尾 */
    plist_for_each_continue(iter, head)
    {
        if (node->prio != iter->prio)
        {
            node_next = &iter->node_list;
            break;
        }
    }

    /* 将节点移动到其优先级组的末尾（即下一个组的头部之前）。
     * 此操作仅修改 node_list，不触及 prio_list，这是正确的。*/
    list_move_tail(&node->node_list, node_next);
}
#endif

#if 0
static void plist_add(plist_node *node, plist_head *head)
{
    list_head *iter = &head->node_list;
    plist_node *last = NULL;

    /* 1. 在主列表 (node_list) 中找到正确的插入位置 */
    list_for_each_entry(last, &head->node_list, node_list)
    {
        if (node->prio < last->prio)
        { // 内核标准：prio值越小，优先级越高
            iter = &last->node_list;
            break;
        }
    }
    list_add_tail(&node->node_list, iter);

    /* 2. 维护优先级列表 (prio_list) */
    if (node->node_list.prev == &head->node_list ||
        list_entry(node->node_list.prev, plist_node, node_list)->prio != node->prio)
    {
        /* 如果新节点是新的"头节点"，则初始化它的prio_list */
        INIT_LIST_HEAD(&node->prio_list);
    }
    else
    {
        /* 否则，它只是"成员"，加入前一个节点的prio_list即可 */
        list_add_tail(&node->prio_list,
                      &list_entry(node->node_list.prev, plist_node, node_list)->prio_list);
        return;
    }

    /* 如果新节点是"头节点"，检查是否需要从下一个节点"继承"prio_list */
    if (node->node_list.next != &head->node_list &&
        list_entry(node->node_list.next, plist_node, node_list)->prio == node->prio)
    {
        list_splice_init(&list_entry(node->node_list.next, plist_node, node_list)->prio_list,
                         &node->prio_list);
    }
}
#endif
/**
 * plist_add()  ——  把 @node 按优先级升序插到 @head
 *
 * 依旧沿用原作者的变量名 + do-while 结构 + goto，只有两处改动：
 *   ①  仍旧在 prio_list 无头环里用 do-while 遍历，但绝不把
 *       head 当节点来 container_of；
 *   ②  当 @node 的 prio 是当前队列里“数值最大”时，
 *       prio_list 插到环尾 (prev 之后)，而不是环头 (first 前)。
 */
static void plist_add(plist_node *node, plist_head *head)
{
    plist_node *first, *iter, *prev = NULL;
    list_head *node_next = &head->node_list; /* node_list 插入点 */

    plist_check_head(head);

    /* 0.  断言 node 尚未入链，可按需恢复 ASSERT */
    // BUG_ON(!plist_node_empty(node));
    // BUG_ON(!list_empty(&node->prio_list));

    /* 1.  队列为空：node 成为两条链的第一个元素 */
    if (plist_head_empty(head))
    {
        INIT_LIST_HEAD(&node->prio_list); /* 自环 */
        list_add(&node->node_list, &head->node_list);
        return;
    }

    first = iter = plist_first(head); /* 环上的起点 */

    /* 2.  在 prio_list 无头环里寻找插入位置                     */
    /*     遍历顺序：first → ... → first (一圈即止)                */
    do
    {
        if (node->prio < iter->prio)
        {                                 /* 找到更高优先级 */
            node_next = &iter->node_list; /* node_list 插到它前 */
            break;
        }
        prev = iter; /* 记录最后一个 ≤node 的组头 */
        iter = list_entry(iter->prio_list.next,
                          plist_node, prio_list);
    } while (iter != first);
    /* 退出循环后：
     *   - node_next 指向“第一条比 node->prio 大”的节点之前，
     *     若没找到则仍是 head->node_list —— 等价于尾插。
     *   - prev 指向链中最后一个 ≤ node->prio 的组头，
     *     若 node->prio 最小则 prev 仍为 NULL。
     */

    /* 3.  决定是否需要把 node 放进 prio_list 环                  */
    if (!prev || prev->prio != node->prio)
    {
        /*
         * 需要新建组头。
         *   prev != NULL  → 插到 prev 后面        (常规／最低优先级)
         *   prev == NULL  → 插到 first 前面       (最高优先级)
         */
        if (prev)
            list_add(&node->prio_list, &prev->prio_list);
        else
            list_add_tail(&node->prio_list, &first->prio_list);
    }
    else
    {
        INIT_LIST_HEAD(&node->prio_list); /* 同优先级：保持空环 */
    }

ins_node:
    /* 4.  最后插进 node_list，保持整体升序 + FIFO */
    list_add_tail(&node->node_list, node_next);

    plist_check_head(head);
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
#if 0
static void plist_del(plist_node *node, plist_head *head)
{
    if (list_is_empty(&node->node_list))
        return;

    /* 如果被删除的节点是"头节点"，则将"头节点"身份传给下一个同优先级节点 */
    if (!list_is_empty(&node->prio_list))
    {
        list_head *next_node = node->node_list.next;

        if (next_node != &head->node_list &&
            list_entry(next_node, plist_node, node_list)->prio == node->prio)
        {
            list_splice_init(&node->prio_list,
                             &list_entry(next_node, plist_node, node_list)->prio_list);
        }
    }

    list_del_init(&node->node_list);
    INIT_LIST_HEAD(&node->prio_list);
}
#endif
static void plist_del(plist_node *node, plist_head *head)
{
#ifdef PLIST_DEBUG
    plist_check_head(head);
    configASSERT(!plist_head_empty(head));
    configASSERT(!list_empty(&node->node_list));
#endif

    /* If the node to be deleted is a priority group head... */
    if (!list_is_empty(&node->prio_list))
    {
        list_head *next = node->node_list.next;

        /* ...and it's not the last node in the entire list... */
        if (next != &head->node_list)
        {
            plist_node *next_node;
            next_node = list_entry(next, plist_node, node_list);

            /* ...and the next node has the SAME priority, promote it. */
            if (next_node->prio == node->prio)
            {
                /* Add the next node to the prio_list right where the old one is. */
                list_add(&next_node->prio_list, &node->prio_list);
            }
        }
        /* Always remove the old head from the prio_list. */
        list_del_init(&node->prio_list);
    }

    /* Finally, always remove the node from the main node_list. */
    list_del_init(&node->node_list);

#ifdef PLIST_DEBUG
    plist_check_head(head);
#endif
}
static void plist_requeue(plist_node *node, plist_head *head)
{
    plist_node *iter;
    list_head *node_next;

    /* 如果节点已经是其优先级组的最后一个，则无需移动 */
    iter = list_entry(node->node_list.next, plist_node, node_list);

    if (node->node_list.next == &head->node_list || iter->prio != node->prio)
        return;

    /* 高效移动到组的末尾 */
    node_next = &head->node_list;
    list_for_each_entry_continue(iter, &head->node_list, node_list)
    {
        if (iter->prio != node->prio)
        {
            node_next = &iter->node_list;
            break;
        }
    }

    list_move_tail(&node->node_list, node_next);
}
#endif /* LIST_H_ */
