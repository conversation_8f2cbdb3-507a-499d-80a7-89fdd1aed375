/**
 ******************************************************************************
 * @file      pcuCtrl.c
 * @brief     C Source file of pcuCtrl.c.
 * @details   This file including all API functions's
 *            implement of pcuCtrl.c.
 *
 * @copy      Copyright(C), 2008-2020,Sanxing Smart Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
/* NONE */
#include "pcuCtrl.h"
#include <ccu\lib\ccuCanFrame.h>
#include "pcuMain.h"
#include <trace.h>
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
/* NONE */
static ccu_to_pcu_t s_ccuToPcu = {0};
static pcu_to_ccu_t s_pcuToCcu = {0};
static frame_type_t s_frameType = {0};
static pcu_ctrl_status_t s_PcuStatus = {0};

/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/* NONE */
// static frame_state_t   send_pcu_frame_state_t[] =
//{
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
//     {FALSE,FALSE,FALSE,1,0xffff,0},
// };
//
// const frame_cfg_t   send_pcu_frame_tab_t[] =
//{
//     { TRUE,Pgn_Ccu_Yk_Cmd,      6, 8,FALSE, TRUE,   250,NULL,&send_pcu_frame_state_t[0], &s_frameType,&s_ccuToPcu.ykCmdPcu          },    /**<遥控命令帧配置*/
//     { TRUE,Pgn_Ccu_Yx,          6, 8,FALSE, TRUE,  1000,NULL,&send_pcu_frame_state_t[1], &s_frameType,&s_ccuToPcu.remoteSignal      },    /**<遥信帧 配置*/
//     { TRUE,Pgn_Ccu_Yc,          6, 8,FALSE, TRUE,  1000,NULL,&send_pcu_frame_state_t[2], &s_frameType,&s_ccuToPcu.remoteMeter       },    /**<遥测帧配置*/
//     { TRUE,Pgn_Ccu_Heart,       6, 8,FALSE,FALSE,  2000,NULL,&send_pcu_frame_state_t[3], &s_frameType,&s_ccuToPcu.heart             },    /**<心跳帧配置*/
//     {FALSE,Pgn_Ccu_Up_Heart,    4, 8,FALSE,FALSE,  1000,NULL,&send_pcu_frame_state_t[4], &s_frameType,&s_ccuToPcu.upIssue.heart     },    /**<升级心跳帧配置*/
//     {FALSE,Pgn_Ccu_Up_Start,    4, 8,FALSE,FALSE,   500,NULL,&send_pcu_frame_state_t[5], &s_frameType,&s_ccuToPcu.upIssue.start     },    /**<启动下载帧配置*/
//     {FALSE,Pgn_Ccu_Up_Deinter,  4, 8,FALSE,FALSE,   500,NULL,&send_pcu_frame_state_t[6], &s_frameType,&s_ccuToPcu.upIssue.deInter   },    /**<索要区间帧配置*/
//     {FALSE,Pgn_Ccy_Up_Sgp,      4, 8,FALSE,FALSE,   500,NULL,&send_pcu_frame_state_t[7], &s_frameType,&s_ccuToPcu.upIssue.startGp   },    /**<启动组包帧配置*/
//     {FALSE,Pgn_Ccu_Up_Send_Dat, 4, 8,FALSE,FALSE,    10,NULL,&send_pcu_frame_state_t[8], &s_frameType,&s_ccuToPcu.upIssue.sendDat   },    /**<数据发送帧配置*/
//     {FALSE,Pgn_Ccu_Up_Egp,      4, 8,FALSE,FALSE,   500,NULL,&send_pcu_frame_state_t[9], &s_frameType,&s_ccuToPcu.upIssue.endGp     },    /**<完成组包命令帧配置*/
//     {FALSE,Pgn_Ccu_Up_Check,    4, 8,FALSE,FALSE,   500,NULL,&send_pcu_frame_state_t[10],&s_frameType,&s_ccuToPcu.upIssue.allCheck  },    /**<程序校验帧配置*/
//     {FALSE,Pgn_Ccu_Up_Reset,    4, 8,FALSE,FALSE,   500,NULL,&send_pcu_frame_state_t[11],&s_frameType,&s_ccuToPcu.upIssue.immedReset},    /**<立即复位帧配置*/
//     {FALSE,Pgn_Ccu_Fix_Set,     6,32, TRUE,FALSE,0xffff,NULL,&send_pcu_frame_state_t[12],&s_frameType,&s_ccuToPcu.fixSet            },    /**<定值设定帧配置*/
//     {FALSE,Pgn_Ccu_Fix_Query,   6,32, TRUE,FALSE,0xffff,NULL,&send_pcu_frame_state_t[13],&s_frameType,&s_ccuToPcu.fixQuery          },    /**<定值查询帧配置*/
//     {FALSE,Pgn_Ccu_Debug,       6, 8, TRUE,FALSE,0xffff,NULL,&send_pcu_frame_state_t[14], &s_frameType,&s_ccuToPcu.debug            },    /**<调试帧*/
// };
//
//
// static frame_state_t   rec_pcu_frame_state_t[] =
//{
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
//     {FALSE,FALSE,FALSE,1,FRAME_NOT_TIMEOUT,0},
// };
//
// const frame_cfg_t   rec_pcu_frame_tab_t[] =
//{
//     { TRUE,Pgn_Ccu_Yk_Ack,          6, 8,FALSE, TRUE, 0xffff,NULL, &rec_pcu_frame_state_t[0],&s_frameType,&s_pcuToCcu.ykCmdPcuAck        },    /**<遥控命令帧配置  */
//     { TRUE,Pgn_Pcu_Ws_Err,          6, 8,FALSE, TRUE,   1000,NULL, &rec_pcu_frame_state_t[1],&s_frameType,&s_pcuToCcu.modStatusErr       },    /**<工作状态及故障信息帧 配置*/
//     { TRUE,Pgn_Pcu_Yc,              6, 8,FALSE, TRUE,   2000,NULL, &rec_pcu_frame_state_t[2],&s_frameType,&s_pcuToCcu.remoteMeter        },    /**<遥测帧 功率控制模块到充电主控模块配置 */
//     { TRUE,Pgn_Pcu_Alarm,           6, 8,FALSE, TRUE,   1000,NULL, &rec_pcu_frame_state_t[3],&s_frameType,&s_pcuToCcu.pcuAlarm           },    /**<告警信息帧  功率控制模块到充电主控模块配置 */
//     { TRUE,Pgn_Pcu_Heart,           6, 8,FALSE,FALSE,   2000,NULL, &rec_pcu_frame_state_t[4],&s_frameType,&s_pcuToCcu.heart              },    /**<心跳帧配置*/
//     {FALSE,Pgn_Pcu_Up_Heart,        4, 8,FALSE,FALSE,   1000,NULL, &rec_pcu_frame_state_t[5],&s_frameType,&s_pcuToCcu.upGoup.heart       },    /**<升级心跳帧配置*/
//     {FALSE,Pgn_Pcu_Up_Start_Ack,    4, 8,FALSE,FALSE,    500,NULL, &rec_pcu_frame_state_t[6],&s_frameType,&s_pcuToCcu.upGoup.startGpAck  },    /**<启动下载应答帧配置*/
//     {FALSE,Pgn_Pcu_Up_Deinter_Ack1, 4, 8,FALSE,FALSE,    500,NULL, &rec_pcu_frame_state_t[7],&s_frameType,&s_pcuToCcu.upGoup.deInter1Ack },    /**<索要区间应答帧1配置*/
//     {FALSE,Pgn_Pcu_Up_Deinter_Ack2, 4, 8,FALSE,FALSE,    500,NULL, &rec_pcu_frame_state_t[8],&s_frameType,&s_pcuToCcu.upGoup.deInter2Ack },    /**<索要区间应答帧2配置*/
//     {FALSE,Pgn_Pcu_Up_Sgp_Ack,      4, 8,FALSE,FALSE,    500,NULL, &rec_pcu_frame_state_t[9],&s_frameType,&s_pcuToCcu.upGoup.startGpAck  },    /**<启动组包应答帧配置*/
//     {FALSE,Pgn_Pcu_Up_Egp_Ack,      4, 8,FALSE,FALSE,    500,NULL,&rec_pcu_frame_state_t[10],&s_frameType,&s_pcuToCcu.upGoup.endGpAck    },    /**<完成组包命令应答帧配置*/
//     {FALSE,Pgn_Pcu_Up_Check_Ack,    4, 8,FALSE,FALSE,    500,NULL,&rec_pcu_frame_state_t[11],&s_frameType,&s_pcuToCcu.upGoup.allCheckAck },    /**<程序校验应答帧配置*/
//     {FALSE,Pgn_Pcu_Up_Reset_Ack,    4, 8,FALSE,FALSE,    500,NULL,&rec_pcu_frame_state_t[12],&s_frameType,&s_pcuToCcu.upGoup.immedResetAck},    /**<立即复位应答帧配置*/
//     {FALSE,Pgn_Pcu_Fix_Set_Ack,     6,32, TRUE,FALSE, 0xffff,NULL,&rec_pcu_frame_state_t[13],&s_frameType,&s_pcuToCcu.fixSetAck           },    /**<定值设定应答帧配置*/
//     {FALSE,Pgn_Pcu_Fix_Query_Ack,   6,32, TRUE,FALSE, 0xffff,NULL,&rec_pcu_frame_state_t[14],&s_frameType,&s_pcuToCcu.fixQueryAck         },    /**<定值查询应答帧配置*/
//     {FALSE,Pgn_Pcu_Debug,           6, 8, TRUE,FALSE, 0xffff,NULL,&rec_pcu_frame_state_t[15],&s_frameType,&s_pcuToCcu.debug              },    /**<调试帧*/
// };
/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/**
 ******************************************************************************
 * @brief       chargeStartIsOn
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        是否启动充电
 *
 ******************************************************************************
 */
static void setPcuCtrlState(uint8 state)
{
    s_PcuStatus.state = state;
}
/**
 ******************************************************************************
 * @brief       chargeStartIsOn
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        是否启动充电
 *
 ******************************************************************************
 */
static bool_e chargeStartIsOn(pcu_ctrl_status_t *pPcuStatus)
{
    if (pPcuStatus == NULL)
        return FALSE;
    if (eActFlag_On == Get_ChargeActFlag())
    {

        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

/**
 ******************************************************************************
 * @brief       chargeStartIsOn
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        是否启动充电
 *
 ******************************************************************************
 */
static void dealPcuStateFree(pcu_ctrl_status_t *pPcuStatus)
{
    //    pPcuStatus->state = ePcuCtrlStage_QStart;
    pPcuStatus->run_tick = tickGet();
}

/**
 ******************************************************************************
 * @brief       modelStateIsOk
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        判断模块状态
 *
 ******************************************************************************
 */
static bool_e modelStateIsOk(pcu_ctrl_status_t *pPcuStatus)
{
    if (pPcuStatus == NULL)
        return FALSE;
}
/**
 ******************************************************************************
 * @brief       pcuStateTable
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        pcu状态管理表
 *
 ******************************************************************************
 */
pcu_state_mange_t pcuStateTable[] =
    {
        {ePcuCtrlStage_Free, {chargeStartIsOn, NULL, NULL}, dealPcuStateFree},
        //    {ePcuCtrlStage_QStart,{modelStateIsOk,NULL,NULL},NULL},
        {ePcuCtrlStage_ImdAdjPara, {NULL, NULL, NULL}, NULL},
        {ePcuCtrlStage_MidStop, {NULL, NULL, NULL}, NULL},
        {ePcuCtrlStage_SStart, {NULL, NULL, NULL}, NULL},
        {ePcuCtrlStage_AdjPara, {NULL, NULL, NULL}, NULL},
        {ePcuCtrlStage_Pause, {NULL, NULL, NULL}, NULL},
        {ePcuCtrlStage_Stop, {NULL, NULL, NULL}, NULL},
};

/**
 ******************************************************************************
 * @brief       pcuStateManage
 * @param[in]   pgn参数表，piro 优先级，sAdr原地址，dAdr目的地址
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        过程管理函数
 *
 ******************************************************************************
 */

uint32 getCanFrameId(uint8 pgn, uint8 piro, uint8 sAdr, uint8 dAdr)
{
    uint32 retId = sAdr | (dAdr << 8) | (pgn << 16) | (piro << 21);
    return retId;
}

/**
 ******************************************************************************
 * @brief       recPcuFrameData
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        接受帧数据处理函数
 *
 ******************************************************************************
 */

static uint8 recFrameData(frame_cfg_t *pFramecfg, uint8 *pBuf)
{
    if (pFramecfg->mulFrameFlag)
    {
        return recvPcuMulFrameDat(pFramecfg, pBuf);
    }
    else
    {
        memcpy(pFramecfg->frameType->singleFrame.dat, pBuf, 8);
        return TRUE;
    }
}

/**
 ******************************************************************************
 * @brief       recPcuFrameDataManage
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        接受帧数据处理函数
 *
 ******************************************************************************
 */

static void recPcuFrameDataManage(CAN_DATA *pCanData)
{
    //    const frame_cfg_t* pPcuRecv;
    //    for (int i = 0; i < FCNT(rec_pcu_frame_tab_t); i++)
    //    {
    //        pPcuRecv = &rec_pcu_frame_tab_t[i];
    //        if((pPcuRecv->en)&&(pPcuRecv->state->act))              /**<使能该帧接受，并打开了帧接受功能*/
    //        {
    //            if (pCanData->canId ==
    //                    getCanFrameId(pPcuRecv->pgn,
    //                            pPcuRecv->piro, Get_CcuAddr(),Get_PcuAddr()))                   /**<接受帧中原地址是PCU地址，目的地址是CCU地址*/
    //            {
    //                pPcuRecv->state->frameFinish = recFrameData(pPcuRecv,pCanData->dataBuf);
    //                if(pPcuRecv->state->frameFinish)
    //                {
    //                    pPcuRecv->state->tick = tickGet();
    //                    if(NULL != pPcuRecv->runfunc)
    //                    {
    //                        pPcuRecv->runfunc(pPcuRecv->frameType);
    //                    }
    ////                    putPgnDat(pPcuRecv);
    ////                    clearframeTypeDat(pPcuRecv);    /**<清除暂存数据*/
    //                    pPcuRecv->state->frameFinish = FALSE;
    //                }
    //            }
    //        }
    //    }
}

/**
 ******************************************************************************
 * @brief       recPcuFrameTimeoutManage
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        接受帧超时管理
 *
 ******************************************************************************
 */

static void recPcuFrameTimeoutManage(void)
{
    //    const frame_cfg_t *pPcuRecv = NULL;
    //    for (int i = 0; i < FCNT(rec_pcu_frame_tab_t); i++)
    //    {
    //        pPcuRecv = &rec_pcu_frame_tab_t[i];
    //        if((pPcuRecv->en)&&(pPcuRecv->state->act))              /**<使能该帧接受，并打开了帧接受功能*/
    //        {
    //            if(FRAME_NOT_TIMEOUT != pPcuRecv->state->timeOut)                 /**<为0xffff时表示不作超时判断*/
    //            {
    //                if (abs(tickGet() - pPcuRecv->state->tick) > pPcuRecv->state->timeOut)
    //                {
    //                    if(FALSE==pPcuRecv->state->timeOut)
    //                    {
    //                        pPcuRecv->state->timeOut = TRUE;
    //                        trace(TR_DEBUG, "PCU帧超时 pgn = %x,\n",pPcuRecv->pgn);
    //                    }
    //                }
    ////                else
    ////                {
    ////                    if(pPcuRecv->state->flag_u.timeOut)
    ////                    {
    ////                        pPcuRecv->state->flag_u.timeOut = FALSE;
    ////                        trace(TR_DEBUG, "PCU帧恢复 通讯 pgn = %x,\n",pPcuRecv->pgn);
    ////                    }
    ////                }
    //            }
    //        }
    //    }
}

/**
 ******************************************************************************
 * @brief       recPcuFrameData
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        接受帧数据处理函数
 *
 ******************************************************************************
 */

static uint8 sendFrameData(frame_cfg_t *pFramecfg, uint8 *pBuf)
{
    if (pFramecfg->mulFrameFlag)
    {
        return recvPcuMulFrameDat(pFramecfg, pBuf);
    }
    else
    {
        memcpy(pBuf, pFramecfg->frameType->singleFrame.dat, 8);
        return TRUE;
    }
}

/**
 ******************************************************************************
 * @brief       sendPcuFrameDataManage
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        发送帧数据处理函数
 *
 ******************************************************************************
 */

static void sendPcuFrameDataManage(void)
{
    //    uint8 sendFlag = FALSE;
    //    uint8 sendBuf[8] = { 0 };
    //    static uint32 laskSendTick = 0;
    //    const frame_cfg_t* pPcuSend;
    //    if(abs(tickGet()-laskSendTick) < 5)        /**为保证串口的正常发送，将帧的发送间隔定在5ms以上*/
    //    {
    //        return;
    //    }
    //    bzero(sendBuf, sizeof(sendBuf));
    //    for (int i = 0; i < FCNT(send_pcu_frame_tab_t); i++)
    //    {
    //        pPcuSend = &send_pcu_frame_tab_t[i];
    //        if((pPcuSend->en)&&(pPcuSend->state->act))              /**<使能该帧发送，并打开了帧发送功能*/
    //        {
    //            if ((FALSE == pPcuSend->state->frameFinish)
    //                    && (abs(tickGet() - pPcuSend->state->tick) >= 10))
    //            {
    //                if(NULL != pPcuSend->runfunc)
    //                {
    //                    pPcuSend->runfunc(pPcuSend->frameType);
    //                }
    //                pPcuSend->state->frameFinish = sendFrameData(pPcuSend,sendBuf);
    //                sendFlag = TRUE;
    //            }
    //            else if ((TRUE == pPcuSend->state->frameFinish)
    //                    && ((abs(tickGet() - pPcuSend->state->tick)
    //                            >= pPcuSend->state->cyc)
    //                            || (pPcuSend->state->immed == TRUE)))
    //            {
    //                pPcuSend->state->frameFinish = sendFrameData(pPcuSend,sendBuf);
    //                pPcuSend->state->immed = FALSE;
    //                sendFlag = TRUE;
    //            }
    //            if(sendFlag)
    //            {
    //                laskSendTick = tickGet();
    //                pPcuSend->state->tick = laskSendTick;
    //                can_trans(PCU_CHAN,getCanFrameId(pPcuSend->pgn,
    //                        pPcuSend->piro,Get_PcuAddr(), Get_CcuAddr()), sendBuf, 8);
    //
    //            }
    //        }
    //    }
}

/**
 ******************************************************************************
 * @brief       pcuStateManage
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        过程管理函数
 *
 ******************************************************************************
 */
static void pcuStateManage(void)
{
    pcu_state_mange_t *pStateManage = NULL;
    pConditionFunc pFunc = NULL;
    pcu_ctrl_status_t *pPcuStatus = &s_PcuStatus;
    bool_e ret = TRUE;

    for (uint8 i = 0; i < FCNT(pcuStateTable); i++)
    {
        pStateManage = &pcuStateTable[i];
        ret = TRUE;
        if (pPcuStatus->state != pStateManage->state)
        {
            continue;
        }
        for (uint8 j = 0;
             (j < FCNT(pStateManage->conditionFunc) && (TRUE == ret)); j++)
        {
            pFunc = pStateManage->conditionFunc[j];

            if (NULL == pFunc)
            {
                continue;
            }
            ret = pFunc(pPcuStatus);
        }
        if (TRUE == ret)
        {
            pStateManage->executionFunc(pPcuStatus);
        }
    }
    return;
}

/*----------------------------pcuCtrl.c--------------------------------*/
