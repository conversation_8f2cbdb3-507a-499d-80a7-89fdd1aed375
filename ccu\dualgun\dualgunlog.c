/**
 * @file dualgun_log.c
 * @brief 双枪充电增强日志系统实现
 */

#include "dualgun.h"
#include <shell.h>   // 假设这是注册Shell命令需要的头文件
#include <tasklib.h> // 假设这是获取tick的头文件

//==============================================================================
// 全局变量定义
//==============================================================================

// 当前允许的最大日志级别 (0-4)
DualgunLogLevel g_dualgun_log_level = LOG_LEVEL_DEBUG; // 默认INFO级别

// 节流控制 - 每种日志类型的最小间隔时间(ms), 0表示不限制
DualgunThrottleConfig g_log_throttle = {
    .error_interval = 0,      // 错误始终输出
    .warn_interval = 1000,    // 警告至少1秒间隔
    .info_interval = 2000,    // 信息至少2秒间隔
    .debug_interval = 5000,   // 调试至少5秒间隔
    .trace_interval = 10000,  // 跟踪至少10秒间隔
    .voltage_interval = 3000, // 电压至少3秒间隔
    .current_interval = 3000, // 电流至少3秒间隔
    .state_interval = 0,      // 状态变化始终输出
    .can_interval = 1000      // 新增CAN日志间隔
};

// 上次打印时间记录
DualgunLogTime g_last_log_time = {0};

// 上次输出的值记录 - 用于变化检测
DualgunLoggedValue g_last_logged_value = {{0}, {0}};

// 变化阈值设置
DualgunChangeThreshold g_change_threshold = {
    .voltage_threshold = 50, // 默认5V变化阈值
    .current_threshold = 100 // 默认1A变化阈值
};

// 日志级别前缀和颜色映射
const char *LOG_LEVEL_PREFIX[] = {
    LOG_COLOR_RED "[ERROR] ",
    LOG_COLOR_YELLOW "[WARN]  ",
    LOG_COLOR_GREEN "[INFO]  ",
    LOG_COLOR_CYAN "[DEBUG] ",
    LOG_COLOR_WHITE "[TRACE] "};

//==============================================================================
// 函数实现
//==============================================================================

/**
 * @brief 获取当前系统tick (毫秒)
 * @return 当前系统时间(毫秒)
 */
uint32 dualgun_tickGet(void)
{
    // 根据实际系统返回毫秒级别的tick
    // 这里假设使用系统的tickGet函数
    return tickGet();
}

/**
 * @brief 内部日志处理函数
 * @param level 日志级别
 * @param channel 通道号
 * @param throttle_time 节流时间(ms)
 * @param last_time 上次输出时间指针
 * @return 是否应该输出日志
 */
bool dualgun_should_log(DualgunLogLevel level, uint8 channel, uint32 throttle_time, uint32 *last_time)
{
    // 级别过滤
    if (level > g_dualgun_log_level)
    {
        return false;
    }

    // 节流控制 - 检查是否满足间隔条件
    uint32 current_tick = dualgun_tickGet();
    if (throttle_time > 0 && (current_tick - *last_time) < throttle_time)
    {
        return false;
    }

    // 更新时间戳
    *last_time = current_tick;
    return true;
}
/**
 * @brief 检查CAN帧内容是否变化
 * @param canId CAN标识符
 * @param data 数据内容
 * @param dataLen 数据长度
 * @return 如果内容变化或需要周期性打印返回true，否则返回false
 */
bool is_can_frame_changed(uint32 canId, uint8 *data, uint8 dataLen)
{
    int i;
    bool found = false;
    int oldest_idx = 0;
    uint32 oldest_time = 0xFFFFFFFF;
    uint32 now = dualgun_tickGet();

    // 查找缓存中的匹配项
    for (i = 0; i < MAX_CAN_ID_CACHE; i++)
    {
        // 找到空槽位记录
        if (g_last_logged_value.canCache[i].canId == 0)
        {
            found = true;
            break;
        }

        // 找到匹配的CAN ID
        if (g_last_logged_value.canCache[i].canId == canId)
        {
            found = true;
            break;
        }

        // 追踪最旧的条目(用于缓存满时替换)
        if (g_last_logged_value.canCache[i].lastPrintTime < oldest_time)
        {
            oldest_time = g_last_logged_value.canCache[i].lastPrintTime;
            oldest_idx = i;
        }
    }

    // 如果缓存已满，替换最旧的条目
    if (!found)
    {
        i = oldest_idx;
    }

    // 检查内容是否变化
    bool content_changed = false;

    // 新的CAN ID
    if (g_last_logged_value.canCache[i].canId != canId)
    {
        content_changed = true;
    }
    // 数据长度变化
    else if (g_last_logged_value.canCache[i].dataLen != dataLen)
    {
        content_changed = true;
    }
    // 数据内容变化
    else
    {
        for (int j = 0; j < dataLen; j++)
        {
            if (g_last_logged_value.canCache[i].data[j] != data[j])
            {
                content_changed = true;
                break;
            }
        }
    }

    // 周期性打印检查 - 即使内容相同，也每10秒打印一次
    bool timeout_print = (now - g_last_logged_value.canCache[i].lastPrintTime) > 10000;

    // 节流检查 - 如果内容相同且未到周期性打印时间，检查是否符合节流间隔
    bool throttle_pass = dualgun_should_log(LOG_LEVEL_INFO, 0, g_log_throttle.can_interval, &g_last_log_time.can_time);

    // 更新缓存
    if (content_changed || timeout_print || (g_last_logged_value.canCache[i].canId == 0 && throttle_pass))
    {
        uint32 old_repeat_count = 0;

        // 如果是周期性打印且内容没变化，保留重复计数用于显示
        if (timeout_print && !content_changed)
        {
            old_repeat_count = g_last_logged_value.canCache[i].repeatCount;
        }

        // 如果是新ID或内容变化，初始化缓存
        if (g_last_logged_value.canCache[i].canId != canId || content_changed)
        {
            g_last_logged_value.canCache[i].canId = canId;
            if (dataLen > 0 && data != NULL)
            {
                memcpy(g_last_logged_value.canCache[i].data, data, dataLen);
            }
            g_last_logged_value.canCache[i].dataLen = dataLen;
            g_last_logged_value.canCache[i].repeatCount = 0;
        }
        else
        {
            // 内容相同但周期性打印，保留重复计数并重置
            g_last_logged_value.canCache[i].repeatCount = old_repeat_count;
        }

        g_last_logged_value.canCache[i].lastPrintTime = now;
        return true;
    }
    else
    {
        // 内容相同，增加重复计数
        g_last_logged_value.canCache[i].repeatCount++;
        return false;
    }
}

/**
 * @brief 显示CAN日志统计信息
 */
void dualgun_show_can_stats(void)
{
    double log_rate = 0.0;
    if (g_last_logged_value.can_total_count > 0)
    {
        log_rate = (double)g_last_logged_value.can_logged_count * 100.0 / g_last_logged_value.can_total_count;
    }

    printf("%sCAN日志统计: 总消息:%d 已记录:%d 重复次数 (只记录变化内容)%s\n",
           LOG_COLOR_CYAN,
           g_last_logged_value.can_total_count,
           g_last_logged_value.can_logged_count,
           LOG_COLOR_RESET);

    // 输出当前缓存内容统计
    int active_ids = 0;
    for (int i = 0; i < MAX_CAN_ID_CACHE; i++)
    {
        if (g_last_logged_value.canCache[i].canId != 0)
        {
            active_ids++;
        }
    }

    printf("%s当前跟踪CAN ID数量: %d/%d%s\n",
           LOG_COLOR_CYAN,
           active_ids,
           MAX_CAN_ID_CACHE,
           LOG_COLOR_RESET);
}
/**
 * @brief 设置日志级别
 * @param level 0-ERROR, 1-WARN, 2-INFO, 3-DEBUG, 4-TRACE
 */
void dualgun_set_log_level(int level)
{
    if (level >= LOG_LEVEL_ERROR && level <= LOG_LEVEL_TRACE)
    {
        g_dualgun_log_level = (DualgunLogLevel)level;

        // 使用彩色输出显示当前设置
        printf("%s当前日志级别设置为: %d (%s)%s\n",
               LOG_COLOR_GREEN,
               level,
               (level == 0) ? "ERROR" : (level == 1) ? "WARN"
                                    : (level == 2)   ? "INFO"
                                    : (level == 3)   ? "DEBUG"
                                                     : "TRACE",
               LOG_COLOR_RESET);
    }
    else
    {
        printf("%s无效日志级别! 有效值: 0(ERROR), 1(WARN), 2(INFO), 3(DEBUG), 4(TRACE)%s\n",
               LOG_COLOR_RED, LOG_COLOR_RESET);
    }
}
SHELL_CMD(
    duallvl, 2, dualgun_set_log_level,
    "duallvl <level>\r\n"
    "\t设置日志级别: 0-ERROR, 1-WARN, 2-INFO, 3-DEBUG, 4-TRACE\r\n");
/**
 * @brief 设置节流间隔
 * @param type 0-error, 1-warn, 2-info, 3-debug, 4-trace, 5-voltage, 6-current, 7-state, 8-can
 * @param ms 间隔时间(毫秒), 0表示不限制
 */
void dualgun_set_throttle(int type, int ms)
{
    if (ms < 0)
        ms = 0;

    switch (type)
    {
    case 0:
        g_log_throttle.error_interval = ms;
        break;
    case 1:
        g_log_throttle.warn_interval = ms;
        break;
    case 2:
        g_log_throttle.info_interval = ms;
        break;
    case 3:
        g_log_throttle.debug_interval = ms;
        break;
    case 4:
        g_log_throttle.trace_interval = ms;
        break;
    case 5:
        g_log_throttle.voltage_interval = ms;
        break;
    case 6:
        g_log_throttle.current_interval = ms;
        break;
    case 7:
        g_log_throttle.state_interval = ms;
        break;
    case 8:
        g_log_throttle.can_interval = ms;
        break;
    default:
        printf("%s无效的节流类型! 有效值: 0(ERROR), 1(WARN), 2(INFO), 3(DEBUG), 4(TRACE), 5(VOLTAGE), 6(CURRENT), 7(STATE), 8(CAN)%s\n",
               LOG_COLOR_RED, LOG_COLOR_RESET);
        return;
    }

    printf("%s设置节流间隔: 类型=%d, 间隔=%dms%s\n",
           LOG_COLOR_GREEN, type, ms, LOG_COLOR_RESET);
}
SHELL_CMD(
    dualthrt, 3, dualgun_set_throttle,
    "dualthrt <type> <ms>\r\n"
    "\t设置节流间隔: type(0-8), ms(毫秒)\r\n"
    "\t类型: 0-ERROR, 1-WARN, 2-INFO, 3-DEBUG, 4-TRACE, 5-VOLTAGE, 6-CURRENT, 7-STATE, 8-CAN\r\n");
/**
 * @brief 设置变化阈值
 * @param type 0-voltage, 1-current
 * @param value 阈值值
 */
void dualgun_set_threshold(int type, int value)
{
    if (value < 0)
        value = 0;

    switch (type)
    {
    case 0:
        g_change_threshold.voltage_threshold = value;
        break;
    case 1:
        g_change_threshold.current_threshold = value;
        break;
    default:
        printf("%s无效的阈值类型! 有效值: 0(VOLTAGE), 1(CURRENT)%s\n",
               LOG_COLOR_RED, LOG_COLOR_RESET);
        return;
    }

    printf("%s设置变化阈值: 类型=%d, 阈值=%d%s\n",
           LOG_COLOR_GREEN, type, value, LOG_COLOR_RESET);
}
SHELL_CMD(
    dualthr, 3, dualgun_set_threshold,
    "dualthr <type> <value>\r\n"
    "\t设置变化阈值: type(0-1), value\r\n"
    "\t类型: 0-VOLTAGE(0.1V), 1-CURRENT(0.01A)\r\n");
/**
 * @brief 显示当前日志设置
 */
void dualgun_show_log_config(void)
{
    printf("%s============ 双枪充电日志配置 ============%s\n", LOG_COLOR_CYAN, LOG_COLOR_RESET);
    printf("日志级别: %d (%s)\n",
           g_dualgun_log_level,
           (g_dualgun_log_level == 0) ? "ERROR" : (g_dualgun_log_level == 1) ? "WARN"
                                              : (g_dualgun_log_level == 2)   ? "INFO"
                                              : (g_dualgun_log_level == 3)   ? "DEBUG"
                                                                             : "TRACE");

    printf("\n%s==== 节流间隔(ms) ====%s\n", LOG_COLOR_YELLOW, LOG_COLOR_RESET);
    printf("ERROR: %d\n", g_log_throttle.error_interval);
    printf("WARN:  %d\n", g_log_throttle.warn_interval);
    printf("INFO:  %d\n", g_log_throttle.info_interval);
    printf("DEBUG: %d\n", g_log_throttle.debug_interval);
    printf("TRACE: %d\n", g_log_throttle.trace_interval);
    printf("电压:  %d\n", g_log_throttle.voltage_interval);
    printf("电流:  %d\n", g_log_throttle.current_interval);
    printf("状态:  %d\n", g_log_throttle.state_interval);
    printf("CAN:   %d\n", g_log_throttle.can_interval);

    printf("\n%s==== 变化阈值 ====%s\n", LOG_COLOR_GREEN, LOG_COLOR_RESET);
    printf("电压: %d (0.1V)\n", g_change_threshold.voltage_threshold);
    printf("电流: %d (0.01A)\n", g_change_threshold.current_threshold);
    printf("%s=========================================%s\n", LOG_COLOR_CYAN, LOG_COLOR_RESET);
}

SHELL_CMD(
    dualcfg, 1, dualgun_show_log_config,
    "dualcfg\r\n"
    "\t显示当前日志配置\r\n");

/**
 * @brief 初始化双枪充电日志系统时的节流设置
 * 根据并充系统特性优化的日志节流参数
 */
void init_optimal_log_throttle(void)
{
    // 错误日志 - 始终输出，无需节流
    dualgun_set_throttle(0, 10); // ERROR级别

    // 警告日志 - 短时间节流，保证关键警告被及时看到
    dualgun_set_throttle(1, 20); // WARN级别 (500ms)

    // 信息日志 - 较宽松的节流，适合正常工作状态
    dualgun_set_throttle(2, 100); // INFO级别 (2秒)

    // 调试日志 - 较长节流，减少大量调试信息
    dualgun_set_throttle(3, 3000); // DEBUG级别 (3秒)

    // 跟踪日志 - 长时间节流，仅在需要时查看
    dualgun_set_throttle(4, 5000); // TRACE级别 (5秒)

    // 电压日志 - 充电电压变化通常较缓慢，适中节流
    dualgun_set_throttle(5, 50); // 电压相关 (2秒)

    // 电流日志 - 电流波动可能较频繁，较短节流
    dualgun_set_throttle(6, 50); // 电流相关 (1秒)

    // 状态日志 - 状态变化较重要，较短节流
    dualgun_set_throttle(7, 10); // 状态相关 (300ms)

    // CAN日志 - 适中节流，避免频繁打印
    dualgun_set_throttle(8, 250); // CAN相关 (1秒)
}

/**
 * @brief 初始化双枪充电日志系统时的变化阈值设置
 * 根据并充系统电压电流波动特性优化的变化阈值
 */
void init_optimal_change_thresholds(void)
{
    // 电压变化阈值 - 基于充电波动特性设置
    // 充电模式下3-5V的变化是有意义的变化
    dualgun_set_threshold(0, 100); // 电压阈值 (30 = 3.0V)

    // 电流变化阈值 - 基于充电特性设置
    // 对于大功率充电，0.5A-1A的变化是有意义的变化
    dualgun_set_threshold(1, 50); // 电流阈值 (50 = 0.5A)
}
/**
 * @brief 根据充电模式动态调整日志设置
 * @param mode 0-正常模式, 1-调试模式, 2-维护模式, 3-工厂模式
 */
void dualgun_adjust_log_settings(int mode)
{
    switch (mode)
    {
    case 0:                                    // 正常充电模式 - 更关注错误和重要信息，减少日志量
        dualgun_set_log_level(LOG_LEVEL_INFO); // 只显示INFO及以上级别

        // 设置较长的节流间隔
        dualgun_set_throttle(2, 3000); // INFO 3秒
        dualgun_set_throttle(5, 3000); // 电压 3秒
        dualgun_set_throttle(6, 3000); // 电流 3秒

        // 设置较大的变化阈值
        dualgun_set_threshold(0, 50);  // 电压变化阈值 5V
        dualgun_set_threshold(1, 100); // 电流变化阈值 1A
        break;

    case 1:                                     // 调试模式 - 输出更多调试信息
        dualgun_set_log_level(LOG_LEVEL_DEBUG); // 显示DEBUG及以上级别

        // 设置较短的节流间隔
        dualgun_set_throttle(2, 1000); // INFO 1秒
        dualgun_set_throttle(3, 2000); // DEBUG 2秒
        dualgun_set_throttle(5, 1000); // 电压 1秒
        dualgun_set_throttle(6, 1000); // 电流 1秒

        // 设置较小的变化阈值，捕获更多波动
        dualgun_set_threshold(0, 20); // 电压变化阈值 2V
        dualgun_set_threshold(1, 50); // 电流变化阈值 0.5A
        break;

    case 2:                                     // 维护模式 - 显示所有日志，帮助诊断问题
        dualgun_set_log_level(LOG_LEVEL_TRACE); // 显示所有级别

        // 设置更短的节流间隔
        dualgun_set_throttle(2, 500);  // INFO 0.5秒
        dualgun_set_throttle(3, 1000); // DEBUG 1秒
        dualgun_set_throttle(4, 2000); // TRACE 2秒
        dualgun_set_throttle(5, 500);  // 电压 0.5秒
        dualgun_set_throttle(6, 500);  // 电流 0.5秒
        dualgun_set_throttle(7, 0);    // 状态变化立即输出

        // 设置很小的变化阈值，捕获微小波动
        dualgun_set_threshold(0, 10); // 电压变化阈值 1V
        dualgun_set_threshold(1, 20); // 电流变化阈值 0.2A
        break;

    case 3:                                    // 工厂模式 - 专注于状态和错误
        dualgun_set_log_level(LOG_LEVEL_WARN); // 只显示WARN及以上级别

        // 错误和状态变化优先，其他信息较少
        dualgun_set_throttle(7, 0); // 状态变化立即输出

        // 电压电流只关注大幅波动
        dualgun_set_threshold(0, 100); // 电压变化阈值 10V
        dualgun_set_threshold(1, 200); // 电流变化阈值 2A
        break;
    }

    printf("%s日志设置已调整为模式%d%s\n", LOG_COLOR_GREEN, mode, LOG_COLOR_RESET);
}
/**
 * @brief 初始化双枪充电日志系统
 */
void dualgun_log_init(void)
{
    // 初始化所有状态变量
    memset(&g_last_log_time, 0, sizeof(g_last_log_time));
    memset(&g_last_logged_value, 0, sizeof(g_last_logged_value));

    // 初始化未知状态为0xFF
    for (int i = 0; i < DUAL_CHARGE_CHANNEL_LOG_NUM; i++)
    {
        g_last_logged_value.mode[i] = eDualChargeStatus_Independent;
        g_last_logged_value.status[i] = STATUS_STANDBY;
        g_last_logged_value.service[i] = eDualChargeServ_Disallowed;
    }

    // 应用优化的节流设置
    init_optimal_log_throttle();

    // 应用优化的变化阈值设置
    init_optimal_change_thresholds();

    // 输出初始化完成消息
    printf("%s双枪充电增强日志系统初始化完成%s\n", LOG_COLOR_GREEN, LOG_COLOR_RESET);
}
