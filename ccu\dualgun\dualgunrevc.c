#include "dualgun.h"
#include <message.h>
#include <pcu/eicu/inc/port.h>
#include "transceiver.h"
#include "../pcu/pcuMain.h"
#include "shell.h"
#include <trace.h>
#include <bms.h>
MSG_Q_ID msg_ccu = NULL; /**< SCU消息接收指针*/

/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/

/**
 ******************************************************************************
 * @brief      处理所有通道的控制器接收超时事件
 * @param[in]  dev - 设备标识符（未直接使用）
 * @retval     固定返回0表示操作完成
 * @details    遍历所有通道，标记对应控制器的接收超时标志为TRUE
 * @note       用于检测通信中断后的状态处理
 ******************************************************************************
 */
static int
Recv_CcuRevCtrlTimeout(int dev)
{
    DUALCHARGE_CTRL *pCtrl = NULL;

    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        pCtrl = Get_DualchargeCtrl(channel);
        if (pCtrl != NULL)
        {
            pCtrl->revctrltimeout = TRUE; // 设置接收超时标志
        }
    }
    return 0; // 统一返回0表示操作完成
}

/**
 ******************************************************************************
 * @brief      复位所有通道的控制器接收超时标志
 * @param[in]  dev - 设备标识符（未直接使用）
 * @retval     固定返回0表示操作完成
 * @details    遍历所有通道，清除对应控制器的接收超时标志（置为FALSE）
 * @note       通常在通信恢复后调用
 ******************************************************************************
 */
static int
Recv_CcuRevCtrlTimeoutRst(int dev)
{
    DUALCHARGE_CTRL *pCtrl = NULL;

    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        pCtrl = Get_DualchargeCtrl(channel);
        if (pCtrl != NULL)
        {
            pCtrl->revctrltimeout = FALSE; // 清除接收超时标志
        }
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      处理所有通道的控制器应答超时事件
 * @param[in]  dev - 设备标识符（未直接使用）
 * @retval     固定返回0表示操作完成
 * @details    遍历所有通道，标记对应控制器的应答超时标志为TRUE
 * @note       用于检测命令响应超时
 ******************************************************************************
 */
static int
Recv_CcuRevTeleTimeout(int dev)
{
    DUALCHARGE_CTRL *pCtrl = NULL;

    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        pCtrl = Get_DualchargeCtrl(channel);
        if (pCtrl != NULL)
        {
            pCtrl->revctrlacktimeout = TRUE; // 设置应答超时标志
        }
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      复位所有通道的控制器应答超时标志
 * @param[in]  dev - 设备标识符（未直接使用）
 * @retval     固定返回0表示操作完成
 * @details    遍历所有通道，清除对应控制器的应答超时标志（置为FALSE）
 * @note       在收到有效应答后调用
 ******************************************************************************
 */
static int
Recv_CcuRevTeleTimeoutRst(int dev)
{
    DUALCHARGE_CTRL *pCtrl = NULL;

    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        pCtrl = Get_DualchargeCtrl(channel);
        if (pCtrl != NULL)
        {
            pCtrl->revctrlacktimeout = FALSE; // 清除应答超时标志
        }
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      解析并更新控制器状态数据
 * @param[in]  dev    - 设备标识符
 * @param[in]  rxBuf  - 接收到的原始数据缓冲区
 * @param[in]  rxLen  - 数据长度
 * @retval     固定返回0表示操作完成
 * @details    从接收数据中提取RemoteControlCmd结构，更新通道1的控制器状态
 * @note       数据解析依赖于RemoteControlCmd结构定义
 ******************************************************************************
 */
static int
Recv_CcuRevCtrl(int dev, uint8 rxBuf[], int rxLen)
{
    RemoteControlCmd *pStatus = (RemoteControlCmd *)rxBuf; // 强制类型转换解析数据
    DUALCHARGE_CTRL *pCtrl = NULL;

    pCtrl = Get_DualchargeCtrl(DUAL_CHARGE_CHANNEL_01); // 仅处理通道1
    if (pCtrl != NULL)
    {
        // 更新控制器状态字段
        pCtrl->reconnect_status = pStatus->reconnect_status; // 重连状态
        pCtrl->spn = pStatus->spn_flag;                      // 设置SPN标志
        pCtrl->result_status = pStatus->result_status;       // 操作结果状态
        pCtrl->ctrlCmd = pStatus->cmd;                       // 记录控制命令
        pCtrl->voltage = pStatus->set_voltage;               // 设置电压值
        pCtrl->current = pStatus->set_current;               // 设置电流值
        pCtrl->batVoltage = pStatus->battery_voltage;        // 电池电压
        if (pCtrl->operate == eDualChargeOpr_Stop)
        {
            return 0;
        }
        if (CMD_FAST_START == pCtrl->ctrlCmd && eDualChargeStatus_Independent == pCtrl->mode)
        {
            //            trace(TR_CCU_DEBUG,"============Recv_CcuRevCtrl :[]===============");
            //            trace_buf(TR_CCU_DEBUG,rxBuf,rxLen);
            Set_ChargeActFlag(eActFlag_On);
            Start_DualImdCharge(DUAL_CHARGE_CHANNEL_01,
                                CMD_FAST_START,
                                eDualChargeDir_Responder, pStatus->set_voltage, pStatus->set_current, pStatus->battery_voltage);

            printf("并柜操作---[通道%d]----[开机响应]-[Vol= %d,Cur= %d,Bat= %d]\n",
                   DUAL_CHARGE_CHANNEL_01, pCtrl->voltage,
                   pCtrl->current, pCtrl->batVoltage);
        }
        else if (CMD_SOFT_START == pCtrl->ctrlCmd && eDualChargeStatus_Insulation == pCtrl->mode)

        {
            Start_DualSoftCharge(DUAL_CHARGE_CHANNEL_01,
                                 CMD_FAST_START,
                                 eDualChargeDir_Responder, pCtrl->voltage, pCtrl->current, pCtrl->batVoltage);
            printf(
                "%-6s并柜操作%-6s[通道%d]%-6s[软起响应]-[Vol= %d,Cur= %d,Bat= %d]\n", "**",
                "**", DUAL_CHARGE_CHANNEL_01, "**", pCtrl->voltage,
                pCtrl->current, pCtrl->batVoltage);
        }
        else if (CMD_MODIFY_PARAM == pCtrl->ctrlCmd)
        {
            Set_SafeDualChargeParams(DUAL_CHARGE_CHANNEL_01,
                                     pCtrl->voltage, pCtrl->current, pCtrl->batVoltage);
        }
        if (CMD_RELEASE_MODULE == pCtrl->ctrlCmd)
        {
            Set_ChargeActFlag(eActFlag_Off);
            //            stop_DualCharge(DUAL_CHARGE_CHANNEL_01);// (eActFlag_Off)后在Enhanced_Check_DualChargeService中处理
        }
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      解析并更新控制器应答数据
 * @param[in]  dev    - 设备标识符
 * @param[in]  rxBuf  - 接收到的原始数据缓冲区
 * @param[in]  rxLen  - 数据长度
 * @retval     固定返回0表示操作完成
 * @details    从接收数据中提取RemoteControlAck结构，更新通道1的控制器应答信息
 * @note       数据解析依赖于RemoteControlAck结构定义
 ******************************************************************************
 */
static int Recv_CcuRevTele(int dev, uint8 rxBuf[], int rxLen)
{
    RemoteControlAck *pStatus = (RemoteControlAck *)rxBuf;
    DUALCHARGE_CTRL *pCtrl = Get_DualchargeCtrl(DUAL_CHARGE_CHANNEL_01);

    if (NULL == pCtrl)
        return -1;

    typedef struct
    {
        bool success_flag;
        uint8 mode, spn_id, service, status;
        uint16 voltage, current, battery_voltage;
    } StateFields;

    //
    if (memcmp(&(StateFields){pCtrl->success_flag, pCtrl->offsidemode, pCtrl->offsidespn,
                              pCtrl->OffsideService, pCtrl->offsidestatus,
                              pCtrl->actual_voltage, pCtrl->actual_current, pCtrl->battery_voltage},
               &(StateFields){pStatus->success_flag, pStatus->mode, pStatus->spn_id,
                              pStatus->service, pStatus->status,
                              pStatus->actual_voltage, pStatus->actual_current, pStatus->battery_voltage},
               sizeof(StateFields)) != 0)
    {
        printf("状态变化: 模式=%d, 服务=%d, spn :%d ,状态=%d, 电压=%d, 电流=%d, 电池电压=%d\n",
               pStatus->mode, pStatus->service, pStatus->spn_id, pStatus->status,
               pStatus->actual_voltage, pStatus->actual_current, pStatus->battery_voltage);
    }

    // 更新所有控制器字段
    pCtrl->success_flag = pStatus->success_flag;       // 操作成功标志
                                                       //    pCtrl->connected = pStatus->connected;          // 连接状态
    pCtrl->offsidemode = pStatus->mode;                // 工作状态
                                                       //    pCtrl->ctrlCmd = pStatus->cmd;                  // 操作命令
    pCtrl->offsidespn = pStatus->spn_id;               // 当前状态码
    pCtrl->OffsideService = pStatus->service;          // 服务状态
    pCtrl->offsidestatus = pStatus->status;            // 当前状态码
    pCtrl->actual_voltage = pStatus->actual_voltage;   // 实际输出电压
    pCtrl->actual_current = pStatus->actual_current;   // 实际输出电流
    pCtrl->battery_voltage = pStatus->battery_voltage; // 电池电压反馈

    if (pStatus->connected != pCtrl->offsideconnected)
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "对侧连接状态变化: %d -> %d\n",
                     pCtrl->offsideconnected, pStatus->connected);
        pCtrl->offsideconnected = pStatus->connected;
    }

    // 根据接收到的状态码同步从机状态
    if (pCtrl->mode == eDualChargeStatus_Slave)
    {
        // 从机状态处理 - 只在从机模式下进行
        if (pCtrl->offsidestatus == STATUS_PAUSED)
        {
            // 收到暂停状态，设置本地BMS暂停标志
            if (chargePauseFlg(FALSE, FALSE) == enumAllowFlag_Allow)
            {
                DUALGUN_INFO(TR_CCU_DEBUG, "从机收到暂停指令，设置暂停标志\n");
                chargePauseFlg(TRUE, enumAllowFlag_Forbid);
            }
        }
        else if (pCtrl->offsidestatus == STATUS_CHARGING)
        {
            // 收到充电状态，如果当前是暂停状态则恢复
            if (chargePauseFlg(FALSE, 0) == enumAllowFlag_Forbid)
            {
                DUALGUN_INFO(TR_CCU_DEBUG, "从机收到恢复充电指令，清除暂停标志\n");
                chargePauseFlg(TRUE, enumAllowFlag_Allow);
            }
        }
    }
    // B板处理 - 不与BMS通信的板子
    if (Is_DualCharge_Responder(DUAL_CHARGE_CHANNEL_01))
    {
        // 根据A板的重连状态同步本地通信状态
        switch (pStatus->reconnect)
        {
        case RECONNECT_ATTEMPTING:
            // A板正在尝试重连，B板同步为超时状态
            Set_BmsComState(eComState_TimeOut);
            DUALGUN_INFO(TR_CCU_DEBUG, "B板同步A板通信超时状态\n");
            break;

        case RECONNECT_FAILED:
            // A板重连失败，B板同步为通信中断
            Set_BmsComState(eComState_Break);
            DUALGUN_INFO(TR_CCU_DEBUG, "B板同步A板通信中断状态\n");
            break;

        case RECONNECT_IDLE:
        default:
            // A板通信正常，B板也同步为正常
            Set_BmsComState(eComState_Normal);
            // 如果之前是超时状态，记录日志
            if (eComState_Normal != Get_BmsComState())
            {
                DUALGUN_INFO(TR_CCU_DEBUG, "B板同步A板通信恢复正常\n");
            }
            break;
        }
    }

    return 0;
}

static int ccu_Recv(int channel, int *dev, int *id, uint8 rxBuf[], int rxSize)
{
    int length = 0;
    CAN_DATA data;  // CAN报文容器
    uint32 len = 0; // 实际接收长度
    uint8 resl = 0; // 接收结果码

    /* 从PDCU专用消息队列获取报文 */
    resl = msgq_rcv(msg_ccu, (uint8 *)&data, &len, 1); // 1ms超时
    if ((resl == MSG_ERR_NONE) && (len > 0))
    {
        CAN_ID *pId = (CAN_ID *)&data.canId; // 解析CAN标识符

        if (pId->prio == Get_CcuPrio(pId->pf))
        {
            *id = pId->pf;  // 输出报文ID（如ID_PDCU_STATUS）
                            //            *dev = pId->sa;   // 输出源设备地址
            *dev = pId->ps; // 输出目的设备地址

            /* 安全拷贝数据到用户缓冲区 */
            if (data.dataLen <= rxSize)
            {
                memcpy(rxBuf, data.dataBuf, data.dataLen);
                length = data.dataLen; // 返回实际有效数据长度
            }
            else
            {
                trace(TR_CCU_DEBUG, "CCU接收: 缓冲区太小 [需要%d,可用%d]\n",
                      data.dataLen, rxSize);
            }
            /* 验证报文优先级与设备配置匹配 */
            DUALGUN_CAN_LOG(TR_CCU_DEBUG, data.canId, data.dataBuf, data.dataLen,
                            "<channel:%d>--<ccu pf :%x>---<Ccu rx3: %X>",
                            channel, pId->pf, data.canId);
        }
        else
        {
            trace(TR_CCU_DEBUG, "CCU接收: 优先级不匹配 [实际:0x%X,预期:0x%X]\n",
                  pId->prio, Get_CcuPrio(pId->pf));
        }
    }
    else
    {
        // DUALGUN_DEBUG(TR_CCU_DEBUG, "CCU接收失败!!!\n");
    }
    return length;
}

void Ccu_RecvInit(void)
{
    int dev = 0; // 动态设备标识

    /* 创建专用消息队列 */
    msg_ccu = msgQCreate(MSG_MALLOC_NUM);

    /* 注册CAN3通道，地址范围0x10-0x11 */
    msg_register(msg_ccu, PCU_CHAN, 0x10, 0x11);

    /* 确定本机设备类型（主备模式切换） */
    //    dev = (DEV_CCU_ADDR_A == Get_CcuAddr()) ? DEV_CCU_A : DEV_CCU_B;
    dev = Get_CCU();

    /* 注册通道回调函数 */
    reg_channel(CHANNEL_CCU, ccu_Recv, NULL);

    // 状态报文接收器（超时处理+自动恢复）
    reg_receiver(CHANNEL_CCU, dev, ID_DUALCHARGE_CTRL, sizeof(RemoteControlCmd),
                 Recv_CcuRevCtrl, 5 * sysClkRateGet(), NULL,
                 NULL);

    // 控制指令接收器（无超时处理）
    reg_receiver(CHANNEL_CCU, dev, ID_DUALCHARGE_TELE, sizeof(RemoteControlAck),
                 Recv_CcuRevTele, 5 * sysClkRateGet(),
                 Recv_CcuRevTeleTimeout, Recv_CcuRevTeleTimeoutRst);

    set_recvTimeout_enable(CHANNEL_CCU, dev, ID_DUALCHARGE_TELE, eEnableFlag_On);
    /* ----------------------------------------------------------------------*/

    /* 使能接收器状态（三重校验）---------------------------------------------*/
    // 状态报文接收使能
    if (eEnableFlag_Off == check_recv_enable(CHANNEL_CCU, dev, ID_DUALCHARGE_CTRL))
    {
        set_recv_enable(CHANNEL_CCU, dev, ID_DUALCHARGE_CTRL, eEnableFlag_On);
        set_recv_lastTime(CHANNEL_CCU, dev, ID_DUALCHARGE_CTRL, 0); // 重置计时器
    }

    // 控制指令接收使能
    if (eEnableFlag_Off == check_recv_enable(CHANNEL_CCU, dev, ID_DUALCHARGE_TELE))
    {
        set_recv_enable(CHANNEL_CCU, dev, ID_DUALCHARGE_TELE, eEnableFlag_On);
        set_recv_lastTime(CHANNEL_CCU, dev, ID_DUALCHARGE_TELE, 0);
    }
}

static uint32 Test_ccuRecv(cmd_tbl_t *cmdtp, uint32 argc, const uint8 *argv[])
{
    int dev = 0;
    //    dev = (DEV_CCU_ADDR_A == Get_CcuAddr()) ? DEV_CCU_A : DEV_CCU_B;
    dev = Get_CCU();

    /* 手动使能接收器（与初始化逻辑一致）*/
    // 状态报文接收使能
    if (eEnableFlag_Off == check_recv_enable(CHANNEL_CCU, dev, ID_DUALCHARGE_CTRL))
    {
        set_recv_enable(CHANNEL_CCU, dev, ID_DUALCHARGE_CTRL, eEnableFlag_On);
        set_recv_lastTime(CHANNEL_CCU, dev, ID_DUALCHARGE_CTRL, 0); // 重置计时器
    }

    // 控制指令接收使能
    if (eEnableFlag_Off == check_recv_enable(CHANNEL_CCU, dev, ID_DUALCHARGE_TELE))
    {
        set_recv_enable(CHANNEL_CCU, dev, ID_DUALCHARGE_TELE, eEnableFlag_On);
        set_recv_lastTime(CHANNEL_CCU, dev, ID_DUALCHARGE_TELE, 0);
    }
    return 0;
}

SHELL_CMD(ccu_recv, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)Test_ccuRecv,
          "ccu_recv \r\t\t\t\t PDCU接收测试\n");
