/**
 ******************************************************************************
 * @file      pcuRecvCtrl.c
 * @brief     C Source file of pcuRecvCtrl.c.
 * @details   This file including all API functions's
 *            implement of pcuRecvCtrl.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <stdio.h>
#include <sxlib.h>
#include <trace.h>
#include <gpio.h>

#include "pcuMain.h"
#include "ccu\tcu\tcuMain.h"
#include "pcuRecvCtrl.h"
#include "pcuSendCtrl.h"
#include <ccu\bsn\deviceState.h>
#include <ccu\charge\ccuChargeMain.h>
#include "ccu\para\para.h"
#include <ttylib.h>
#include "../lib/ccuFix.h"
#include "public.h"
#include "message.h"
#include "cpcu/cpcuTrans.h"
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
// static PCU_WS_DATA           pcuWsData;        /**< pcu工作状态及故障信息帧数据 */
// static PCU_YXYC_DATA             pcuYXYCData;        /**< PCU遥信遥测数据 */
// static PCU_ALARM_DATA           pcuAlarmData;        /**< PCU告警数据 */

static uint8 s_backupBuf[128];
static uint8 s_backupLen;
static PCU_RECV_CTRL s_PcuRecvCtrl[PCU_PGN_CNT];
static uint8 pcuErroInfo[8];

static bool_e Recv_PcuHeart(uint8 *pInBuf, uint32 len);
static bool_e Recv_PcuWS(uint8 *pInBuf, uint32 len);
static bool_e Recv_PcuYXYC(uint8 *pInBuf, uint32 len);
static bool_e Recv_PcuExYC(uint8 *pInBuf, uint32 len);
static bool_e Recv_PcuAlarm(uint8 *pInBuf, uint32 len);
static bool_e Recv_PcuYC2(uint8 *pInBuf, uint32 len);
static bool_e Recv_PcuFixSetACK(uint8 *pInBuf, uint32 len);
static bool_e Recv_PcuFixQueryACK(uint8 *pInBuf, uint32 len);
static bool_e Recv_YK_QStart_ACK(uint8 *pInBuf, uint32 len);
static bool_e Recv_YK_CStop_ACK(uint8 *pInBuf, uint32 len);
static bool_e Recv_YK_SStart_ACK(uint8 *pInBuf, uint32 len);
static bool_e Recv_YK_Aaddr_ACK(uint8 *pInBuf, uint32 len);
static bool_e Recv_YK_Opara_ACK(uint8 *pInBuf, uint32 len);
static bool_e Recv_YK_Stop_ACK(uint8 *pInBuf, uint32 len);
static uint32 Get_PcuNoneTimeOut(void);
static uint32 Get_PcuTimeOut(void);

const PCU_RECV_DEAL PCU_RECV_DEAL_TABLE[] =
	{
		{PGN_YK_QSTART_ACK, 6, 0, FALSE, Get_PcuNoneTimeOut, Recv_YK_QStart_ACK},
		{PGN_YK_CSTOP_ACK, 6, 0, FALSE, Get_PcuNoneTimeOut, Recv_YK_CStop_ACK},
		{PGN_YK_SSTART_ACK, 6, 0, FALSE, Get_PcuNoneTimeOut, Recv_YK_SStart_ACK},
		{PGN_YK_SADDR_ACK, 6, 0, FALSE, Get_PcuNoneTimeOut, Recv_YK_Aaddr_ACK},
		{PGN_YK_OPARA_ACK, 6, 0, FALSE, Get_PcuNoneTimeOut, Recv_YK_Opara_ACK},
		{PGN_YK_STOP_ACK, 6, 0, FALSE, Get_PcuNoneTimeOut, Recv_YK_Stop_ACK},
		{PGN_PCU_FIX_SET_ACK, 6, 1, FALSE, Get_PcuNoneTimeOut, Recv_PcuFixSetACK},	   /**<接受完成后关闭应答*/
		{PGN_PCU_FIX_QUERY_ACK, 6, 1, FALSE, Get_PcuNoneTimeOut, Recv_PcuFixQueryACK}, /**<接受完成后关闭应答*/
		{PGN_HEART_PCU, 6, 0, TRUE, Get_PcuTimeOut, Recv_PcuHeart},
		{PGN_PCU_WS, 6, 0, TRUE, Get_PcuTimeOut, Recv_PcuWS},
		{PGN_PCU_YXYC, 6, 0, TRUE, Get_PcuTimeOut, Recv_PcuYXYC},
		{PGN_PCU_ALARM, 6, 0, TRUE, Get_PcuNoneTimeOut, Recv_PcuAlarm},
		{PGN_PCU_YC2, 6, 0, TRUE, Get_PcuTimeOut, Recv_PcuYC2},
		{PGN_PCU_EXYC, 6, 0, TRUE, Get_PcuTimeOut, Recv_PcuExYC},
};

static uint32 Get_PcuNoneTimeOut(void)
{
	return 0xFFFF;
}

static uint32 Get_PcuTimeOut(void)
{
	CONFIG_PARA strCfgPara;
	Get_PilePara((void *)&strCfgPara, eParaType_ConfigPara);
	return (strCfgPara.pcuAndCcuOvertime * 1000) / PCU_CALL_CYCLE;
}
/**
 ******************************************************************************
 * @brief       PCU心跳帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_PcuHeart(uint8 *pInBuf, uint32 len)
{
	return TRUE;
}

/**
 ******************************************************************************
 * @brief       PCU遥信遥测帧1接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_PcuWS(uint8 *pInBuf, uint32 len)
{
	const YX_ERR_MATCH *pErrMatch = NULL;
	uint8 errFlag = FALSE;
	uint8 pcuStage = Get_PcuStage();
	memcpy(pPcuWsData, pInBuf, sizeof(PCU_WS_DATA));
	//    if (Get_TcuProtocolVer() >= TCU_PROTOCOL_VER_0121)
	//    {
	//        Set_ErrDat(eErrType_PcuAndEcuTimeout,pPcuWsData->pcuAndEcuTimeOut );
	//        Set_ErrDat(eErrType_PcuAndPcuTimeout,pPcuWsData->pcuAndPcuTimeOut );
	//        Set_ErrDat(eErrType_PcuAndSwcuTimeout,pPcuWsData->pcuAndSwcuTimeOut);
	//        Set_ErrDat(eErrType_PcuAndCcuTimeout,pPcuWsData->pcuAndCcuTimeOut );
	//        Set_ErrDat(eErrType_NoMod,pPcuWsData->noModFlt);
	//        Set_ErrDat(eErrType_HjxxAlm,pPcuWsData->hjsdAlm);
	//        Set_ErrDat(eErrType_BlqErr,pPcuWsData->blqFlt);
	////        Set_ErrDat(eErrType_AcJCQErr,pPcuWsData->acJCQnl);
	//        Set_ErrDat(eErrType_AcJCQErr,pPcuWsData->acJCQFlt);//todo baohaopeng 故障错位
	//    }
	Set_ErrDat(eErrType_PcuEmergencyStop, pPcuWsData->PcuJt);
	Set_ErrDat(eErrType_PEErr, pPcuWsData->peErr);
	Set_ErrDat(eErrType_PcuAndEcuTimeout, pPcuWsData->pcuAndEcuTimeOut);
	Set_ErrDat(eErrType_PcuAndPcuTimeout, pPcuWsData->pcuAndPcuTimeOut);
	Set_ErrDat(eErrType_PcuAndSwcuTimeout, pPcuWsData->pcuAndSwcuTimeOut);
	Set_ErrDat(eErrType_PcuAndCcuTimeout, pPcuWsData->pcuAndCcuTimeOut);
	Set_ErrDat(eErrType_NoMod, pPcuWsData->noModFlt);
	if (TRUE != pPcuWsData->hjsdAlm && Get_FaultMask(eErrType_HjxxAlm))
	{
		Set_ErrDat(eErrType_HjxxAlm, Get_FaultMask(eErrType_HjxxAlm));
	}
	else
	{
		Set_ErrDat(eErrType_HjxxAlm, pPcuWsData->hjsdAlm);
	}
	//    Set_ErrDat(eErrType_HjxxAlm,pPcuWsData->hjsdAlm);
	Set_ErrDat(eErrType_BlqErr, pPcuWsData->blqFlt);
	//        Set_ErrDat(eErrType_AcJCQErr,pPcuWsData->acJCQnl);
	if (TRUE != pPcuWsData->acJCQFlt && Get_FaultMask(eErrType_AcJCQErr))
	{
		Set_ErrDat(eErrType_AcJCQErr, Get_FaultMask(eErrType_AcJCQErr));
	}
	else
	{
		Set_ErrDat(eErrType_AcJCQErr, pPcuWsData->acJCQFlt);
	}
	Set_ErrDat(eErrType_CcuYxYcTimeoutErr, pPcuWsData->pcuRecvCcuTimeOut);
	if (Get_EnableFlag(eParaFmt_CfgFissionEnable))
	{
		Set_ErrDat(eErrType_CabTempOverErr, pPcuWsData->tempFlt);
	}
	else
	{
		Set_ErrDat(eErrType_PileTempOverLimitErr, pPcuWsData->tempFlt);
	}
	Set_ErrDat(eErrType_SwErr, pPcuWsData->acSWFlt);

	if (TRUE != pPcuWsData->modFlt && Get_FaultMask(eErrType_PowerModuleErr))
	{
		Set_ErrDat(eErrType_PowerModuleErr, Get_FaultMask(eErrType_PowerModuleErr));
	}
	else
	{
		Set_ErrDat(eErrType_PowerModuleErr, pPcuWsData->modFlt);
	}

	//    Set_ErrDat(eErrType_PowerModuleErr   ,pPcuWsData->modFlt           );

	if ((TRUE != pPcuWsData->inVolOvernFlt || TRUE != pPcuWsData->inOpenPhaseFlt || TRUE != pPcuWsData->inVolLessnFlt) && Get_FaultMask(eErrType_InputVolOverLimit))
	{
		Set_ErrDat(eErrType_InputVolOverLimit, Get_FaultMask(eErrType_InputVolOverLimit));
	}
	else
	{
		Set_ErrDat(eErrType_InputVolOverLimit, pPcuWsData->inVolOvernFlt);
		Set_ErrDat(eErrType_InputOpenphase, pPcuWsData->inOpenPhaseFlt);
		Set_ErrDat(eErrType_InputVolLessLimit, pPcuWsData->inVolLessnFlt);
	}

	//    Set_ErrDat(eErrType_InputVolOverLimit,pPcuWsData->inVolOvernFlt    );

	if (TRUE != pPcuWsData->pcuSJFlt && Get_FaultMask(eErrType_PcuWaterFault))
	{
		Set_ErrDat(eErrType_PcuWaterFault, Get_FaultMask(eErrType_PcuWaterFault));
	}
	else
	{
		Set_ErrDat(eErrType_PcuWaterFault, pPcuWsData->pcuSJFlt);
	}

	//    Set_ErrDat(eErrType_WaterLoggingErr,pPcuWsData->pcuSJFlt);
	Set_ErrDat(eErrType_PcuDoorOpenErr, pPcuWsData->pcuDoorFlt);
	Set_ErrDat(eErrType_HearterErr, pPcuWsData->heaterFlt);
	Set_ErrDat(eErrType_PileFanErr, pPcuWsData->fsFlt);

	if (TRUE != pPcuWsData->smokeFlt && Get_FaultMask(eErrType_SmokeErr))
	{
		Set_ErrDat(eErrType_SmokeErr, Get_FaultMask(eErrType_SmokeErr));
	}
	else
	{
		Set_ErrDat(eErrType_SmokeErr, pPcuWsData->smokeFlt);
	}
	//    Set_ErrDat(eErrType_SmokeErr ,pPcuWsData->smokeFlt);
	//    Set_ErrDat(eErrType_MultipleContactorSynechia,pPcuWsData->acJCQFlt);
	Set_ErrDat(eErrType_ACBreakerErr, pPcuWsData->acDLQFlt);
	Set_ErrDat(eErrType_PCUOtherErr, pPcuWsData->res5);

	if ((ePcuFaultState_Yes == pPcuWsData->errState) && (pPcuWsData->ErrState & 0xFFFC00 == 0x00000000))
	{
		Set_ErrType(eErrType_PCUOtherErr);
	}
	else
	{
		Clr_ErrType(eErrType_PCUOtherErr);
	}

	// 处理PCU禁止充电故障在其他遥信故障后，保证上报TCU正确.
	if (((pcuStage >= ePcuCtrlStage_QStartWait) && (pcuStage <= ePcuCtrlStage_ImdAdjPara)) || ((pcuStage >= ePcuCtrlStage_SStartWs) && (pcuStage <= ePcuCtrlStage_AdjPara)))
	{
		if (ePcuServeState_Forbid == pPcuWsData->serveState)
		{
			if (((pcuStage >= ePcuCtrlStage_QStart) && (pcuStage < ePcuCtrlStage_MidStop)) || ((pcuStage >= ePcuCtrlStage_SStart) && (pcuStage < ePcuCtrlStage_Pause)))
			{
				Set_ErrType(eErrType_PcuForbidCharge);
			}
		}
		else
		{
			Clr_ErrType(eErrType_PcuForbidCharge);
		}
	}
	else
	{
		Clr_ErrType(eErrType_PcuForbidCharge);
	}

	return TRUE;
}

/**
 ******************************************************************************
 * @brief       快速启动遥控命令帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_YK_QStart_ACK(uint8 *pInBuf, uint32 len)
{
	memcpy(pPcuYkCmdAck, pInBuf, sizeof(PCU_YK_ACK_DATA));
	return TRUE;
}

/**
 ******************************************************************************
 * @brief       保留模块关机帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_YK_CStop_ACK(uint8 *pInBuf, uint32 len)
{
	memcpy(pPcuYkCmdAck, pInBuf, sizeof(PCU_YK_ACK_DATA));
	return TRUE;
}

/**
 ******************************************************************************
 * @brief       软启开机帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_YK_SStart_ACK(uint8 *pInBuf, uint32 len)
{
	memcpy(pPcuYkCmdAck, pInBuf, sizeof(PCU_YK_ACK_DATA));
	return TRUE;
}

/**
 ******************************************************************************
 * @brief       关机接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_YK_Stop_ACK(uint8 *pInBuf, uint32 len)
{
	memcpy(pPcuYkCmdAck, pInBuf, sizeof(PCU_YK_ACK_DATA));
	return TRUE;
}

/**
 ******************************************************************************
 * @brief       参数调整
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_YK_Opara_ACK(uint8 *pInBuf, uint32 len)
{

	memcpy(pPcuYkCmdAck, pInBuf, sizeof(PCU_YK_ACK_DATA));
	return TRUE;
}

/**
 ******************************************************************************
 * @brief       参数调整
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_YK_Aaddr_ACK(uint8 *pInBuf, uint32 len)
{
	memcpy(pPcuYkCmdAck, pInBuf, sizeof(PCU_YK_ACK_DATA));
	return TRUE;
}

/**
 ******************************************************************************
 * @brief       参数设置应答帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_PcuFixSetACK(uint8 *pInBuf, uint32 len)
{
	PARA_OPERATE Curtype;

	Curtype = Get_CurParaOperateType();

	if (Curtype.devType == pInBuf[1] && eOperateType_Set == Curtype.operateType)
	{
		Clr_CurParaOperateType();
		TakeTcuSem();
		fix_oprate_t *Tcu_FixPara = Get_tcu_FixPara();
		Tcu_FixPara->fix.devType = Curtype.devType;
		Tcu_FixPara->fix.comAdr = pInBuf[2];
		Tcu_FixPara->fix.fixNum = Curtype.paraType;
		Tcu_FixPara->fix.ack_u.ret.sign = pInBuf[5] & 0x80;
		Tcu_FixPara->fix.ack_u.ret.res = pInBuf[5] & 0x70;
		Tcu_FixPara->fix.ack_u.ret.reason = pInBuf[5] & 0x0f;
		Set_TcuSendRemainTimer(PGN_FIX_SET_ACK, 250);
		Set_TcuStartTimer(PGN_FIX_SET_ACK);
		Set_TcuLastSendTimer(PGN_FIX_SET_ACK, 0xFFFF); /*立即发送*/
		GiveTcuSem();
		return TRUE;
	}
	else
	{
		return FALSE;
	}
}

/**
 ******************************************************************************
 * @brief       参数设置应答帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_PcuFixQueryACK(uint8 *pInBuf, uint32 len)
{
	return TRUE;
}

/**
 ******************************************************************************
 * @brief       PCU遥信遥测帧2接收处理
 * @param[in]   uint32 pgn             参数组编号
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_PcuYXYC(uint8 *pInBuf, uint32 len)
{
	memcpy(pPcuYXYCData, pInBuf, sizeof(PCU_YXYC_DATA));

	return TRUE;
}

/**
 ******************************************************************************
 * @brief       PCU扩展数据
 * @param[in]   uint32 pgn             参数组编号
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_PcuExYC(uint8 *pInBuf, uint32 len)
{
	memcpy(pPcuExYcData, pInBuf, sizeof(PCU_EXYC_DATA));
	trace(TR_DEBUG, "\n");
	trace_buf(TR_DEBUG, pInBuf, 8);
	trace(TR_DEBUG, "curr: %d\n\n", (pInBuf[1] << 8) | pInBuf[0]);
	return TRUE;
}

/**
 ******************************************************************************
 * @brief       PCU告警数据帧
 * @param[in]   uint32 pgn             参数组编号
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，
 * @details
 * @note
 ******************************************************************************
 */
static bool_e Recv_PcuAlarm(uint8 *pInBuf, uint32 len)
{

	const YX_ERR_MATCH *pErrMatch = NULL;
	memcpy(pPcuAlarmData, pInBuf, sizeof(PCU_ALARM_DATA));
	//    binvert(&pcuYXYC1Data.ErrState[0], 4);

	const YX_ERR_MATCH PCU_ALARM_MAP[] =
		{
			{(uint8 *)&pPcuAlarmData->AlarmState, 12, eErrType_PowerModuleXfAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 13, eErrType_PowerModuleComAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 14, eErrType_PowerModuleInOpenPhaseAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 15, eErrType_PowerModuleInVolLessAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 16, eErrType_PowerModuleInVolOverAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 17, eErrType_PowerModuleOutVolLessAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 18, eErrType_PowerModuleOutVolOverAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 19, eErrType_PowerModuleOutCurOverAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 20, eErrType_PowerModuleOutShortCutAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 21, eErrType_PowerModuleACInAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 22, eErrType_PowerModuleTempAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 23, eErrType_PowerModuleFanAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 24, eErrType_PileTempOverLimitAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 25, eErrType_SwAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 26, eErrType_InOpenPhaseAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 27, eErrType_InVolLessAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 28, eErrType_InVolOverAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 29, eErrType_HearterAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 30, eErrType_FanAlarm},
			{(uint8 *)&pPcuAlarmData->AlarmState, 31, eErrType_BlqAlarm},
		};
	for (uint8 index = 0; index < FCNT(PCU_ALARM_MAP); index++)
	{
		pErrMatch = &PCU_ALARM_MAP[index];

		if (Get_BitFlag(pErrMatch->errAddr, pErrMatch->errbit))
		{
			Set_ErrType(pErrMatch->errType);
		}
		else
		{
			Clr_ErrType(pErrMatch->errType);
		}
	}
	return TRUE;
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_PcuYC2(uint8 *pInBuf, uint32 len)
{
	memcpy(pPcuYc2Data, pInBuf, sizeof(PCU_YC2_DATA));
	return TRUE;
}

/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/

/**
 ******************************************************************************
 * @brief       PCU错误信息置位
 * @param[in]   uint32 pgn
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note
 ******************************************************************************
 */
static void
Set_PcuErrInfo(uint32 pgn)
{
	if (PGN_YK_QSTART_ACK == pgn)
	{
		Set_BitFlag(pcuErroInfo, 0);
	}
	else if (PGN_YK_CSTOP_ACK == pgn)
	{
		Set_BitFlag(pcuErroInfo, 1);
	}
	else if (PGN_YK_SSTART_ACK == pgn)
	{
		Set_BitFlag(pcuErroInfo, 2);
	}
	else if (PGN_YK_SADDR_ACK == pgn)
	{
		Set_BitFlag(pcuErroInfo, 3);
	}
	else if (PGN_YK_OPARA_ACK == pgn)
	{
		Set_BitFlag(pcuErroInfo, 4);
	}
	else if (PGN_YK_STOP_ACK == pgn)
	{
		Set_BitFlag(pcuErroInfo, 5);
	}
	else if (PGN_PCU_FIX_SET_ACK == pgn)
	{
		Set_BitFlag(pcuErroInfo, 6);
	}
	else if (PGN_PCU_FIX_QUERY_ACK == pgn)
	{
		Set_BitFlag(pcuErroInfo, 7);
	}
	else if (PGN_PCU_WS == pgn)
	{
		Set_BitFlag(pcuErroInfo, 8);
	}
	else if (PGN_PCU_YXYC == pgn)
	{
		Set_BitFlag(pcuErroInfo, 9);
	}
	else if (PGN_PCU_ALARM == pgn)
	{
		Set_BitFlag(pcuErroInfo, 10);
	}
	else if (PGN_HEART_PCU == pgn)
	{
		Set_BitFlag(pcuErroInfo, 11);
	}
	else if (PGN_PCU_YC2 == pgn)
	{
		Set_BitFlag(pcuErroInfo, 12);
	}
}

/**
 ******************************************************************************
 * @brief       清除错误帧信息
 * @param[in]   uint32 pgn  参数组编号
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details

 * @note
 ******************************************************************************
 */
void Clr_PcuErrInfo(uint32 pgn)
{
	uint8 temp[8] = {0};

	if (PGN_YK_QSTART_ACK == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 0);
	}
	else if (PGN_YK_CSTOP_ACK == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 1);
	}
	else if (PGN_YK_SSTART_ACK == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 2);
	}
	else if (PGN_YK_SADDR_ACK == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 3);
	}
	else if (PGN_YK_OPARA_ACK == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 4);
	}
	else if (PGN_YK_STOP_ACK == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 5);
	}
	else if (PGN_PCU_FIX_SET_ACK == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 6);
	}
	else if (PGN_PCU_FIX_QUERY_ACK == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 7);
	}
	else if (PGN_PCU_WS == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 8);
	}
	else if (PGN_PCU_YXYC == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 9);
	}
	else if (PGN_PCU_ALARM == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 10);
	}
	else if (PGN_HEART_PCU == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 11);
	}
	else if (PGN_PCU_YC2 == pgn)
	{
		Clr_BitFlag(pcuErroInfo, 12);
	}
	if (memcmp(temp, pcuErroInfo, sizeof(pcuErroInfo)) == 0)
	{
		Clr_ErrType(eErrType_ComErrWithPCU);
	}
}
/**
 ******************************************************************************
 * @brief       获取PCU帧错误信息
 * @param[in]   uint32 pgn  参数组编号
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details

 * @note
 ******************************************************************************
 */
uint8 Get_PcuErrInfo(uint32 pgn)
{
	if (PGN_YK_QSTART_ACK == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 0);
	}
	else if (PGN_YK_CSTOP_ACK == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 1);
	}
	else if (PGN_YK_SSTART_ACK == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 2);
	}
	else if (PGN_YK_SADDR_ACK == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 3);
	}
	else if (PGN_YK_OPARA_ACK == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 4);
	}
	else if (PGN_YK_STOP_ACK == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 5);
	}
	else if (PGN_PCU_FIX_SET_ACK == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 6);
	}
	else if (PGN_PCU_FIX_QUERY_ACK == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 7);
	}
	else if (PGN_PCU_WS == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 8);
	}
	else if (PGN_PCU_YXYC == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 9);
	}
	else if (PGN_PCU_ALARM == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 10);
	}
	else if (PGN_HEART_PCU == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 11);
	}
	else if (PGN_PCU_EXYC == pgn)
	{
		return Get_BitFlag(pcuErroInfo, 12);
	}
	else
	{
		return FALSE;
	}
}
/**
 ******************************************************************************
 * @brief      接收的PGN的地址分配
 * @param[in]   uint32 Pgn  参数组编号
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      PCU_RECV_CTRL *返回控制地址指针
 *
 * @details     获取对应PGN的控制变量
 *
 * @note
 ******************************************************************************
 */
static PCU_RECV_CTRL *
Get_PcuRecvCtrl(uint32 pgn)
{
	switch (pgn)
	{
	case PGN_YK_QSTART_ACK:
		return &s_PcuRecvCtrl[0];
	case PGN_YK_CSTOP_ACK:
		return &s_PcuRecvCtrl[1];
	case PGN_YK_SSTART_ACK:
		return &s_PcuRecvCtrl[2];
	case PGN_YK_SADDR_ACK:
		return &s_PcuRecvCtrl[3];
	case PGN_YK_OPARA_ACK:
		return &s_PcuRecvCtrl[4];
	case PGN_YK_STOP_ACK:
		return &s_PcuRecvCtrl[5];
	case PGN_PCU_FIX_SET_ACK:
		return &s_PcuRecvCtrl[6];
	case PGN_PCU_FIX_QUERY_ACK:
		return &s_PcuRecvCtrl[7];
	case PGN_PCU_WS:
		return &s_PcuRecvCtrl[8];
	case PGN_PCU_YXYC:
		return &s_PcuRecvCtrl[9];
	case PGN_PCU_ALARM:
		return &s_PcuRecvCtrl[10];
	case PGN_HEART_PCU:
		return &s_PcuRecvCtrl[11];
	case PGN_PCU_EXYC:
		return &s_PcuRecvCtrl[12];
	case PGN_PCU_YC2:
		return &s_PcuRecvCtrl[13];
	default:
		break;
	}

	return NULL;
}

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
bool_e Get_fanState(void)
{
	return pPcuWsData->fsState;
}

/**
 ******************************************************************************
 * @brief       获取交流接触器状态
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      SWITCH_STATE
 * @details
 * @note
 ******************************************************************************
 */
SWITCH_STATE Get_ACJCQState(void)
{
	return pPcuWsData->acJCQState ? eSwitchState_ON : eSwitchState_OFF;
}

/**
 ******************************************************************************
 * @brief       获取断路器状态
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      SWITCH_STATE
 * @details
 * @note
 ******************************************************************************
 */
SWITCH_STATE Get_ACDLQState(void)
{
	return pPcuWsData->acDLQState ? eSwitchState_ON : eSwitchState_OFF;
}

/**
 ******************************************************************************
 * @brief       获取风机状态
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      uint8
 * @details
 * @note
 ******************************************************************************
 */
uint8 Get_FsState(void)
{
	return pPcuWsData->fsState;
}

/**
 ******************************************************************************
 * @brief       获取加热器状态
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      uint8
 * @details
 * @note
 ******************************************************************************
 */
uint8 Get_HeaterState(void)
{
	return pPcuWsData->heaterState;
}
/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/

/**
******************************************************************************
* @brief       获取PCU工作状态
* @param[in]
* @param[in]
* @param[out]  NONE
* @retval
*
* @details
*
* @note
******************************************************************************
*/
PCU_WORK_STAE Get_PcuWorkState(void)
{
	return pPcuWsData->workState;
}

/**
 ******************************************************************************
 * @brief       PCU告警状态
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      0正常，1告警
 * @details
 * @note
 ******************************************************************************
 */
PCU_ALARM_STATE Get_PcuAlarmState(void)
{
	return pPcuWsData->allarmState;
}
/**
 ******************************************************************************
 * @brief       PCU错误状态
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      0正常，1故障
 * @details
 * @note
 ******************************************************************************
 */
PCU_FAULT_STATE Get_PcuFaultState(void)
{
	return pPcuWsData->errState;
}

/**
 ******************************************************************************
 * @brief       PCU服务状态
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval
 * @details     0禁止，1允许，2等待
 * @note
 ******************************************************************************
 */
PCU_SERVE_STATE Get_PcuServeState(void)
{
	return pPcuWsData->serveState;
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 Get_InFanTemp(void)
{
	return pPcuYc2Data->inFanTemp; /**< 进风口温度 -50偏移 -50~200 */
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 Get_OutFanTemp(void)
{
	return pPcuYc2Data->outFanTemp; /**< 进风口温度 -50偏移 -50~200 */
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 Get_Humidity(void)
{
	return pPcuYc2Data->humidity; /**< 环境湿度 */
}
/**
******************************************************************************
* @brief       设置接收使能标记
* @param[in]   uint32 Pgn              参数组编号
* @param[in]   RECV_ENABLE  enableFlg  使能标记
* @param[out]  NONE
* @retval
*
* @details     0x55-能接收、0x00-不能接收、 0xFF-无效
*
* @note
******************************************************************************
*/
void Set_PcuRecvEnable(uint32 pgn, RECV_ENABLE enableFlg)
{
	PCU_RECV_CTRL *pRecvCtrl = NULL;

	pRecvCtrl = Get_PcuRecvCtrl(pgn);

	if (NULL == pRecvCtrl)
	{
		return;
	}

	pRecvCtrl->recvEnableFlg = enableFlg;

	return;
}

/**
******************************************************************************
* @brief       获取接收使能标记
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]   NONE
* @param[out]  NONE
* @retval      RECV_ENABLE  enableFlg  使能标记
*
* @details     0x55-已接收、0x00-未接收、 0xFF-无效
*
* @note
******************************************************************************
*/
RECV_ENABLE
Get_PcuRecvEnable(uint32 pgn)
{
	PCU_RECV_CTRL *pRecvCtrl = NULL;

	pRecvCtrl = Get_PcuRecvCtrl(pgn);

	if (NULL == pRecvCtrl)
	{
		return eRecvEnable_Null;
	}

	return pRecvCtrl->recvEnableFlg;
}

/**
******************************************************************************
* @brief       设置已接收标记
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]   RECV_FLAG    RecvFlg    接收标记
* @param[out]  NONE
* @retval      NONE
*
* @details     0x55-已接收、0x00-未接收、 0xFF-无效
*
* @note
******************************************************************************
*/
void Set_PcuRecvFlag(uint32 pgn, RECV_FLAG RecvFlg)
{
	PCU_RECV_CTRL *pRecvCtrl = NULL;

	pRecvCtrl = Get_PcuRecvCtrl(pgn);

	if (NULL == pRecvCtrl)
	{
		return;
	}

	pRecvCtrl->recvFlg = RecvFlg;

	return;
}

/**
******************************************************************************
* @brief       获取已接收标记
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]   NONE
* @param[out]  NONE
* @retval      RECV_FLAG    RecvFlg    接收标记
*
* @details     0x55-已接收、0x00-未接收、 0xFF-无效
*
* @note
******************************************************************************
*/
RECV_FLAG
Get_PcuRecvFlag(uint32 pgn)
{
	PCU_RECV_CTRL *pRecvCtrl = NULL;

	pRecvCtrl = Get_PcuRecvCtrl(pgn);

	if (NULL == pRecvCtrl)
	{
		return eRecvFlag_No;
	}

	return pRecvCtrl->recvFlg;
}

/**
******************************************************************************
* @brief       设置接收计时器使能
* @param[in]   uint32 Pgn                     参数组编号
* @param[in]   TIMER_ENABLE enableFlg    接收计时使能标记
* @param[out]  NONE
* @retval
*
* @details     0x55-计时、0x00-不计时、 0xFF-无效
*
* @note
******************************************************************************
*/
void Set_PcuRecvTimerEnable(uint32 pgn, TIMER_ENABLE enableFlg)
{
	PCU_RECV_CTRL *pRecvCtrl = NULL;

	pRecvCtrl = Get_PcuRecvCtrl(pgn);

	if (NULL == pRecvCtrl)
	{
		return;
	}

	pRecvCtrl->recvTimerEnableFlg = enableFlg;

	return;
}

/**
******************************************************************************
* @brief       获取接收计时器使能标记
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]   NONE
* @param[out]  NONE
* @retval      TIMER_ENABLE           接收计时器使能标记
*
* @details     0x55-计时、0x00-未接收、 0xFF-无效
* @note
******************************************************************************
*/
TIMER_ENABLE
Get_PcuRecvTimerEnable(uint32 pgn)
{
	PCU_RECV_CTRL *pRecvCtrl = NULL;

	pRecvCtrl = Get_PcuRecvCtrl(pgn);

	if (NULL == pRecvCtrl)
	{
		return eTimerEnable_Null;
	}

	return pRecvCtrl->recvTimerEnableFlg;
}

/**
******************************************************************************
* @brief       初始化接收计时器
* @param[in]   uint32 Pgn                     参数组编号
* @param[in]   int32  countValue              计时器赋值
* @param[out]  NONE
* @retval      NONE
* @details
* @note
******************************************************************************
*/
void Set_PcuRecvTimer(uint32 pgn, int32 countValue)
{
	PCU_RECV_CTRL *pRecvCtrl = NULL;

	pRecvCtrl = Get_PcuRecvCtrl(pgn);

	if (NULL == pRecvCtrl)
	{
		return;
	}

	pRecvCtrl->recvTimer = countValue;

	return;
}

/**
******************************************************************************
* @brief       接收计时器自增
* @param[in]   uint32 Pgn                参数组编号
* @param[in]   NONE
* @param[out]  NONE
* @retval
*
* @details
*
* @note
******************************************************************************
*/
void Set_PcuRecvTimerInc(uint32 pgn)
{
	PCU_RECV_CTRL *pRecvCtrl = NULL;

	pRecvCtrl = Get_PcuRecvCtrl(pgn);

	if (NULL == pRecvCtrl)
	{
		return;
	}

	pRecvCtrl->recvTimer++;

	return;
}

/**
******************************************************************************
* @brief       获取接收计时器值
* @param[in]   uint32 Pgn                     参数组编号
* @param[in]   NONE
* @param[out]  NONE
* @retval
* @details
* @note
******************************************************************************
*/
int32 Get_PcuRecvTimer(uint32 pgn)
{
	PCU_RECV_CTRL *pRecvCtrl = NULL;

	pRecvCtrl = Get_PcuRecvCtrl(pgn);

	if (NULL == pRecvCtrl)
	{
		return 0;
	}

	return pRecvCtrl->recvTimer;
}

static bool_e
Check_PcuFrameAddr(CAN_ID *pCanID)
{
	if (Get_CcuAddr() == pCanID->ps || ADDR_CCU_BOARD == pCanID->ps)
	{
		if (ADDR_PCU_BOARD == pCanID->sa)
		{
			return TRUE;
		}

		if (TRUE == Get_PcuAddrRecvSuccFlag())
		{
			if (Get_PcuAddr() == pCanID->sa)
			{
				return TRUE;
			}
		}
		else
		{
			if (pCanID->sa >= ADDR_PCU_MIN && pCanID->sa <= ADDR_PCU_MAX)
			{
				Set_PcuAddr(pCanID->sa);
				Set_PcuAddrRecvSuccFlag(TRUE);
				return TRUE;
			}
		}
	}
	return FALSE;
}
/**
******************************************************************************
* @brief       pcu合法帧检验
* @param[in]   uint32 Pgn             参数组编号
* @param[in]   NONE
* @param[out]  NONE
* @retval
* @details     判断的比较简单，主要是判地址
* @note
******************************************************************************
*/
static uint8 *
Check_PcuFrame(uint8 *buf, uint16 TotolLen)
{
	CAN_ID *pCanID = NULL;
	uint16 index = 0;

	while (TotolLen - index >= 4)
	{
		pCanID = (CAN_ID *)(buf + index);

		if (Check_PcuFrameAddr(pCanID))
		{
			return (uint8 *)pCanID;
		}

		index++;
	}

	return (buf + index);
}

/**
******************************************************************************
* @brief       Pcu多帧数据处理
* @param[in]   CAN_DATA *pCanData
* @param[in]   NONE
* @param[out]  NONE
* @retval      NONE
* @details
* @note
******************************************************************************
*/
static uint8 pcuMulFrame_Decode(CAN_DATA *pCanData, PCU_CAN_MULFRAME_RECV *pMulFrame)
{
	if (pCanData->dataBuf[0] == 1)
	{
		pMulFrame->canId = pCanData->canId;
		pMulFrame->frameCnt = pCanData->dataBuf[1];
		pMulFrame->dataLen = *(uint16 *)&pCanData->dataBuf[2];
		if (pMulFrame->dataLen <= 2) /**有效数据长度小于等于2时在一帧内完成*/
		{
			memcpy(pMulFrame->dataBuf, &pCanData->dataBuf[4], pMulFrame->dataLen);
			pMulFrame->sumCheck = *(uint16 *)&pCanData->dataBuf[4 + pMulFrame->dataLen];
			pMulFrame->dataCnt = pMulFrame->dataLen;
			pMulFrame->sumRecFlag = 2;
			trace(TR_PCU_PROCESS, "PCU分帧接受完成1 CAN_ID = %X\n", pMulFrame->canId);
		}
		else if (pMulFrame->dataLen == 3)
		{
			pMulFrame->dataCnt = pMulFrame->dataLen;
			memcpy(pMulFrame->dataBuf, &pCanData->dataBuf[4], 3);
			pMulFrame->sumCheck = pCanData->dataBuf[7];
			pMulFrame->sumRecFlag = 1;
			return ePcuMulFrame_NotFinish;
		}
		else
		{
			pMulFrame->dataCnt = 4;
			memcpy(pMulFrame->dataBuf, &pCanData->dataBuf[4], 4);
			pMulFrame->sumRecFlag = 0;
			return ePcuMulFrame_NotFinish;
		}
	}
	else
	{
		if (pMulFrame->dataLen >= pMulFrame->dataCnt + 7)
		{
			memcpy(&pMulFrame->dataBuf[pMulFrame->dataCnt], &pCanData->dataBuf[1], 7);
			pMulFrame->dataCnt += 7;
			pMulFrame->sumRecFlag = 0;
			return ePcuMulFrame_NotFinish;
		}
		else if (pMulFrame->dataLen == pMulFrame->dataCnt + 6)
		{
			memcpy(&pMulFrame->dataBuf[pMulFrame->dataCnt], &pCanData->dataBuf[1], 6);
			pMulFrame->dataCnt += 6;
			pMulFrame->sumCheck = pCanData->dataBuf[7];
			pMulFrame->sumRecFlag = 1;
			return ePcuMulFrame_NotFinish;
		}
		else // if(pMulFrame->dataLen != pMulFrame->dataCnt)
		{
			memcpy(&pMulFrame->dataBuf[pMulFrame->dataCnt], &pCanData->dataBuf[1], pMulFrame->dataLen - pMulFrame->dataCnt);
			pMulFrame->sumCheck = *(uint16 *)&pCanData->dataBuf[1 + pMulFrame->dataLen - pMulFrame->dataCnt];
			pMulFrame->dataCnt = pMulFrame->dataLen;
			pMulFrame->sumRecFlag = 2;
			trace(TR_PCU_PROCESS, "PCU分帧接受完成2 CAN_ID = %X\n", pMulFrame->canId);
		}
		// 这两个判断永远不会进0，1在赋值的时候就return
		//		if(0 == pMulFrame->sumRecFlag)
		//		{
		//			pMulFrame->sumCheck = *(uint16 *)&pCanData->dataBuf[1];
		//			pMulFrame->sumRecFlag = 2;
		//			trace(TR_PCU_PROCESS, "PCU分帧接受完成3 CAN_ID = %X\n",pMulFrame->canId);
		//		}
		//		else if(1 == pMulFrame->sumRecFlag)
		//		{
		//			pMulFrame->sumCheck |= pCanData->dataBuf[1]<<8;
		//			pMulFrame->sumRecFlag = 2;
		//			trace(TR_PCU_PROCESS, "PCU分帧接受完成4 CAN_ID = %X\n",pMulFrame->canId);
		//		}
	}
	if ((pMulFrame->sumRecFlag == 2) && (pMulFrame->dataCnt == pMulFrame->dataLen))
	{
		if (pMulFrame->sumCheck == Get_Cs16(&pMulFrame->frameCnt, pMulFrame->dataLen + 3))
		{
			trace(TR_PCU_PROCESS, "PCU分帧校验合格 CAN_ID = %X\n", pMulFrame->canId);
			return ePcuMulFrame_Check_OK;
		}
		else
		{
			trace(TR_PCU_PROCESS, "PCU分帧校验错误  CAN_ID = %X\n", pMulFrame->canId);
			memset(pMulFrame, 0, sizeof(PCU_CAN_MULFRAME_RECV));
			return ePcuMulFrame_Check_ERR;
		}
	}
	return ePcuMulFrame_Check_ERR;
}

/**
******************************************************************************
* @brief       Pcu解码函数
* @param[in]   CAN_DATA *pCanData
* @param[in]   NONE
* @param[out]  NONE
* @retval      NONE
* @details
* @note
******************************************************************************
*/
static void Pcu_Decode(CAN_DATA *pCanData)
{
	const PCU_RECV_DEAL *pRecvDeal = NULL;
	uint8 index = 0, pcuMulFrameRecState = ePcuMulFrame_NotFinish;
	static PCU_CAN_MULFRAME_RECV pcuMulFrame = {0};
#if 1 //-todo-xugang
	CAN_ID *pId = NULL;

	for (index = 0; index < FCNT(PCU_RECV_DEAL_TABLE); index++)
	{
		pRecvDeal = &PCU_RECV_DEAL_TABLE[index];

		pId = (CAN_ID *)&pCanData->canId;
		if ((uint8)(pRecvDeal->pgn >> 8) != pId->pf)
		{
			continue;
		}

		if (0 != pId->dp || 0 != pId->res2)
		{
			return;
		}

		if (pRecvDeal->prio != pId->prio)
		{
			return;
		}
#else
	for (index = 0; index < FCNT(PCU_RECV_DEAL_TABLE); index++)
	{
		pRecvDeal = &PCU_RECV_DEAL_TABLE[index];

		if ((uint8)(pRecvDeal->pgn >> 8) != pCanData->strCanId.pf)
		{
			continue;
		}

		if (0 != pCanData->strCanId.dp || 0 != pCanData->strCanId.res2)
		{
			return;
		}

		if (pRecvDeal->prio != pCanData->strCanId.prio)
		{
			return;
		}
#endif
		if (eRecvEnable_On != Get_PcuRecvEnable(pRecvDeal->pgn))
		{
			continue;
		}
		if (NULL != pRecvDeal->pDecodeFunc)
		{
			if (pRecvDeal->mulFrameFlag) /**<多帧处理*/
			{

				pcuMulFrameRecState = pcuMulFrame_Decode(pCanData, &pcuMulFrame);
				if (pcuMulFrameRecState != ePcuMulFrame_NotFinish)
				{
					if ((ePcuMulFrame_Check_OK == pcuMulFrameRecState) && (TRUE == pRecvDeal->pDecodeFunc(
																					   pcuMulFrame.dataBuf,
																					   pcuMulFrame.dataLen)))
					{
						Deal_PcuLogPackage(pRecvDeal->pgn, pcuMulFrame.dataBuf, pcuMulFrame.dataLen); // 接收PCU数据log
						Set_PcuRecvFlag(pRecvDeal->pgn, eRecvFlag_Yes);
						Set_PcuRecvTimer(pRecvDeal->pgn, 0);
						Clr_PcuErrInfo(pRecvDeal->pgn);
					}
					memset(&pcuMulFrame, 0, sizeof(pcuMulFrame));
				}
			}
			else
			{
				if (TRUE == pRecvDeal->pDecodeFunc(pCanData->dataBuf, pCanData->dataLen))
				{
					Deal_PcuLogPackage(pRecvDeal->pgn, pcuMulFrame.dataBuf, pcuMulFrame.dataLen); // 接收PCU数据log
					Set_PcuRecvFlag(pRecvDeal->pgn, eRecvFlag_Yes);
					Set_PcuRecvTimer(pRecvDeal->pgn, 0);
					Clr_PcuErrInfo(pRecvDeal->pgn);
				}
			}
		}
	}

	return;
}

static void Pcu_RecvByCpcu()
{
	uint8 res = 0;
	STWORKBUF the_data;
	CAN_DATA *pCan = NULL;
	while (1)
	{
		memset(&the_data, 0, sizeof(STWORKBUF));
		res = Cpcu_RecvCcu(the_data.ucDataBuf, (uint32 *)&the_data.iDataLen, 1);
		if (MSG_ERR_NONE == res)
		{
			if (the_data.iDataLen == sizeof(CAN_DATA))
			{
				pCan = (CAN_DATA *)the_data.ucDataBuf;
				if (Check_PcuFrameAddr((CAN_ID *)&pCan->canId))
				{
					trace(TR_CH3, "<R3: %X>  ", pCan->canId);
					trace_buf(TR_CH3, pCan->dataBuf, 8);
					Pcu_Decode(pCan);
				}
			}
		}
		else
		{
			break;
		}
	}
}
/**
******************************************************************************
* @brief       Pcu接收服务
* @param[in]   int fd
* @param[in]   NONE
* @param[out]  NONE
* @retval      NONE
* @details
* @note        因为接收到的帧会断，所以需要拼帧
******************************************************************************
*/
void Pcu_RecvServer(void)
{
	CAN_DATA strData;
	uint8 err = 0;
	uint8 *pStart = NULL;
	uint8 buf[64] = {0};
	uint8 dataLen = 0;
	uint8 dealLen = 0;
	uint8 maxReadlen = 0;

	if (MODE_CPCU == sysGetMode())
	{
		Pcu_RecvByCpcu();
	}
	else
	{
		can_rx_cycle(PCU_CHAN); // CAN 3接收回调函数,仅针对407平台有效.
		maxReadlen = ((sizeof(s_backupBuf) - s_backupLen) >= sizeof(buf)) ? sizeof(buf) : (sizeof(s_backupBuf) - s_backupLen);
		dataLen = can_recv(PCU_CHAN, buf, maxReadlen, &err);
		memcpy(s_backupBuf + s_backupLen, buf, dataLen);
		s_backupLen += dataLen;

		while (s_backupLen >= 16)
		{
			pStart = Check_PcuFrame(s_backupBuf, s_backupLen);
			dealLen = (uint8 *)pStart - s_backupBuf;
			s_backupLen = s_backupLen - dealLen;
			memmove(s_backupBuf, s_backupBuf + dealLen, s_backupLen);

			if (s_backupLen < 16)
			{
				return;
			}

			memset(&strData, 0x00, sizeof(strData));
#if 1 //-todo-xugang
			strData.canId = FourUint8ToUint32(s_backupBuf);
#else
			strData.strCanId.canId = FourUint8ToUint32(s_backupBuf);
#endif
			strData.dataLen = 8;
			memcpy(strData.dataBuf, s_backupBuf + 8, 8);

			trace(TR_CH3, "<R3: %X>  ", strData.canId);
			trace_buf(TR_CH3, strData.dataBuf, 8);

			Pcu_Decode(&strData);
			dealLen = 16;
			s_backupLen = s_backupLen - dealLen;
			memmove(s_backupBuf, s_backupBuf + dealLen, s_backupLen);
		}
	}
}

/**
 ******************************************************************************
 * @brief       Pcu接收超时管理
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note
 ******************************************************************************
 */
void Pcu_RecvTimerManage(void)
{
	const PCU_RECV_DEAL *pRecvDeal = NULL;
	uint8 index = 0;

	for (index = 0; index < FCNT(PCU_RECV_DEAL_TABLE); index++)
	{
		pRecvDeal = &PCU_RECV_DEAL_TABLE[index];
		if (eRecvEnable_On != Get_PcuRecvEnable(pRecvDeal->pgn))
		{
			continue;
		}
		if (eTimerEnable_On != Get_PcuRecvTimerEnable(pRecvDeal->pgn))
		{
			continue;
		}
		if (0xFFFF == pRecvDeal->pOverTime())
		{
			continue;
		}
		else
		{
			Set_PcuRecvTimerInc(pRecvDeal->pgn);
		}
		if (Get_FaultMask(eErrType_ComErrWithPCU) || (Get_PcuRecvTimer(pRecvDeal->pgn) > pRecvDeal->pOverTime()))
		{
			if (eActFlag_On == Get_ChargeActFlag()) /**<工作状态下只判遥信遥测*/
			{
				if (Get_FaultMask(eErrType_ComErrWithPCU) || ((PGN_PCU_WS == pRecvDeal->pgn) || (PGN_PCU_YXYC == pRecvDeal->pgn)))
				{
					if (Get_FaultMask(eErrType_ComErrWithPCU))
					{
						Set_PcuErrInfo(PGN_PCU_WS);
					}
					else
					{
						Set_PcuErrInfo(pRecvDeal->pgn);
					}
					if (TRUE != Check_ErrType(eErrType_ComErrWithPCU))
					{
						Set_ErrType(eErrType_ComErrWithPCU);
						trace(TR_PCU_PROCESS, "超时PGN = %X\n", pRecvDeal->pgn);
					}
				}
			}
			else /**<非工作状态下判心跳*/
			{
				if (PGN_HEART_PCU == pRecvDeal->pgn)
				{
					Set_PcuErrInfo(pRecvDeal->pgn);
					if (TRUE != Check_ErrType(eErrType_ComErrWithPCU))
					{
						Set_ErrType(eErrType_ComErrWithPCU);
						trace(TR_PCU_PROCESS, "超时PGN = %X\n", pRecvDeal->pgn);
					}
				}
			}
			if (FALSE == pRecvDeal->stopRecvFlag)
			{
				Set_PcuRecvEnable(pRecvDeal->pgn, eRecvEnable_Off);
				Set_PcuRecvTimerEnable(pRecvDeal->pgn, eTimerEnable_Off);
			}
		}
	}
}

/*----------------------------pcuRecvCtrl.c--------------------------------*/
