/**
 ******************************************************************************
 * @file       bmsDll.h
 * @brief      API include file of bmsDll.h.
 * @details    This file including all API functions's declare of bmsDll.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */

#ifndef __BMS_DLL_H__
#define __BMS_DLL_H__

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <can.h>
#include "bmsMain.h"
#include <ccu\lib\ccuLib.h>
/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/
/**< 节点地址                        */
#define DLL_SRC_ADDR ADDR_CHARGER /**< 源地址                         */
#define DLL_DEST_ADDR ADDR_BMS    /**< 目的地址                     */

/**< CAN通道                         */
#define DLL_CHAN CAN_2

/**< 完整数据帧最大长度     */
#define DLL_MAX_FRAME_LEN 200

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
/**< CAN完整帧结构（含处理后分帧）     */
typedef struct __CAN_FRAME_STRU__
{
    CAN_ID canId;
    uint8 msgPGN[3];
    uint8 dataBuf[DLL_MAX_FRAME_LEN];
    uint16 dataLen;
} CAN_FRAME;

/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
void Dll_Init(void);

void Dll_RecvServer(void);

void Dll_OverTimer(void);

/*--------------------------End of bmsDll.h----------------------------*/
#endif
