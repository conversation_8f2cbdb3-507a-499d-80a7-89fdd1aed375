/**
 ******************************************************************************
 * @file       tcuRecvCtrl.h
 * @brief      API include file of tcuRecvCtrl.h.
 * @details    This file including all API functions's declare of tcuRecvCtrl.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */

#ifndef __TCU_RECV_CTRL_H__
#define __TCU_RECV_CTRL_H__

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <types.h>
#include <ccu\lib\ccuLib.h>

/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
/* NONE */
#pragma pack(push)
#pragma pack(1) // 设定为1字节对齐
typedef struct
{
    uint32 canId;        /**<帧ID*/
    uint8 frameCnt;      /**<报文总帧数*/
    uint16 dataLen;      /**<报问总长度*/
    uint8 dataBuf[128];  /**<数据缓冲区*/
    uint16 dataCnt;      /**<当前接受数据长度*/
    uint16 sumCheck;     /**<累加和校验*/
    uint8 sumRecFlag;    /**<校验接受完成 0表示未接受，1表示接受一半，2表示接受完成*/
} TCU_CAN_MULFRAME_RECV; /**<PCU多帧数据结构*/
#pragma pack(pop)
/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
void Set_TcuRecvFlag(uint32 pgn, RECV_FLAG RecvFlg);

RECV_FLAG Get_TcuRecvFlag(uint32 pgn);

void Set_TcuRecvTimerEnable(uint32 pgn, TIMER_ENABLE enableFlg);

TIMER_ENABLE Get_TcuRecvTimerEnable(uint32 pgn);

void Set_TcuRecvTimer(uint32 pgn, int32 countValue);

int32 Get_TcuRecvTimer(uint32 pgn);

void Tcu_RecvServer(void);

void Tcu_RecvTimerManage(void);

TCU_RECV_CTRL *Get_TcuRecvCtrl(uint32 pgn);

#endif //__TCU_RECV_CTRL_H__

/*--------------------------End of tcuRecvCtrl.h----------------------------*/
