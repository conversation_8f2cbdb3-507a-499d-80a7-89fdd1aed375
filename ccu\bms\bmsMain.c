/**
 ******************************************************************************
 * @file      bmsMain.c
 * @brief     C Source file of bmsMain.c.
 * @details   This file including all API functions's
 *            implement of bmsMain.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <can.h>
#include <string.h>
#include <taskLib.h>
#include <dmnLib.h>
#include <stdlib.h>
#include <stdio.h>
#include <sxlib.h>
#include <trace.h>

#include "bmsMain.h"
#include "bmsDll.h"
#include "bmsSendCtrl.h"
#include "bmsRecvCtrl.h"

#include <bms.h>
#include <ccu\charge\ccuChargeMain.h>
#include <ccu\bsn\deviceState.h>
#include <test.h>
#include <ccu\para\para.h>
#include <maths.h>
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
typedef void (*PdealFunc)(void);
typedef struct BMS_STAGE_DEAL_STRU
{
    uint8 stage;
    PdealFunc func;
} BMS_STAGE_DEAL;
/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */
/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
BMS_DATA bmsData; /* BMS数据*/
BMS_CTRL bmsCtrl; /* BMS控制*/

extern uint8 isBHMFirstData;
/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
/////////////////////////////////////////////////////////////////////////////////
void Bms_Init(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    BMS_DATA *pBmsData = &bmsData;

    memset(pBmsCtrl, 0x00, sizeof(BMS_CTRL));
    if (Get_CcuCfgParaEuropeEnable()) // 欧标增加
    {
        memset(&pBmsData->strBRM, 0x00, sizeof(BMS_DATA) - MEMBER_SIZE(BMS_DATA, strBHM));
        memset(&pBmsData->strBHM.highlestTotalVoltage[0], 0x00, sizeof(BHM_DATA) - MEMBER_SIZE(BHM_DATA, plcCpStatus));
        //        memset(&pBmsData->strBHM.highlestTotalVoltage[0], 0x00, MOFFSET(BHM_DATA,plcCpStatus));
        printf("=======[%s]======[%d]======[%d]=====\n", __FUNCTION__, __LINE__, pBmsData->strBHM.plc_CpStatus.CpStatus);
    }
    else
    {
        memset(pBmsData, 0x00, sizeof(BMS_DATA));
    }
    // 默认为启动
    pBmsData->strBSM.strBatteryCtrl.chargeAllowFlg = enumAllowFlag_Allow;
    pBmsData->strBSM.strBatteryCtrl.reconnect = enumAllowFlag_Allow;
    for (uint8 i = 0; i < 2; i++)
    {
        Set_InitPrintf(i, FALSE);
    }
    isBHMFirstData = 1; // 第一次接收BHM报文标记
    return;
}

uint8 Get_BmsStage(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    return pBmsCtrl->bmsStage;
}

void Set_BmsStage(uint8 stage)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    if (stage != pBmsCtrl->bmsStage)
    {
        trace(TR_BMS_PROCESS, "BMS阶段切换  当前阶段:%d   前一阶段:%d\n", stage,
              pBmsCtrl->bmsStage);
        pBmsCtrl->bmsStage = stage;
    }
}

/**
 ******************************************************************************
 * @brief      Bms_FreeDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details     1.检测BMS服务启动标记
 *              2.初始化处理
 *              3.置bms阶段为握手阶段
 * @note         空闲阶段处理
 ******************************************************************************
 */
static void
Bms_FreeDeal(void)
{
    trace(TR_BMS_PROCESS, "Bms_FreeDeal()\n");
    can_init(BMS_CHAN, CAN_BAD_250);
    Bms_Init();
    trace(TR_BMS_PROCESS, "Init_OK()\n");
    Set_BmsStage(BMS_STAGE_SHAKEHAND);
    return;
}

/**
 ******************************************************************************
 * @brief       握手阶段处理
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details     1.检测停机标记
 *              2.启动CHM发送（执行一次）
 *
 * @note
 ******************************************************************************
 */
extern BOOL Is_DualCharge_Responder(int channel);

static void
Bms_ShakeHandDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    static uint32 start_wait_time = 0;
    // 是否外部需要停机

    if (Is_DualCharge_Responder(DUAL_CHARGE_CHANNEL_01))
    {
        return;
    }
    if (Get_CcuCfgParaEuropeEnable()) // 欧标增加
    {
        if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CHM))
        {
            Set_BmsSendRemainTimer(BMS_PGN_CHM, 0xFFFF);
            Set_BmsLastSendTimer(BMS_PGN_CHM, 0xFFFF);
            return;
        }
        if (Get_EcPlcAagVal() != 0)
        {
            Set_BmsStage(BMS_STAGE_RECOGNIZE);
        }
    }
    else
    {
        if (eActFlag_Off == Get_ChargeActFlag())
        {
            Stop_BMS(TRUE);
            return;
        }
        // 2011国标，无BHM，直接等绝缘泄放完成
        if (Get_BMS_Ver() != 0xA5)
        {
            // 是否通信超时
            if (eComState_Normal != pBmsCtrl->comState)
            {
                Bms_OverTimeDeal();
                return;
            }
            // CHM的发送使能是否打开
            if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CHM))
            {
                Set_BmsSendRemainTimer(BMS_PGN_CHM, 0xFFFF);
                Set_BmsLastSendTimer(BMS_PGN_CHM, 0xFFFF);
                return;
            }
        }
        // 第一次泄放完成
        if (TRUE == Get_Release01SuccFlag())
        {
            // 检查是否有挂起的双充启动请求
            if (TRUE == Get_DualChargeImdInitiatedFlag())
            {
                // 有启动请求，暂不进入下一阶段，等待双充系统处理
                if (start_wait_time == 0)
                {
                    start_wait_time = tickGet();
                }
                if (TRUE == Is_DualCharge_Initiator(DUAL_CHARGE_CHANNEL_01))
                {
                    if (Check_Is_Send2560(FALSE, TRUE))
                    {
                        start_wait_time = 0;
                        Check_Is_Send2560(TRUE, FALSE);
                        Set_DualChargeImdInitiatedFlag(FALSE);
                        Set_BmsStage(BMS_STAGE_RECOGNIZE);
                    }
                }

                // 等待超时保护（例如5秒）
                if (abs(tickGet() - start_wait_time) > 60 * sysClkRateGet()) // 45
                {
                    trace(TR_BMS_PROCESS, "等待双充启动超时，强制进入下一阶段\n");
                    start_wait_time = 0;
                    Set_DualChargeImdInitiatedFlag(FALSE);
                    Set_BmsStage(BMS_STAGE_RECOGNIZE);
                }
                // 继续等待
            }

            else
            {
                Set_BmsStage(BMS_STAGE_RECOGNIZE);
            }
        }
    }
    return;
}

/**
 ******************************************************************************
 * @brief      Bms_RecognizeDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note        辨识阶段处理
 ******************************************************************************
 */
static void
Bms_RecognizeDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    isBHMFirstData = 1;
    Set_BmsSendFlg(BMS_PGN_CST, eSendFlag_No);
    Set_BmsSendRemainTimer(BMS_PGN_CST, 0);
    // 是否外部需要停机
    if (eActFlag_Off == Get_ChargeActFlag() && !Get_CcuCfgParaEuropeEnable())
    {
        Set_BmsStage(BMS_STAGE_STOP);
        Stop_BMS(TRUE);
        return;
    }

    if (eComState_Normal != pBmsCtrl->comState)
    {
        Bms_OverTimeDeal();
        return;
    }
    // CRM的发送使能是否打开

    if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CRM))
    {
        Set_BmsSendRemainTimer(BMS_PGN_CRM, 0xFFFF);
        Set_BmsLastSendTimer(BMS_PGN_CRM, 0xFFFF);
        pBmsCtrl->spn_2560 = 0x00;

        Set_BmsSendRemainTimer(BMS_PGN_CHM, 0);
    }
    if (Get_DualChargeEnableStatus())
    {
        if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CFC))
        {
            Set_BmsSendRemainTimer(BMS_PGN_CFC, 0xFFFF);
            Set_BmsLastSendTimer(BMS_PGN_CFC, 0xFFFF);
            pBmsCtrl->Dual_Cfc = DUAL_ACK_00;
        }
    }
    pBmsCtrl->spn_2830 = 0x00;
    pBmsCtrl->lastBROState = 0x00; /**不清除掉会导致重连时数据终止*/
    return;
}

/**
 ******************************************************************************
 * @brief      Bms_ParaConfigDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note        参数配置阶段处理
 ******************************************************************************
 */
static void
Bms_ParaConfigDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    Set_BmsSendFlg(BMS_PGN_CST, eSendFlag_No);
    Set_BmsSendRemainTimer(BMS_PGN_CST, 0);

    // 是否外部需要停机

    if (eActFlag_Off == Get_ChargeActFlag())
    {
        if (Get_CcuCfgParaEuropeEnable())
        {
            Set_BmsSendRemainTimer(BMS_PGN_CRO, 0x00);
            Set_BmsStage(BMS_STAGE_STOP);
        }
        else
        {
            Set_BmsStage(BMS_STAGE_STOP);
            Stop_BMS(TRUE);
            Set_BmsRecvEnable(BMS_PGN_BRO, eRecvEnable_Off);

            Set_BmsRecvEnable(BMS_PGN_BRO_AA, eRecvEnable_Off);
            Set_BmsRecvTimerEnable(BMS_PGN_BRO_AA, eTimerEnable_Off);
            return;
        }
    }

    if (eComState_Normal != pBmsCtrl->comState)
    {
        Bms_OverTimeDeal();
        return;
    }
    //    deviceState = Get_DeviceState();
    //    if ((DEVICE_STATE_MAJOR_FAULT == deviceState)
    //            || (DEVICE_STATE_FAULT == deviceState))
    //    {
    //        Set_BmsStage(BMS_STAGE_STOP);
    //        return;
    //    }
    if (Get_CcuCfgParaEuropeEnable())
    {
        if (Get_ImdSuccFlag() && Get_K1K2InsideVol() < 600)
        {
            if (Is_DualCharge_MasterMode(DUAL_CHARGE_CHANNEL_01))
            {
                if (TRUE == Get_DUALCHARGE_CTRL_pre_bypass())
                {
                    pBmsCtrl->spn_2830 = 0xAA;
                }
            }
            else
            {
                pBmsCtrl->spn_2830 = 0xAA;
            }
            if (Check_ErrType(eErrType_ImdAlarm))
            {
                pBmsCtrl->evseIsolationStatus = 0x02;
            }
            else if (Check_ErrType(eErrType_ImdTimeOut) ||
                     Check_ErrType(eErrType_ImdErr))
            {
                pBmsCtrl->evseIsolationStatus = 0x03;
            }
            else
            {
                pBmsCtrl->evseIsolationStatus = 0x01;
            }
        }
    }
    else
    {
        if (TRUE == Get_PreChargeSuccFlag())
        {
            pBmsCtrl->spn_2830 = 0xAA; /**<置CRO发送AA*/
        }
    }
}

/**
 ******************************************************************************
 * @brief       Bms_ChargingDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note        充电阶段处理
 ******************************************************************************
 */
static void
Bms_ChargingDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    Set_BmsSendFlg(BMS_PGN_CST, eSendFlag_No);
    Set_BmsSendRemainTimer(BMS_PGN_CST, 0);

    // 是否外部需要停机
    if (eActFlag_Off == Get_ChargeActFlag())
    {
        Set_BmsStage(BMS_STAGE_STOP);
        return;
    }

    if (Is_DualCharge_Responder(DUAL_CHARGE_CHANNEL_01))
    {
        Stop_BMS(TRUE);
        Set_BmsStage(BMS_STAGE_FREE);
        return;
    }

    if (eComState_Normal != pBmsCtrl->comState)
    {
        Bms_OverTimeDeal();
        return;
    }
}

/**
 ******************************************************************************
 * @brief       Bms_StopDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note        充电结束阶段处理
 ******************************************************************************
 */
static void
Bms_StopDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    Set_BmsSendRemainTimer(BMS_PGN_CRO, 0x00);
    if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BEM))
    {
        Set_BmsRecvEnable(BMS_PGN_BEM, eRecvEnable_On);
        Set_BmsRecvFlag(BMS_PGN_BEM, eRecvFlag_No);
    }

    if (eComState_Normal != pBmsCtrl->comState)
    {
        Bms_OverTimeDeal();
    }

    if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CST))
    {
        if (eSendFlag_Yes != Get_BmsSendFlg(BMS_PGN_CST))
        {
            /* 已经发送过了，无需重复开启，等超时停止即可 */
            Set_BmsSendRemainTimer(BMS_PGN_CST, 10 * sysClkRateGet());
            Set_BmsLastSendTimer(BMS_PGN_CST, 0xFFFF);
            Set_BmsStartTimer(BMS_PGN_CST);
        }
        Set_BmsRecvEnable(BMS_PGN_BCL, eRecvEnable_Off);
        Set_BmsRecvTimerEnable(BMS_PGN_BCL, eTimerEnable_Off);

        Set_BmsRecvEnable(BMS_PGN_BCS, eRecvEnable_Off);
        Set_BmsRecvTimerEnable(BMS_PGN_BCS, eTimerEnable_Off);

        Set_BmsRecvEnable(BMS_PGN_BSM, eRecvEnable_Off);
        Set_BmsRecvEnable(BMS_PGN_BMV, eRecvEnable_Off);
        Set_BmsRecvEnable(BMS_PGN_BMT, eRecvEnable_Off);
        Set_BmsRecvEnable(BMS_PGN_BSP, eRecvEnable_Off);

        Set_BmsSendRemainTimer(BMS_PGN_CCS, 0);
        if (0x0000 == Get_BmsSendRemainTimer(BMS_PGN_CST))
        {
            Set_BmsStage(BMS_STAGE_STOPFINISH);
        }
    }
}

/**
 ******************************************************************************
 * @brief       Bms_StopFinishDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note        充电结束完成阶段处理
 ******************************************************************************
 */
static void
Bms_StopFinishDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (eComState_Normal != pBmsCtrl->comState)
    {
        Bms_OverTimeDeal();
    }
    if (Get_CcuCfgParaEuropeEnable())
    {
        Set_BmsStage(BMS_STAGE_FREE);
    }
    else
    {
        Stop_BMS(TRUE);
    }
}

const BMS_STAGE_DEAL BMS_STAGE_DEAL_TABLE[] =
    {
        {BMS_STAGE_FREE, Bms_FreeDeal},
        {BMS_STAGE_SHAKEHAND, Bms_ShakeHandDeal},
        {BMS_STAGE_RECOGNIZE, Bms_RecognizeDeal},
        {BMS_STAGE_PARACONFIG, Bms_ParaConfigDeal},
        {BMS_STAGE_CHARGING, Bms_ChargingDeal},
        {BMS_STAGE_STOP, Bms_StopDeal},
        {BMS_STAGE_STOPFINISH, Bms_StopFinishDeal}};

/**
 ******************************************************************************
 * @brief       Bms_StageManage
 * @param[in]   None
 * @param[out]  None
 * @retval
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
static void
Bms_StageManage(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    const BMS_STAGE_DEAL *pStageDeal = NULL;
    uint8 index = 0;

    for (index = 0; index < FCNT(BMS_STAGE_DEAL_TABLE); index++)
    {
        pStageDeal = &BMS_STAGE_DEAL_TABLE[index];

        if (Get_BmsStage() == pStageDeal->stage)
        {
            if (NULL != pStageDeal->func)
            {
                pStageDeal->func();
            }
        }
    }
}

/**
 ******************************************************************************
 * @brief      Bms_Task
 * @param[in]   None
 * @param[out]  None
 * @retval
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
void Bms_Task(void)
{
    uint32 dmnTick = 0;
    Bms_Init();
    dmnTaskRegister();

    FOREVER
    {
        if (abs(tickGet() - dmnTick) >= sysClkRateGet())
        {
            dmnTick = tickGet();
            dmnTaskSigned();
        }

        taskDelay(BMS_CALL_CYCLE);

        TESTLISTEN; /**< 板级测试模式监听 */

        if (TRUE == Get_StopFinishFlag() && !Get_CcuCfgParaEuropeEnable())
        {
            Bms_Init();
        }

        if (eActFlag_On != Get_BMS_StartFlg() && !Get_CcuCfgParaEuropeEnable())
        {
            can_clear(DLL_CHAN);
            continue;
        }

        Dll_RecvServer();

        Dll_OverTimer();

        Bms_RecvTimerManage();

        Bms_StageManage();

        Bms_SendServer();
    }

    dmnTaskUnRegister();
    taskDelete(NULL);
}

/*----------------------------bmsMain.c--------------------------------*/
