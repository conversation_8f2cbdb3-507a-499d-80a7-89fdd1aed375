/*
 * Copyright (c) 2006-2021, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2023-08-29     SenyPC       the first version
 */
#include <stdio.h>
#include <ctype.h>
#include <string.h>
#include "stm32f2xxuart.h"
#include "platform/modbus_rt_platform_serial.h"

#define serial_max 9 // 同步 NUM_TTY

/* 外部函数，由 modbus_rt_platform_serial.c 提供 */
extern modbus_rt_serial_t *modbus_rt_serial_get_list_head(void);

void modbus_bsp_sem_init(void)
{
    /* 此函数不再需要，信号量随 modbus_rt_serial_open 创建 */
}

int serial_device_find(const char *devname)
{
    uint8_t ix = 0;
    /* 缓冲区足够大以安全地容纳 "ttyc" 和多位数字，以及 '\0' */
    char tyName[16];

    if (devname == NULL)
    {
        return -RT_ERROR;
    }

    for (ix = 0; ix < serial_max; ix++)
    {
        /* 使用 sprintf 安全地构造带 '\0' 结尾的字符串，并能处理多位数 */
        sprintf(tyName, "ttyc%u", ix);

        /* 使用 strcmp 安全地比较两个完整的 '\0' 结尾的字符串 */
        if (strcmp(devname, tyName) == 0)
        {
            return ix;
        }
    }
    return -RT_ERROR;
}

/**
 * @brief   由中断服务程序调用，通过遍历链表查找并释放信号量
 * @note    此函数在中断上下文中执行，绝对不能加锁！
 *          其安全性依赖于所有修改链表的操作都在临界区内完成。
 * @param   fd 串口文件描述符
 * @return  void
 */
void serial_sem_post(int serial_port)
{
    modbus_rt_serial_t *temp = modbus_rt_serial_get_list_head();

    while (temp != NULL)
    {
        if (temp->serial_port == serial_port)
        {
            /*
             * 使用原生OS API `semGive`。
             * 注意：如果您的RTOS提供了专门用于ISR的 `semGive_from_isr` 版本，
             * 使用那个版本会更安全、更高效。
             * 为保持通用性，此处仍使用 semGive。
             */
            semGive(temp->serial_sem);
            break; // 找到了，立即退出
        }
        temp = temp->next;
    }
}

/**
 * @brief   由接收任务调用，通过遍历链表查找并等待信号量
 * @param   fd 串口文件描述符
 * @param   timeout 等待超时时间 (ticks)
 * @return  获取信号量的结果
 */
int serial_sem_take(int serial_port, uint32_t timeout)
{
    modbus_rt_serial_t *temp = modbus_rt_serial_get_list_head();

    /*
     * 在任务中遍历查找是安全的，因为所有写操作都被临界区保护。
     * 查找到正确的信号量句柄后，再执行等待操作。
     */
    //    printf("[WORKER DEBUG] ### 正在等待 serial_port 信号量... (Tick: %u)(serial_port : %d)\n", tickGet(),serial_port);
    while (temp != NULL)
    {
        //        printf("[WORKER DEBUG] ### 正在等待 serial_port 信号量... (Tick: %u)(serial_port : %d)\n", tickGet(),temp->serial_port);
        if (temp->serial_port == serial_port)
        {
            return semTake(temp->serial_sem, timeout);
        }
        temp = temp->next;
    }
    return -RT_ERROR; // 未在链表中找到对应的fd
}

/**
 * @brief   serial_open:            打开串口信息
 * @param   devname:                串口设备名称
 * @param   baudrate:               波特率
 * @param   parity:                 校验位：'N', 'E', 'O', 'M', 'S'
 * @param   stopbits:               停止位：1，2
 * @param   xonxoff:                控制流xonxoff开关，暂时不支持其他流控制模式
 * @return  rt_device_t:            返回成功打开的设备，如果没有打开，则返回RT_NULL
 *
 */
int serial_open(char *devname, int baudrate, int bytesize, char parity, int stopbits, int xonxoff)
{
    int serial_port = -1;

    uint32_t config = 0;

    (void)(xonxoff);

    if (serial_device_find(devname) < 0)
    {
        return -RT_ERROR;
    }

    parity = toupper(parity);

    // 1. 设置数据位
    switch (bytesize)
    {
    case UART_DATA_BITS_5:
        config |= UART_CONFIG_WLEN_5;
        break;
    case UART_DATA_BITS_6:
        config |= UART_CONFIG_WLEN_6;
        break;
    case UART_DATA_BITS_7:
        config |= UART_CONFIG_WLEN_7;
        break;
    case UART_DATA_BITS_8:
    default: // 默认 8 位
        config |= UART_CONFIG_WLEN_8;
        break;
    }

    // 2. 设置校验位
    switch (parity)
    {
    case UART_PARITY_EVEN:
        config |= UART_CONFIG_PAR_EVEN;
        break;
    case UART_PARITY_ODD:
        config |= UART_CONFIG_PAR_ODD;
        break;
    case UART_PARITY_NONE:
    default: // 默认无校验
        config |= UART_CONFIG_PAR_NONE;
        break;
    }

    // 3. 设置停止位
    switch (stopbits)
    {
    case UART_STOP_BITS_2:
        config |= UART_CONFIG_STOP_TWO;
        break;
    case UART_STOP_BITS_1:
    default: // 默认 1 停止位
        config |= UART_CONFIG_STOP_ONE;
        break;
    }

    serial_port = ttyOpen(devname);
    /* 1. 控制串口设备。通过控制接口传入命令控制字，与控制参数 */
    ttyIoctl(serial_port, SIO_BAUD_SET, baudrate);
    ttyIoctl(serial_port, SIO_HW_OPTS_SET, config);

    /* 信号量的创建已移至 modbus_rt_platform_serial.c 中，以确保在结构体存在后才创建 */

    return serial_port;
}

/**
 * @brief   serial_close:       关闭串口设备
 * @param   serial              串口设备信息
 * @return  int:                RT_EOK：成功，其他：失败
 *
 */
int serial_close(int serial_port)
{
    if (serial_port != 0)
    {
        ttyClose(serial_port);
        /* 信号量的销毁已移至 modbus_rt_platform_serial.c 中，随结构体一同销毁 */
    }
    return RT_EOK;
}

/**
 * @brief   serial_send     发送数据函数
 * @param   serial          串口设备信息
 * @param   buf             接收数据缓冲区
 * @param   len             发送数据长度
 * @return  int             发送完成的数据长度
 */
int serial_send(int serial_port, uint8_t *buf, int len)
{
    if (ttyWrite(serial_port, (char *)buf, len) > 0)
    {
        return RT_TRUE;
    }
    else
    {
        return RT_FALSE;
    }
}

/**
 * @brief   serial_receive          接收数据函数
 * @param   serial                  串口设备信息
 * @param   buf                     接收数据缓冲区
 * @param   bufsz                   接收的数据缓冲区长度
 * @param   timeout                 接收超时时间
 * @return  int                     接收的数据长度
 */
#if 0
int serial_receive(int serial_port, uint8_t *buf, int bufsz, int timeout, int bytes_timeout)
{
    int len = 0;
    int rc;
    int current_timeout;

    if (buf == NULL || bufsz == 0)
    {
        return -RT_ERROR;
    }

    /* 初始等待：使用较长的"帧间超时"(timeout)，等待新一帧的第一个字节 */
    current_timeout = timeout;

    while (len < bufsz)
    {
        // 1. 等待信号量，表示有数据到达。超时时间是动态变化的。
        if (serial_sem_take(serial_port, current_timeout) != RT_EOK)
        {
            // 等待超时。
            // 如果 len > 0，说明是在等待下一字节时超时（字节间超时），表示一帧已接收完毕。
            // 如果 len == 0，说明是在等待第一字节时超时（帧间超时），表示没有新的数据帧。
            // 无论哪种情况，都应结束接收。
            break;
        }

        // 2. 信号量被释放，说明有数据。调用 ttyRead 读取。
        //    由于 ISR 是逐字节释放信号量，为提高效率，这里尝试一次性读完缓冲区所有内容。
        //    ttyRead 在无数据时应立即返回0，这里不应阻塞。
        rc = ttyRead(serial_port, (char *)(buf + len), bufsz - len);

        if (rc > 0)
        {
            // 成功读取到数据
            len += rc;

            /* 关键逻辑：一旦收到第一个字节，后续的等待都使用较短的"字节间超时"(bytes_timeout) */
            current_timeout = bytes_timeout;
        }
        else
        {
            // 虽然收到了信号，但 ttyRead 未读出数据。
            // 这可能是个异常情况，或者只是 ISR 和本任务之间的微小竞态。
            // 为了健壮性，我们也将其视为一帧的结束。
            break;
        }
    }

    return len;
}
#endif
int serial_receive(int serial_port,
                   uint8_t *buf,
                   int bufsz,
                   int timeout_ms, /* 整帧超时 (Tframe)  */
                   int bytes_timeout_ms /* 字节间超时 (Tbyte) */)
{
    if ((buf == RT_NULL) || (bufsz <= 0))
        return -RT_ERROR;

    /* ── 预计算 tick ───────────────────────────────────────── */
    uint32_t clk_hz = sysClkRateGet(); /* 1 s = clk_hz ticks */
    uint32_t t_frame_ticks = timeout_ms;
    uint32_t t_byte_ticks = bytes_timeout_ms;

    int len = 0, rc;
    //    printf("[WORKER DEBUG] ### 正在等待 serial_port 信号量... (Tick: %u)(timeout_ms : %dms)\n", tickGet(),timeout_ms);
    /* ── 1. 等首字节 ──────────────────────────────────────── */
    if (serial_sem_take(serial_port, t_frame_ticks) != RT_EOK)
    {
        //        printf("[WORKER DEBUG] ### 等待 serial_port 信号量超时... (Tick: %u)\n", tickGet());
        return 0; /* 首字节超时，直接返回 */
    }
    rc = ttyRead(serial_port, (char *)buf, bufsz);
    if (rc <= 0) /* 理论上不会发生 */
    {
        return 0;
    }
    len = rc;

    /* ── 2. 记录截止时刻 ──────────────────────────────────── */
    uint32_t start_tick = tickGet(); /* 首字节时间 */
    uint32_t deadline_tick = start_tick + t_frame_ticks;

    /* ── 3. 收后续字节 ───────────────────────────────────── */
    while (len < bufsz)
    {
        uint32_t now_tick = tickGet();
        if ((int32_t)(deadline_tick - now_tick) <= 0) /* 整帧超时 */
        {
            break;
        }
        uint32_t remain_ticks = deadline_tick - now_tick;
        uint32_t wait_ticks = (remain_ticks < t_byte_ticks) ? remain_ticks
                                                            : t_byte_ticks;

        if (serial_sem_take(serial_port, wait_ticks) != RT_EOK)
        {
            break; /* 字节间超时 */
        }
        /* 一次性读空 FIFO，降低线程切换次数 */
        do
        {
            rc = ttyRead(serial_port,
                         (char *)(buf + len),
                         bufsz - len);
            if (rc > 0)
            {
                len += rc;
            }
        } while ((rc > 0) && (len < bufsz));
    }

    return len; /* 返回实际接收长度 */
}
