#ifndef __MODBUS_RT_PLATFORM_SERIAL_H_
#define __MODBUS_RT_PLATFORM_SERIAL_H_

#include "../modbus_config.h"

#if (MODBUS_RTU_SLAVE_ENABLE) || (MODBUS_RTU_MASTER_ENABLE)

#ifdef __cplusplus
extern "C"
{
#endif
#include "../rtt_uart.h"
#include "taskLib.h"

    typedef struct modbus_rt_serial
    {
        int serial_port;
        char tyName[5];
        SEM_ID serial_sem;
        struct modbus_rt_serial *pre;
        struct modbus_rt_serial *next;
    } modbus_rt_serial_t;

    int modbus_rt_serial_open(char *devname, int baudrate, int bytesize, char parity, int stopbits, int xonxoff);
    void modbus_rt_serial_close(int serial_port);
    void modbus_rt_serial_send(int serial_port, void *buf, int len);
    int modbus_rt_serial_receive(int serial_port, void *buf, int bufsz, const int timeout, const int bytes_timeout);

    /* 安全地根据句柄获取内部结构体的函数 */
    modbus_rt_serial_t *modbus_rt_serial_get_struct(int serial_port);
    /* 新增：提供给底层驱动一个访问链表头的方法 */
    modbus_rt_serial_t *modbus_rt_serial_get_list_head(void);

#ifdef __cplusplus
}
#endif

#endif

#endif /* __PKG_MODBUS_RT_PLATFORM_SERIAL_H_ */
