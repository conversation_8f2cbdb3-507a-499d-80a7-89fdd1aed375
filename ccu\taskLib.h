/**
 *******************************************************************************
 * @file      taskLib.h
 * @brief     多任务内核模块函数接口.
 * @details   本文件封装了所有任务、信号量、消息队列的相关操作接口
 *
 * @copyright      Copyright(C), 2005-2020,Sanxing Medical & Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef _TASKLIB_H_
#define _TASKLIB_H_

/*-----------------------------------------------------------------------------
 Section: Includes
 -----------------------------------------------------------------------------*/
#include <types.h>

/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 -----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 -----------------------------------------------------------------------------*/

typedef void *TASK_ID;
typedef void *SEM_ID;
typedef void *MSG_Q_ID;
typedef void *EventGroupHandle_t;
typedef void *QueueHandle_t;
typedef uint32_t TickType_t;
typedef TickType_t EventBits_t;
typedef long BaseType_t;
typedef unsigned long UBaseType_t;
/*-----------------------------------------------------------------------------
 Section: Globals
 -----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 -----------------------------------------------------------------------------*/

extern TASK_ID
taskSpawn(const signed char *const name, uint32 priority, uint32 stackSize,
          OSFUNCPTR entryPt, uint32 arg);

extern void
taskDelete(TASK_ID tid);

extern void
taskSuspend(TASK_ID tid);

extern void
taskResume(TASK_ID tid);

extern void
taskLock(void);

extern void
taskUnlock(void);

extern void
taskDelay(uint32 ticks);

extern char *
taskName(TASK_ID tid);

extern STATUS
taskIdVerify(TASK_ID tid);

extern TASK_ID
taskIdSelf();

extern uint32
tickGet(void);

extern uint32
sysClkRateGet(void);

extern SEM_ID
semBCreate(uint32 cnt);

extern void
semDelete(SEM_ID semId);

extern STATUS
semTake(SEM_ID semId, uint32 timeout);

extern STATUS
semGive(SEM_ID semId);

extern MSG_Q_ID
msgQCreate(uint32 msgQLen);

extern void
msgQDelete(MSG_Q_ID msgQId);

extern STATUS
msgQSend(MSG_Q_ID msgQId, void *pmsg);

extern STATUS
msgQReceive(MSG_Q_ID msgQId, uint32 timeout, void **pmsg);

extern int
msgQNumMsgs(MSG_Q_ID msgQId);

extern EventGroupHandle_t xEventGroupCreate();

extern EventBits_t xEventGroupSetBits(EventGroupHandle_t xEventGroup, const EventBits_t uxBitsToSet);

extern EventBits_t xEventGroupWaitBits(EventGroupHandle_t xEventGroup,
                                       const EventBits_t uxBitsToWaitFor,
                                       const BaseType_t xClearOnExit,
                                       const BaseType_t xWaitForAllBits,
                                       TickType_t xTicksToWait);
EventBits_t xEventGroupClearBits(EventGroupHandle_t xEventGroup, const EventBits_t uxBitsToClear);

extern QueueHandle_t xQueueGenericCreate(const UBaseType_t uxQueueLength, const UBaseType_t uxItemSize, const uint8_t ucQueueType);

extern void vPortEnterCritical(void);
extern void vPortExitCritical(void);

extern int
isIdValid(void *pId);
// extern EventBits_t xEventGroupGetBitsFromISR( EventGroupHandle_t xEventGroup );
// extern BaseType_t xEventGroupSetBitsFromISR( EventGroupHandle_t xEventGroup, const EventBits_t uxBitsToSet, BaseType_t *pxHigherPriorityTaskWoken );
#endif /* _TASKLIB_H_ */
/*--------------------------End of taskLib.h----------------------------*/
