/**
 ******************************************************************************
 * @file      bmsDll.c
 * @brief     C Source file of bmsDll.c.
 * @details   This file including all API functions's
 *            implement of bmsDll.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/

#include "bmsDll.h"
#include <string.h>
#include <taskLib.h>
#include <dmnLib.h>
#include <stdlib.h>
#include <stdio.h>
#include <sxlib.h>
#include <trace.h>
#include "ccu/ccuLog/ccuLog.h"
#include "bmsRecvCtrl.h"
#include <ccu\para\para.h>
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
/**< 传输协议功能PGN  */
#define PGN_TP_CM 0x00EC00 /**< 连接管理                                 */
#define PGN_TP_DT 0x00EB00 /**< 数据传输                                 */

/**< 分帧状态                 */
#define DLL_CUT_FRAME_NULL 0x00 /**< 无分帧                                     */
#define DLL_CUT_FRAME_ON 0x01   /**< 分帧中                                     */

/**< 连接管理控制字     */
#define CMD_TP_CM_RTS 0x10            /**< 请求发送                                 */
#define CMD_TP_CM_CTS 0x11            /**< 准许发送                                 */
#define CMD_TP_CM_END_OF_MSG_ACK 0x13 /**< 结束分帧                                 */
#define CMD_TP_CM_ABORT 0xFF          /**< 放弃连接                                 */
#define CMD_TP_CM_BAM 0x20            /**< 广播消息                                 */

/**< 链路超时时间     */
#define DLL_MAX_OVER_TIME 1250

/**< 传输协议控制结构   */
typedef struct __DLL_CTRL_STRU_
{
    uint16 totalMsgSize;     /**< 整个消息大小的字节数            */
    uint8 totalPackets;      /**< 全部数据包数                           */
    uint8 oneCtsMaxPackets;  /**< 一个CTS最大包数                     */
    uint8 oneCtsSendPackets; /**< 一个CTS允许发送的包数          */
    uint8 OneCtsSendCnt;     /**< 一个CTS发送的包计数              */

    uint8 nextPacketNums; /**< 下一个要发送的数据包编号     */
    uint8 cutFrameState;  /**< 分帧状态                                   */
    uint8 timerEnable;    /**< 计时使能                                   */
    uint32 timer;         /**< 分帧计时                                   */

    uint8 isExceed; /**< 分帧长度超长标志,BMV\BMT\BSP */

    CAN_FRAME cacheNormalFrame; /**< 普通数据帧                               */
    CAN_FRAME cacheCutFrame;    /**< 分帧数据帧                               */
} DLL_CTRL;

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/

static DLL_CTRL s_DllCtrl; /**< 数据链路层控制   */

/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/

/**
 ******************************************************************************
 * @brief      Dll_CheckFrameValid
 * @param[in]   CAN_ID CanID
 *              uint16 Len      长度
 * @param[out]
 * @retval      TRUE-合法   FALSE-非法
 *
 * @details
 *
 * @note        只适用于PDU1格式
 ******************************************************************************
 */
static uint8 Dll_CheckFrameValid(CAN_ID canID, uint16 len)
{
    // 判断帧数据长度
    if (0 == len || len > 8)
    {
        return FALSE;
    }

    // 判断目标地址源地址
    if (DLL_SRC_ADDR != canID.ps ||
        DLL_DEST_ADDR != canID.sa)
    {
        return FALSE;
    }

    if (0 != canID.dp || 0 != canID.res2)
    {
        return FALSE;
    }

    // 判断PDU格式
    if (canID.pf > 239)
    {
        return FALSE;
    }

    return TRUE;
}
/**
 ******************************************************************************
 * @brief      Dll_Send_TP_CM_CTS
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details     发送准许发送帧组帧，接下来发送方发送数据
 *
 * @note        传输协议功能
 ******************************************************************************
 */
static void Dll_Send_TP_CM_CTS(void)
{
    DLL_CTRL *pDllCtrl = &s_DllCtrl;
    uint32 canId = 0;
    uint8 buf[8] = {0};
    uint8 index = 0;

    canId = GetCanID(PGN_TP_CM, 0x07, DLL_SRC_ADDR, DLL_DEST_ADDR);

    buf[index++] = CMD_TP_CM_CTS;
    buf[index++] = pDllCtrl->oneCtsSendPackets;
    buf[index++] = pDllCtrl->nextPacketNums;
    buf[index++] = 0xFF;
    buf[index++] = 0xFF;

    memcpy(&buf[index], pDllCtrl->cacheCutFrame.msgPGN,
           sizeof(pDllCtrl->cacheCutFrame.msgPGN));
    index += sizeof(pDllCtrl->cacheCutFrame.msgPGN);

    can_send(DLL_CHAN, canId, buf, sizeof(buf));

    // 初始化计时器并启动计时
    pDllCtrl->timer = 0;
    pDllCtrl->timerEnable = 0x55;
}

/**
 ******************************************************************************
 * @brief      Dll_Send_TP_CM_ABORT
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details     发送放弃链接帧组帧，分帧结束
 *
 * @note        传输协议功能
 ******************************************************************************
 */
static void Dll_Send_TP_CM_ABORT(void)
{
    DLL_CTRL *pDllCtrl = &s_DllCtrl;
    uint32 canId = 0;
    uint8 buf[8] = {0};
    uint8 index = 0;

    canId = GetCanID(PGN_TP_CM, 0x07, DLL_SRC_ADDR, DLL_DEST_ADDR);

    buf[index++] = CMD_TP_CM_ABORT;
    buf[index++] = 0xFF;
    buf[index++] = 0xFF;
    buf[index++] = 0xFF;
    buf[index++] = 0xFF;

    memcpy(&buf[index], pDllCtrl->cacheCutFrame.msgPGN,
           sizeof(pDllCtrl->cacheCutFrame.msgPGN));
    index += sizeof(pDllCtrl->cacheCutFrame.msgPGN);

    can_send(DLL_CHAN, canId, buf, sizeof(buf));
}

/**
 ******************************************************************************
 * @brief      Dll_Send_TP_CM_EndofMsg
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details     发送结束分帧组帧，分帧结束
 *
 * @note        传输协议功能
 ******************************************************************************
 */
static void Dll_Send_TP_CM_EndofMsg(void)
{
    DLL_CTRL *pDllCtrl = &s_DllCtrl;
    uint32 canId = 0;
    uint8 buf[8] = {0};
    uint8 index = 0;

    canId = GetCanID(PGN_TP_CM, 0x07, DLL_SRC_ADDR, DLL_DEST_ADDR);

    buf[index++] = CMD_TP_CM_END_OF_MSG_ACK;

    Uint16ToTwoUint8(&buf[index], pDllCtrl->totalMsgSize);
    index += 2;

    buf[index++] = pDllCtrl->totalPackets;
    buf[index++] = 0xFF;

    memcpy(&buf[index], pDllCtrl->cacheCutFrame.msgPGN,
           sizeof(pDllCtrl->cacheCutFrame.msgPGN));
    index += 3;

    can_send(DLL_CHAN, canId, buf, sizeof(buf));
}

/**
 ******************************************************************************
 * @brief      Dll_Recv_TP_CM_RTS
 * @param[in]   uint8 *pData  can数据区
 * @param[out]
 * @retval
 *
 * @details     接收请求分帧，分帧开始
 *              当处在分帧状态下，应先发送放弃分帧，再发送分帧准许
 * @note        传输协议功能
 ******************************************************************************
 */
static void Dll_Recv_TP_CM_RTS(uint8 *pData)
{
    DLL_CTRL *pDllCtrl = &s_DllCtrl;
    uint8 Index = 1;
    uint8 PGN[3] = {0};

    memcpy(PGN, &pData[5], 3);

    if (DLL_CUT_FRAME_ON == pDllCtrl->cutFrameState)
    {
        if (0 != memcmp(PGN, pDllCtrl->cacheCutFrame.msgPGN, 3))
        {
            Dll_Send_TP_CM_ABORT();
            return;
        }
    }

    memset(pDllCtrl, 0x00, sizeof(DLL_CTRL));

    // 包总字节数
    pDllCtrl->totalMsgSize = TwoUint8ToUint16(&pData[Index]);
    Index += 2;

    // 包总帧数
    pDllCtrl->totalPackets = pData[Index++];

    // 一个CTS最大包数
    pDllCtrl->oneCtsMaxPackets = pData[Index++];

    // 参数组编号
    memcpy(pDllCtrl->cacheCutFrame.msgPGN, &pData[Index], 3);
    Index += 3;

    // 确定出一个CTS允许发送的包数
    if (0xFF == pDllCtrl->oneCtsMaxPackets)
    {
        pDllCtrl->oneCtsSendPackets = pDllCtrl->totalPackets;
    }
    else
    {
        if (pDllCtrl->oneCtsMaxPackets <= pDllCtrl->totalPackets)
        {
            pDllCtrl->oneCtsSendPackets = pDllCtrl->oneCtsMaxPackets;
        }
        else
        {
            pDllCtrl->oneCtsSendPackets = pDllCtrl->totalPackets;
        }
    }

    pDllCtrl->nextPacketNums = 1;

    Dll_Send_TP_CM_CTS();

    pDllCtrl->cutFrameState = DLL_CUT_FRAME_ON;

    //    printf("包总字节数 = <%d>\n" ,pDllCtrl->totalMsgSize);
    //    printf("包总帧数 = <%d>\n" ,pDllCtrl->totalPackets);
    //    printf("一个CTS最大包数 = <%d>\n" ,pDllCtrl->oneCtsMaxPackets);
    //    printf("参数组编号\n");
    //    print_buf(0, pDllCtrl->cacheCutFrame.msgPGN, 3);
    //    printf("一个CTS允许发送的包数 = <%d>\n" ,pDllCtrl->oneCtsSendPackets);

    return;
}

/**
 ******************************************************************************
 * @brief      Dll_Recv_TP_CM_ABORT
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details     接收结束分帧，分帧结束，初始化链路控制
 *
 * @note        传输协议功能
 ******************************************************************************
 */
static void Dll_Recv_TP_CM_ABORT(void)
{
    DLL_CTRL *pDllCtrl = &s_DllCtrl;

    memset(pDllCtrl, 0x00, sizeof(DLL_CTRL));

    return;
}

/**
 ******************************************************************************
 * @brief      Dll_Decode_TP_CM
 * @param[in]   uint8 *pData  Can数据区
 * @param[out]
 * @retval
 *
 * @details     接收传输协议帧 含链路控制帧和数据帧
 *
 * @note
 ******************************************************************************
 */
static void Dll_Decode_TP_CM(uint8 *pData)
{
    uint8 Cmd = pData[0];
    // trace_buf(TR_CAN, pData, 8);
    switch (Cmd)
    {
    case CMD_TP_CM_RTS:
        Dll_Recv_TP_CM_RTS(pData);
        break;

    case CMD_TP_CM_ABORT:
        Dll_Recv_TP_CM_ABORT();
        break;

    default:
        break;
    }

    return;
}

/**
 ******************************************************************************
 * @brief      Dll_Decode_TP_DT
 * @param[in]   CAN_DATA *pCanData
 * @param[out]
 * @retval
 *
 * @details     接收传输协议帧 含链路控制帧和数据帧
 *
 * @note
 ******************************************************************************
 */
static void Dll_Decode_TP_DT(CAN_DATA *pCanData)
{
    DLL_CTRL *pDllCtrl = &s_DllCtrl;
    uint8 CurSeq = pCanData->dataBuf[0];
    // trace_buf(TR_CAN, pCanData->dataBuf, 8);
    if (DLL_CUT_FRAME_ON != pDllCtrl->cutFrameState)
    {
        trace(TR_CAN, "分帧没有打开\n");
        return;
    }

    if (CurSeq != pDllCtrl->nextPacketNums)
    {
        return;
    }

    pCanData->dataLen -= 1;
    pDllCtrl->cacheCutFrame.canId.canId = pCanData->canId;

    if ((pDllCtrl->cacheCutFrame.dataLen + pCanData->dataLen) >
        sizeof(pDllCtrl->cacheCutFrame.dataBuf))
    {
        pDllCtrl->isExceed = TRUE;
    }

    if (!pDllCtrl->isExceed)
    {
        memcpy(pDllCtrl->cacheCutFrame.dataBuf + pDllCtrl->cacheCutFrame.dataLen,
               &pCanData->dataBuf[1], pCanData->dataLen);

        pDllCtrl->cacheCutFrame.dataLen += pCanData->dataLen;
    }
    pDllCtrl->nextPacketNums++;

    pDllCtrl->OneCtsSendCnt++;

    if (CurSeq >= pDllCtrl->totalPackets)
    {
        Dll_Send_TP_CM_EndofMsg();
        pDllCtrl->cutFrameState = DLL_CUT_FRAME_NULL;
        if (!pDllCtrl->isExceed)
        {
            Bms_Decode(&pDllCtrl->cacheCutFrame);
        }
        memset(pDllCtrl, 0x00, sizeof(DLL_CTRL));
    }
    else
    {
        if (pDllCtrl->OneCtsSendCnt >= pDllCtrl->oneCtsSendPackets)
        {
            pDllCtrl->OneCtsSendCnt = 0;
            Dll_Send_TP_CM_CTS();
        }
    }

    return;
}

/**
 ******************************************************************************
 * @brief      Dll_Cache_APP_DT
 * @param[in]   CAN_DATA *pCanData
 * @param[out]
 * @retval
 *
 * @details     缓存单帧数据
 *
 * @note
 ******************************************************************************
 */
static void Dll_Cache_APP_DT(CAN_DATA *pCanData)
{
    CAN_ID *pId = (CAN_ID *)&pCanData->canId;
    DLL_CTRL *pDllCtrl = &s_DllCtrl;

    pDllCtrl->cacheNormalFrame.canId.canId = pId->canId;

    memcpy(pDllCtrl->cacheNormalFrame.dataBuf, pCanData->dataBuf, pCanData->dataLen);

    pDllCtrl->cacheNormalFrame.dataLen = pCanData->dataLen;

    pDllCtrl->cacheNormalFrame.msgPGN[0] = 0x00;
    pDllCtrl->cacheNormalFrame.msgPGN[1] = pId->pf;
    pDllCtrl->cacheNormalFrame.msgPGN[2] = 0x00;
    if (Get_CcuCfgParaEuropeEnable()) // 欧标BRM 单帧处理
    {
        /* BMS_PGN_BCP,BMS_PGN_BCS必须采用多帧发送  */
        if (((uint8)(BMS_PGN_BCP >> 8) == pId->pf) ||
            ((uint8)(BMS_PGN_BCS >> 8) == pId->pf))
        {
            return;
        }
    }
    else
    {
        /* BMS_PGN_BRM,BMS_PGN_BCP,BMS_PGN_BCS必须采用多帧发送  */
        if (((uint8)(BMS_PGN_BRM >> 8) == pId->pf) ||
            ((uint8)(BMS_PGN_BCP >> 8) == pId->pf) ||
            ((uint8)(BMS_PGN_BCS >> 8) == pId->pf))
        {
            return;
        }
    }
    Bms_Decode(&pDllCtrl->cacheNormalFrame);
    return;
}

/**
 ******************************************************************************
 * @brief      Dll_PretreatData
 * @param[in]   CAN_DATA *pCanData
 * @param[out]
 * @retval
 *
 * @details     预处理接收到的数据
 *
 * @note
 ******************************************************************************
 */
static void Dll_PretreatData(CAN_DATA *pCanData)
{
    CAN_ID canId;
    canId.canId = pCanData->canId;

    if (FALSE == Dll_CheckFrameValid(canId, pCanData->dataLen))
    {
        return;
    }

    switch (canId.pf)
    {
    case (uint8)(PGN_TP_CM >> 8):
        Dll_Decode_TP_CM(pCanData->dataBuf);
        break;

    case (uint8)(PGN_TP_DT >> 8):
        Dll_Decode_TP_DT(pCanData);
        break;

    default:
        Dll_Cache_APP_DT(pCanData);

        break;
    }
    return;
}

/**
 ******************************************************************************
 * @brief      Dll_OverTimer
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details     链路超时计时器
 *
 * @note
 ******************************************************************************
 */
// Todo: 将pDllCtrl->timer计时改成tick计时
void Dll_OverTimer(void)
{
    DLL_CTRL *pDllCtrl = &s_DllCtrl;

    if (0x55 == pDllCtrl->timerEnable)
    {
        pDllCtrl->timer++;

        if (pDllCtrl->timer > DLL_MAX_OVER_TIME)
        {
            Dll_Send_TP_CM_ABORT();

            memset(pDllCtrl, 0x00, sizeof(DLL_CTRL));
        }
    }

    return;
}

/**
 ******************************************************************************
 * @brief      Dll_Init
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details     链路初始化
 *
 * @note
 ******************************************************************************
 */
void Dll_Init(void)
{
    DLL_CTRL *pDllCtrl = &s_DllCtrl;

    memset(pDllCtrl, 0x00, sizeof(DLL_CTRL));

    return;
}

/**
******************************************************************************
* @brief       Dll_RecvServer
* @param[in]
* @param[out]
* @retval
*
* @details     链路数据接收
*
* @note
******************************************************************************
*/
void Dll_RecvServer(void)
{
    CAN_DATA strCanData[10];
    CAN_DATA strData;
    uint16 readLen = 0;
    uint8 erro = 0;
    uint8 frameCnt = 0;
    uint8 index = 0;
    CAN_ID *pId = NULL;

    memset(strCanData, 0x00, sizeof(strCanData));
    readLen = can_recv(DLL_CHAN, (uint8 *)strCanData, sizeof(strCanData), &erro);
    frameCnt = readLen / sizeof(CAN_DATA);

    while (index < frameCnt && frameCnt > 0)
    {
        pId = (CAN_ID *)&strCanData[index].canId;
        // 打包成内部使用的结构
        memset(&strData, 0x00, sizeof(strData));
        strData.canId = pId->canId;

        strData.dataLen = strCanData[index].dataLen;
        memcpy(strData.dataBuf, strCanData[index].dataBuf, sizeof(strData.dataBuf));

        // ccuLogIn(strData.canId,strData.dataBuf,strData.dataLen);
        // 数据接收处理
        Dll_PretreatData(&strData);

        // 处理标记
        memset(&strCanData[index], 0x00, sizeof(CAN_DATA));
        index++;
    }
}

/*----------------------------bmsDll.c--------------------------------*/
