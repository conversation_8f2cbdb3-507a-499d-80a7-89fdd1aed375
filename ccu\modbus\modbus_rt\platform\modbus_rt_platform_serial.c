#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "modbus_rt_platform_serial.h"

#if (MODBUS_RTU_SLAVE_ENABLE) || (MODBUS_RTU_MASTER_ENABLE)
#include "modbus_rt_platform_memory.h"
#include "modbus_rt_platform_thread.h"

static modbus_rt_serial_t *p_serial_info = NULL;
#if 0
static int modbus_rt_serial_add_dev(int serial_port, char *name)
{
    modbus_rt_serial_t *serial_port_temp = modbus_rt_malloc(sizeof(struct modbus_rt_serial));
    if (NULL == serial_port_temp)
    {
        return -MODBUS_RT_ENOMEM;
    }
    memset(serial_port_temp, 0, sizeof(struct modbus_rt_serial));

    if (NULL == p_serial_info)
    {
        p_serial_info = serial_port_temp;
    }
    else
    {
        modbus_rt_serial_t *temp = p_serial_info;
        while (NULL != temp->next)
        {
            temp = temp->next;
        }
        temp->next = serial_port_temp;
        serial_port_temp->pre = temp;
    }

    memcpy(serial_port_temp->tyName, name, sizeof(serial_port_temp->tyName) - 1);
    serial_port_temp->tyName[sizeof(serial_port_temp->tyName) - 1] = '\0';
    serial_port_temp->serial_port = serial_port;

    return serial_port;
}
#endif
static int modbus_rt_serial_add_dev(int serial_port, char *name)
{

    modbus_rt_serial_t *serial_port_temp = modbus_rt_malloc(sizeof(struct modbus_rt_serial));
    if (NULL == serial_port_temp)
    {
        return -MODBUS_RT_ENOMEM;
    }
    memset(serial_port_temp, 0, sizeof(struct modbus_rt_serial));

    serial_port_temp->serial_sem = semBCreate(0);
    if (serial_port_temp->serial_sem == NULL)
    {
        modbus_rt_free(serial_port_temp);
        return -MODBUS_RT_ENOMEM;
    }

    intLock();
    if (NULL == p_serial_info)
    {
        p_serial_info = serial_port_temp;
    }
    else
    {
        modbus_rt_serial_t *temp = p_serial_info;
        while (NULL != temp->next)
        {
            temp = temp->next;
        }
        temp->next = serial_port_temp;
        serial_port_temp->pre = temp;
    }
    intUnlock();

    memcpy(serial_port_temp->tyName, name, sizeof(serial_port_temp->tyName) - 1);
    serial_port_temp->tyName[sizeof(serial_port_temp->tyName) - 1] = '\0';
    serial_port_temp->serial_port = serial_port;

    return serial_port;
}
static int modbus_rt_serial_get_dev(int serial_port)
{
    modbus_rt_serial_t *serial_port_temp = p_serial_info;
    while (NULL != serial_port_temp)
    {
        if (serial_port == serial_port_temp->serial_port)
        {
            return serial_port_temp->serial_port;
        }
        serial_port_temp = serial_port_temp->next;
    }
    return -MODBUS_RT_ERROR;
}

/**
 * @brief   根据串口句柄安全地获取内部管理结构体的指针
 * @param   serial_port 串口句柄
 * @return  !=NULL: 指向 modbus_rt_serial_t 结构体的指针; =NULL: 未找到
 */
modbus_rt_serial_t *modbus_rt_serial_get_struct(int serial_port)
{
    modbus_rt_serial_t *serial_port_temp = p_serial_info;
    while (NULL != serial_port_temp)
    {
        if (serial_port == serial_port_temp->serial_port)
        {
            return serial_port_temp;
        }
        serial_port_temp = serial_port_temp->next;
    }
    return NULL;
}
#if 0
static void modbus_rt_serial_close_dev(int serial_port)
{
    modbus_rt_serial_t *serial_port_temp = p_serial_info;
    while (NULL != serial_port_temp)
    {
        if (serial_port == serial_port_temp->serial_port)
        {
            break;
        }
        serial_port_temp = serial_port_temp->next;
    }
    if (NULL == serial_port_temp)
    {
        return;
    }

    /* 底层串口关闭 */
    serial_close(serial_port_temp->serial_port);

    /* 从链表中移除 */
    if (NULL == serial_port_temp->pre)
    {
        if (NULL == serial_port_temp->next)
        {
            p_serial_info = NULL;
        }
        else
        {
            p_serial_info = serial_port_temp->next;
            p_serial_info->pre = NULL;
        }
    }
    else if (NULL == serial_port_temp->next)
    {
        serial_port_temp->pre->next = NULL;
    }
    else
    {
        serial_port_temp->pre->next = serial_port_temp->next;
        serial_port_temp->next->pre = serial_port_temp->pre;
    }
    modbus_rt_free(serial_port_temp);
}
#endif
static void modbus_rt_serial_close_dev(int serial_port)
{
    modbus_rt_serial_t *serial_port_temp = p_serial_info;
    while (NULL != serial_port_temp)
    {
        if (serial_port == serial_port_temp->serial_port)
        {
            break;
        }
        serial_port_temp = serial_port_temp->next;
    }
    if (NULL == serial_port_temp)
    {
        return;
    }

    serial_close(serial_port_temp->serial_port);

    if (serial_port_temp->serial_sem != NULL)
    {
        semDelete(serial_port_temp->serial_sem);
    }

    intLock();
    if (NULL == serial_port_temp->pre)
    {
        if (NULL == serial_port_temp->next)
        {
            p_serial_info = NULL;
        }
        else
        {
            p_serial_info = serial_port_temp->next;
            p_serial_info->pre = NULL;
        }
    }
    else if (NULL == serial_port_temp->next)
    {
        serial_port_temp->pre->next = NULL;
    }
    else
    {
        serial_port_temp->pre->next = serial_port_temp->next;
        serial_port_temp->next->pre = serial_port_temp->pre;
    }
    intUnlock();

    modbus_rt_free(serial_port_temp);
}
int modbus_rt_serial_open(char *devname, int baudrate, int bytesize, char parity, int stopbits, int xonxoff)
{
    int serial_port = serial_open(devname, baudrate, bytesize, parity, stopbits, xonxoff);
    if (serial_port < 0)
    {
        return -MODBUS_RT_ERROR;
    }

    return modbus_rt_serial_add_dev(serial_port, devname);
}

void modbus_rt_serial_close(int serial_port)
{
    modbus_rt_serial_close_dev(serial_port);
}

void modbus_rt_serial_send(int serial_port, void *buf, int len)
{
    if (modbus_rt_serial_get_dev(serial_port) <= 0)
    {
        return;
    }
    serial_send(serial_port, buf, len);
}

int modbus_rt_serial_receive(int serial_port, void *buf, int bufsz, const int timeout, const int bytes_timeout)
{
    if (modbus_rt_serial_get_dev(serial_port) <= 0)
    {
        return -MODBUS_RT_ERROR;
    }
    return serial_receive(serial_port, buf, bufsz, timeout, bytes_timeout);
}

/**
 * @brief   获取串口管理链表的头指针
 * @note    此函数供底层驱动(rtt_uart.c)调用，以实现通过fd查找信号量的机制。
 * @return  链表头指针
 */
modbus_rt_serial_t *modbus_rt_serial_get_list_head(void)
{
    return p_serial_info;
}

#endif
