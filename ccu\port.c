/**
 ******************************************************************************
 * @file      port.c
 * @brief     C Source file of port.c.
 * @details   This file including all API functions's
 *            implement of port.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include "stddef.h"
#include "maths.h"
#include "trace.h"
#include "can.h"
#include "../inc/message.h"
#include "cfg/EVC_DC/prdcfg.h"
#include "../dualgun/dualgun.h"
#include <gpio.h>
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
#define MSG_PORT_NUM (3) // CAN口数
#define MSG_DEV_NUM (6)  // 设备数

/**
 * 设备MSG.
 */
typedef struct _DEV_MSG
{
    MSG_Q_ID msg_id; /**< 消息ID*/
    uint8 min_addr;  /**< 起始地址*/
    uint8 max_addr;  /**< 结束地址*/
} DEV_MSG;

/**
 *  设备端口.
 */
typedef struct _DEV_PORT
{
    uint8 port;                   /**< 端口号*/
    DEV_MSG dev_msg[MSG_DEV_NUM]; /**< 设备msg*/
} DEV_PORT;
/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
static DEV_PORT sDevPort[MSG_PORT_NUM];
/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
/**
 ******************************************************************************
 * @brief      msg_registerInit.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void dev_msgInit(void)
{
    DEV_PORT *pCtrl = NULL;

    for (int i = 0; i < MSG_PORT_NUM; i++)
    {
        pCtrl = (DEV_PORT *)&sDevPort[i];
        pCtrl->port = 0xff;
        for (int j = 0; j < MSG_DEV_NUM; j++)
        {
            pCtrl->dev_msg[j].msg_id = NULL;
            pCtrl->dev_msg[j].min_addr = 0;
            pCtrl->dev_msg[j].max_addr = 0;
        }
    }
}

/**
 ******************************************************************************
 * @brief      msg_register.
 * @param[in]  None
 * @param[out] None
 * @retval     消息注册.
 *
 * @details   成功返回0,失败返回-1
 *
 * @note
 ******************************************************************************
 */
int msg_register(MSG_Q_ID msg_id, uint8 port, uint8 min_addr, uint8 max_addr)
{
    DEV_PORT *pCtrl = NULL;

    if ((NULL == msg_id) || (port > CAN_3))
    {
        return -2; // 失败返回
    }

    for (int i = 0; i < MSG_PORT_NUM; i++)
    {
        pCtrl = (DEV_PORT *)&sDevPort[i];
        if ((port == pCtrl->port) || (0xff == pCtrl->port))
        {
            pCtrl->port = port;
            for (int j = 0; j < MSG_DEV_NUM; j++)
            {
                if ((msg_id == pCtrl->dev_msg[j].msg_id) || (NULL == pCtrl->dev_msg[j].msg_id))
                {
                    pCtrl->dev_msg[j].msg_id = msg_id;
                    pCtrl->dev_msg[j].min_addr = min_addr;
                    pCtrl->dev_msg[j].max_addr = max_addr;
                    return 0;
                }
            }
        }
    }
    return -1;
}
/**
 ******************************************************************************
 * @brief      判断CAN消息是否为双枪充电相关消息
 * @param[in]  pf - 消息ID (Protocol Field)
 * @retval     bool_e - TRUE表示是双枪充电消息，FALSE表示不是
 * @details    在CCU模式下，检查消息ID是否为双枪控制或遥测消息
 * @note       此函数用于快速过滤CAN总线上的消息
 ******************************************************************************
 */
extern uint8 sysGetMode(void);
bool_e Is_DualCharge_Message(uint8 pf)
{
    // 检查系统是否处于CCU或CCUXXH模式
    if (MODE_CCU == sysGetMode() || MODE_CCUXXH == sysGetMode())
    {
        // 判断是否为双枪充电相关消息
        if (pf == ID_DUALCHARGE_CTRL || pf == ID_DUALCHARGE_TELE)
        {
            // 是双枪充电消息
            return TRUE;
        }
        else
        {
            // 不是双枪充电消息
            return FALSE;
        }
    }

    // 非CCU模式下不处理双枪充电消息
    return FALSE;
}
/**
 * @brief CAN数据端口服务器，用于转发CAN数据到消息队列
 * @param[in] chan CAN通道号
 * @param[in] id CAN消息ID (为0时表示原始CAN数据包)
 * @param[in] buf 数据缓冲区
 * @param[in] len 数据长度
 * @param[in] msg_q 目标消息队列ID
 * @return 消息发送结果，0表示成功
 */
extern uint32 FourUint8ToUint32(uint8 *pInData);
extern uint8 Get_CcuAddr(void);
extern uint8 Get_CcuDualAddr(void);
uint8 port_Canserver(int chan, uint32 id, uint8 *buf, uint32 len, MSG_Q_ID msg_q)
{
    uint8 result = MSG_ERR_EVENT_TYPE;
    CAN_DATA can_data;

    // 参数有效性检查
    if (NULL == buf || NULL == msg_q || len < 16)
    {
        trace(TR_CCU_DEBUG, "port_server:[%d][%d][%d] 参数无效\n",
              (NULL == buf), (NULL == msg_q), (len));
        return result;
    }

    // 构造CAN数据结构
    memset(&can_data, 0, sizeof(CAN_DATA));

    if (id == 0)
    {
        // 原始数据包，需要提取CAN ID
        // 假设原始数据包的前4个字节是CAN ID，需要根据实际情况调整

        /*if (len < 4)  //上面已经判了len<16时就直接return, 因此此时不会再执行到这
        {
            trace(TR_CCU_DEBUG, "port_server: 原始数据包太短\n");
            return result;
        }*/

        // 提取CAN ID并判断是否是CCU相关通信
        can_data.canId = FourUint8ToUint32(buf);
        can_data.dataLen = len - 8;                          // 减去CAN ID的长度
        memcpy(can_data.dataBuf, buf + 8, can_data.dataLen); // 跳过CAN ID

        // 检查是否是CCU相关通信
        CAN_ID *pId = (CAN_ID *)&can_data.canId;

        // 获取本地设备地址 (使用DEV_CCU_ADDR_A/B作为物理地址，但在CAN通信中使用DEV_CCU_A/B)
        //        uint8 local_addr = Get_CcuAddr();  // 返回DEV_CCU_ADDR_A或DEV_CCU_ADDR_B
        uint8 local_addr = Get_CCU(); // 返回DEV_CCU_ADDR_A或DEV_CCU_ADDR_B

        // 判断是否处理此消息
        bool should_process = FALSE;

        // 将本地地址映射到CAN通信地址
        //        uint8 local_can_addr = (local_addr == DEV_CCU_ADDR_A) ? DEV_CCU_A : DEV_CCU_B;

        // 双枪通信（05或06帧）：只处理对方发给我们的消息
        if (pId->pf == 0x05 || pId->pf == 0x06)
        {
            // 如果本地是A枪，只处理B枪发给A枪的消息
            should_process = (0xff == Get_CcuDualAddr()) ? ((pId->sa == (local_addr ^ 0x01)) && (pId->ps == local_addr))
                                                         : ((pId->sa == (Get_CcuDualAddr())) && (pId->ps == local_addr));
        }
        // 其他CCU通信：只处理与本设备相关的消息
        else
        {
            //            should_process = (pId->sa == local_can_addr || pId->ps == local_can_addr);
            should_process = (pId->sa == local_addr || pId->ps == local_addr);
        }
        if (!should_process)
        {
            return MSG_ERR_NONE;
        }
    }
    else
    {
        // 已知CAN ID，直接使用
        can_data.canId = id;
        can_data.dataLen = len;
        memcpy(can_data.dataBuf, buf, len);
    }

    // 调试信息
    //    trace(TR_CCU_DEBUG, "port_server: 转发CAN消息 [CH:%d ID:0x%X LEN:%d]\n",
    //          chan, can_data.canId, can_data.dataLen);
    //    trace_buf(TR_CCU_DEBUG, can_data.dataBuf, can_data.dataLen);

    // 发送到消息队列
    result = msgq_snd(msg_q, (uint8 *)&can_data, sizeof(CAN_DATA));
    if (result != MSG_ERR_NONE)
    {
        trace(TR_CCU_DEBUG, "port_server: 消息队列发送失败 [ERR:%d]\n", result);
    }

    return result;
}
/**
 ******************************************************************************
 * @brief      port_proc.
 * @param[in]  None
 * @param[out] None
 * @retval     端口数据处理.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
port_proc(DEV_PORT *pCtrl)
{
    uint8 err = 0;
    STWORKBUF rxBuf;
    CAN_DATA *pCan = NULL;
    CAN_ID *pId = NULL;

    memset(&rxBuf, 0, sizeof(STWORKBUF));
    rxBuf.iDataLen = can_recv(pCtrl->port, rxBuf.ucDataBuf, sizeof(rxBuf.ucDataBuf), &err);
    if (rxBuf.iDataLen > 0)
    {
        for (int i = 0; i < (rxBuf.iDataLen / sizeof(CAN_DATA)); i++)
        {
            pCan = (CAN_DATA *)(rxBuf.ucDataBuf + i * sizeof(CAN_DATA));
            pId = (CAN_ID *)&pCan->canId;
            for (int j = 0; j < MSG_DEV_NUM; j++)
            {
                DEV_MSG *pMsg = &pCtrl->dev_msg[j];
                if (NULL != pMsg->msg_id)
                {
                    if ((pId->sa >= pMsg->min_addr) && (pId->sa <= pMsg->max_addr))
                    {
                        msgq_snd(pMsg->msg_id, (uint8 *)pCan, sizeof(CAN_DATA));
                    }
                }
            }
        }
    }
}

/**
 ******************************************************************************
 * @brief      port_server.
 * @param[in]  None
 * @param[out] None
 * @retval     端口数据处理.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void port_server(void)
{
    DEV_PORT *pCtrl = NULL;

    for (int i = 0; i < MSG_PORT_NUM; i++)
    {
        pCtrl = (DEV_PORT *)&sDevPort[i];
        if (0xff != pCtrl->port)
        {
            port_proc(pCtrl);
        }
    }
}

/*----------------------------port.c--------------------------------*/
