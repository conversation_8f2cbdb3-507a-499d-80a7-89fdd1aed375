/**
 ******************************************************************************
 * @file       tcuSendCtrl.h
 * @brief      API include file of tcuSendCtrl.h.
 * @details    This file including all API functions's declare of tcuSendCtrl.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */

#ifndef __TCU_SEND_CTRL_H__
#define __TCU_SEND_CTRL_H__

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <types.h>
#include <ccu\lib\ccuLib.h>
/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
TCU_SEND_CTRL *Get_TcuSendCtrl(uint32 pgn);

void Set_TcuLastSendTimer(uint32 pgn, uint32 countVal);

uint32 Get_TcuSendRemainTimer(uint32 pgn);

void Set_TcuSendRemainTimer(uint32 pgn, uint32 countValue);

SEND_FLAG Get_TcuSendFlg(uint32 pgn);

void Set_TcuSendFlg(uint32 pgn, SEND_FLAG sendFlg);

void Tcu_SendServer(void);

void Init_TcuCutFrame(void);

void Set_TcuStartTimer(uint32 pgn);

void Get_TcuReportYx(uint8 *pOutData, const uint8 len);

RESULT Clear_ProcessFault(uint8 workState);

#endif

/*--------------------------End of tcuSendCtrl.h----------------------------*/
