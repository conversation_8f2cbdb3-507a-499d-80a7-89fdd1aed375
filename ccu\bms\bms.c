/**
 ******************************************************************************
 * @file      bms.c
 * @brief     C Source file of bms.c.
 * @details   This file including all API functions's
 *            implement of bms.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <bms.h>
#include <stddef.h>
#include "bmsMain.h"
#include "bmsRecvCtrl.h"
#include <stdlib.h>
#include <taskLib.h>
#include <trace.h>
#include <maths.h>
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
extern BMS_DATA bmsData;
extern BMS_CTRL bmsCtrl;

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
static uint8 s_bmsStartFlag = 0; /* BMS服务启停标记*/
static uint8 s_bmsVer;           /**< bms版本，默认0x00：新国标；在bhm超时的时候置 0xA5：老国标 */

/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/

/**
 ******************************************************************************
 * @brief      Start_BMS
 * @param[in]  None
 * @param[out] None
 * @retval
 * @details
 *
 * @note		启动bms通信，数据初始化
 ******************************************************************************
 */
void Start_BMS(void)
{
    Bms_Init();
    Dll_Init();

    trace(TR_BMS_PROCESS, "Start_BMS()\n");
    s_bmsStartFlag = eActFlag_On;
    return;
}

uint32 Get_BmsOverTime()
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    return pBmsCtrl->overTimePgnforBms;
}
/**
 ******************************************************************************
 * @brief      void Stop_BMS(bool_e flg)
 * @param[in]  bool
 * @param[out] None
 * @retval
 * @details
 *
 * @note		停止bms通信，支持延时停止
 ******************************************************************************
 */
void Stop_BMS(bool_e flg)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (flg == FALSE)
    {
        s_bmsStartFlag = eActFlag_Off;
        return;
    }

    if (pBmsCtrl->stopBmsDelay > 0)
    {
        if (abs(tickGet() - pBmsCtrl->stopBmsDelay) >= 2000)
        {
            trace(TR_BMS_PROCESS, "BMS服务------停止完成\n");
            s_bmsStartFlag = eActFlag_Off;
        }
    }
    else
    {
        // 延时停止bms服务给超时情况下的CEM足够发送时间
        pBmsCtrl->stopBmsDelay = tickGet();
    }

    return;
}

/**
 ******************************************************************************
 * @brief      Get_BMS_StartFlg
 * @param[in]  None
 * @param[out] ACT_FLAG
 * @retval	   0xAA:停止 0x55:启动 0xFF:无效
 * @details
 *
 * @note	   获取BMS启停标志
 ******************************************************************************
 */
ACT_FLAG Get_BMS_StartFlg(void)
{
    return s_bmsStartFlag;
}

/**
 ******************************************************************************
 * @brief      Get_BMS_Ver
 * @param[in]  None
 * @param[out] uint8
 * @retval	   0x00:新国标    0xA5:老国标
 * @details
 *
 * @note	   获取BMS版本
 ******************************************************************************
 */
uint8 Get_BMS_Ver(void)
{
    return s_bmsVer;
}

/**
 ******************************************************************************
 * @brief      Set_BMS_Ver
 * @param[in]  uint8
 * @param[out] uint8
 * @retval	   0x00:新国标    0xA5:老国标
 * @details
 *
 * @note	   设置BMS版本
 ******************************************************************************
 */
uint8 Set_BMS_Ver(uint8 ver)
{
    s_bmsVer = ver;
    return s_bmsVer;
}

/**
 ******************************************************************************
 * @brief      Get_BMS_RecvFlg
 * @param[in]  uint32 pgn
 * @param[out] RECV_FLAG
 * @retval	   0x00:未接收    0x55:已接收 0xFF:无效
 * @details
 *
 * @note	   根据对应pgn获取接收状态
 ******************************************************************************
 */
RECV_FLAG Get_BMS_RecvFlg(uint32 pgn)
{
    const BMS_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_BmsRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return eRecvFlag_Null;
    }

    return pRecvCtrl->recvFlg;
}

/**
 ******************************************************************************
 * @brief      Clr_BMS_RecvFlg
 * @param[in]  uint32 pgn
 * @param[out] void
 * @retval	   0x00:未接收    0x55:已接收 0xFF:无效
 * @details
 *
 * @note		清除对应pgn接收状态
 ******************************************************************************
 */
void Clr_BMS_RecvFlg(uint32 pgn)
{
    BMS_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_BmsRecvCtrl(pgn);

    if (NULL != pRecvCtrl)
    {
        pRecvCtrl->recvFlg = eRecvFlag_No;
    }
}

/**
 ******************************************************************************
 * @brief      Get_BmsComState
 * @param[in]  None
 * @param[out] COM_STATE
 * @retval	   0x00:正常    0x01:超时 0x02:中断
 * @details
 *
 * @note	   获取bms通信状态
 ******************************************************************************
 */
COM_STATE Get_BmsComState(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    return pBmsCtrl->comState;
}

/**
 ******************************************************************************
 * @brief      chargePauseFlg
 * @param[in]  None
 * @param[out] None
 * @retval	   0x00:充电暂停     0x01:
充电允许
 * @details
 *
 * @note		暂停充电标志，可设置
 ******************************************************************************
 */
uint8 chargePauseFlg(uint8 wr, uint8 flag)
{
    static uint8 pauseFlg = enumAllowFlag_Allow;
    if (wr)
    {
        pauseFlg = flag;
    }
    return pauseFlg;
}

/**
 ******************************************************************************
 * @brief      chargePauseFlg
 * @param[in]  None
 * @param[out] None
 * @retval	   0x00:充电暂停     0x01:
充电允许
 * @details
 *
 * @note		BSM充电允许标志&设置充电允许标志
 ******************************************************************************
 */
uint8 Get_ChargePauseFlg(void)
{
    BMS_DATA *pBmsData = &bmsData;

    return (pBmsData->strBSM.strBatteryCtrl.chargeAllowFlg & chargePauseFlg(FALSE, enumAllowFlag_Allow));
}

/**
 ******************************************************************************
 * @brief      chargeReconnectFinishFlg
 * @param[in]  None
 * @param[out] None
 * @retval
 * @details
 *
 * @note	   bms三次重连标志
 ******************************************************************************
 */
uint8 chargeReconnectFinishFlg(uint8 wr, uint8 flag)
{
    static uint8 finishFlg = FALSE;
    if (wr)
    {
        finishFlg = flag;
    }
    return finishFlg;
}

/**
 ******************************************************************************
 * @brief      chargeReconnectFlg
 * @param[in]  None
 * @param[out] None
 * @retval
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 chargeReconnectFlg(uint8 wr, CHARGE_ALLOW_FLAG flag)
{
    static uint8 reConnectFlg = enumAllowFlag_Allow;
    if (wr)
    {
        reConnectFlg = flag;
    }
    return reConnectFlg;
}

/**
 ******************************************************************************
 * @brief      Get_SPN_2829
 * @param[in]  None
 * @param[out] uint8
 * @retval
 * @details
 *
 * @note	   获取BRO准备状态   0x00:车辆未准备就绪 0xAA:车辆准备就绪 0xff:无效
 ******************************************************************************
 */
uint8 Get_SPN_2829(void)
{
    BMS_DATA *pBmsData = &bmsData;

    return pBmsData->strBRO.bmsPrepareState;
}

/**
 ******************************************************************************
 * @brief      Get_SPN_2830
 * @param[in]  None
 * @param[out] uint8
 * @retval
 * @details
 *
 * @note		获取CRO准备状态   0x00:充电机未准备就绪 0xAA:充电机准备就绪 0xff:无效
 ******************************************************************************
 */
uint8 Get_SPN_2830(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    return pBmsCtrl->spn_2830;
}

/**
 ******************************************************************************
 * @brief      Get_Europe_Pre
 * @param[in]  None
 * @param[out] uint8
 * @retval
 * @details
 *
 * @note		获取欧标预充状态
 ******************************************************************************
 */
uint8 Get_Europe_Pre(void)
{
    BMS_DATA *pBmsData = &bmsData;

    return pBmsData->strBCL.plcPreChargeStatus;
}

/**
 ******************************************************************************
 * @brief      Set_BmsOvertimeDealFlag
 * @param[in]  RESULT dealFlag
 * @param[out] None
 * @retval
 * @details
 *
 * @note
 ******************************************************************************
 */
void Set_BmsOvertimeDealFlag(RESULT dealFlag)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    pBmsCtrl->overTimeDealFlg = dealFlag;

    return;
}

/**
 ******************************************************************************
 * @brief      Get_BMS_OverTimePgn
 * @param[in]  None
 * @param[out] uint32
 * @retval
 * @details
 *
 * @note
 ******************************************************************************
 */
uint32 Get_BMS_OverTimePgn(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    return pBmsCtrl->overTimePgnforTcu;
}

/**
 ******************************************************************************
 * @brief      Get_BMS_OverTimePgn
 * @param[in]  None
 * @param[out] uint16
 * @retval
 * @details
 *
 * @note		获取BMV单体电池中的最低电压
 ******************************************************************************
 */
uint16 Get_LowestSigVol(void)
{
    BMV_DATA strBmv;
    uint8 tmp = 0;
    uint16 minSigVol = 0;

    Get_BMS_Data(BMS_PGN_BMV, &strBmv);

    minSigVol = TwoUint8ToUint16(&strBmv.singleVoltage[0][0]);
    for (tmp = 0; tmp < 256; tmp++)
    {
        minSigVol = (minSigVol >= TwoUint8ToUint16(&strBmv.singleVoltage[tmp][0])) ? TwoUint8ToUint16(&strBmv.singleVoltage[tmp][0]) : minSigVol;
        if (minSigVol == 0)
        {
            break;
        }
    }

    return minSigVol;
}

/**
 ******************************************************************************
 * @brief      Get_BMS_Data
 * @param[in]  uint32 pgn, void *buf
 * @param[out] None
 * @retval
 * @details
 *
 * @note		根据pgn获取对用BMS数据
 ******************************************************************************
 */
void Get_BMS_Data(uint32 pgn, void *buf)
{
    uint8 *pInbuf = (uint8 *)buf;

    switch (pgn)
    {
    case BMS_PGN_BHM:
        memcpy(pInbuf, &bmsData.strBHM, sizeof(bmsData.strBHM));
        break;
    case BMS_PGN_BRM:
        memcpy(pInbuf, &bmsData.strBRM, sizeof(bmsData.strBRM));
        break;
    case BMS_PGN_BCP:
        memcpy(pInbuf, &bmsData.strBCP, sizeof(bmsData.strBCP));
        break;
    case BMS_PGN_BRO:
        memcpy(pInbuf, &bmsData.strBRO, sizeof(bmsData.strBRO));
        break;
    case BMS_PGN_BCL:
        memcpy(pInbuf, &bmsData.strBCL, sizeof(bmsData.strBCL));
        break;
    case BMS_PGN_BCS:
        memcpy(pInbuf, &bmsData.strBCS, sizeof(bmsData.strBCS));
        break;
    case BMS_PGN_BSM:
        memcpy(pInbuf, &bmsData.strBSM, sizeof(bmsData.strBSM));
        break;
    case BMS_PGN_BMV:
        memcpy(pInbuf, &bmsData.strBMV, sizeof(bmsData.strBMV));
        break;
    case BMS_PGN_BMT:
        memcpy(pInbuf, &bmsData.strBMT, sizeof(bmsData.strBMT));
        break;
    case BMS_PGN_BSP:
        memcpy(pInbuf, &bmsData.strBSP, sizeof(bmsData.strBSP));
        break;
    case BMS_PGN_BST:
        memcpy(pInbuf, &bmsData.strBST, sizeof(bmsData.strBST));
        break;
    case BMS_PGN_BSD:
        memcpy(pInbuf, &bmsData.strBSD, sizeof(bmsData.strBSD));
        break;
    case BMS_PGN_BEM:
        memcpy(pInbuf, &bmsData.strBEM, sizeof(bmsData.strBEM));
        break;
    case BMS_PGN_CSD:
        memcpy(pInbuf, &bmsData.strCSD, sizeof(bmsData.strCSD));
        break;
    case BMS_PGN_CEM:
        memcpy(pInbuf, &bmsData.strCEM, sizeof(bmsData.strCEM));
        break;
    default:
        break;
    }

    return;
}

/**
 ******************************************************************************
 * @brief      Get_EcPlcAagVal
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note	   获取欧标信号衰减值
 ******************************************************************************
 */
uint8 Get_EcPlcAagVal(void)
{
    return bmsData.strBHM.plcAagVal;
}
/**
 ******************************************************************************
 * @brief      Get_EcPlcCpStatus
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note		获取欧标CP状态
 ******************************************************************************
 */
uint8 Get_EcPlcCpStatus(void)
{
    return bmsData.strBHM.plc_CpStatus.CpStatus;
}

/**
 ******************************************************************************
 * @brief      Get_HighestSingleVoltageNO
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 Get_HighestSingleVoltageNO(void)
{
    return bmsData.strBSM.highestSingleVoltageNO;
}

/**
 ******************************************************************************
 * @brief      Get_HighestTemperature
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 Get_HighestTemperature(void)
{
    return bmsData.strBSM.highestTemperature;
}

/**
 ******************************************************************************
 * @brief      Get_HighestTemperatureNO
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 Get_HighestTemperatureNO(void)
{
    return bmsData.strBSM.highestTemperatureNO;
}

/**
 ******************************************************************************
 * @brief      Get_LowestTemperature
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 Get_LowestTemperature(void)
{
    return bmsData.strBSM.lowestTemperature;
}

/**
 ******************************************************************************
 * @brief      Get_LowestTemperatureNO
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 Get_LowestTemperatureNO(void)
{
    return bmsData.strBSM.lowestTemperatureNO;
}

/**
 ******************************************************************************
 * @brief      Get_StrBatteryState
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
BATTERY_STATE Get_StrBatteryState(void)
{
    return bmsData.strBSM.strBatteryState;
}

/**
 ******************************************************************************
 * @brief      Get_StrBatteryCtrl
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
BATTERY_CTRL Get_StrBatteryCtrl(void)
{
    return bmsData.strBSM.strBatteryCtrl;
}

/**
 ******************************************************************************
 * @brief      Get_Bcp_HighestCurrent
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint16 Get_Bcp_HighestCurrent(void)
{
    uint16 Plc_Cur = 0;
    uint16 Ret_Cur = 0;
    Plc_Cur = TwoUint8ToUint16(bmsData.strBCP.highestCurrent);
    Ret_Cur = (BITS(Plc_Cur, 15) ? (4000 + (Plc_Cur & 0x7fff)) : (4000 - Plc_Cur));
    return Ret_Cur; // 单位:mA;
}
/*----------------------------bms.c--------------------------------*/
