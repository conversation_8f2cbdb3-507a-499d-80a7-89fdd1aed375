/**
 ******************************************************************************
 * @file      publicPort.c
 * @brief     C Source file of publicPort.c.
 * @details   This file including all API functions's
 *            implement of publicPort.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */
#include <types.h>
#include <stddef.h>
#include <maths.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <tasklib.h>
#include <transceiver.h>
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
static CHANNEL_STRUCT *s_channel_array[CHANNEL_MAX_LENGTH]; // 通道控制静态变量.
/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
static uint32
get_send_lastTime(int channel, int dev, int id);
static uint32
get_send_remainTime(int channel, int dev, int id);
static uint32
get_recv_lastTime(int channel, int dev, int id);
static uint8
check_recvTimeout_enable(int channel, int dev, int id);
static uint32
get_send_startTime(int channel, int dev, int id);
static void
set_sendFlag(int channel, int dev, int id, uint8 value);
static void
set_recvFlag(int channel, int dev, int id, uint8 value);
static void
set_recvTimeoutFlag(int channel, int dev, int id, uint8 value);
/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
/**
 ******************************************************************************
 * @brief      get_sum_16.
 * @param[in]  None
 * @param[out] None
 * @retval     计算16位和校验码.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint16
get_sum_16(uint8 *pBuf, int len)
{
    uint32 sum = 0;

    for (int i = 0; i < len; i++)
    {
        sum += pBuf[i];
    }

    return (uint16)(sum);
}

/**
 ******************************************************************************
 * @brief      transceiver_init.
 * @param[in]  None
 * @param[out] None
 * @retval     收发器初始化.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void transceiver_init(void)
{
    // 收发器通道初始化
    for (int i = 0; i < ARRAY_SIZE(s_channel_array); i++)
    {
        s_channel_array[i] = NULL;
    }
}

/**
 ******************************************************************************
 * @brief      transceiver_channel_init.
 * @param[in]  None
 * @param[out] None
 * @retval     通道注册.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static CHANNEL_STRUCT *
transceiver_channel_init(int channel)
{
    for (int i = 0; i < ARRAY_SIZE(s_channel_array); i++)
    {
        if (NULL == s_channel_array[i])
        {
            s_channel_array[i] = malloc(sizeof(CHANNEL_STRUCT));
            if (NULL != s_channel_array[i])
            {
                printf("malloc channel...%d\n", channel);
                memset(s_channel_array[i], 0x00, sizeof(CHANNEL_STRUCT));
                s_channel_array[i]->channelId = channel;
                return (s_channel_array[i]);
            }
        }

        if (channel == s_channel_array[i]->channelId)
        {
            return (s_channel_array[i]);
        }
    }
    printf("transceiver_channel_init()...error!\n");
    return NULL;
}

/**
 ******************************************************************************
 * @brief      reg_channel.
 * @param[in]  None
 * @param[out] None
 * @retval     注册通道回调函数;
 *
 * @details
 *
 * @note      通道收发处理.
 ******************************************************************************
 */
void reg_channel(int channel,
                 CHANNEL_RECEIVE_PROC recvFunc,
                 CHANNEL_TRANSFER_PROC transFunc)
{
    CHANNEL_STRUCT *p_channel = transceiver_channel_init(channel);

    if (NULL != p_channel)
    {
        if (NULL != recvFunc)
        {
            p_channel->receiv_proc = recvFunc;
        }

        if (NULL != transFunc)
        {
            p_channel->transfer_proc = transFunc;
        }
    }
}

/**
 ******************************************************************************
 * @brief      transceiver_dev_init.
 * @param[in]  None
 * @param[out] None
 * @retval     设备注册.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static DEVICE_STRUCT *
transceiver_dev_init(int channel, int dev)
{
    CHANNEL_STRUCT *p_channel = transceiver_channel_init(channel);

    if (NULL != p_channel)
    {
        for (int i = 0; i < ARRAY_SIZE(p_channel->device_array); i++)
        {
            if (NULL == p_channel->device_array[i])
            {
                p_channel->device_array[i] = malloc(sizeof(DEVICE_STRUCT));
                if (NULL != p_channel->device_array[i])
                {
                    printf("malloc device...%d\n", dev);
                    memset(p_channel->device_array[i], 0x00, sizeof(DEVICE_STRUCT));
                    p_channel->device_array[i]->deviceId = dev;
                    return (p_channel->device_array[i]);
                }
            }

            if (dev == p_channel->device_array[i]->deviceId)
            {
                return (p_channel->device_array[i]);
            }
        }
    }
    printf("transceiver_dev_init()...error!\n");
    return NULL;
}

/**
 ******************************************************************************
 * @brief      get_receiver.
 * @param[in]  None
 * @param[out] None
 * @retval     返回接收器.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static RECEIVER_STRUCT *
get_receiver(int channel, int dev, int id)
{
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        for (int i = 0; i < ARRAY_SIZE(p_dev->receiver_array); i++)
        {
            if (id == p_dev->receiver_array[i]->id)
            {
                return (p_dev->receiver_array[i]);
            }
        }
    }
    printf("get_receiver()...error!\n");
    return NULL;
}

/**
 ******************************************************************************
 * @brief      get_transfer.
 * @param[in]  None
 * @param[out] None
 * @retval     返回发送器.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static TRANSFER_STRUCT *
get_transfer(int channel, int dev, int id)
{
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        for (int i = 0; i < ARRAY_SIZE(p_dev->transfer_array); i++)
        {
            if (id == p_dev->transfer_array[i]->id)
            {
                return (p_dev->transfer_array[i]);
            }
        }
    }
    printf("get_transfer()...error\n");
    return NULL;
}

/**
 ******************************************************************************
 * @brief      receiver_init.
 * @param[in]  None
 * @param[out] None
 * @retval     接收器初始化.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static RECEIVER_STRUCT *
receiver_init(DEVICE_STRUCT *p_dev, int id)
{
    for (int i = 0; i < ARRAY_SIZE(p_dev->receiver_array); i++)
    {
        if (NULL == p_dev->receiver_array[i])
        {
            p_dev->receiver_array[i] = malloc(sizeof(RECEIVER_STRUCT));
            if (NULL != p_dev->receiver_array[i])
            {
                memset(p_dev->receiver_array[i], 0x00, sizeof(RECEIVER_STRUCT));
                p_dev->receiver_array[i]->id = id;
                return (p_dev->receiver_array[i]);
            }
        }

        if (id == p_dev->receiver_array[i]->id)
        {
            return (p_dev->receiver_array[i]);
        }
    }
    printf("receiver_init()...error\n");
    return NULL;
}

/**
 ******************************************************************************
 * @brief      transfer_init.
 * @param[in]  None
 * @param[out] None
 * @retval     发送器初始化.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static TRANSFER_STRUCT *
transfer_init(DEVICE_STRUCT *p_dev, int id)
{
    for (int i = 0; i < ARRAY_SIZE(p_dev->transfer_array); i++)
    {
        if (NULL == p_dev->transfer_array[i])
        {
            p_dev->transfer_array[i] = malloc(sizeof(TRANSFER_STRUCT));
            if (NULL != p_dev->transfer_array[i])
            {
                memset(p_dev->transfer_array[i], 0x00, sizeof(TRANSFER_STRUCT));
                p_dev->transfer_array[i]->id = id;
                return (p_dev->transfer_array[i]);
            }
        }

        if (id == p_dev->transfer_array[i]->id)
        {
            return (p_dev->transfer_array[i]);
        }
    }
    printf("transfer_init()...error\n");
    return NULL;
}

/**
 ******************************************************************************
 * @brief      reg_receiver.
 * @param[in]  None
 * @param[out] None
 * @retval     接收器注册函数.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void reg_receiver(int channel,
                  int dev,
                  int id,
                  int length,
                  RECEIVER_PROC_CALLBACK proc,
                  int timeout_ms,
                  RECEIVER_TIMEOUT_PROC_CALLBACK timeout_proc,
                  RECEIVER_TIMEOUT_PROC_CALLBACK timeout_rst_proc)
{
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        RECEIVER_STRUCT *p_receiver = receiver_init(p_dev, id);
        if (NULL != p_receiver)
        {
            p_receiver->ctrl_reg = malloc(sizeof(RECEIVER_CTRL));
            if (NULL != p_receiver->ctrl_reg)
            {
                memset(p_receiver->ctrl_reg, 0x00, sizeof(RECEIVER_CTRL));
            }
            p_receiver->proc = proc;

            p_receiver->data = malloc(length);
            if (NULL != p_receiver->data)
            {
                p_receiver->length = 0;
                memset(p_receiver->data, 0x00, length);
            }

            p_receiver->timeout_ms = timeout_ms;
            p_receiver->timeout_proc = timeout_proc;
            p_receiver->timeout_rst_proc = timeout_rst_proc;
        }
    }
    else
    {
        printf("reg_receiver()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      reg_transfer.
 * @param[in]  None
 * @param[out] None
 * @retval     发送器注册函数.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void reg_transfer(int channel,
                  int dev,
                  int id,
                  int length,
                  TRANSFER_PROC_CALLBACK proc,
                  int period_ms)
{
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        TRANSFER_STRUCT *p_transfer = transfer_init(p_dev, id);
        if (NULL != p_transfer)
        {
            p_transfer->ctrl_reg = malloc(sizeof(TRANSFER_CTRL));
            if (NULL != p_transfer->ctrl_reg)
            {
                memset(p_transfer->ctrl_reg, 0x00, sizeof(TRANSFER_CTRL));
            }
            p_transfer->proc = proc;
            p_transfer->period_ms = period_ms; // 发送周期,ms

            p_transfer->data = malloc(length);
            if (NULL != p_transfer->data)
            {
                p_transfer->length = 0;
                memset(p_transfer->data, 0x00, length);
            }
        }
    }
    else
    {
        printf("reg_transfer()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      send_complete.
 * @param[in]  None
 * @param[out] None
 * @retval     发送结束处理.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
send_complete(int channel, int dev, int id)
{
    set_sendFlag(channel, dev, id, TRUE);
    set_send_lastTime(channel, dev, id, 0);
    if (SEND_FOREVER != get_send_remainTime(channel, dev, id))
    {
        if ((SEND_ONCE == get_send_remainTime(channel, dev, id)) || (abs(tickGet() - get_send_startTime(channel, dev, id)) > get_send_remainTime(channel, dev, id)))
        {
            set_sendCompleteFlag(channel, dev, id, TRUE);
            set_send_enable(channel, dev, id, eEnableFlag_Off);
        }
    }
}

/**
 ******************************************************************************
 * @brief      check_frame_bussy.
 * @param[in]  None
 * @param[out] None
 * @retval     检测分帧发送bussy.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint8
check_frame_bussy(int channel, int dev)
{
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        return (p_dev->frameTransfer.bussy);
    }
    else
    {
        printf("check_frame_bussy()...error!\n");
    }
    return FALSE;
}

/**
 ******************************************************************************
 * @brief      check_frame_send_enable.
 * @param[in]  None
 * @param[out] None
 * @retval     分帧发送使能检测(配置某个或某些数据采用分帧形式发送).
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint8
check_frame_send_enable(int channel, int dev, int id)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        return (p_transfer->ctrl_reg->frameEnable);
    }
    else
    {
        printf("check_frame_send_enable()...error!\n");
    }
    return eEnableFlag_Off;
}

/**
 ******************************************************************************
 * @brief      set_frame_send_enable.
 * @param[in]  None
 * @param[out] None
 * @retval     分帧发送使能.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void set_frame_send_enable(int channel, int dev, int id, uint8 value)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        p_transfer->ctrl_reg->frameEnable = value;
    }
    else
    {
        printf("set_frame_send_enable()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      set_frame_bussy.
 * @param[in]  None
 * @param[out] None
 * @retval     设置分帧发送bussy.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
set_frame_bussy(int channel, int dev, uint8 value)
{
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        p_dev->frameTransfer.bussy = value;
    }
    else
    {
        printf("set_frame_bussy()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      get_frame_send_lastTime.
 * @param[in]  None
 * @param[out] None
 * @retval     查询分帧发送上次时间.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint32
get_frame_send_lastTime(int channel, int dev)
{
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        return (p_dev->frameTransfer.lastTime);
    }
    else
    {
        printf("get_frame_send_lastTime()...error!\n");
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      set_frame_send_lastTime.
 * @param[in]  None
 * @param[out] None
 * @retval     设置分帧发送上次时间.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
set_frame_send_lastTime(int channel, int dev, int offset)
{
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        p_dev->frameTransfer.lastTime = tickGet() + offset;
    }
    else
    {
        printf("get_frame_send_lastTime()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      set_frame_recv_enable.
 * @param[in]  None
 * @param[out] None
 * @retval     设置接收分帧使能.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void set_frame_recv_enable(int channel, int dev, int id, uint8 value)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        p_receiver->ctrl_reg->frameEnable = value;
    }
    else
    {
        printf("set_frame_recv_enable()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      check_frame_recv_enable.
 * @param[in]  None
 * @param[out] None
 * @retval     检测接收分帧使能.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint8
check_frame_recv_enable(int channel, int dev, int id)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        return (p_receiver->ctrl_reg->frameEnable);
    }
    else
    {
        printf("check_frame_recv_enable()...error!\n");
    }
    return FALSE;
}

/**
 ******************************************************************************
 * @brief      write_recv_frame.
 * @param[in]  None
 * @param[out] None
 * @retval     写接收分帧.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static int
write_recv_frame(int channel, int dev, int id, uint8 rxBuf[], int rxLength)
{
    int resl = 0;
    uint16 crc16 = 0;
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);
    TRANSEIVER_FRAME_PACKAGE *p_frame = (TRANSEIVER_FRAME_PACKAGE *)rxBuf;
    TRANSCEIVER_FRAME_STR *pData = NULL;

    if (NULL != p_receiver)
    {
        if (0x01 == p_frame->index)
        {
            p_receiver->ctrl_reg->index = p_frame->index;
            memcpy(p_receiver->ctrl_reg->data, p_frame->buf,
                   TRANSFER_FRAME_SIZE);
        }
        else
        {
            if (p_frame->index == ++p_receiver->ctrl_reg->index)
            {
                memcpy(p_receiver->ctrl_reg->data + (p_frame->index - 1) * TRANSFER_FRAME_SIZE,
                       p_frame->buf, TRANSFER_FRAME_SIZE);
            }

            pData = (TRANSCEIVER_FRAME_STR *)p_receiver->ctrl_reg->data;
            if (p_frame->index == pData->toatal)
            {
                crc16 = BUILD_UINT16(pData->buf[pData->len], pData->buf[pData->len + 1]);
                if (crc16 == get_sum_16(p_receiver->ctrl_reg->data, pData->len + 3))
                {
                    resl = 1;
                }
                else
                {
                    printf("write_recv_frame...cs error!\n");
                    printf("get_sum_16...%xH\n", get_sum_16(p_receiver->ctrl_reg->data, pData->len + 3));
                    printf("crc16...%xH\n", crc16);
                }
            }
        }
    }
    else
    {
        printf("write_recv_frame()...error!\n");
    }
    return resl;
}

/**
 ******************************************************************************
 * @brief      read_recv_frame.
 * @param[in]  None
 * @param[out] None
 * @retval     读接收分帧数据.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static int
read_recv_frame(int channel, int dev, int id, uint8 txBuf[], int size)
{
    int length = 0;
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);
    TRANSCEIVER_FRAME_STR *pCtrl = NULL;

    if (NULL != p_receiver)
    {
        pCtrl = (TRANSCEIVER_FRAME_STR *)p_receiver->ctrl_reg->data;
        if (pCtrl->len <= size)
        {
            length = pCtrl->len;
            memcpy(txBuf, pCtrl->buf, pCtrl->len);
        }
    }
    else
    {
        printf("read_recv_frame()...error!\n");
    }
    return length;
}

/**
 ******************************************************************************
 * @brief      read_send_frame.
 * @param[in]  None
 * @param[out] None
 * @retval     读发送分帧数据.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static int
read_send_frame(int channel, int dev, int *id, uint8 txBuf[], int size)
{
    int length = 0;
    TRANSEIVER_FRAME_PACKAGE *p_frame = (TRANSEIVER_FRAME_PACKAGE *)txBuf;
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        *id = p_dev->frameTransfer.id;
        if (p_dev->frameTransfer.index <= p_dev->frameTransfer.total)
        {
            if (TRANSFER_FRAME_SIZE + 1 <= size)
            {
                p_frame->index = p_dev->frameTransfer.index;
                memcpy(p_frame->buf, p_dev->frameTransfer.data + (p_dev->frameTransfer.index - 1) * TRANSFER_FRAME_SIZE, TRANSFER_FRAME_SIZE);
                p_dev->frameTransfer.index++;
                length = TRANSFER_FRAME_SIZE + 1; // 发送数据长度
            }
        }
    }
    else
    {
        printf("read_send_frame()...error!\n");
    }
    return length;
}

/**
 ******************************************************************************
 * @brief      write_send_frame.
 * @param[in]  None
 * @param[out] None
 * @retval     写分帧数据.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
write_send_frame(int channel, int dev, int id, uint8 rxBuf[], int rxLen)
{
    uint16 sum = 0;
    int index = 0;
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        p_dev->frameTransfer.id = id;
        if (rxLen + 5 <= sizeof(p_dev->frameTransfer.data))
        {
            p_dev->frameTransfer.index = 1;                                 /**< 分帧编号1.2.3...*/
            p_dev->frameTransfer.total = (rxLen + 5) / TRANSFER_FRAME_SIZE; // 总帧数(1)+有效数据长度(2)+校验码(2)
            if ((rxLen + 5) % TRANSFER_FRAME_SIZE != 0)
            {
                p_dev->frameTransfer.total++;
            }

            p_dev->frameTransfer.data[index++] = p_dev->frameTransfer.total;
            p_dev->frameTransfer.data[index++] = BREAK_UINT16(rxLen, 0);
            p_dev->frameTransfer.data[index++] = BREAK_UINT16(rxLen, 1);

            memcpy(&p_dev->frameTransfer.data[index], rxBuf, rxLen);
            index += rxLen;

            sum = get_sum_16(p_dev->frameTransfer.data, index);
            p_dev->frameTransfer.data[index++] = BREAK_UINT16(sum, 0);
            p_dev->frameTransfer.data[index++] = BREAK_UINT16(sum, 1);
            p_dev->frameTransfer.length = index;
        }
    }
    else
    {
        printf("write_send_frame()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      device_receiver_handle.
 * @param[in]  None
 * @param[out] None
 * @retval     设备接收器处理.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
device_receiver_handle(int channel, int dev, int id, uint8 rxBuf[], int rxLen)
{
    STWORKBUF strBuf;
    RECEIVER_STRUCT *p_receiver = NULL;
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (eEnableFlag_Off == check_recv_enable(channel, dev, id)) /**< 接收使能检测!*/
    {
        return;
    }

    // 分帧接收处理.
    if (eEnableFlag_On == check_frame_recv_enable(channel, dev, id))
    {
        if (0 == write_recv_frame(channel, dev, id, rxBuf, rxLen)) // 分帧接收
        {
            return;
        }
        strBuf.iDataLen = read_recv_frame(channel, dev, id, strBuf.ucDataBuf,
                                          sizeof(strBuf.ucDataBuf));
    }
    else
    {
        strBuf.iDataLen = rxLen;
        memcpy(strBuf.ucDataBuf, rxBuf, rxLen);
    }

    /**< 接收处理函数*/
    for (int i = 0; i < ARRAY_SIZE(p_dev->receiver_array); i++)
    {
        p_receiver = (RECEIVER_STRUCT *)p_dev->receiver_array[i];
        if ((NULL != p_receiver) && (id == p_receiver->id) && (NULL != p_receiver->proc))
        {
            set_recvFlag(channel, dev, id, TRUE);
            set_recv_lastTime(channel, dev, id, 0);
            if (strBuf.iDataLen <= sizeof(p_receiver->data)) // 保存接收数据帧
            {
                p_receiver->length = strBuf.iDataLen;
                memcpy(p_receiver->data, &strBuf.ucDataBuf, strBuf.iDataLen);
            }
            p_receiver->proc(dev, strBuf.ucDataBuf, strBuf.iDataLen);
        }
    }
}

/**
 ******************************************************************************
 * @brief      device_transfer_handle.
 * @param[in]  None
 * @param[out] None
 * @retval     设备发送器处理程序.
 *
 * @details
 *
 * @note       分帧发送周期固定为50ms;
 ******************************************************************************
 */
static void
device_transfer_handle(int channel, int dev)
{
    TRANSFER_STRUCT *p_transfer = NULL;
    STWORKBUF strBuff;
    int id = 0;
    CHANNEL_STRUCT *p_channel = transceiver_channel_init(channel);
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    if (NULL != p_dev)
    {
        memset(&strBuff, 0x00, sizeof(strBuff));
        if (TRUE == check_frame_bussy(channel, dev))
        {
            if (abs(tickGet() - get_frame_send_lastTime(channel, dev)) >= FRAM_TRANSFER_TICK)
            {
                set_frame_send_lastTime(channel, dev, 0);
                strBuff.iDataLen = read_send_frame(channel, dev, &id,
                                                   strBuff.ucDataBuf, sizeof(strBuff.ucDataBuf));
                if (strBuff.iDataLen > 0)
                {
                    if (NULL != p_channel->transfer_proc)
                    {
                        p_channel->transfer_proc(channel, dev, id,
                                                 strBuff.ucDataBuf, strBuff.iDataLen);
                    }
                }
                else
                {
                    set_frame_bussy(channel, dev, FALSE);
                    send_complete(channel, dev, id);
                }
            }
            return;
        }

        for (int i = 0; i < ARRAY_SIZE(p_dev->transfer_array); i++)
        {

            p_transfer = (TRANSFER_STRUCT *)p_dev->transfer_array[i];
            if (NULL == p_transfer)
            {
                break;
            }

            if (eEnableFlag_On != check_send_enable(channel, dev, p_transfer->id))
            {
                continue;
            }
            //
            if ((abs(tickGet() - get_send_lastTime(channel, dev, p_transfer->id)) < p_transfer->period_ms))
            {
                continue;
            }

            //
            if (NULL != p_transfer->proc)
            {
                // printf("device_transfer_handle5 %d\n",i);
                strBuff.iDataLen = p_transfer->proc(channel, dev,
                                                    strBuff.ucDataBuf, sizeof(strBuff.ucDataBuf));
                if ((strBuff.iDataLen > 8) || (eEnableFlag_On == check_frame_send_enable(channel, dev, p_transfer->id)))
                {
                    write_send_frame(channel, dev, p_transfer->id,
                                     strBuff.ucDataBuf, strBuff.iDataLen);
                    set_frame_bussy(channel, dev, TRUE);
                    set_frame_send_lastTime(channel, dev, 0);
                }
                else if (NULL != p_channel->transfer_proc)
                {
                    p_channel->transfer_proc(channel, dev, p_transfer->id,
                                             strBuff.ucDataBuf, strBuff.iDataLen);
                    send_complete(channel, dev, p_transfer->id);
                    // printf("transceiver_handle <%d>  <channelIdid:%8x><deviceId:%8x>\n",i,p_channel->channelId,p_transfer->id);
                }
            }
        }
    }
}

/**
 ******************************************************************************
 * @brief      channel_receiver_handle.
 * @param[in]  None
 * @param[out] None
 * @retval     通道接收器处理函数.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
channel_receiver_handle(int channel)
{
    STWORKBUF strBuf;
    int dev = 0, id = 0;
    CHANNEL_STRUCT *p_channel = transceiver_channel_init(channel);

    memset(&strBuf, 0x00, sizeof(STWORKBUF));
    if (NULL != p_channel->receiv_proc)
    {
        strBuf.iDataLen = p_channel->receiv_proc(channel, &dev, &id,
                                                 strBuf.ucDataBuf, sizeof(strBuf.ucDataBuf));
        if (strBuf.iDataLen > 0)
        {
            device_receiver_handle(channel, dev, id, strBuf.ucDataBuf,
                                   strBuf.iDataLen);
        }
    }
}

/**
 ******************************************************************************
 * @brief      receiver_timeout_handle.
 * @param[in]  None
 * @param[out] None
 * @retval     接收器超时处理函数(外部调用).
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void receiver_timeout_handle(int channel, int dev)
{
    RECEIVER_STRUCT *p_receiver = NULL;
    DEVICE_STRUCT *p_dev = transceiver_dev_init(channel, dev);

    for (int i = 0; i < ARRAY_SIZE(p_dev->receiver_array); i++)
    {
        p_receiver = (RECEIVER_STRUCT *)p_dev->receiver_array[i];
        if ((NULL == p_receiver) || (NULL == p_receiver->timeout_proc))
        {
            continue;
        }

        if (((eEnableFlag_On == check_recv_enable(channel, dev, p_receiver->id))) && (eEnableFlag_On == check_recvTimeout_enable(channel, dev, p_receiver->id)))
        {
            if (TRUE == get_recvTimeoutFlag(channel, dev, p_receiver->id))
            {
                if (abs(tickGet() - get_recv_lastTime(channel, dev, p_receiver->id)) < p_receiver->timeout_ms)
                {
                    set_recvTimeoutFlag(channel, dev, p_receiver->id, FALSE);
                }
            }
            else if (FALSE == get_recvTimeoutFlag(channel, dev, p_receiver->id))
            {
                if (abs(tickGet() - get_recv_lastTime(channel, dev, p_receiver->id)) >= p_receiver->timeout_ms)
                {
                    set_recvTimeoutFlag(channel, dev, p_receiver->id, TRUE);
                    p_receiver->timeout_proc(dev);
                }
            }
        }
    }
}

/**
 ******************************************************************************
 * @brief      channel_receiver_timeout_handle.
 * @param[in]  None
 * @param[out] None
 * @retval     通道接收器超时处理.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
channel_receiver_timeout_handle(int channel)
{
    DEVICE_STRUCT *p_dev = NULL;
    CHANNEL_STRUCT *p_channel = transceiver_channel_init(channel);

    for (int i = 0; i < ARRAY_SIZE(p_channel->device_array); i++)
    {
        p_dev = p_channel->device_array[i];
        if (NULL != p_dev)
        {
            receiver_timeout_handle(p_channel->channelId, p_dev->deviceId);
        }
    }
}

/**
 ******************************************************************************
 * @brief      channel_transfer_handle.
 * @param[in]  None
 * @param[out] None
 * @retval     通道发送器处理程序.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
channel_transfer_handle(int channel)
{
    DEVICE_STRUCT *p_dev = NULL;
    CHANNEL_STRUCT *p_channel = transceiver_channel_init(channel);

    for (int i = 0; i < ARRAY_SIZE(p_channel->device_array); i++)
    {
        p_dev = p_channel->device_array[i];
        if (NULL != p_dev)
        {
            device_transfer_handle(p_channel->channelId, p_dev->deviceId);
        }
    }
}

/**
 ******************************************************************************
 * @brief      transceiver_handle.
 * @param[in]  None
 * @param[out] None
 * @retval     发送器处理函数(外部调用).
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void transceiver_handle(void)
{
    CHANNEL_STRUCT *p_channel = NULL;
    if (FALSE == Check_ExtchargeEnable())
    {
        return;
    }
    for (int i = 0; i < ARRAY_SIZE(s_channel_array); i++)
    {
        p_channel = s_channel_array[i];
        if (NULL != p_channel)
        {
            channel_transfer_handle(p_channel->channelId);
            channel_receiver_handle(p_channel->channelId);
            channel_receiver_timeout_handle(p_channel->channelId);
        }
    }
}

/**
 ******************************************************************************
 * @brief      transceiver_handle.
 * @param[in]  None
 * @param[out] None
 * @retval     发送器处理函数(外部调用).
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void dual_transceiver_handle(void)
{
    CHANNEL_STRUCT *p_channel = NULL;
    for (int i = 0; i < ARRAY_SIZE(s_channel_array); i++)
    {
        p_channel = s_channel_array[i];
        if (NULL != p_channel)
        {
            channel_transfer_handle(p_channel->channelId);
            channel_receiver_handle(p_channel->channelId);
            channel_receiver_timeout_handle(p_channel->channelId);
        }
    }
}

/**
 ******************************************************************************
 * @brief      get_transfer_data.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
int get_transfer_data(int channel, int dev, int id, void **ptr)
{
    int resl = 0;
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        *ptr = p_transfer->data;
        resl = p_transfer->length;
    }
    else
    {
        printf("get_transfer_data()...error!\n");
    }
    return resl;
}

/**
 ******************************************************************************
 * @brief      get_receiver_data.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
int get_receiver_data(int channel, int dev, int id, void **ptr)
{
    int resl = 0;
    RECEIVER_STRUCT *p_reciver = get_receiver(channel, dev, id);

    if (NULL != p_reciver)
    {
        *ptr = p_reciver->data;
        resl = p_reciver->length;
    }
    else
    {
        printf("get_receiver_data()...error!\n");
    }
    return resl;
}

/**
 ******************************************************************************
 * @brief      set_send_enable.
 * @param[in]  None
 * @param[out] None
 * @retval     发送使能.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void set_send_enable(int channel, int dev, int id, uint8 value)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        p_transfer->ctrl_reg->enable = value;
    }
    else
    {
        printf("set_send_enable()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      check_send_enable.
 * @param[in]  None
 * @param[out] None
 * @retval     检测发送使能.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 check_send_enable(int channel, int dev, int id)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        return (p_transfer->ctrl_reg->enable);
    }
    else
    {
        printf("check_send_enable()...error!\n");
    }
    return eEnableFlag_Off;
}

/**
 ******************************************************************************
 * @brief      get_send_lastTime.
 * @param[in]  None
 * @param[out] None
 * @retval     查询上次发送时间.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint32
get_send_lastTime(int channel, int dev, int id)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        return (p_transfer->ctrl_reg->lastTime);
    }
    else
    {
        printf("get_send_lastTime()...error!\n");
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      set_send_lastTime.
 * @param[in]  None
 * @param[out] None
 * @retval     设置上次发送时间.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void set_send_lastTime(int channel, int dev, int id, int time)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        p_transfer->ctrl_reg->lastTime = tickGet() + time;
    }
    else
    {
        printf("set_send_lastTime()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      get_send_startTime.
 * @param[in]  None
 * @param[out] None
 * @retval     查询发送起始时间.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint32
get_send_startTime(int channel, int dev, int id)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        return (p_transfer->ctrl_reg->startTime);
    }
    else
    {
        printf("get_send_startTime()...error!\n");
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      set_send_startTime.
 * @param[in]  None
 * @param[out] None
 * @retval     设置发送起始时间.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void set_send_startTime(int channel, int dev, int id, int time)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        p_transfer->ctrl_reg->startTime = tickGet() + time;
    }
    else
    {
        printf("set_send_startTime()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      get_send_remainTime.
 * @param[in]  None
 * @param[out] None
 * @retval     查询发送剩余时间.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint32
get_send_remainTime(int channel, int dev, int id)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        return (p_transfer->ctrl_reg->remainTime);
    }
    else
    {
        printf("get_send_remainTime()...error!\n");
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      set_send_remainTime.
 * @param[in]  None
 * @param[out] None
 * @retval     设置发送剩余时间.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void set_send_remainTime(int channel, int dev, int id, uint32 time)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        p_transfer->ctrl_reg->remainTime = time;
    }
    else
    {
        printf("set_send_remainTime()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      get_sendFlag.
 * @param[in]  None
 * @param[out] None
 * @retval     查询发送标志.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 get_sendFlag(int channel, int dev, int id)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        return (p_transfer->ctrl_reg->flag);
    }
    else
    {
        printf("get_sendFlag()...error!\n");
    }
    return FALSE;
}

/**
 ******************************************************************************
 * @brief      set_sendFlag.
 * @param[in]  None
 * @param[out] None
 * @retval     设置发送标志.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
set_sendFlag(int channel, int dev, int id, uint8 value)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        p_transfer->ctrl_reg->flag = value;
    }
    else
    {
        printf("set_sendFlag()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      get_sendCompleteFlag.
 * @param[in]  None
 * @param[out] None
 * @retval     查询发送结束标志.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 get_sendCompleteFlag(int channel, int dev, int id)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        return (p_transfer->ctrl_reg->complete);
    }
    else
    {
        printf("get_sendCompleteFlag()...error!\n");
    }
    return FALSE;
}

/**
 ******************************************************************************
 * @brief      set_sendCompleteFlag.
 * @param[in]  None
 * @param[out] None
 * @retval     设置发送结束标志.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void set_sendCompleteFlag(int channel, int dev, int id, uint8 value)
{
    TRANSFER_STRUCT *p_transfer = get_transfer(channel, dev, id);

    if (NULL != p_transfer)
    {
        p_transfer->ctrl_reg->complete = value;
    }
    else
    {
        printf("set_sendCompleteFlag()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      set_recv_enable.
 * @param[in]  None
 * @param[out] None
 * @retval     接收使能.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void set_recv_enable(int channel, int dev, int id, uint8 value)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        p_receiver->ctrl_reg->enable = value;
    }
    else
    {
        printf("set_recv_enable()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      check_recv_enable.
 * @param[in]  None
 * @param[out] None
 * @retval     查询接收是否使能?
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 check_recv_enable(int channel, int dev, int id)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        return (p_receiver->ctrl_reg->enable);
    }
    else
    {
        printf("check_recv_enable()...error! chn:%2x,dev:%2x,id:%2x\n", channel, dev, id);
    }
    return eEnableFlag_Off;
}

/**
 ******************************************************************************
 * @brief      get_recv_lastTime.
 * @param[in]  None
 * @param[out] None
 * @retval     查询上次接收时间.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint32
get_recv_lastTime(int channel, int dev, int id)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        return (p_receiver->ctrl_reg->lastTime);
    }
    else
    {
        printf("get_recv_lastTime()...error!\n");
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      set_recv_lastTime.
 * @param[in]  None
 * @param[out] None
 * @retval     设置上次接收时间.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void set_recv_lastTime(int channel, int dev, int id, int time)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        p_receiver->ctrl_reg->lastTime = tickGet() + time;
    }
    else
    {
        printf("set_recv_lastTime()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      set_recvFlag.
 * @param[in]  None
 * @param[out] None
 * @retval     设置接收标志.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
set_recvFlag(int channel, int dev, int id, uint8 value)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        p_receiver->ctrl_reg->flag = value;
    }
    else
    {
        printf("set_recvFlag()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      get_recvFlag.
 * @param[in]  None
 * @param[out] None
 * @retval     接收标志.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 get_recvFlag(int channel, int dev, int id)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        return (p_receiver->ctrl_reg->flag);
    }
    else
    {
        printf("get_recvFlag()...error!\n");
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      set_recvTimeoutFlag.
 * @param[in]  None
 * @param[out] None
 * @retval     设置接收超时.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
set_recvTimeoutFlag(int channel, int dev, int id, uint8 value)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        p_receiver->ctrl_reg->timeoutFlag = value;
    }
    else
    {
        printf("set_recvTimeoutFlag()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      get_recvTimeoutFlag.
 * @param[in]  None
 * @param[out] None
 * @retval     查询接收超时.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint8 get_recvTimeoutFlag(int channel, int dev, int id)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        return (p_receiver->ctrl_reg->timeoutFlag);
    }
    else
    {
        printf("get_recvTimeoutFlag()...error!\n");
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      set_recvTimeout_enable.
 * @param[in]  None
 * @param[out] None
 * @retval     设置接收超时.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void set_recvTimeout_enable(int channel, int dev, int id, uint8 enable)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        p_receiver->ctrl_reg->timeoutEnable = enable;
    }
    else
    {
        printf("set_recvTimeout_enable()...error!\n");
    }
}

/**
 ******************************************************************************
 * @brief      check_recvTimeout_enable.
 * @param[in]  None
 * @param[out] None
 * @retval     接收超时检测使能检测.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint8
check_recvTimeout_enable(int channel, int dev, int id)
{
    RECEIVER_STRUCT *p_receiver = get_receiver(channel, dev, id);

    if (NULL != p_receiver)
    {
        return (p_receiver->ctrl_reg->timeoutEnable);
    }
    else
    {
        printf("get_recvTimeoutFlag()...error!\n");
    }
    return eEnableFlag_Off;
}

/*----------------------------publicPort.c--------------------------------*/
