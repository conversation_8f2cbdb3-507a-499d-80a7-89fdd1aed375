/*
 * led_pwm.h
 *
 *  Created on: 2025年8月4日
 *      Author: bao<PERSON>peng
 */

#ifndef BSP_DRV_LED_PWM_H_
#define BSP_DRV_LED_PWM_H_

#ifndef __LED_PWM_H
#define __LED_PWM_H

#include "stm32f2xx.h"
#include <stdbool.h>

/* LED颜色枚举 */
typedef enum
{
    LED_RED = 0,
    LED_YELLOW,
    LED_GREEN,
    LED_COUNT
} LED_Color_t;

/* 呼吸模式 */
typedef enum
{
    BREATH_SINE = 0,   // 正弦波呼吸
    BREATH_LINEAR,     // 线性呼吸
    BREATH_EXPONENTIAL // 指数呼吸
} Breath_Mode_t;

/* PWM状态 */
typedef enum
{
    PWM_STATE_DISABLED = 0,
    PWM_STATE_STATIC,   // 静态亮度
    PWM_STATE_BREATHING // 呼吸模式
} PWM_State_t;

/* LED状态机 */
typedef enum
{
    LED_STATE_OFF = 0,         // 关闭
    LED_STATE_STATIC,          // 静态亮度
    LED_STATE_BREATHING,       // 呼吸模式
    LED_STATE_FADING_OUT,      // 淡出中
    LED_STATE_FADING_IN,       // 淡入中
    LED_STATE_CROSS_BREATHING, // 反相呼吸
    LED_STATE_TRANSITIONING    // 颜色切换中
} LED_State_t;

/* 切换模式 */
typedef enum
{
    SWITCH_MODE_IMMEDIATE = 0, // 立即切换
    SWITCH_MODE_FADE,          // 淡入淡出切换
    SWITCH_MODE_SEQUENTIAL,    // 顺序切换(避免混合色)
    SWITCH_MODE_CROSS_BREATH,  // 反相呼吸切换
    SWITCH_MODE_INHERIT        // 继承当前亮度
} Switch_Mode_t;

/* 优先级定义 */
typedef enum
{
    LED_PRIORITY_LOW = 0,     // 普通装饰效果
    LED_PRIORITY_NORMAL = 1,  // 正常用户操作
    LED_PRIORITY_HIGH = 2,    // 重要状态指示
    LED_PRIORITY_CRITICAL = 3 // 紧急警告
} LED_Priority_t;

/* 优先级队列请求 */
typedef struct
{
    LED_Color_t target_color;  // 目标颜色
    LED_State_t target_state;  // 目标状态
    Switch_Mode_t switch_mode; // 切换模式
    LED_Priority_t priority;   // 优先级
    uint32_t request_time;     // 请求时间戳
    uint16_t duration_ms;      // 持续时间
    uint8_t target_duty;       // 目标占空比
    bool is_valid;             // 请求是否有效
} LED_Request_t;

/* 反相呼吸控制 */
typedef struct
{
    LED_Color_t color1, color2; // 两个LED颜色
    uint16_t breath_step;       // 呼吸步长
    uint8_t total_brightness;   // 总亮度
    uint8_t min_brightness;     // 最小亮度
    bool is_active;             // 是否激活
} CrossBreath_Control_t;

/* LED控制结构体 */
typedef struct
{
    LED_State_t state;            // LED状态
    Breath_Mode_t breath_mode;    // 呼吸模式
    uint16_t breath_step;         // 呼吸步长
    uint8_t target_duty;          // 目标占空比
    uint8_t current_duty;         // 当前占空比
    uint8_t fade_step;            // 淡入淡出步长
    uint32_t state_start_time;    // 状态开始时间
    uint16_t transition_duration; // 转换持续时间
    bool is_active;               // 是否激活
} LED_Control_t;
/* 优先级队列API */
bool LED_PWM_RequestColor(LED_Color_t color, LED_Priority_t priority, uint8_t duty);
bool LED_PWM_RequestSwitch(LED_Color_t from, LED_Color_t to, Switch_Mode_t mode, LED_Priority_t priority);
bool LED_PWM_RequestCrossBreath(LED_Color_t color1, LED_Color_t color2, LED_Priority_t priority);
void LED_PWM_ClearQueue(LED_Priority_t min_priority);

/* 原有函数(保持兼容) */
void LED_PWM_SetLEDState(LED_Color_t color, LED_State_t state);
void LED_PWM_StartSingleBreath(LED_Color_t color, Breath_Mode_t mode);
void LED_PWM_StopSingleBreath(LED_Color_t color);
void LED_PWM_SwitchLED(LED_Color_t from_color, LED_Color_t to_color, Switch_Mode_t mode);
LED_State_t LED_PWM_GetLEDState(LED_Color_t color);

/* 函数声明 */
bool LED_PWM_Init(void);
void LED_PWM_SetDuty(LED_Color_t color, uint8_t duty);
void LED_PWM_SetAllDuty(uint8_t red, uint8_t yellow, uint8_t green);
void LED_PWM_StartBreath(Breath_Mode_t mode);
void LED_PWM_StopBreath(void);
void LED_PWM_BreathUpdate(void); // 非阻塞更新，需定时调用
uint8_t LED_PWM_GetDuty(LED_Color_t color);
PWM_State_t LED_PWM_GetState(void);

#endif
#endif /* BSP_DRV_LED_PWM_H_ */