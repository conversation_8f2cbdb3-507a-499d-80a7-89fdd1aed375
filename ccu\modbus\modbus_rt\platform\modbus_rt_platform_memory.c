
#include "modbus_rt_platform_memory.h"
#include "../ccu/charge/fault_inject.h"
void *modbus_rt_malloc(size_t size)
{
    if (fi_should_fail(FI_MALLOC))
    {
        return NULL;
    }
    return malloc(size);
}

void *modbus_rt_calloc(size_t num, size_t size)
{
    if (fi_should_fail(FI_MALLOC))
    {
        return NULL;
    }
    return calloc(num, size);
}

void modbus_rt_free(void *ptr)
{
    free(ptr);
}
