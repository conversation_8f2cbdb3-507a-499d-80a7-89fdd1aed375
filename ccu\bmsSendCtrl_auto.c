/**
 ******************************************************************************
 * @file      bmsSendCtrl.c
 * @brief     C Source file of bmsSendCtrl.c.
 * @details   This file including all API functions's
 *            implement of bmsSendCtrl.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <string.h>
#include <bcdLib.h>
#include <tmLib.h>
#include <taskLib.h>
#include <stdlib.h>
#include <stdio.h>
#include <trace.h>

#include "bmsMain.h"
#include "bmsRecvCtrl.h"
#include "bmsSendCtrl.h"
#include <ccu\para\para.h>
#include <ccu/bsn/sample.h>
#include <ccu\bsn\deviceState.h>
#include <ccu\charge\ccuChargeMain.h>
#include <ccu\tcu\tcuMain.h>
#include "ccu/ccuLog/ccuLog.h"
#include "ccu/para/para.h"
#include <maths.h>
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
typedef  void (*PsendFunc)(uint8 *pOutBuf, uint8 *pOutLen);
typedef  void (*PsendDealFunc)(void);

typedef struct  BMS_SEND_DEAL_STRU
{
    uint32          pgn;
    uint8           prio;
    uint16          sendCycle;
    PsendFunc       pSendFunc;
    PsendDealFunc   pSendDealFunc;
}BMS_SEND_DEAL;

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
 /* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
extern  BMS_CTRL    bmsCtrl;
extern  BMS_DATA    bmsData;

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/**
 ******************************************************************************
 * @brief      Get_BmsSendCtrl
* @param[in]   uint32 Pgn  参数组编号
* @param[in]
* @param[out]
* @retval      BMS_SEND_CTRL *返回控制变量指针
*
* @details     获取对应PGN的控制变量
*
* @note
******************************************************************************
*/
static BMS_SEND_CTRL *Get_BmsSendCtrl(uint32 pgn)
{
   switch (pgn)
   {
   case BMS_PGN_CHM:
       return &bmsCtrl.bmsSendCtrl[0];

   case BMS_PGN_CRM:
       return &bmsCtrl.bmsSendCtrl[1];

   case BMS_PGN_CTS:
       return &bmsCtrl.bmsSendCtrl[2];

   case BMS_PGN_CML:
       return &bmsCtrl.bmsSendCtrl[3];

   case BMS_PGN_CRO:
       return &bmsCtrl.bmsSendCtrl[4];

   case BMS_PGN_CCS:
       return &bmsCtrl.bmsSendCtrl[5];

   case BMS_PGN_CST:
       return &bmsCtrl.bmsSendCtrl[6];

   case BMS_PGN_CSD:
       return &bmsCtrl.bmsSendCtrl[7];

   case BMS_PGN_CEM:
       return &bmsCtrl.bmsSendCtrl[8];

   case BMS_PGN_CDT:
         return &bmsCtrl.bmsSendCtrl[9];
   default:
       break;
   }

   return NULL;
}

/**
 ******************************************************************************
 * @brief      获取BMS上次发送时间
* @param[in]   uint32 Pgn                参数组编号
* @param[in]
* @param[out]
* @retval
*
* @details
*
* @note
******************************************************************************
*/
static uint32 Get_BmsLastSendTimer(uint32 pgn)
{
    BMS_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_BmsSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return tickGet();
    }

    return pSendCtrl->lastSendTimer;
}


/**
 ******************************************************************************
 * @brief      Send_CHM
* @param[in]
* @param[out]  uint8 *pOutBuf   输出缓存区
* @param[out]  uint8 *pOutLen   输出长度
* @retval
*
* @details     充电机握手帧
*
* @note
******************************************************************************
*/
static void Send_CHM(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    if(Get_CcuCfgParaEuropeEnable())
    {
        pBuf[index++] = PLC_CR_SECC_VER_MINOR;
//        pBuf[index++] = (uint8)(PLC_CR_SECC_VER_MAJOR >> 16);
//        pBuf[index++] = (uint8)(PLC_CR_SECC_VER_MAJOR >> 8);
        pBuf[index++] = (uint8)(PLC_CR_SECC_VER_MAJOR >> 8);
        pBuf[index++] = (uint8)(PLC_CR_SECC_VER_MAJOR & 0x0f);
        pBuf[index++] = 0x03
                | ((eRecvFlag_Yes == Get_BMS_RecvFlg(BMS_PGN_BHM)
                        && (eSendFlag_Yes == Get_BmsSendFlg(BMS_PGN_CHM))
                        && (eActFlag_On == Get_ChargeActFlag())) ?
                        0x10 : 0x00); 							//供电设备传输类型 默认值3   |0x01启动PLC
        pBuf[index++] = (Get_CcuChargeParaMaxPower()/10); 		//最大功率限制
        pBuf[index++] = (Get_CcuChargeParaMaxPower()/10) >> 8;

        if(eActFlag_On == Get_ChargeActFlag())
        {
            DisableBmsComIfEuropeGwAutoChargeEnabled();
        }
    }
    else
    {
        pBuf[index++] = (uint8)(BMS_PROTOCOL_VER >> 16); 		//充电机通信协议版本号 V1.1
        pBuf[index++] = (uint8)(BMS_PROTOCOL_VER >> 8);
        pBuf[index++] = (uint8)(BMS_PROTOCOL_VER);
    }
    pOutLen[0] = index;
    return;
}


/**
 ******************************************************************************
 * @brief      Send_CHM
* @param[in]
* @param[out]  uint8 *pOutBuf   输出缓存区
* @param[out]  uint8 *pOutLen   输出长度
* @retval
*
* @details     充电机握手帧
*
* @note
******************************************************************************
*/

static void Send_CDT(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    if(DEV_STD_GW == Get_CcuCfgParaEuropeEnable()&& Get_EnableAutoCharge())
    {
        pBuf[index++] = MAC_DETECTION_ENABLED;
        pBuf[index++] = Get_AutoChargeDelay();
        pBuf[index++] = DEFAULT_MAC_REQUEST_TIMEOUT;

    }
    pOutLen[0] = index;
    return;
}
/**
 ******************************************************************************
 * @brief      Deal_CHM
* @param[in]
* @param[out]
* @param[out]
* @retval
*
* @details     充电机握手帧发送后处理
*
* @note
******************************************************************************
*/
static void Deal_CHM(void)
{
    if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BHM))
    {
        Set_BmsRecvTimer(BMS_PGN_BHM, 0x00);
        Set_BmsRecvTimerEnable(BMS_PGN_BHM, eTimerEnable_On);
        Set_BmsRecvEnable(BMS_PGN_BHM, eRecvEnable_On);
        Set_BmsRecvEnable(BMS_PGN_BEM, eRecvEnable_On);
    }

    return;
}

static void Deal_CDT(void)
{
    printf("========Get_BmsRecvEnable(BMS_PGN_BAC)=======:%d===========\n",Get_BmsRecvEnable(BMS_PGN_BAC));
    if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BAC))
    {
        Set_BmsRecvTimer(BMS_PGN_BAC, 0);
        Set_BmsRecvTimerEnable(BMS_PGN_BAC, eTimerEnable_On);
        Set_BmsRecvEnable(BMS_PGN_BAC, eRecvEnable_On);
        Set_BmsRecvEnable(BMS_PGN_BAC, eRecvEnable_On);
    }

    return;
}
/**
 ******************************************************************************
 * @brief      Send_CRM
* @param[in]
* @param[out]  uint8 *pOutBuf   输出缓存区
* @param[out]  uint8 *pOutLen   输出长度
* @retval
*
* @details     充电机辨识帧
*
* @note
******************************************************************************
*/
static void Send_CRM(uint8 *pOutBuf, uint8 *pOutLen)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    OPERATE_PARA strOperatePara;

    Get_PilePara((void *)&strOperatePara, eParaType_OperatePara);

    pBuf[index++] = pBmsCtrl->spn_2560;    				 //辨识结果 0x00:未辨识     0xAA:已辨识 收到BRM后置AA

    memcpy(&pBuf[index], strOperatePara.chargerNO, 4);	 //充电机编号
    index += 4;

    memcpy(&pBuf[index], strOperatePara.areaCode, 3);	 //充电机所在区域编码
    index += 3;

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief      Deal_CRM
* @param[in]
* @param[out]
* @param[out]
* @retval
*
* @details     充电机辨识帧发送后处理
*
* @note
******************************************************************************
*/
static void Deal_CRM(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    Set_BmsRecvEnable(BMS_PGN_BHM, eRecvEnable_Off);
    Set_BmsRecvTimerEnable(BMS_PGN_BHM, eTimerEnable_Off);

    if (0x00 == pBmsCtrl->spn_2560)
    {
        if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BRM))
        {
			Set_BmsRecvFlag(BMS_PGN_BRM, eRecvFlag_No);
            Set_BmsRecvTimer(BMS_PGN_BRM, 0x00);
            Set_BmsRecvEnable(BMS_PGN_BRM, eRecvEnable_On);
            Set_BmsRecvTimerEnable(BMS_PGN_BRM, eTimerEnable_On);
        }
    }

    if (0xAA == pBmsCtrl->spn_2560)
    {
        if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BCP))
        {
            Set_BmsRecvFlag(BMS_PGN_BCP, eRecvFlag_No);
        	Set_BmsRecvFlag(BMS_PGN_BCS, eRecvFlag_No);
        	Set_BmsRecvFlag(BMS_PGN_BCL, eRecvFlag_No);
            Set_BmsRecvTimer(BMS_PGN_BCP, 0x00);
            Set_BmsRecvEnable(BMS_PGN_BCP, eRecvEnable_On);
            Set_BmsRecvTimerEnable(BMS_PGN_BCP, eTimerEnable_On);

        }
    }

    return;
}

/**
 ******************************************************************************
 * @brief      Send_CTS
* @param[in]
* @param[out]  uint8 *pOutBuf   输出缓存区
* @param[out]  uint8 *pOutLen   输出长度
* @retval
*
* @details     充电机发送时间同步信息
*
* @note
******************************************************************************
*/
static void Send_CTS(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

     struct tm daytime;
     time_t stime = time(NULL);
     (void)localtime_r(&stime, &daytime);
    pBuf[index++] = hex2bcd(daytime.tm_sec);//SEC_OF_NOW);
    pBuf[index++] = hex2bcd(daytime.tm_min);//MIN_OF_NOW);
    pBuf[index++] = hex2bcd( daytime.tm_hour);//HOUR_OF_NOW);
    pBuf[index++] = hex2bcd(daytime.tm_mday);//DAY_OF_NOW);
    pBuf[index++] = hex2bcd(daytime.tm_mon + 1);//MON_OF_NOW);
    Uint16ToTwoUint8(&pBuf[index], hex2bcd(daytime.tm_year + 1900));//YEAR_OF_NOW + 2000));
    index += 2;

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief      Deal_CTS
* @param[in]
* @param[out]
* @param[out]
* @retval
*
* @details     充电机时间同步信息发送后处理
*
* @note
******************************************************************************
*/
static void Deal_CTS(void)
{
    //没什么要处理的
    return;
}

/**
 ******************************************************************************
 * @brief      Send_CML
* @param[in]
* @param[out]  uint8 *pOutBuf   输出缓存区
* @param[out]  uint8 *pOutLen   输出长度
* @retval
*
* @details     发送充电机最大输出能力
*
* @note
******************************************************************************
*/
static void Send_CML(uint8 *pOutBuf, uint8 *pOutLen)
{
    CHARGE_PARA chargePara;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    uint16 plc_cur = 0;
    Get_PilePara((void *)&chargePara, eParaType_ChargePara);

    Uint16ToTwoUint8(&pBuf[index],  FourUint8ToUint32(chargePara.maxOutputVoltage));	//2byte 充电机最高充电电压
    index += 2;

    Uint16ToTwoUint8(&pBuf[index],  FourUint8ToUint32(chargePara.minOutputVoltage));	//2byte	充电机最低充电电压
    index += 2;
    if(Get_CcuCfgParaEuropeEnable())/**/
    {
        plc_cur = ((FourUint8ToUint32(chargePara.maxOutputCurrent) / 100) > 4000)
                ?(FourUint8ToUint32(chargePara.maxOutputCurrent) / 100) - 4000
                :(4000 - (FourUint8ToUint32(chargePara.maxOutputCurrent) / 100));
        if((FourUint8ToUint32(chargePara.maxOutputCurrent) / 100) > 4000)
        {
            SETBITS(plc_cur,15,1);	//最大输出电流大于400A标志位
        }

        Uint16ToTwoUint8(&pBuf[index],plc_cur);
        index += 2;

    }
    else
    {
        Uint16ToTwoUint8(&pBuf[index],  Get_HighestCurrentLimit() - (FourUint8ToUint32(chargePara.maxOutputCurrent) / 100));	//2byte 充电机最大充电电流 -400偏移量
        index += 2;
    }
    if(Get_BMS_Ver() != 0xA5)
    {
		Uint16ToTwoUint8(&pBuf[index],  Get_HighestCurrentLimit() - (FourUint8ToUint32(chargePara.minOutputCurrent) / 100));	//2byte 充电机最小充电电流 -400偏移量
		index += 2;
    }

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief      Deal_CML
* @param[in]
* @param[out]
* @param[out]
* @retval
*
* @details     充电机最大输出能力发送后处理
*
* @note
******************************************************************************
*/
static void Deal_CML(void)
{
	Set_BmsSendFlg(BMS_PGN_CML, eSendFlag_Yes);

	if(TRUE == Check_ErrType(eErrType_ChargeParaNoMatch))
	{
		Set_BmsStage(BMS_STAGE_STOP);
	}
    if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BRO) && (TRUE != Check_ErrType(eErrType_ChargeParaNoMatch)))
    {
        Set_BmsRecvEnable(BMS_PGN_BRO, eRecvEnable_On);
        Set_BmsRecvEnable(BMS_PGN_BRO_AA, eRecvEnable_On);
        Set_BmsRecvTimerEnable(BMS_PGN_BRO, eTimerEnable_On);
        Set_BmsRecvTimerEnable(BMS_PGN_BRO_AA, eTimerEnable_On);
        Set_BmsRecvTimer(BMS_PGN_BRO, 0x00);
        Set_BmsRecvTimer(BMS_PGN_BRO_AA, 0x00);
    }
    return;
}

/**
 ******************************************************************************
 * @brief      Send_CRO
* @param[in]
* @param[out]  uint8 *pOutBuf   输出缓存区
* @param[out]  uint8 *pOutLen   输出长度
* @retval
*
* @details     充电机输出准备就绪状态发送
*
* @note
******************************************************************************
*/
static void Send_CRO(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    pBuf[index++] = pBmsCtrl->spn_2830;
    if(Get_CcuCfgParaEuropeEnable())
    {
        pBuf[index++] = pBmsCtrl->evseIsolationStatus;
    }
    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief      Deal_CRO
* @param[in]
* @param[out]
* @param[out]
* @retval
*
* @details     充电机输出准备就绪状态发送后处理
*
* @note
******************************************************************************
*/
static void Deal_CRO(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (0xAA == pBmsCtrl->spn_2830)
    {
        if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BCL))
        {
            Set_BmsRecvEnable(BMS_PGN_BCL, eRecvEnable_On);
            Set_BmsRecvTimerEnable(BMS_PGN_BCL, eTimerEnable_On);
            Set_BmsRecvTimer(BMS_PGN_BCL, 0x00);
        }

        if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BCS))
        {
            Set_BmsRecvEnable(BMS_PGN_BCS, eRecvEnable_On);
            /*Set_BmsRecvTimerEnable(BMS_PGN_BCS, eTimerEnable_On);
            Set_BmsRecvTimer(BMS_PGN_BCS, 0x00);*/   //2023版27930协议没有收到BCS时不判超时
        }
        if(eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BMV))
        {
            Set_BmsRecvTimer(BMS_PGN_BMV, 0x00);
            Set_BmsRecvEnable(BMS_PGN_BMV, eRecvEnable_On);
            Set_BmsRecvTimerEnable(BMS_PGN_BMV, eTimerEnable_On);
        }
        if(eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BMT))
        {
            Set_BmsRecvTimer(BMS_PGN_BMT, 0x00);
            Set_BmsRecvEnable(BMS_PGN_BMT, eRecvEnable_On);
            Set_BmsRecvTimerEnable(BMS_PGN_BMT, eTimerEnable_On);
        }
        if(eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BSP))
        {
            Set_BmsRecvTimer(BMS_PGN_BSP, 0x00);
            Set_BmsRecvEnable(BMS_PGN_BSP, eRecvEnable_On);
            Set_BmsRecvTimerEnable(BMS_PGN_BSP, eTimerEnable_On);
        }
    }
    else
    {
//        Set_BmsRecvEnable(BMS_PGN_BCL, eTimerEnable_Off);
//        Set_BmsRecvEnable(BMS_PGN_BCS, eTimerEnable_Off);
//        Set_BmsRecvEnable(BMS_PGN_BMV, eTimerEnable_Off);
//        Set_BmsRecvEnable(BMS_PGN_BMT, eTimerEnable_Off);
//        Set_BmsRecvEnable(BMS_PGN_BSP, eTimerEnable_Off);
    }
    return;
}

/**
 ******************************************************************************
 * @brief      Send_CCS
* @param[in]
* @param[out]  uint8 *pOutBuf   输出缓存区
* @param[out]  uint8 *pOutLen   输出长度
* @retval
*
* @details     充电机充电状态发送
*
* @note
******************************************************************************
*/
static void Send_CCS(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    Uint16ToTwoUint8(&pBuf[index],  Get_K1K2OutsideVol());		//车辆接口当前电压测量值
    index += 2;

    if(Get_CcuCfgParaEuropeEnable())
    {
        if( PreCharge_Stauts_ING == Get_Europe_Pre())
        {
            Uint16ToTwoUint8(&pBuf[index],  Get_HighestCurrentLimit());
            index += 2;
        }
        else
        {
            Uint16ToTwoUint8(&pBuf[index],  Get_HighestCurrentLimit() - (Get_K1K2Current() / 100));
            index += 2;
        }
        pBuf[index++] = (Get_CcuChargeParaMaxPower()/10);
        pBuf[index++] = (Get_CcuChargeParaMaxPower()/10) >> 8;
    }
    else
    {
        Uint16ToTwoUint8(&pBuf[index],  Get_HighestCurrentLimit() - (Get_K1K2Current() / 100));	//车辆接口当前电流值
        index += 2;

        //累计充电时间由TCU提供，若为0则用软件计时时间
        Uint16ToTwoUint8(&pBuf[index], Get_ChargeTime());					//累计充电时间
        index += 2;
    }
    uint16 tmp = 0xffff;
    if(Get_BMS_Ver() != 0xA5)
    {
		if (enumAllowFlag_Allow == Get_ChargePauseFlg())
		{
		    tmp &= 0xfffd; //为了补全，实际是低2bit
		}
		else
		{
		    tmp &= 0xfffc;												//2bit 充电允许
		}
//		pBuf[index++] = 0xFF;      //参考34658-2017
    }
    if(Get_CcuCfgParaEuropeEnable())
    {
        tmp &= 0x0003;
        tmp |= (pBmsCtrl->evseIsolationStatus & 0x07) << 2;
        tmp |= 0x01<<5;
        tmp |= ((Get_CcuChargeParaMaxCur() / 1000) & 0x03ff) << 6;
        pBuf[index++] = tmp;
        pBuf[index++] = tmp >> 8;
    }
    else
    {
        pBuf[index++] = tmp;
    }
    pOutLen[0] = index;
    return;
}

/**
 ******************************************************************************
 * @brief      Deal_CCS
* @param[in]
* @param[out]
* @param[out]
* @retval
*
* @details     充电机充电状态发送后处理
*
* @note
******************************************************************************
*/
static void Deal_CCS(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (BMS_STAGE_PARACONFIG == Get_BmsStage())
    {
        Set_BmsStage(BMS_STAGE_CHARGING);
    }

    if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BSM))
    {
        Set_BmsRecvEnable(BMS_PGN_BSM, eRecvEnable_On);
        Set_BmsRecvTimerEnable(BMS_PGN_BSM, eTimerEnable_Off);
//        Set_BmsRecvEnable(BMS_PGN_BMV, eRecvEnable_On);
//        Set_BmsRecvEnable(BMS_PGN_BMT, eRecvEnable_On);
//        Set_BmsRecvEnable(BMS_PGN_BSP, eRecvEnable_On);
        Set_BmsRecvEnable(BMS_PGN_BST, eRecvEnable_On);
    }
    return;
}

/**
 ******************************************************************************
 * @brief      Send_CST
* @param[in]
* @param[out]  uint8 *pOutBuf   输出缓存区
* @param[out]  uint8 *pOutLen   输出长度
* @retval
*
* @details     充电机中止充电发送
*
* @note
******************************************************************************
*/
static void Send_CST(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    uint8 flag = 0;
    if (Get_StopSrc() == eChargeStopFlag_Auto)
    {
        Set_BitFlag(pBuf + 0, 0);
    }
    if (Get_StopSrc() == eChargeStopFlag_Manual)
    {
        Set_BitFlag(pBuf + 0, 2);
    }
    if (Get_StopSrc() == eChargeStopFlag_Err)
    {
        Set_BitFlag(pBuf + 0, 4);
    }
    if (Get_StopSrc() == eChargeStopFlag_BMS)
    {
        Set_BitFlag(pBuf + 0, 6);
    }

    if (DEVICE_STATE_MAJOR_FAULT == Get_DeviceState() ||
        DEVICE_STATE_FAULT == Get_DeviceState())
    {
        if (TRUE == Check_ErrType(eErrType_PileTempOverLimitErr))
        {
            Set_BitFlag(pBuf + 1, 0);
            flag = 1;
        }
        if (TRUE == Check_ErrType(eErrType_GunConnectErr))
        {
            Set_BitFlag(pBuf + 1, 2);
            flag = 1;
        }
        if (TRUE == Check_ErrType(eErrType_PileTempOverLimitErr))
        {
            Set_BitFlag(pBuf + 1, 4);
            flag = 1;
        }
        if (TRUE == Check_ErrType(eErrType_EmergencyStop))
        {
            Set_BitFlag(pBuf + 2, 0);
            flag = 1;
        }
        if ((TRUE == Check_ErrType(eErrType_ImdErr))
				|| (TRUE == Check_ErrType(eErrType_ImdTimeOut))
				|| (TRUE == Check_ErrType(eErrType_OutputShortCut)))
        {
            Set_BitFlag(pBuf + 2, 4); //2023新增自检故障
            flag = 1;
        }
        if ((TRUE == Check_ErrType(eErrType_K1K2OutsideVolErr2))
        		|| (TRUE == Check_ErrType(eErrType_K1K2OutsideVolErr3))
        		|| (TRUE == Check_ErrType(eErrType_BCPVolErr1))
				|| (TRUE == Check_ErrType(eErrType_BROErr)))
        {
            Set_BitFlag(pBuf + 2, 6); // 2023新增预充故障
            flag = 1;
        }
        if ((TRUE == Check_ErrType(eErrType_OutputOverDemandCur))
                || (TRUE == Check_ErrType(eErrType_OutputOverMaxChargeCur))
                || (TRUE == Check_ErrType(eErrType_OutputOverMaxAllowOutputCur))
                || (TRUE == Check_ErrType(eErrType_DemandOverMaxAllowChargeCur)))
        {
            Set_BitFlag(pBuf + 3, 0);
            flag = 1;
        }
        if ((TRUE == Check_ErrType(eErrType_BatteryVolErr))
                || (TRUE == Check_ErrType(eErrType_OutputOverMaxAllowOutputVol))
                || (TRUE == Check_ErrType(eErrType_OutputOverMaxAllowChargeVol))
                || (TRUE == Check_ErrType(eErrType_OutputOverDemandVol))
                || (TRUE == Check_ErrType(eErrType_DemandOverMaxAllowChargeVol))
				|| (TRUE == Check_ErrType(eErrType_DemandLowMinAllowOutputVol)))
        {
            Set_BitFlag(pBuf + 3, 2);
            flag = 1;
        }
        if (TRUE == Check_ErrType(eErrType_ChargeParaNoMatch)) //2023新增充电参数不匹配故障
        {
            Set_BitFlag(pBuf + 3, 4);
            flag = 1;
        }
        if(flag  == 0)
        {
            Set_BitFlag(pBuf + 1, 10);//置其他故障 11-12位 01
        }
    }

    pBuf[3] |= 0xC0;//最高2bit预留置11

    index += 4;
    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief      Deal_CST
* @param[in]
* @param[out]
* @param[out]
* @retval
*
* @details     充电机中止充电发送后处理
*
* @note
******************************************************************************
*/
static void Deal_CST(void)
{
    if (eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BST))
    {
    	Set_BmsRecvEnable(BMS_PGN_BST, eRecvEnable_On);
        if (eTimerEnable_On != Get_BmsRecvTimerEnable(BMS_PGN_BST))
        {
            Set_BmsRecvTimerEnable(BMS_PGN_BST, eTimerEnable_On);
            Set_BmsRecvTimer(BMS_PGN_BST, 0x00);
        }
    }
//    else
//    {
        if (eTimerEnable_On != Get_BmsRecvTimerEnable(BMS_PGN_BSD))
        {
            Set_BmsRecvTimerEnable(BMS_PGN_BSD, eTimerEnable_On);
            Set_BmsRecvTimer(BMS_PGN_BSD, 0x00);
        }
//    }

    return;
}

/**
 ******************************************************************************
 * @brief      Send_CSD
* @param[in]
* @param[out]  uint8 *pOutBuf   输出缓存区
* @param[out]  uint8 *pOutLen   输出长度
* @retval
*
* @details     充电机统计数据发送
*
* @note
******************************************************************************
*/
static void Send_CSD(uint8 *pOutBuf, uint8 *pOutLen)
{
    OPERATE_PARA strOperatePara;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    BMS_DATA  *pBmsData = &bmsData;
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    Get_PilePara((void *)&strOperatePara, eParaType_OperatePara);

    Uint16ToTwoUint8(&pBuf[index], Get_ChargeTime());				//2byte 本次充电时间/min
    index += 2;
    if(Get_CcuCfgParaEuropeEnable())
    {
        pBuf[index++] = pBmsData->strBCS.voltageMeasuredValue[0];
        pBuf[index++] = pBmsData->strBCS.voltageMeasuredValue[1];
        pBuf[index++] = pBmsCtrl->evseIsolationStatus;
        pBuf[index++] = 0;
        pBuf[index++] = 0;
        pBuf[index++] = 0;
    }
    else
    {
        Uint16ToTwoUint8(&pBuf[index], Get_ChargeEnerge());			//2byte 输出能量/kWh
        index += 2;
        if(Get_BMS_Ver() != 0xA5)
        {
            memcpy(&pBuf[index], strOperatePara.chargerNO, 4);		//4byte 充电机编号
            index += 4;
        }
        else
        {
            memcpy(&pBuf[index], strOperatePara.chargerNO, 1);
            index += 1;
        }
    }

    pOutLen[0] = index;

    pBmsData->strCSD.chargerTime   = Get_ChargeTime();
    pBmsData->strCSD.totalPower    = Get_ChargeEnerge();

    memcpy(pBmsData->strCSD.chargerNumber, strOperatePara.chargerNO, 4);

    trace(TR_BMS_PROCESS, "发送CSD,tick = %d\n",tickGet());
    return;
}

/**
 ******************************************************************************
 * @brief      Deal_CSD
* @param[in]
* @param[out]
* @param[out]
* @retval
*
* @details     充电机统计数据发送后处理
*
* @note
******************************************************************************
*/
static void Deal_CSD(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (BMS_STAGE_STOP == Get_BmsStage())
    {
        Set_BmsStage(BMS_STAGE_STOPFINISH);
    }

    return;
}

/**
 ******************************************************************************
 * @brief      Send_CEM
* @param[in]
* @param[out]  uint8 *pOutBuf   输出缓存区
* @param[out]  uint8 *pOutLen   输出长度
* @retval
*
* @details     充电机错误报文发送
*
* @note
******************************************************************************
*/
static void Send_CEM(uint8 *pOutBuf, uint8 *pOutLen)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    pBuf[0] |= 0xFC;
    pBuf[1] |= 0xF0;
    pBuf[2] |= 0xC0;
    pBuf[3] |= 0xF0;

    if (pBmsCtrl->overTimePgnforBms == BMS_PGN_BRM)
    {
        Set_BitFlag(pBuf + 0, 0);
    }
    else if (pBmsCtrl->overTimePgnforBms == BMS_PGN_BCP)
    {
        Set_BitFlag(pBuf + 1, 0);
    }
    else if (pBmsCtrl->overTimePgnforBms == BMS_PGN_BRO ||
             pBmsCtrl->overTimePgnforBms == BMS_PGN_BRO_AA)
    {
        Set_BitFlag(pBuf + 1, 2);
    }
    else if (pBmsCtrl->overTimePgnforBms == BMS_PGN_BCS)
    {
        Set_BitFlag(pBuf + 2, 0);
    }
    else if (pBmsCtrl->overTimePgnforBms == BMS_PGN_BCL)
    {
        Set_BitFlag(pBuf + 2, 2);
    }
    else if (pBmsCtrl->overTimePgnforBms == BMS_PGN_BST)
    {
        Set_BitFlag(pBuf + 2, 4);
    }
    else if (pBmsCtrl->overTimePgnforBms == BMS_PGN_BSD)
    {
        Set_BitFlag(pBuf + 3, 0);
    }
    else if (pBmsCtrl->overTimePgnforBms == BMS_PGN_BSM) //2023版新增BSM超时
    {
        Set_BitFlag(pBuf + 3, 2);
    }

    index += 4;

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief      Deal_CEM
* @param[in]
* @param[out]
* @param[out]
* @retval
*
* @details     充电机错误报文发送后处理
*
* @note
******************************************************************************
*/
static void Deal_CEM(void)
{
    //无处理
    return;
}
/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
//Todo: 先清计时器，再置标记
const BMS_SEND_DEAL BMS_SEND_DEAL_TABLE[] =
{
   { BMS_PGN_CHM,     6,     250 ,       Send_CHM,    Deal_CHM },
   { BMS_PGN_CDT,     6,     250 ,       Send_CDT,    Deal_CDT },
   { BMS_PGN_CRM,     6,     250 ,       Send_CRM,    Deal_CRM },
   { BMS_PGN_CTS,     6,     500 ,       Send_CTS,    Deal_CTS },
   { BMS_PGN_CML,     6,     250 ,       Send_CML,    Deal_CML },
   { BMS_PGN_CRO,     4,     250 ,       Send_CRO,    Deal_CRO },
   { BMS_PGN_CCS,     6,      50 ,       Send_CCS,    Deal_CCS },
   { BMS_PGN_CST,     4,      10 ,       Send_CST,    Deal_CST },
   { BMS_PGN_CSD,     6,     250 ,       Send_CSD,    Deal_CSD },
   { BMS_PGN_CEM,     2,     250 ,       Send_CEM,    Deal_CEM }
};

/**
 ******************************************************************************
 * @brief      设置BMS发送剩余时间
* @param[in]   uint32 Pgn                参数组编号
* @param[in]   int32 countValue          计时器初始值
* @param[out]
* @retval
*
* @details     0xFFFF表示永久发送
*
* @note
******************************************************************************
*/
void Set_BmsSendRemainTimer(uint32 pgn, uint32 countValue)
{
    BMS_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_BmsSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->sendRemainTimer = countValue;
    return;
}

/**
 ******************************************************************************
 * @brief      获取当前发送剩余时间
* @param[in]   uint32 Pgn                参数组编号
* @param[in]
* @param[out]
* @retval      返回发送剩余时间
*
* @details
*
* @note
******************************************************************************
*/
uint32 Get_BmsSendRemainTimer(uint32 pgn)
{
    BMS_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_BmsSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return 0;
    }

    return pSendCtrl->sendRemainTimer;
}

/**
 ******************************************************************************
 * @brief      设置BMS发送剩余时间计时起始时刻
* @param[in]   uint32  Pgn                参数组编号
* @param[in]
* @param[out]
* @retval
*
* @details
* @note
******************************************************************************
*/
void Set_BmsStartTimer(uint32 pgn)
{
    BMS_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_BmsSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->sendStartTimer = tickGet();
}

/**
 ******************************************************************************
 * @brief      获取BMS发送剩余时间计时起始时刻
* @param[in]   uint32  Pgn                参数组编号
* @param[in]
* @param[out]
* @retval      uint32  发送起始时间
*
* @details
* @note
******************************************************************************
*/
static uint32 Get_BmsStartTimer(uint32 pgn)
{
    BMS_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_BmsSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return 0;
    }

    return pSendCtrl->sendStartTimer;
}


/**
 ******************************************************************************
 * @brief      Set_BmsSendFlg
* @param[in]   uint32 Pgn                参数组编号
* @param[in]   SEND_FLAG  enableFlg  已发送标记
* @param[out]
* @retval
*
* @details     0x55-已发送、0x00-未发送、0xFF-无效
*
* @note        设置发送标记
******************************************************************************
*/
void Set_BmsSendFlg(uint32 pgn, SEND_FLAG sendFlg)
{
    BMS_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_BmsSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->sendFlg = sendFlg;
    return;
}

/**
 ******************************************************************************
 * @brief      Get_BmsSendFlg
* @param[in]   uint32 Pgn                参数组编号
* @param[in]
* @param[out]
* @retval      SEND_FLAG  enableFlg  已发送标记
*
* @details     0x55-已发送、0x00-未发送、0xFF-无效
*
* @note        获取发送标记
******************************************************************************
*/
SEND_FLAG Get_BmsSendFlg(uint32 pgn)
{
    BMS_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_BmsSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return eSendFlag_Null;
    }

    return pSendCtrl->sendFlg;
}

/**
 ******************************************************************************
 * @brief      设置上次发送时间
* @param[in]   uint32 Pgn                参数组编号
* @param[in]
* @param[out]
* @retval
*
* @details     立即发送给countVal赋值FFFF
*
* @note        设置计时器
******************************************************************************
*/
void Set_BmsLastSendTimer(uint32 pgn, uint32 countVal)
{
    BMS_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_BmsSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->lastSendTimer = tickGet() + countVal;
}

/**
 ******************************************************************************
 * @brief      Bms_SendServer
* @param[in]   NONE
* @param[in]   NONE
* @param[out]
* @retval
*
* @details     BMS发送服务
*
* @note
******************************************************************************
*/
void Bms_SendServer(void)
{
    const BMS_SEND_DEAL *pBmsSendDeal = NULL;
    uint8 index = 0;
    uint8 len = 0;
    uint8 buf[8] = { 0 };
    uint32 canId = 0;

    for (index = 0; index < FCNT(BMS_SEND_DEAL_TABLE); index++)
    {
        pBmsSendDeal = &BMS_SEND_DEAL_TABLE[index];

        //报文生命周期判断
        if (0xFFFF != Get_BmsSendRemainTimer(pBmsSendDeal->pgn))
        {
            if (abs(tickGet() - Get_BmsStartTimer(pBmsSendDeal->pgn))
                    > Get_BmsSendRemainTimer(pBmsSendDeal->pgn))
            {
                Set_BmsSendRemainTimer(pBmsSendDeal->pgn, 0x00);
            }

            if (0 == Get_BmsSendRemainTimer(pBmsSendDeal->pgn))
            {
                continue;
            }
        }

        //报文发送周期判断
        if (abs(tickGet() - Get_BmsLastSendTimer(pBmsSendDeal->pgn)) <
                pBmsSendDeal->sendCycle)
        {
            continue;
        }
        len = 0;
        memset(buf, 0x00, sizeof(buf));

        if (NULL != pBmsSendDeal->pSendFunc)
        {
            //Todo: 将pSendFunc 改成 packFunc
            pBmsSendDeal->pSendFunc(buf, &len);
        }

        canId = GetCanID(pBmsSendDeal->pgn, pBmsSendDeal->prio,
                ADDR_CHARGER, ADDR_BMS);
        can_send(BMS_CHAN, canId, buf, len);

        trace(TR_CH2, "<S2: %X>  ", canId);
        trace_buf(TR_CH2, buf, len);

        Set_BmsSendFlg(pBmsSendDeal->pgn, eSendFlag_Yes);
        Set_BmsLastSendTimer(pBmsSendDeal->pgn, 0x00);

		log_recv_pgn_data(pBmsSendDeal->pgn, buf, len);

        if (NULL != pBmsSendDeal->pSendDealFunc)
        {
            pBmsSendDeal->pSendDealFunc();
        }
    }

    return;
}



/*----------------------------bmsSendCtrl.c--------------------------------*/
