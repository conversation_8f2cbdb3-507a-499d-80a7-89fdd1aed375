/**
 ******************************************************************************
 * @file       imd.h
 * @brief      API include file of imd.h.
 * @details    This file including all API functions's declare of imd.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef __IMD_H__
#define __IMD_H__
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <types.h>

/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/

// #define   IMD_CALCULATE_VOL_SCALE              /**< 根据电压比率计算绝缘 */
// #define   IMD_CALCULATE_EXP1                    /**< 计算公式1 */
#define IMD_CALCULATE_EXP2 /**< 计算公式2 */

#define IMD_CALCULATE_UNVOL /**< 不换算成电压的计算电阻宏 */
#define IMD_OFFSET_MAX 1.15 /**< 最大偏差 */
#define IMD_OFFSET_MIN 0.85 /**< 最小偏差 */

#define IMD_CMD_INIT 0xF5  /**< 恢复出厂设置 */
#define IMD_CMD_MODE 0x50  /**< 接触器闭合 */
#define IMD_CMD_SWON 0x97  /**< 接触器闭合 */
#define IMD_CMD_SWOFF 0x90 /**< 接触器断开 */
#define IMD_CMD_READ 0x01  /**< 读绝缘值 */

#define IMD_RELAY_DELAY 900 // 1500        /**< 继电器延时值 */

#define IMD_CHARGE_START_TIMEOUT 10000 /**< 绝缘检测阶段充电机启动超时时间  10S*/

#define DEVICE_ID 0xA0 /**< 设备ID */

#define R5 0.51                 /**< 计算R5 */
#define R1 (75 * 8)             /**< 计算R1*/
#define R2 (200 * 3)            /**< 计算R2*/
#define R3 (300)                /**< 计算R2*/
#define R4 (300)                /**< 计算R2*/
#define R1_R2 (R1 + R2)         /**< 计算R1,R2*/
#define R1_R5 (R1 + R5)         /**< 计算R1,R5 */
#define R1_R2_R5 (R1 + R2 + R5) /**< 计算R1,R2,R5 */
#define R3_R4 (R3 + R4)         /**< 计算R1,R2*/
#define R3_R5 (R3 + R5)         /**< 计算R1,R5 */
#define R3_R4_R5 (R3 + R4 + R5) /**< 计算R1,R2,R5 */

#define IMD_DEBUG /**< 打开调试功能 */

#define IMD_MAX_TRY_CNT 10   /**< 最大尝试次数 */
#define IMD_MAX_WAIT_DELAY 3 /**< 最大等待时间（3 * 500ms） */

#define IMD_MAX_SAMPLE_413 4 // 1027
#define IMD_MAX_SAMPLE_407 10
#define IMD_MAX_SAMPLE 10 /**< 绝缘检测最大采样次数,5处于临界状态 */

#define KE_KP_KN_ON \
    {               \
        IMD_KE_ON;  \
        IMD_KP_ON;  \
        IMD_KN_ON;  \
    }

#define KE_ON_KP_KN_OFF \
    {                   \
        IMD_KE_ON;      \
        IMD_KP_OFF;     \
        IMD_KN_OFF;     \
    }

#define KE_KP_KN_OFF \
    {                \
        IMD_KE_OFF;  \
        IMD_KP_OFF;  \
        IMD_KN_OFF;  \
    }

#define KE_KN_ON_KP_OFF \
    {                   \
        IMD_KE_ON;      \
        IMD_KP_OFF;     \
        IMD_KN_ON;      \
    }

#define KE_KP_ON_KN_OFF \
    {                   \
        IMD_KE_ON;      \
        IMD_KP_ON;      \
        IMD_KN_OFF;     \
    }

#define KE_OFF_KP_KN_ON \
    {                   \
        IMD_KE_OFF;     \
        IMD_KP_ON;      \
        IMD_KN_ON;      \
    }

#define KE_KN_OFF_KP_ON \
    {                   \
        IMD_KE_OFF;     \
        IMD_KP_ON;      \
        IMD_KN_OFF;     \
    }

#define KE_KP_OFF_KN_ON \
    {                   \
        IMD_KE_OFF;     \
        IMD_KP_OFF;     \
        IMD_KN_ON;      \
    }

// #define IMD_EXTERN                  Check_Mode3()
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/

// 绝缘检测阶段枚举
typedef enum
{
    IMD_IDLE = 0,           /**< 空闲*/
    IMD_START = 1,          /**< 开始启动阶段*/
    IMD_KEKPKN_ON = 2,      /**< 三个继电器全闭合 测量阶段*/
    IMD_KEKN_ON_KP_OFF = 3, /**< 母线正极侧继电器断开测量阶段*/
    IMD_KEKP_ON_KN_OFF = 4, /**< 母线负极侧继电器断开测量阶段*/
    IMD_KE_ON_KPKN_OFF = 5, /**< 此过程调试用 母线正负极侧继电器断开测量阶段*/
    IMD_CALCULATE = 6,      /**< 绝缘计算*/
    IMD_FINISH = 7,         /**< 绝缘检测完成阶段 此阶段完成电阻值计算*/
} IMD_STAGE_FLAG;

// 绝缘检测阶段枚举
typedef enum
{
    K_OFF = 0,
    K_ON = 1,
} IMD_BOOL;

// 电压类型枚举
typedef enum
{
    VN_VOL = 0,
    VP_VOL = 1,
} IMD_VpVnBOOL;

// 电压类型枚举
typedef enum
{
    IMD_NONE = 0,           /**<无异常*/
    IMD_NCHECK = 1,         /**<不检测*/
    IMD_CHARGE_TIMEOUT = 2, /**<启动充电机超时*/
    IMD_CHECK_TIMEOUT = 3,  /**<检测超时*/
    IMD_CHECK_ALARM = 4,    /**<绝缘警告*/
    IMD_CHECK_ERR = 5,      /**<绝缘检测错误*/
    IMD_VOL_ERR = 6,        /**<启动电压小于200V*/
} IMD_Err;

// 绝缘检测阶段枚举
typedef enum
{
    IMDCON_KEON_KPOFF_KNOFF = 0,
    IMDCON_KEON_KPON_KNON = 1,
    IMDCON_KEON_KPOFF_KNON = 2,
    IMDCON_KEON_KPON_KNOFF = 3,
    IMDCON_KEOFF_KPON_KNON = 4,
    IMDCON_KEOFF_KPON_KNOFF = 5,
    IMDCON_KEOFF_KPOFF_KNON = 6,
    IMDCON_KEOFF_KPOFF_KNOFF = 7,
} IMD_CONKEKPKN;

// TODO: 用这种表示，看上去清楚些
typedef enum
{
    IMD_EPN_O_F_F = 0,
    IMD_EPN_O_O_O = 1,
    IMD_EPN_O_F_O = 2,
    IMD_EPN_O_O_F = 3,
    IMD_EPN_F_O_O = 4,
    IMD_EPN_F_O_F = 5,
    IMD_EPN_F_F_O = 6,
    IMD_EPN_F_F_F = 7,
} IMD_KEKPKN;

// 绝缘检测阶段枚举
typedef enum
{
    IMD_TEST_CLOSE_RELAY = 0, /**<断开继电器*/
    IMD_TEST_OPEN_RELAY = 1,  /**<闭合继电器*/
    IMD_TEST_READ_VOL = 2,    /**<读电压参数*/
    IMD_TEST_SINGLE_CAL = 3,  /**<单步计算*/
    IMD_TEST_ACT = 4,         /**<执行整个绝缘计算*/
    IMD_EX_SET = 5,           /**<设置外部绝缘检测*/
} IMD_TEST_CMD;

/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
void Imd_Server(uint32 ms, int fd);

bool_e Get_ImdSwOffFlag(void);

void Set_ImdSwOffFlag(uint8 flag);

// void Set_Imd_Stage(uint8 stage);

void Imd_Start(void);

uint8 Get_Imd_Stage(void);

uint32 Get_ImdRes(void);

uint32 Get_ImdVol(void);

void Imd_Err_Ctrl(uint8 err);

void Imd_Kopen_Start(void);

void Set_ImdSwOffActFlag();

void Imd_Ctrl_KP_OFF_KN_OFF(void);

void Imd_Ctrl_KP_ON_KN_ON(void);

void Imd_Ctrl_KP_ON_KN_OFF(void);

void Imd_Ctrl_KP_OFF_KN_ON(void);

void Imd_Ctrl_Calculate(void);

void Imd_Ctrl_CalibrateCon(uint8 cmd, uint8 act, uint8 caltype);

void Imd_Ctrl_CalibrateInput(int inputHDat, int inputLDat);

// bool_e LeastSquareLinearFit(float *x, float *y, int num, float *a, float *b);

void pcuSetImdFinishFlag(uint8 flag);

uint8 Get_Imdstartstate(void);
#endif //__IMD_H__

/*--------------------------End of imd.h----------------------------*/
