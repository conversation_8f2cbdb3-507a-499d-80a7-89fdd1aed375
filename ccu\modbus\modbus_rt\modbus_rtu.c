/**
 * @file    modbus_rtu.c
 * @brief   基于Agile Modbus 的modbus rtu应用层实现，包含slave和master
 * <AUTHOR> (<EMAIL>)
 * @date    2023-05-14
 *
 * @attention
 *
 * <h2><center>&copy; Copyright (c) 2022 <PERSON>wei.
 * All rights reserved.</center></h2>
 *
 */

// 声明 本文件的if 0是因为多款方案的原因，后续会初步删除（三种方案均可用）
#include "modbus_rtu.h"
#include <trace.h>
#if (MODBUS_RTU_SLAVE_ENABLE) || (MODBUS_RTU_MASTER_ENABLE)

#if MODBUS_P2P_ENABLE
#include "modbus_p2p.h"
int modbus_slave_special_callback(agile_modbus_t *ctx, struct agile_modbus_slave_info *slave_info);
uint8_t compute_meta_length_after_function_callback(agile_modbus_t *ctx, int function, agile_modbus_msg_type_t msg_type);
int compute_data_length_after_meta_callback(agile_modbus_t *ctx, uint8_t *msg, int msg_length, agile_modbus_msg_type_t msg_type);
#endif

/**
 @verbatim
    如果开启了modbus slave的支持，则需要添加一些函数声明，函数实现在modbus_slave.c中
    因为这些函数无需对外，所以，不需要开放给用户使用
 @endverbatim
 */
#if MODBUS_RTU_SLAVE_ENABLE
void modbus_slave_util_init(agile_modbus_slave_util_t *util);
int modbus_slave_add_val(agile_modbus_slave_util_t *util, modbus_register_type_t type,
                         int data_addr, void *data, int nums);
int modbus_slave_clear_val(agile_modbus_slave_util_t *util);
int modbus_slave_read(agile_modbus_slave_util_t *util, modbus_register_type_t type, int addr, int quantity, void *ptr_data);
int modbus_slave_write(agile_modbus_slave_util_t *util, modbus_register_type_t type, int addr, int quantity, void *ptr_data);
#if SLAVE_DATA_DEVICE_BINDING
int modbus_slave_util_dev_binding(agile_modbus_slave_util_t *util, int flag);
#endif
#endif

/**
 @verbatim
    如果使能modbus ascii的支持，这里采用了morbus ascii数据和rtu数据转换函数的形式
    对收发的数据进行转换，从而实现对modbus ascii的支持
 @endverbatim
 */
#if MODBUS_ASCII_SLAVE_ENABLE || MODBUS_ASCII_MASTER_ENABLE
void modbus_ascii2rtu(uint8_t *data, int *data_len);
void modbus_rtu2ascii(uint8_t *data, int *data_len);
#endif
#ifdef MODBUS_DEBUG_TXRX
void print_hex_data(const char *prompt,
                    const uint8_t *data,
                    int len)
{
    if (!data || len <= 0)
        return;
    if (!TRCBIT(TR_DREAD_BUF))
        return; /* 通道关闭则什么也不做 */

    trace(TR_DREAD_BUF, "[HEX] %s len=%d :", prompt, len);

    trace_buf(TR_DREAD_BUF, (uint8 *)data, (uint16)len);

    /* logBuf()（由 trace_buf 调用）本身会在最后补 \\n，
       如果你的版本不会自动换行，就再加一句： logMsg("\\n"); */
}
#endif
static modbus_rt_mutex_t g_atomic_lock; /* BSS 全零 */
static volatile int g_refcnt_lock_inited = 0;
static inline void refcnt_lock_ensure_init(void)
{
    if (0 == g_refcnt_lock_inited)
    {
        /* 简单的双检 + 自旋即可；也可用更正规的 once API */
        if (modbus_rt_mutex_init(&g_atomic_lock) == OK)
        {
            int rc = modbus_rt_mutex_init(&g_atomic_lock);
            g_refcnt_lock_inited = (rc == OK) ? 1 : -1;
        }
    }
}

void cmd_cleanup(modbus_cmd_t *cmd)
{
    modbus_rt_sem_destroy(&cmd->completion_sem);
    modbus_rt_sem_destroy(&cmd->caller_ready_sem);
    modbus_rt_free(cmd);
#if LC_LOG_STAT_EN
    lc_stat_inc_cmd_free();
#endif
}

int ref_dec_and_fetch(volatile int *p, int delta)
{

    /* 其他编译器可用互斥锁或 C11 atomic 实现 */
    refcnt_lock_ensure_init();

    /* ① 正常路径：互斥量已就绪 */
    if (g_refcnt_lock_inited == 1)
    {
        int new_val;
        modbus_rt_mutex_lock(&g_atomic_lock);
        *p -= delta;
        new_val = *p;
        modbus_rt_mutex_unlock(&g_atomic_lock);
        return new_val;
    }
    /* ② 退化路径：互斥量创建失败 —— 关闭调度器/关中断保护 */
    taskLock(); /* 或者 taskLock()/disableIRQ() */
    int new_val;
    *p -= delta;
    new_val = *p;
    taskUnlock(); /* 或 taskUnlock()/enableIRQ() */
    return new_val;
}

/**
 * @brief   modbus_rtu_dev_init:    modbus rtu初始化数据函数，把modbus rtu的数据赋值到全局指针当中
 * @param   pos_dev:                指向rtu_modbus_device_t的指针，
 * @param   mode                    模式，为SLAVE/MASTER两种情况
 * @param   data                    设备数据指针
 * @return  int:                    MODBUS_RT_EOK：成功，其他：失败
 *
 */
static int modbus_rtu_dev_init(rtu_modbus_device_t *pos_dev, modbus_mode_type mode, void *data)
{
    int ret = 0;
    if (NULL != (*pos_dev))
    {
        return -MODBUS_RT_EINVAL;
    }

    *pos_dev = modbus_rt_malloc(sizeof(struct rtu_modbus_device));
    if (NULL == (*pos_dev))
    {
        return -MODBUS_RT_ENOMEM;
    }
    rtu_modbus_device_t dev = *pos_dev;

    memset(dev, 0, sizeof(struct rtu_modbus_device));
    dev->mode = mode;
    ret = modbus_rt_sem_init(&dev->sem);
    if (MODBUS_RT_EOK != ret)
    {
        modbus_rt_free(dev);
        return ret;
    }

    dev->data = data;
    return MODBUS_RT_EOK;
}

#if MODBUS_RTU_SLAVE_ENABLE

/**
 * @brief   modbus_rtu_slave_data_create:   rtu_slave_data_t数据创建函数
 * @param   None
 * @return  rtu_slave_data_t:               rtu_slave_data_t 数据指针
 *
 */
static rtu_slave_data_t modbus_rtu_slave_data_create(void)
{
    rtu_slave_data_t data = modbus_rt_malloc(sizeof(struct rtu_slave_data));
    if (NULL == data)
    {
        return NULL;
    }
    memset(data, 0, sizeof(struct rtu_slave_data));
    return data;
}

#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
/**
 * @brief   rtu_slave_close_client_sock:    关闭连接到modbus Slave上的设备socket
 * @param   p_sock:                         指向client socket数组的指针
 * @return  None
 *
 */
static void rtu_slave_close_client_sock(int *p_sock)
{
    // 清除被链接的socket资源
    for (int i = 0; i < SOCKET_CONNECT_NUMS; i++)
    {
        if (-1 != p_sock[i])
        {
            modbus_rt_net_close(p_sock[i]);
            p_sock[i] = -1;
#if TCP_MODBUS_NUMS_ENABLE
            tcp_modbus_nums--;
#endif
        }
    }
}

/**
 * @brief   modbus_rtu_slave_net_entry:     modbus rtu slave over net的入口函数
 * @param   parameter:                      入口参数， rtu_modbus_device_t 设备
 * @return  无
 *
 */
static void modbus_rtu_slave_net_entry(rtu_modbus_device_t dev)
{
    if ((NULL == dev) || (OVER_NET != dev->over_type) || (0 >= dev->port) ||
        ((SOCK_STREAM != dev->type) && (SOCK_DGRAM != dev->type)))
    {
        return;
    }
    rtu_slave_data_t data = (rtu_slave_data_t)dev->data;
    int send_len = dev->send_len;
    int read_len = dev->read_len;
    agile_modbus_t *ctx = dev->ctx;

    fd_set sock_all_set, sock_read_set;                 // 存储socket的索引，用于select参数使用
    int cfd = 0, maxfd = 0, i = 0, nready = 0, ret = 0; // 零时存储数据
    int sock_client[SOCKET_CONNECT_NUMS] = {0};         // 连接到slave上的socket
    struct sockaddr_in client_addr = {0};               // 存储连接客户端的数据信息
    socklen_t client_addr_len = sizeof(client_addr);    // 存储链接到改server上的client的IP地址信息

    struct timeval timeout = {0}; // 用于设置select的超时值

    // 初始化 maxfd 等于 sock_server
    maxfd = dev->sock;
    // 清空fdset
    FD_ZERO(&sock_all_set);
    // 把sfd文件描述符添加到集合中
    FD_SET(dev->sock, &sock_all_set);
    if (SOCK_STREAM == dev->type)
    {
        // 初始化客户端fd的集合
        for (i = 0; i < SOCKET_CONNECT_NUMS; i++)
        {
            // 初始化为-1
            sock_client[i] = -1;
        }
    }
    while (1)
    {
        /* 检查退出信号 */
        if (dev->thread_owner && modbus_rt_sem_wait(&dev->sem, 0) == MODBUS_RT_EOK)
        {
            modbus_rt_sem_post(&dev->sem); // Acknowledge
            return;
        }

        // 设置超时时间为100ms
        timeout.tv_sec = 0;
        timeout.tv_usec = 100000;
        // 每次select返回之后，fd_set集合就会变化，再select时，就不能使用，
        // 所以我们要保存设置fd_set 和 读取的fd_set
        sock_read_set = sock_all_set;
        nready = select(maxfd + 1, &sock_read_set, NULL, NULL, &timeout);
        // 没有超时机制，不会返回0
        if (0 > nready)
        {
            // 系统错误，线程异常退出
            printf("0 > nready:%d.\n", nready);
            // 清除连接到tcp server上的tcp client数据
            if (SOCK_STREAM == dev->type)
            {
                rtu_slave_close_client_sock(sock_client);
            }

            modbus_rt_sem_post(&dev->sem); // Acknowledge on error
            return;
        }
        else if (0 == nready)
        {
            continue; // 超时，继续等待
        }
#if MODBUS_SERIAL_OVER_TCP_ENABLE
        if (SOCK_STREAM == dev->type)
        {
            // 判断监听的套接字是否有数据
            if (FD_ISSET(dev->sock, &sock_read_set))
            {
                // 有客户端进行连接了
                cfd = modbus_rt_net_accept(dev->sock, (struct sockaddr *)&client_addr, &client_addr_len);
                if (cfd < 0)
                {
                    continue; // 继续select
                }
#if TCP_MODBUS_NUMS_ENABLE
                if (tcp_modbus_nums >= TCP_MODBUS_NUMS)
                {
                    modbus_rt_net_close(cfd);
                    continue;
                }
#endif
                // 把新的cfd 保存到cfds集合中
                for (i = 0; i < SOCKET_CONNECT_NUMS; i++)
                {
                    if (sock_client[i] == -1)
                    {
                        sock_client[i] = cfd;
#if TCP_MODBUS_NUMS_ENABLE
                        tcp_modbus_nums++;
#endif
                        break;
                    }
                }

                // 如果i超过SOCKET_CONNECT_NUMS，表示client已经超标
                if (i >= SOCKET_CONNECT_NUMS)
                {
                    modbus_rt_net_close(cfd);
                }
                else
                {
                    // 把新的cfd 添加到fd_set集合中
                    FD_SET(cfd, &sock_all_set);
                    // 更新要select的maxfd
                    maxfd = (cfd > maxfd) ? cfd : maxfd;
                    // 没有其他套接字需要处理：这里防止重复工作，就不去执行其他任务
                    if (--nready == 0)
                    {
                        continue; // 继续select
                    }
                }
            }
            // 遍历所有的客户端文件描述符
            for (i = 0; i < SOCKET_CONNECT_NUMS; i++)
            {
                // 如果不是该socket，继续遍历
                if (sock_client[i] == -1)
                {
                    continue;
                }
                // 判断是否在fd_set集合里面
                if (FD_ISSET(sock_client[i], &sock_read_set))
                {
                    read_len = recv(sock_client[i], ctx->read_buf, ctx->read_bufsz, 0);
                    if (read_len <= 0)
                    {
                        modbus_rt_net_close(sock_client[i]);
                        // 从集合里面清除
                        FD_CLR(sock_client[i], &sock_all_set);
                        // 当前的客户端fd 赋值为-1
                        sock_client[i] = -1;
#ifdef TCP_MODBUS_NUMS
                        tcp_modbus_nums--;
#endif

                        continue;
                    }
#if MODBUS_ASCII_SLAVE_ENABLE
                    if (dev->ascii_flag)
                    {
                        modbus_ascii2rtu(ctx->read_buf, &read_len);
                    }
#endif
                    send_len = agile_modbus_slave_handle(ctx, read_len, data->slave_strict, agile_modbus_slave_util_callback, &data->util, NULL);
                    if (send_len > 0)
                    {
#if MODBUS_ASCII_SLAVE_ENABLE
                        if (dev->ascii_flag)
                        {
                            modbus_rtu2ascii(ctx->send_buf, &send_len);
                        }
#endif
                        ret = send(sock_client[i], ctx->send_buf, send_len, 0);
                        if (ret <= 0)
                        {
                            continue; // 发送失败，有可能时client端断开了，在下一次循环的select中会关闭该socket
                        }
                    }
                }
            }
        }
#endif
#if MODBUS_SERIAL_OVER_UDP_ENABLE
        if (SOCK_DGRAM == dev->type)
        {
            // 处理udp的数据
            read_len = recvfrom(dev->sock, ctx->read_buf, ctx->read_bufsz, 0, (struct sockaddr *)&client_addr, &client_addr_len);
            if (read_len <= 0)
            {
                continue;
            }
#if MODBUS_ASCII_SLAVE_ENABLE
            if (dev->ascii_flag)
            {
                modbus_ascii2rtu(ctx->read_buf, &read_len);
            }
#endif
            send_len = agile_modbus_slave_handle(ctx, read_len, data->slave_strict, agile_modbus_slave_util_callback, &data->util, NULL);
            if (send_len > 0)
            {
#if MODBUS_UDP_FOR_SEARCH // 跨网段设备广播检测
                if (modbus_rt_net_segment(dev->ipaddr, client_addr.sin_addr.s_addr) == 0)
                {
                    client_addr.sin_addr.s_addr = 0xffffffff;
                }
#endif
#if MODBUS_ASCII_SLAVE_ENABLE
                if (dev->ascii_flag)
                {
                    modbus_rtu2ascii(ctx->send_buf, &send_len);
                }
#endif
                ret = sendto(dev->sock, ctx->send_buf, send_len, 0, (struct sockaddr *)&client_addr, sizeof(client_addr));
                if (ret <= 0)
                {
                    printf("send error.\n");
                    continue;
                }
            }
        }
#endif
    }
}
#endif

/**
 * @brief   modbus_rtu_slave_entry:         modbus rtu slave的入口函数
 * @param   parameter:                      入口参数， rtu_modbus_device_t 设备
 * @return  无
 *
 */
static void modbus_rtu_slave_entry(void *parameter)
{
    rtu_modbus_device_t dev = (rtu_modbus_device_t)parameter;
    rtu_slave_data_t data = (rtu_slave_data_t)dev->data;
    int send_len = dev->send_len;
    int read_len = dev->read_len;
    agile_modbus_rtu_init(&(dev->ctx_rtu), dev->ctx_send_buf, send_len, dev->ctx_read_buf, read_len);
    agile_modbus_set_slave(dev->ctx, data->addr);
#if MODBUS_P2P_ENABLE
    if (0 != dev->p2p_flag)
    {
        agile_modbus_set_compute_meta_length_after_function_cb(dev->ctx, compute_meta_length_after_function_callback);
        agile_modbus_set_compute_data_length_after_meta_cb(dev->ctx, compute_data_length_after_meta_callback);
    }
#endif

    if (OVER_NONE == dev->over_type)
    {
        while (1)
        {
            /* 检查退出信号 */
            if (dev->thread_owner && modbus_rt_sem_wait(&dev->sem, 0) == MODBUS_RT_EOK)
            {
                modbus_rt_sem_post(&dev->sem); // Acknowledge
                return;
            }

            modbus_rtu_slave_poll(dev);
        }
    }
    else if (OVER_NET == dev->over_type)
    {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        modbus_rtu_slave_net_entry(dev);
#endif
    }
}
#endif

#if MODBUS_RTU_MASTER_ENABLE
/**
 * @brief   modbus_rtu_master_data_create:  tcp_master_data_t数据创建函数
 * @param   None
 * @return  rtu_master_data_t:              rtu_master_data_t 数据指针
 *
 */
static rtu_master_data_t modbus_rtu_master_data_create(void)
{
    rtu_master_data_t data = modbus_rt_malloc(sizeof(struct rtu_master_data));
    if (NULL == data)
    {
        return NULL;
    }
    memset(data, 0, sizeof(struct rtu_master_data));
    return data;
}

#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
/**
 * @brief   modbus_rtu_master_net_txrx:     rtu_master over net数据发送接收
 * @param   dev:                            rtu_modbus_device_t 设备
 * @param   ctx:                            modbus句柄
 * @param   send_len:                       需要发送数据的长度
 * @return  int:                            MODBUS_RT_EOK：成功，其他：失败
 *
 */
static int modbus_rtu_master_net_txrx(rtu_modbus_device_t dev, agile_modbus_t *ctx, int send_len)
{
    int read_len = 0;
    fd_set sock_read_set;         // 存储socket的索引，用于select参数使用
    int maxfd, nready;            // 零时存储数据
    struct timeval timeout = {0}; // 用于设置select的超时值
    /* 清空可读事件描述符列表 */
    FD_ZERO(&sock_read_set);
    /* 将需要监听可读事件的描述符加入列表 */
    FD_SET(dev->sock, &sock_read_set);
    /* 获取需要监听的描述符号最大值 */
    maxfd = dev->sock;

    // 设置超时时间为1s
    timeout.tv_sec = 0;
    timeout.tv_usec = 1000000;
    if (dev->type == SOCK_STREAM)
    {
#if MODBUS_ASCII_SLAVE_ENABLE
        int send_len_temp = send_len;
        uint8_t send_buf_temp[(AGILE_MODBUS_MAX_ADU_LENGTH + 1) * 2] = {0};
        if (dev->ascii_flag)
        {
            memcpy(send_buf_temp, ctx->send_buf, send_len_temp);
            modbus_rtu2ascii(send_buf_temp, &send_len_temp);
            send(dev->sock, send_buf_temp, send_len_temp, 0);
        }
        else
        {
            send(dev->sock, ctx->send_buf, send_len, 0);
        }
#else
        send(dev->sock, ctx->send_buf, send_len, 0);
#endif
        nready = select(maxfd + 1, &sock_read_set, NULL, NULL, &timeout);
        if (nready < 0)
        { // 错误
            return -MODBUS_RT_ERROR;
        }
        if (nready == 0)
        { // 超时
            return -MODBUS_RT_ETIMEOUT;
        }
        read_len = recv(dev->sock, ctx->read_buf, ctx->read_bufsz, 0);
        if (read_len <= 0)
        { // sock断开了
            return -MODBUS_RT_EBUSY;
        }
#if MODBUS_ASCII_SLAVE_ENABLE
        if (dev->ascii_flag)
        {
            modbus_ascii2rtu(ctx->read_buf, &read_len);
        }
#endif
    }
#if MODBUS_SERIAL_OVER_UDP_ENABLE
    else if (dev->type == SOCK_DGRAM)
    {
        struct sockaddr_in server_addr = {0};            // 存储连接udp server 的数据信息
        socklen_t server_addr_len = sizeof(server_addr); // 存储链接到改server上的client的IP地址信息

        rtu_master_data_t data = (rtu_master_data_t)dev->data; // tcp_master数据
        /* 设置服务端地址 */
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(data->sport);
        server_addr.sin_addr.s_addr = inet_addr(data->saddr);
        memset(&(server_addr.sin_zero), 0, sizeof(server_addr.sin_zero));
#if MODBUS_ASCII_SLAVE_ENABLE
        int send_len_temp = send_len;
        uint8_t send_buf_temp[(AGILE_MODBUS_MAX_ADU_LENGTH + 1) * 2] = {0};
        if (dev->ascii_flag)
        {
            memcpy(send_buf_temp, ctx->send_buf, send_len_temp);
            modbus_rtu2ascii(send_buf_temp, &send_len_temp);
            sendto(dev->sock, send_buf_temp, send_len_temp, 0, (struct sockaddr *)&server_addr, sizeof(server_addr));
        }
        else
        {
            sendto(dev->sock, ctx->send_buf, send_len, 0, (struct sockaddr *)&server_addr, sizeof(server_addr));
        }
#else
        sendto(dev->sock, ctx->send_buf, send_len, 0, (struct sockaddr *)&server_addr, sizeof(server_addr));
#endif
        nready = select(maxfd + 1, &sock_read_set, NULL, NULL, &timeout);
        if (nready < 0)
        {
            return -MODBUS_RT_ERROR;
        }
        if (nready == 0)
        {
            return -MODBUS_RT_ETIMEOUT;
        }
        read_len = recvfrom(dev->sock, ctx->read_buf, ctx->read_bufsz, 0, (struct sockaddr *)&server_addr, &server_addr_len);
        if (read_len <= 0)
        {
            return -MODBUS_RT_EBUSY;
        }
#if MODBUS_UDP_FOR_SEARCH
        if (0 == strcmp(data->saddr, "***************"))
        {
            inet_ntop(AF_INET, &(server_addr.sin_addr), data->saddr, INET_ADDRSTRLEN);
        }
#endif
#if MODBUS_ASCII_SLAVE_ENABLE
        if (dev->ascii_flag)
        {
            modbus_ascii2rtu(ctx->read_buf, &read_len);
        }
#endif
    }
#endif
    return read_len;
}
#endif

/**
 * @brief   modbus_rtu_master_txrx:         rtu_master数据发送接收
 * @param   dev:                            rtu_modbus_device_t 设备
 * @param   ctx:                            modbus句柄
 * @param   send_len:                       需要发送数据的长度
 * @return  int:                            MODBUS_RT_EOK：成功，其他：失败
 *
 */
static int modbus_rtu_master_txrx(rtu_modbus_device_t dev, agile_modbus_t *ctx, int send_len)
{
    if (OVER_NONE == dev->over_type)
    {
        int read_len = 0;
#if MODBUS_ASCII_SLAVE_ENABLE
        int send_len_temp = send_len;
        uint8_t send_buf_temp[(AGILE_MODBUS_MAX_ADU_LENGTH + 1) * 2] = {0};
        if (dev->ascii_flag)
        {
            memcpy(send_buf_temp, ctx->send_buf, send_len_temp);
            modbus_rtu2ascii(send_buf_temp, &send_len_temp);
            modbus_rt_serial_send(dev->serial, send_buf_temp, send_len_temp);
        }
        else
        {
            modbus_rt_serial_send(dev->serial, ctx->send_buf, send_len);
        }
#else
        print_hex_data("Master Serial TX", ctx->send_buf, send_len);
#if LC_LOG_STAT_EN
        lc_stat_inc_tx();
#endif
        modbus_rt_serial_send(dev->serial, ctx->send_buf, send_len);
#endif
        read_len = modbus_rt_serial_receive(dev->serial, ctx->read_buf, ctx->read_bufsz, MODBUS_RTU_TIME_OUT, dev->byte_timeout);
        print_hex_data("Master Serial RX", ctx->read_buf, read_len);
#if LC_LOG_STAT_EN
        lc_stat_inc_rx();
#endif
        if (read_len <= 0)
        { // 接收超时
            return -MODBUS_RT_ETIMEOUT;
        }
#if MODBUS_ASCII_SLAVE_ENABLE
        if (dev->ascii_flag)
        {
            modbus_ascii2rtu(ctx->read_buf, &read_len);
        }
#endif
        return read_len;
    }
    else if (OVER_NET == dev->over_type)
    {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        return modbus_rtu_master_net_txrx(dev, ctx, send_len);
#endif
    }
    return -MODBUS_RT_ERROR;
}

/**
 * @brief   modbus_rtu_master_excuse_run:   rtu_master执行执行函数
 * @param   dev:                            rtu_modbus_device_t设备
 * @return  int:                            MODBUS_RT_EOK：成功，其他：失败
 *
 */
static int modbus_rtu_master_excuse_run(rtu_modbus_device_t dev)
{
    int send_len = 0;
    int read_len = 0;
    agile_modbus_t *ctx = dev->ctx;
    rtu_master_data_t data = (rtu_master_data_t)dev->data;

    // 从队列取出的指令，参数已经复制到 data->... 中
    // if ((AGILE_MODBUS_FC_REPORT_SLAVE_ID != data->function) && (0x20 > data->function))
    // {
    //     if ((0 == data->quantity) || (NULL == data->ptr_data))
    //     {
    //         data->ret = -MODBUS_RT_ERROR;
    //         data->function = 0;
    //         return data->ret;
    //     }
    // }
    agile_modbus_set_slave(ctx, data->slave_addr);
    switch (data->function)
    {
    case AGILE_MODBUS_FC_READ_COILS:
    {
        send_len = agile_modbus_serialize_read_bits(ctx, data->data_addr, data->quantity);
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_read_bits(ctx, read_len, data->ptr_data);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
    case AGILE_MODBUS_FC_READ_DISCRETE_INPUTS:
    {
        send_len = agile_modbus_serialize_read_input_bits(ctx, data->data_addr, data->quantity);
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_read_input_bits(ctx, read_len, data->ptr_data);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
    case AGILE_MODBUS_FC_READ_HOLDING_REGISTERS:
    {
        send_len = agile_modbus_serialize_read_registers(ctx, data->data_addr, data->quantity);
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_read_registers(ctx, read_len, data->ptr_data);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
    case AGILE_MODBUS_FC_READ_INPUT_REGISTERS:
    {
        send_len = agile_modbus_serialize_read_input_registers(ctx, data->data_addr, data->quantity);
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_read_input_registers(ctx, read_len, data->ptr_data);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
    case AGILE_MODBUS_FC_WRITE_SINGLE_COIL:
    {
        // 高电平写FF00，低电平写0, 再agile_modbus_serialize_write_bit已经区分了
        send_len = agile_modbus_serialize_write_bit(ctx, data->data_addr, *((int *)data->ptr_data));
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_write_bit(ctx, read_len);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
    case AGILE_MODBUS_FC_WRITE_SINGLE_REGISTER:
    {
        send_len = agile_modbus_serialize_write_register(ctx, data->data_addr, *((uint16_t *)data->ptr_data));
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_write_register(ctx, read_len);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
    case AGILE_MODBUS_FC_WRITE_MULTIPLE_COILS:
    {
        send_len = agile_modbus_serialize_write_bits(ctx, data->data_addr, data->quantity, (uint8_t *)data->ptr_data);
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_write_bits(ctx, read_len);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
    case AGILE_MODBUS_FC_WRITE_MULTIPLE_REGISTERS:
    {
        send_len = agile_modbus_serialize_write_registers(ctx, data->data_addr, data->quantity, (uint16_t *)data->ptr_data);
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_write_registers(ctx, read_len);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
    case AGILE_MODBUS_FC_REPORT_SLAVE_ID:
    {
        send_len = agile_modbus_serialize_report_slave_id(ctx);
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_report_slave_id(ctx, read_len, data->quantity, (uint8_t *)data->ptr_data);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
    case AGILE_MODBUS_FC_MASK_WRITE_REGISTER:
    {
        send_len = agile_modbus_serialize_mask_write_register(ctx, data->data_addr,
                                                              *((uint16_t *)data->ptr_data), *(((uint16_t *)data->ptr_data) + 1));
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_mask_write_register(ctx, read_len);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
    case AGILE_MODBUS_FC_WRITE_AND_READ_REGISTERS:
    {
        send_len = agile_modbus_serialize_write_and_read_registers(ctx, data->data_addr, data->quantity,
                                                                   (uint16_t *)data->ptr_data, data->read_addr, data->read_quantity);
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_write_and_read_registers(ctx, read_len, (uint16_t *)data->ptr_read_data);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
#if MODBUS_P2P_MASTER_ENABLE
#if MODBUS_P2P_SEND_ENABLE
    case AGILE_MODBUS_FC_TRANS_FILE:
    {
        modbus_p2p_master_info_t *p2p_info = &g_modbus_p2p_master_info;
        send_len = agile_modbus_serialize_raw_request(ctx, p2p_info->raw_req, p2p_info->raw_req_len);
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_raw_response(ctx, read_len);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        int flag = ctx->read_buf[ctx->backend->header_length + 3];
        if (0x01 != flag)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        data->ret = MODBUS_RT_EOK;
    }
    break;
#endif
#if MODBUS_P2P_RECV_ENABLE
    case AGILE_MODBUS_FC_READ_FILE:
    {
        modbus_p2p_master_info_t *p2p_info = &g_modbus_p2p_master_info;
        send_len = agile_modbus_serialize_raw_request(ctx, p2p_info->raw_req, p2p_info->raw_req_len);
        read_len = modbus_rtu_master_txrx(dev, ctx, send_len);
        if (read_len < 0)
        {
            data->ret = read_len;
            break;
        }
        int rc = agile_modbus_deserialize_raw_response(ctx, read_len);
        if (rc < 0)
        {
            data->ret = -MODBUS_RT_ERROR;
            break;
        }
        // 把数据返回到上传程序线程操作
        int cmd_data_len = (ctx->read_buf[ctx->backend->header_length + 3] << 8) + ctx->read_buf[ctx->backend->header_length + 4];
        memcpy(&(p2p_info->raw_req[2]), &ctx->read_buf[ctx->backend->header_length + 1], cmd_data_len + 4);
        data->ret = MODBUS_RT_EOK;
    }
    break;
#endif
#endif
    default:
    {
        data->ret = -MODBUS_RT_ERROR;
    }
    break;
    }
    // data->function = 0; // 由外部循环控制
    return data->ret;
}

#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
/**
 * @brief   modbus_rtu_master_net_entry:    rtu_master over net的入口函数
 * @param   parameter:                      入口参数，rtu_modbus_device_t设备
 * @return  无
 *
 */
static void modbus_rtu_master_net_entry(rtu_modbus_device_t dev)
{
    if ((NULL == dev) || (OVER_NET != dev->over_type) ||
        ((SOCK_STREAM != dev->type) && (SOCK_DGRAM != dev->type)))
    {
        return;
    }
    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    static int sock_timeout = 0;
    fd_set sock_read_set;         // 存储socket的索引，用于select参数使用
    int maxfd, nready;            // 零时存储数据
    struct timeval timeout = {0}; // 用于设置select的超时值
    while (1)
    {
        /* 检查退出信号 */
        if (dev->thread_owner && modbus_rt_sem_wait(&dev->sem, 0) == MODBUS_RT_EOK)
        {
            modbus_rt_sem_post(&dev->sem); // Acknowledge
            return;
        }

        /* 等待新命令 */
        modbus_rt_sem_wait(&(data->cmd_sem), (uint32_t)-1);

        /* 再次检查退出信号 */
        if (dev->thread_owner && modbus_rt_sem_wait(&dev->sem, 0) == MODBUS_RT_EOK)
        {
            modbus_rt_sem_post(&dev->sem); // Acknowledge
            return;
        }

        modbus_cmd_t *cmd = NULL;
        /* 取出命令 */
        modbus_rt_mutex_lock(&data->cmd_mutex);
        if (!plist_head_empty(&data->cmd_queue))
        {
            cmd = list_entry(data->cmd_queue.node_list.next, modbus_cmd_t, list_node.node_list);
            plist_del(&cmd->list_node, &data->cmd_queue);
        }
        modbus_rt_mutex_unlock(&data->cmd_mutex);

        if (cmd == NULL)
        {
            continue;
        }

        /* 复制参数 */
        data->slave_addr = cmd->slave_addr;
        data->function = cmd->function;
        data->data_addr = cmd->w_addr;
        data->quantity = cmd->w_quantity;
        data->ptr_data = cmd->ptr_w_data;
        data->read_addr = cmd->r_addr;
        data->read_quantity = cmd->r_quantity;
        data->ptr_read_data = cmd->ptr_r_data;
        data->ret = -MODBUS_RT_ERROR;

        // 如果socket被远程tcp server断开，当有指令时，尝试重新连接
        if ((SOCK_STREAM == dev->type) && (0 >= dev->sock))
        {
            dev->sock = modbus_rt_tcp_client_init(NULL, dev->port, data->saddr, data->sport);
            if (0 >= dev->sock)
            {
                cmd->result = -MODBUS_RT_REMOTE;
                modbus_rt_sem_post(&cmd->completion_sem); // 通知调用者
                continue;
            }
        }
        sock_timeout = 0;
        // 连接成功之后，执行rtu master指令
        modbus_rtu_master_excuse_run(dev);
        cmd->result = data->ret;

        // 通知调用者执行完毕
        modbus_rt_sem_post(&cmd->completion_sem);
    }
}
#endif

/**
 * @brief   modbus_rtu_master_entry:        rtu_master的入口函数
 * @param   parameter:                      入口参数，rtu_modbus_device_t设备
 * @return  无
 *
 */
#if 0
static void modbus_rtu_master_entry(void *parameter)
{
    rtu_modbus_device_t dev = (rtu_modbus_device_t)parameter;
    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    int send_len = dev->send_len;
    int read_len = dev->read_len;
    agile_modbus_rtu_init(&(dev->ctx_rtu), dev->ctx_send_buf, send_len, dev->ctx_read_buf, read_len);
#if MODBUS_P2P_ENABLE
    if (0 != dev->p2p_flag)
    {
        agile_modbus_set_compute_meta_length_after_function_cb(dev->ctx, compute_meta_length_after_function_callback);
        agile_modbus_set_compute_data_length_after_meta_cb(dev->ctx, compute_data_length_after_meta_callback);
    }
#endif

    if (OVER_NONE == dev->over_type)
    {
        while (1)
        {
            /* 检查退出信号 */
            if (dev->thread_owner && modbus_rt_sem_wait(&dev->sem, 0) == MODBUS_RT_EOK)
            {
                modbus_rt_sem_post(&dev->sem); // Acknowledge
                return;
            }

            /* 1. 阻塞等待新命令的到来 */
            if (modbus_rt_sem_wait(&(data->cmd_sem), (uint32_t)-1) != MODBUS_RT_EOK)
            {
                continue;
            }

            /* 再次检查退出信号 */
            if (dev->thread_owner && modbus_rt_sem_wait(&dev->sem, 0) == MODBUS_RT_EOK)
            {
                modbus_rt_sem_post(&dev->sem); // Acknowledge
                return;
            }

            /* 2. 调用核心处理函数来处理该命令 */
            modbus_rtu_master_poll(dev);
        }
    }
    else if (OVER_NET == dev->over_type)
    {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        modbus_rtu_master_net_entry(dev);
#endif
    }
}
#endif
#endif

static void modbus_rtu_master_entry(void *parameter)
{
    rtu_modbus_device_t dev = (rtu_modbus_device_t)parameter;
    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    agile_modbus_rtu_init(&(dev->ctx_rtu), dev->ctx_send_buf, dev->send_len, dev->ctx_read_buf, dev->read_len);

#if MODBUS_P2P_ENABLE
    if (0 != dev->p2p_flag)
    {
        agile_modbus_set_compute_meta_length_after_function_cb(dev->ctx, compute_meta_length_after_function_callback);
        agile_modbus_set_compute_data_length_after_meta_cb(dev->ctx, compute_data_length_after_meta_callback);
    }
#endif

    if (OVER_NONE == dev->over_type)
    {
        while (1)
        {
            /* 1. 阻塞等待新命令的到来 */
            if (modbus_rt_sem_wait(&(data->cmd_sem), (uint32_t)-1) != MODBUS_RT_EOK)
            {
                /* 如果等待信号量出错，也应检查是否需要退出 */
            }

            /* 2. 被唤醒后，检查退出条件 */
            modbus_rt_mutex_lock(&dev->state_mutex);
            modbus_rt_mutex_lock(&data->cmd_mutex);

            bool should_exit = (dev->dev_state != MODBUS_DEV_STATE_ACTIVE && plist_head_empty(&data->cmd_queue));

            modbus_rt_mutex_unlock(&data->cmd_mutex);
            modbus_rt_mutex_unlock(&dev->state_mutex);

            if (should_exit)
            {
                /* 如果设备不再活跃且命令队列已空，则线程可以安全退出 */
                break;
            }

            /* 3. 如果不退出，则处理命令队列中的一个命令 */
            modbus_rtu_master_poll(dev);
        }
    }
    else if (OVER_NET == dev->over_type)
    {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        /* 网络模式的退出逻辑也应遵循相同的状态驱动模式 */
        /* 注意: 此处的 modbus_rtu_master_net_entry 也需要按类似逻辑重构，此处暂不展开 */
        modbus_rtu_master_net_entry(dev);
#endif
    }

    /* 线程退出前，发送确认信号给 close 函数 */
    modbus_rt_sem_post(&dev->sem);
}
/**
 * @brief   modbus_rtu:             创造modbus rtu设备
 * @param   mode:                   modbus类型： MODBUS_SLAVE/MODBUS_MASTER
 * @return  rtu_modbus_device_t:    rtu_modbus_device_t数据指针
 *
 */
rtu_modbus_device_t modbus_rtu(modbus_mode_type mode)
{
    int ret = 0;
    rtu_modbus_device_t dev = NULL;
    if ((MODBUS_SLAVE != mode) && (MODBUS_MASTER != mode))
    {
        return NULL;
    }
#if MODBUS_RTU_SLAVE_ENABLE
#if !MODBUS_RTU_MASTER_ENABLE
    if (MODBUS_MASTER == mode)
    {
        return NULL;
    }
#endif
    if (MODBUS_SLAVE == mode)
    {
        rtu_slave_data_t data = NULL;
        data = modbus_rtu_slave_data_create();
        if (NULL == data)
        {
            return NULL;
        }
        data->addr = 1;         /// 默认地址为1
        data->slave_strict = 1; // 默认开启地址匹配功能
        // 初始化modbus tcp slave的设备信息
        ret = modbus_rtu_dev_init(&dev, mode, data);
        if (MODBUS_RT_EOK != ret)
        {
            modbus_rt_free(data);
            return NULL;
        }
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        if (OVER_NET == dev->over_type)
        {
            dev->port = 502;         // 默认的端口号为502
            dev->type = SOCK_STREAM; // 默认为TCP模式
        }
#endif
        modbus_slave_util_init(&data->util);
    }
#endif
#if MODBUS_RTU_MASTER_ENABLE
#if !MODBUS_RTU_SLAVE_ENABLE
    if (MODBUS_SLAVE == mode)
    {
        return NULL;
    }
#endif
    if (MODBUS_MASTER == mode)
    {
        rtu_master_data_t data = NULL;
        data = modbus_rtu_master_data_create();
        if (NULL == data)
        {
            return NULL;
        }
        ret = modbus_rtu_dev_init(&dev, mode, data);
        if (MODBUS_RT_EOK != ret)
        {
            modbus_rt_free(data);
            return NULL;
        }
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        if (OVER_NET == dev->over_type)
        {
            data->sport = 502;       // 服务器端默认端口0
            dev->port = 0;           // Master默认的端口号为随机
            dev->type = SOCK_STREAM; // 默认为TCP模式
        }
#endif
        // 初始化master执行指令的完成信号量
        plist_head_init(&data->cmd_queue);
        if (modbus_rt_mutex_init(&data->cmd_mutex) != MODBUS_RT_EOK)
        {
            modbus_rt_free(data);
            return NULL;
        }
        //        ret = modbus_rt_sem_init(&(data->cmd_sem));
        ret = modbus_rt_mq_sem_init(&(data->cmd_mq_sem));
        if (MODBUS_RT_EOK != ret)
        {
            modbus_rt_mutex_destroy(&data->cmd_mutex);
            modbus_rt_free(data);
            return NULL;
        }
    }
#endif
    dev->ctx = &(dev->ctx_rtu._ctx);

    // 初始化设备状态相关字段
    dev->dev_state = MODBUS_DEV_STATE_ACTIVE;
    dev->active_cmd_count = 0;

    if (modbus_rt_mutex_init(&dev->state_mutex) != MODBUS_RT_EOK)
    {
        if (MODBUS_MASTER == mode)
        {
            modbus_rt_mutex_destroy(&((rtu_master_data_t)dev->data)->cmd_mutex);
            //            modbus_rt_sem_destroy(&((rtu_master_data_t)dev->data)->cmd_sem);
            modbus_rt_mq_sem_destroy(&((rtu_master_data_t)dev->data)->cmd_mq_sem);
        }
        modbus_rt_free(dev->data);
        modbus_rt_free(dev);
        return NULL;
    }

    if (modbus_rt_sem_init(&dev->all_cmd_done) != MODBUS_RT_EOK)
    {
        modbus_rt_mutex_destroy(&dev->state_mutex);
        if (MODBUS_MASTER == mode)
        {
            modbus_rt_mutex_destroy(&((rtu_master_data_t)dev->data)->cmd_mutex);
            //            modbus_rt_sem_destroy(&((rtu_master_data_t)dev->data)->cmd_sem);
            modbus_rt_mq_sem_destroy(&((rtu_master_data_t)dev->data)->cmd_mq_sem);
        }
        modbus_rt_free(dev->data);
        modbus_rt_free(dev);
        return NULL;
    }
    return dev;
}

/**
 * @brief   modbus_rtu_set_serial:  设置modbus rtu的串口信息
 * @param   dev:                    rtu_modbus_device_t设备
 * @param   devname:                串口设备名称
 * @param   baudrate:               波特率
 * @param   parity:                 校验位：'N', 'E', 'O', 'M', 'S'
 * @param   stopbits:               停止位：1，2
 * @param   xonxoff:                控制流xonxoff开关，暂时不支持其他流控制模式
 * @return  int:                    MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_set_serial(rtu_modbus_device_t dev, const char *devname, int baudrate, int bytesize, char parity, int stopbits, int xonxoff)
{
    if ((NULL == dev) || (NULL == devname))
    {
        return -MODBUS_RT_EINVAL;
    }
    int len = strlen(devname);
    if ((MODBUS_RTU_NAME_MAX <= len) || (5 > bytesize) || (8 < bytesize))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    memcpy(dev->serial_info.devname, devname, len);
    dev->serial_info.devname[len] = 0;
    dev->serial_info.baudrate = baudrate;
    dev->serial_info.bytesize = bytesize;
    dev->serial_info.parity = parity;
    dev->serial_info.stopbits = stopbits;
    dev->serial_info.xonxoff = xonxoff;
    return MODBUS_RT_EOK;
}

#if MODBUS_ASCII_SLAVE_ENABLE || MODBUS_ASCII_MASTER_ENABLE
/**
 * @brief   modbus_rtu_set_ascii_flag:  modbus ascii开关设置
 * @param   dev:                    tcp_modbus_device_t设备
 * @param   flag:                   0：rtu模式，1：ascii模式
 * @return  int:                    MODBUS_RT_EOK：成功，其他：失败
 */
int modbus_rtu_set_ascii_flag(rtu_modbus_device_t dev, int flag)
{
    if ((NULL == dev) || ((0 != flag) && (1 != flag)))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    dev->ascii_flag = flag;
    return MODBUS_RT_EOK;
}
#endif

/**
 * @brief   modbus_rtu_open:    开启modbus rtu启动通信相关的线程
 * @param   dev:                rtu_modbus_device_t设备
 * @return  int:                MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_open(rtu_modbus_device_t dev)
{
    int id_thread = 0;
    char str_name[MODBUS_RT_NAME_MAX] = {0};
    if ((NULL == dev) || (NULL == dev->data))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
#if MODBUS_RTU_SLAVE_ENABLE
#if !MODBUS_RTU_MASTER_ENABLE
    if (MODBUS_MASTER == dev->mode)
    {
        return -MODBUS_RT_EINVAL;
    }
#endif
#if MODBUS_P2P_ENABLE
    if (dev->p2p_flag)
    {

        ((rtu_slave_data_t)(dev->data))->util.special_function = modbus_slave_special_callback;
    }
#endif
#endif
#if MODBUS_RTU_MASTER_ENABLE
#if !MODBUS_RTU_SLAVE_ENABLE
    if (MODBUS_SLAVE == dev->mode)
    {
        return -MODBUS_RT_EINVAL;
    }
#endif
    rtu_master_data_t data_master = NULL;
    (void)(data_master);
    if (MODBUS_MASTER == dev->mode)
    {
        data_master = (rtu_master_data_t)dev->data;
    }
#endif
    if (OVER_NONE == dev->over_type)
    {
        struct modbus_rt_serial_info info = dev->serial_info;
        dev->byte_timeout = (MODBUS_RTU_TIME_OUT_BITS * 1000000) / info.baudrate;
        if (dev->byte_timeout < (MODBUS_RTU_BYTE_TIME_OUT_MIN * 1000))
        { // 转化为ms。此处注意，建议最短的字符间距设置不低于3ms(1750us)。
            dev->byte_timeout = MODBUS_RTU_BYTE_TIME_OUT_MIN;
        }
        else
        {
            dev->byte_timeout = dev->byte_timeout / 1000 + 1;
        }
        int serial = modbus_rt_serial_open(info.devname, info.baudrate, info.bytesize,
                                           info.parity, info.stopbits, info.xonxoff);
        if (0 > serial)
        {
            return serial;
        }
        id_thread = serial;
        dev->serial = serial;
    }
    else if (OVER_NET == dev->over_type)
    {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        int sock = -1;
#if MODBUS_RTU_MASTER_ENABLE
        int64_t net_addr_temp = (int64_t)inet_addr(data_master->saddr);
        if (net_addr_temp < 0)
        {
            return -MODBUS_RT_EINVAL;
        }
#endif
#if TCP_MODBUS_NUMS_ENABLE
        if (tcp_modbus_nums >= TCP_MODBUS_NUMS)
        {
            return -MODBUS_RT_EINVAL;
        }
#endif
        if (SOCK_STREAM == dev->type)
        {
#if MODBUS_RTU_SLAVE_ENABLE
            if (MODBUS_SLAVE == dev->mode)
            {
                sock = modbus_rt_tcp_server_init(dev->ipaddr, dev->port, SOCKET_CONNECT_NUMS);
            }
#endif
#if MODBUS_RTU_MASTER_ENABLE
            if (MODBUS_MASTER == dev->mode)
            {
                sock = modbus_rt_tcp_client_init(dev->ipaddr, dev->port, data_master->saddr, data_master->sport);
            }
#endif
        }
        else if (SOCK_DGRAM == dev->type)
        {
#if MODBUS_SERIAL_OVER_UDP_ENABLE
            sock = modbus_rt_udp_socket_init(dev->ipaddr, dev->port);
#else
            return -MODBUS_RT_EINVAL;
#endif
        }
        if (0 > sock)
        {
            return sock;
        }
        id_thread = sock;
        dev->sock = sock;
#else
        return -MODBUS_RT_EINVAL;
#endif
    }
#if MODBUS_P2P_ENABLE
    if (0 == dev->p2p_flag)
    {
        dev->send_len = AGILE_MODBUS_MAX_ADU_LENGTH;
        dev->read_len = AGILE_MODBUS_MAX_ADU_LENGTH;
    }
    else
    {
        dev->send_len = P2P_SLAVE_BUF_MAX_LEN;
        dev->read_len = P2P_SLAVE_BUF_MAX_LEN;
    }
#else
    dev->send_len = AGILE_MODBUS_MAX_ADU_LENGTH;
    dev->read_len = AGILE_MODBUS_MAX_ADU_LENGTH;
#endif
    dev->ctx_send_buf = modbus_rt_malloc(dev->send_len);
    if (NULL == dev->ctx_send_buf)
    {
        if (OVER_NONE == dev->over_type)
        {
            modbus_rt_serial_close(dev->serial);
            dev->serial = 0;
        }
        else if (OVER_NET == dev->over_type)
        {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
            modbus_rt_net_close(dev->sock); /* 关闭socket */
            dev->sock = 0;
#endif
        }
        return -MODBUS_RT_ENOMEM;
    }
    memset(dev->ctx_send_buf, 0, dev->send_len);
    dev->ctx_read_buf = modbus_rt_malloc(dev->read_len);
    if (NULL == dev->ctx_read_buf)
    {
        modbus_rt_free(dev->ctx_send_buf);
        if (OVER_NONE == dev->over_type)
        {
            modbus_rt_serial_close(dev->serial);
            dev->serial = 0;
        }
        else if (OVER_NET == dev->over_type)
        {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
            modbus_rt_net_close(dev->sock); /* 关闭socket */
            dev->sock = 0;
#endif
        }
        return -MODBUS_RT_ENOMEM;
    }
    memset(dev->ctx_read_buf, 0, dev->read_len);
    // 创建线程
    if (dev->thread_owner)
    {
#if MODBUS_RTU_SLAVE_ENABLE
        if (MODBUS_SLAVE == dev->mode)
        {
            sprintf(str_name, "rtu_ts%d", id_thread);
            dev->thread = modbus_rt_thread_init(str_name, modbus_rtu_slave_entry,
                                                dev, MODBUS_THREAD_STACK_SIZE,
                                                MODBUS_THREAD_PRIO, 10);
        }
#endif
#if MODBUS_RTU_MASTER_ENABLE
        if (MODBUS_MASTER == dev->mode)
        {
            sprintf(str_name, "rtu_tm%d", id_thread);
            dev->thread = modbus_rt_thread_init(str_name, modbus_rtu_master_entry,
                                                dev, MODBUS_THREAD_STACK_SIZE,
                                                MODBUS_THREAD_PRIO, 10);
        }
#endif
        if (NULL != dev->thread)
        {
            modbus_rt_thread_startup(dev->thread);
        }
        else
        {
            modbus_rt_free(dev->ctx_send_buf);
            modbus_rt_free(dev->ctx_read_buf);
            if (OVER_NONE == dev->over_type)
            {
                modbus_rt_serial_close(dev->serial);
                dev->serial = 0;
            }
            else if (OVER_NET == dev->over_type)
            {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
                modbus_rt_net_close(dev->sock); /* 关闭socket */
                dev->sock = 0;
#endif
            }
            return -MODBUS_RT_EINVAL;
        }
    }
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
#if TCP_MODBUS_NUMS_ENABLE
    tcp_modbus_nums++;
#endif
#endif
    dev->status = 1;
    return MODBUS_RT_EOK;
}

/**
 * @brief   modbus_rtu_isopen:   关闭modbus rtu启动通信相关的线程
 * @param   dev:                rtu_modbus_device_t设备
 * @return  int:                MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_isopen(rtu_modbus_device_t dev)
{
    if ((NULL == dev))
    {
        return 0;
    }
    return dev->status;
}

/**
 * @brief   modbus_rtu_close:   关闭modbus rtu启动通信相关的线程
 * @param   dev:                rtu_modbus_device_t设备
 * @return  int:                MODBUS_RT_EOK：成功，其他：失败
 *
 */
#if LAST_VERSION
int modbus_rtu_close(rtu_modbus_device_t dev)
{
    if (!modbus_rtu_isopen(dev))
    {
        return -MODBUS_RT_ERROR;
    }

    if (dev->thread != NULL)
    {
        /*
         * 唤醒可能正在休眠的工作线程，
         * 这是为了确保它能检查到最新的设备状态和命令队列情况，
         * 从而决定自己是否应该退出循环。
         */
        if (dev->mode == MODBUS_MASTER)
        {
            rtu_master_data_t data = (rtu_master_data_t)dev->data;
            modbus_rt_mq_sem_post(&data->cmd_mq_sem);
            // modbus_rt_sem_post(&data->cmd_sem);
        }
        else if (dev->mode == MODBUS_SLAVE)
        {
            // 对于 Slave，它的退出机制是检查 dev->sem
            modbus_rt_sem_post(&dev->sem);
        }

        /*
         * 等待工作线程自己确认退出。
         * 在新的设计中, dev->sem 由工作线程在退出前主动发送。
         */
        modbus_rt_sem_wait(&dev->sem, (uint32_t)-1);

        /* 确认线程结束后，才销毁线程句柄 */
        modbus_rt_thread_destroy(dev->thread);
        dev->thread = NULL;
    }

    /* 清理其他资源 */
    modbus_rt_free(dev->ctx_send_buf);
    modbus_rt_free(dev->ctx_read_buf);
    if (OVER_NONE == dev->over_type)
    {
        if (0 < dev->serial)
        {
            modbus_rt_serial_close(dev->serial);
            dev->serial = 0;
        }
    }
    else if (OVER_NET == dev->over_type)
    {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        modbus_rt_net_close(dev->sock);
#if TCP_MODBUS_NUMS_ENABLE
        tcp_modbus_nums--;
#endif
        dev->sock = 0;
#else
        return -MODBUS_RT_EINVAL;
#endif
    }
    dev->status = 0;

    return MODBUS_RT_EOK;
}
#else
int modbus_rtu_close(rtu_modbus_device_t dev)
{
    if (!modbus_rtu_isopen(dev))
        return -MODBUS_RT_ERROR;

    /* 唤醒工作线程，让其感知关闭意图 */
    if (dev->thread)
    {
        if (dev->mode == MODBUS_MASTER)
        {
            rtu_master_data_t data = (rtu_master_data_t)dev->data;
            modbus_rt_mq_sem_post(&data->cmd_mq_sem); /* 令 token≠队列，线程会立刻醒来 */
        }
        else if (dev->mode == MODBUS_SLAVE)
        {
            modbus_rt_sem_post(&dev->sem);
        }

        /* 等待线程自行退出 */
        modbus_rt_sem_wait(&dev->sem, MODBUS_RT_WAIT_FOREVER);
        modbus_rt_thread_destroy(dev->thread);
        dev->thread = NULL;
    }

    /* 关闭串口 / socket / 缓冲区 */
    modbus_rt_free(dev->ctx_send_buf);
    modbus_rt_free(dev->ctx_read_buf);

    if (OVER_NONE == dev->over_type && dev->serial)
    {
        modbus_rt_serial_close(dev->serial);
        dev->serial = 0;
    }
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
    else if (OVER_NET == dev->over_type && dev->sock)
    {
        modbus_rt_net_close(dev->sock);
        dev->sock = 0;
    }
#endif

    dev->status = 0;
    return MODBUS_RT_EOK;
}
#endif
#if 0
/**
 * @brief   modbus_rtu_destroy: modbus rtu销毁函数
 * @param   pos_dev:            指向rtu_modbus_device_t的指针，
 *                              这里主要目的需要通过指针删除相应的动态分配数据内容
 * @return  int:                MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_destroy(rtu_modbus_device_t *pos_dev)
{
    if ((NULL == pos_dev) || (NULL == (*pos_dev)))
    {
        return -MODBUS_RT_EINVAL;
    }
    rtu_modbus_device_t dev = *pos_dev;
    if (NULL == dev)
    {
        return -MODBUS_RT_EINVAL;
    }
    if (modbus_rtu_isopen(dev))
    {
        modbus_rtu_close(dev);
    }
#if MODBUS_RTU_SLAVE_ENABLE
    if (MODBUS_SLAVE == dev->mode)
    {
        rtu_slave_data_t data = (rtu_slave_data_t)dev->data;
        modbus_slave_clear_val(&(data->util));
        modbus_rt_free(data);
    }
#endif
#if MODBUS_RTU_MASTER_ENABLE
#if 0
    if (MODBUS_MASTER == dev->mode)
    {
        rtu_master_data_t data = (rtu_master_data_t)dev->data;

        modbus_cmd_t *cmd, *n;
        list_for_each_entry_safe(cmd, n, &data->cmd_queue.node_list, list_node.node_list)
        {
            /*
             * 遍历队列中所有未处理的命令。
             * 由于设备正在销毁，这些命令永远不会被执行。
             * 我们需要销毁它们关联的信号量，并释放为它们分配的内存，
             * 以防止资源泄漏。
             */
            modbus_rt_sem_destroy(&cmd->completion_sem);
            modbus_rt_free(cmd);
        }

        modbus_rt_sem_destroy(&(data->cmd_sem));
        modbus_rt_mutex_destroy(&data->cmd_mutex);
        modbus_rt_free(data);
    }
#else
    if (MODBUS_MASTER == dev->mode)
    {
        rtu_master_data_t data = (rtu_master_data_t)dev->data;
        modbus_cmd_t *cmd, *n;

        /*
         * 关键修复：正确处理关闭时队列中剩余的命令
         * 遍历所有等待中的命令，必须唤醒它们的调用者，而不是直接销毁它们。
         * 直接销毁会导致调用者线程内存访问错误或永久阻塞。
         */
        modbus_rt_mutex_lock(&data->cmd_mutex);
        list_for_each_entry_safe(cmd, n, &data->cmd_queue.node_list, list_node.node_list)
        {
            /* 从队列中删除 */
            plist_del(&cmd->list_node, &data->cmd_queue);

            /* 设置一个错误码，告知调用者设备已被销毁 */
            cmd->result = -MODBUS_RT_ERROR;
            cmd->status = MODBUS_CMD_STATE_CANCELLED;

            /* 唤醒阻塞在 completion_sem 上的调用者线程 */
            modbus_rt_sem_post(&cmd->completion_sem);

            /*
             * 注意：此处不再调用 modbus_rt_sem_destroy 和 modbus_rt_free。
             * 唤醒后的调用者 (modbus_rtu_master_send_cmd) 将负责这些清理工作。
             */
        }
        modbus_rt_mutex_unlock(&data->cmd_mutex);

        modbus_rt_sem_destroy(&(data->cmd_sem));
        modbus_rt_mutex_destroy(&data->cmd_mutex);
        modbus_rt_free(data);
    }
#endif
#endif
    modbus_rt_sem_destroy(&(dev->sem));
    modbus_rt_free(dev);
    *pos_dev = NULL;
    return MODBUS_RT_EOK;
}
#endif
/**
 * @brief   modbus_rtu_destroy: modbus rtu销毁函数
 * @param   pos_dev:            指向rtu_modbus_device_t的指针，
 *                              这里主要目的需要通过指针删除相应的动态分配数据内容
 * @return  int:                MODBUS_RT_EOK：成功，其他：失败
 *
 */
#if 0
int modbus_rtu_destroy(rtu_modbus_device_t *pos_dev)
{
    if ((NULL == pos_dev) || (NULL == (*pos_dev)))
    {
        return -MODBUS_RT_EINVAL;
    }
    rtu_modbus_device_t dev = *pos_dev;
    if (NULL == dev)
    {
        return -MODBUS_RT_EINVAL;
    }
    if (modbus_rtu_isopen(dev))
    {
        modbus_rtu_close(dev);
    }

    // 设置设备状态为正在关闭
    modbus_rt_mutex_lock(&dev->state_mutex);
    dev->dev_state = MODBUS_DEV_STATE_CLOSING;
    bool has_active_cmds = (dev->active_cmd_count > 0);
    modbus_rt_mutex_unlock(&dev->state_mutex);

    // 等待所有活动命令完成，但最多等待5秒
    if (has_active_cmds)
    {
        if (modbus_rt_sem_wait(&dev->all_cmd_done, (uint32_t)-1) != MODBUS_RT_EOK)
        {
            // 超时处理：记录警告，强制继续
            printf("等待活动命令完成超时，强制继续销毁过程\n");

            // 强制重置活动命令计数
            modbus_rt_mutex_lock(&dev->state_mutex);
            dev->active_cmd_count = 0;
            modbus_rt_mutex_unlock(&dev->state_mutex);
        }
    }

#if MODBUS_RTU_SLAVE_ENABLE
    if (MODBUS_SLAVE == dev->mode)
    {
        rtu_slave_data_t data = (rtu_slave_data_t)dev->data;
        modbus_slave_clear_val(&(data->util));
        modbus_rt_free(data);
    }
#endif
#if MODBUS_RTU_MASTER_ENABLE
    if (MODBUS_MASTER == dev->mode)
    {
        rtu_master_data_t data = (rtu_master_data_t)dev->data;
        modbus_cmd_t *cmd, *n;

        /*
         * 关键修复：正确处理关闭时队列中剩余的命令
         * 遍历所有等待中的命令，必须唤醒它们的调用者，而不是直接销毁它们。
         * 直接销毁会导致调用者线程内存访问错误或永久阻塞。
         */
        modbus_rt_mutex_lock(&data->cmd_mutex);
        list_for_each_entry_safe(cmd, n, &data->cmd_queue.node_list, list_node.node_list)
        {
            /* 从队列中删除 */
            plist_del(&cmd->list_node, &data->cmd_queue);

            /* 设置一个错误码，告知调用者设备已被销毁 */
            cmd->result = -MODBUS_RT_ERROR;
            cmd->status = MODBUS_CMD_STATE_CANCELLED;

            /* 唤醒阻塞在 completion_sem 上的调用者线程 */
            modbus_rt_sem_post(&cmd->completion_sem);

            /*
             * 注意：此处不再调用 modbus_rt_sem_destroy 和 modbus_rt_free。
             * 唤醒后的调用者 (modbus_rtu_master_send_cmd) 将负责这些清理工作。
             */
        }
        modbus_rt_mutex_unlock(&data->cmd_mutex);

        modbus_rt_sem_destroy(&(data->cmd_sem));
        modbus_rt_mutex_destroy(&data->cmd_mutex);
        modbus_rt_free(data);
    }
#endif

    // 设置设备状态为已关闭
    modbus_rt_mutex_lock(&dev->state_mutex);
    dev->dev_state = MODBUS_DEV_STATE_CLOSED;
    modbus_rt_mutex_unlock(&dev->state_mutex);

    // 销毁设备状态相关资源
    modbus_rt_sem_destroy(&(dev->all_cmd_done));
    modbus_rt_mutex_destroy(&dev->state_mutex);

    modbus_rt_sem_destroy(&(dev->sem));
    modbus_rt_free(dev);
    *pos_dev = NULL;
    return MODBUS_RT_EOK;
}
#endif
#if LAST_VERSION
int modbus_rtu_destroy(rtu_modbus_device_t *pos_dev)
{
    if ((NULL == pos_dev) || (NULL == (*pos_dev)))
    {
        return -MODBUS_RT_EINVAL;
    }
    rtu_modbus_device_t dev = *pos_dev;

    /* 步骤 1: 将设备标记为正在关闭，后续不再接受新命令 */
    modbus_rt_mutex_lock(&dev->state_mutex);
    if (dev->dev_state == MODBUS_DEV_STATE_ACTIVE)
    {
        dev->dev_state = MODBUS_DEV_STATE_CLOSING;
    }
    bool has_active_cmds = (dev->active_cmd_count > 0);
    modbus_rt_mutex_unlock(&dev->state_mutex);

    /* 步骤 2: 安全地关闭工作线程和底层句柄（串口/网络）*/
    if (modbus_rtu_isopen(dev))
    {
        modbus_rtu_close(dev);
    }

    /* 步骤 3: 等待所有正在执行的命令完成（有超时机制） */
    if (has_active_cmds)
    {
        if (modbus_rt_sem_wait(&dev->all_cmd_done, (uint32_t)-1) != MODBUS_RT_EOK)
        {
            // 超时处理：记录警告，强制继续
            printf("等待活动命令完成超时，强制继续销毁过程\n");

            // 强制重置活动命令计数
            modbus_rt_mutex_lock(&dev->state_mutex);
            dev->active_cmd_count = 0;
            modbus_rt_mutex_unlock(&dev->state_mutex);
        }
    }

    /* 步骤 4: 清理队列中所有【尚未执行】的等待命令 */
    if (MODBUS_MASTER == dev->mode)
    {
        rtu_master_data_t data = (rtu_master_data_t)dev->data;
        modbus_cmd_t *cmd, *n;

        modbus_rt_mutex_lock(&data->cmd_mutex);
        list_for_each_entry_safe(cmd, n, &data->cmd_queue.node_list, list_node.node_list)
        {
            plist_del(&cmd->list_node, &data->cmd_queue);

            if (cmd->status == MODBUS_CMD_STATE_DETACHED)
            {
                // 异步命令：直接清理所有关联资源
                modbus_rt_sem_destroy(&cmd->completion_sem);
                modbus_rt_sem_destroy(&cmd->caller_ready_sem);
                modbus_rt_free(cmd);
#if LC_LOG_STAT_EN
                lc_stat_inc_cmd_free();
#endif
            }
            else // 状态为 WAITING
            {
                // 同步命令：唤醒正在等待的调用者，让其自行清理
                cmd->result = -MODBUS_RT_ECANCEL;
                cmd->status = MODBUS_CMD_STATE_COMPLETE;
                modbus_rt_sem_post(&cmd->completion_sem);
            }
        }
        modbus_rt_mutex_unlock(&data->cmd_mutex);
    }

    /* 步骤 5: 销毁设备本身及其核心组件 */
#if MODBUS_RTU_SLAVE_ENABLE
    if (MODBUS_SLAVE == dev->mode)
    {
        rtu_slave_data_t data = (rtu_slave_data_t)dev->data;
        modbus_slave_clear_val(&(data->util));
        modbus_rt_free(data);
    }
#endif
#if MODBUS_RTU_MASTER_ENABLE
    if (MODBUS_MASTER == dev->mode)
    {
        rtu_master_data_t data = (rtu_master_data_t)dev->data;
        modbus_rt_mq_sem_destroy(&(data->cmd_mq_sem));
        // modbus_rt_sem_destroy(&(data->cmd_sem));
        modbus_rt_mutex_destroy(&data->cmd_mutex);
        modbus_rt_free(data);
    }
#endif

    // 销毁设备级的信号量和互斥锁
    modbus_rt_sem_destroy(&(dev->all_cmd_done));
    modbus_rt_mutex_destroy(&dev->state_mutex);
    modbus_rt_sem_destroy(&(dev->sem));

    // 释放设备结构体内存
    modbus_rt_free(dev);
    *pos_dev = NULL;

    return MODBUS_RT_EOK;
}
#else
int modbus_rtu_destroy(rtu_modbus_device_t *pos_dev)
{
    if (!pos_dev || !*pos_dev)
        return -MODBUS_RT_EINVAL;

    rtu_modbus_device_t dev = *pos_dev;

    /* Step-1 : 标记 CLOSING，停止接受新命令 */
    modbus_rt_mutex_lock(&dev->state_mutex);
    if (dev->dev_state == MODBUS_DEV_STATE_ACTIVE)
        dev->dev_state = MODBUS_DEV_STATE_CLOSING;
    bool has_active = (dev->active_cmd_count > 0);
    modbus_rt_mutex_unlock(&dev->state_mutex);

    /* Step-2 : 关闭底层资源 & 工作线程 */
    if (modbus_rtu_isopen(dev))
        modbus_rtu_close(dev);

    /* Step-3 : 如有活动命令，等待 all_cmd_done 信号 */
    if (has_active)
        modbus_rt_sem_wait(&dev->all_cmd_done, 5000); /* 超时 5s */

    /* Step-4 : 清空等待队列 —— 新逻辑 */
    if (dev->mode == MODBUS_MASTER)
    {
        rtu_master_data_t data = (rtu_master_data_t)dev->data;
        modbus_rt_mutex_lock(&data->cmd_mutex);

        modbus_cmd_t *cmd, *tmp;
        list_for_each_entry_safe(cmd, tmp,
                                 &data->cmd_queue.node_list,
                                 list_node.node_list)
        {
            plist_del(&cmd->list_node, &data->cmd_queue);

            /* 统一先设置结果，方便调用者看到 ECANCEL */
            cmd->result = -MODBUS_RT_ECANCEL;

            if (cmd->status == MODBUS_CMD_STATE_WAITING) /* 同步 */
            {
                cmd->status = MODBUS_CMD_STATE_COMPLETE;
                modbus_rt_sem_post(&cmd->caller_ready_sem); /* ready */
                modbus_rt_sem_post(&cmd->completion_sem);   /* result */
            }
            /* DETACHED: 调用者不在等，无需 post */

            /* destroy 阶段顶替“线程”那份引用 */
            if (ref_dec_and_fetch(&cmd->refcnt, 1) == 0)
                cmd_cleanup(cmd);
        }
        modbus_rt_mutex_unlock(&data->cmd_mutex);
    }

    /* Step-5 : 释放 master/slave 专属数据结构 */
#if MODBUS_RTU_MASTER_ENABLE
    if (dev->mode == MODBUS_MASTER)
    {
        rtu_master_data_t data = (rtu_master_data_t)dev->data;
        modbus_rt_mq_sem_destroy(&data->cmd_mq_sem);
        modbus_rt_mutex_destroy(&data->cmd_mutex);
        modbus_rt_free(data);
    }
#endif
#if MODBUS_RTU_SLAVE_ENABLE
    if (dev->mode == MODBUS_SLAVE)
    {
        rtu_slave_data_t data = (rtu_slave_data_t)dev->data;
        modbus_slave_clear_val(&data->util);
        modbus_rt_free(data);
    }
#endif

    /* Step-6 : 销毁设备级资源 */
    modbus_rt_sem_destroy(&dev->all_cmd_done);
    modbus_rt_mutex_destroy(&dev->state_mutex);
    modbus_rt_sem_destroy(&dev->sem);
    modbus_rt_free(dev);

    *pos_dev = NULL;
    return MODBUS_RT_EOK;
}
#endif

/**
 * @brief   modbus_rtu_excuse:  modbus rtu执行函数：读取或者写入寄存器的值
 *          该函数为阻塞函数，执行完成或者超时后返回(采用信号量机制，不影响其他线程运行)。
 *          注意：不存在的寄存器读取和写入不会报错，但是永远为0。
 *
 * @param   dev:                rtu_modbus_device_t设备
 * @param   dir_slave:          针对slave设备：操作方向：MODBUS_READ（读寄存器）； MODBUS_WRITE（写寄存器）
 *                              针对master设备：modbus slave的地址
 * @param   type_function:      针对slave设备：寄存器类型: CIOLS(0), INPUTS(1), INPUT_REGISTERS(3), REGISTERS(4)四种
 *                              针对master设备：modbus命令
 * @param   addr:               需要操作的寄存器地址
 * @param   quantity:           寄存器的数量
 * @param   ptr_data:           需要读取或者写入的内容，不过不需要则为NULL
 * @return  int:                MODBUS_RT_EOK：成功，其他：失败
 *
 * @deprecated 该函数已被 modbus_rtu_master_send_cmd 替代，不应再使用。
 *             其实现已被注释，以防止在新的优先级队列模型下产生死锁。
 */
int modbus_rtu_excuse(rtu_modbus_device_t dev, int dir_slave, int type_function, int addr, int quantity, void *ptr_data)
{
#if MODBUS_RTU_SLAVE_ENABLE
    if (MODBUS_SLAVE == dev->mode)
    {
        rtu_slave_data_t data = NULL;
        if ((NULL == dev) || (NULL == dev->data) ||
            ((MODBUS_READ != dir_slave) && (MODBUS_WRITE != dir_slave)) || (1 != dev->status))
        {
            return -MODBUS_RT_EINVAL;
        }
        data = (rtu_slave_data_t)dev->data;
        if (MODBUS_READ == dir_slave)
        {
            modbus_slave_read(&(data->util), type_function, addr, quantity, ptr_data);
        }
        else if (MODBUS_WRITE == dir_slave)
        {
            modbus_slave_write(&(data->util), type_function, addr, quantity, ptr_data);
        }
        return MODBUS_RT_EOK;
    }
#endif
#if MODBUS_RTU_MASTER_ENABLE
    if (MODBUS_MASTER == dev->mode)
    {
        /*
        rtu_master_data_t data = NULL;
        if ((NULL == dev) || (NULL == dev->data) || (1 != dev->status))
        {
            return -MODBUS_RT_EINVAL;
        }
        data = (rtu_master_data_t)dev->data;

        modbus_rt_mutex_lock(&(dev->mutex));
        data->ret = -MODBUS_RT_ERROR;
        data->slave_addr = dir_slave;
        data->data_addr = addr;
        data->quantity = quantity;
        data->ptr_data = ptr_data;
        data->function = type_function;
        modbus_rt_mutex_unlock(&(dev->mutex));

        //    modbus_rt_sem_wait(&(data->completion));
        return data->ret;
        */
        return -MODBUS_RT_ERROR; // 已弃用
    }
#endif
    return -MODBUS_RT_ERROR;
}

#if MODBUS_P2P_ENABLE
/**
 * @brief   modbus_rtu_set_p2p_flag:    开启modbus p2p功能数
 * @param   dev:                        rtu_modbus_device_t 设备
 * @param   flag:                       0：关闭p2p模式；1：开启p2p模式
 * @return  int:                        MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_set_p2p_flag(rtu_modbus_device_t dev, int flag)
{
    if ((NULL == dev))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    dev->p2p_flag = flag;
    return MODBUS_RT_EOK;
}
#endif

#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
/**
 * @brief   modbus_rtu_set_over_type:   开启modbus rtu over type功能数
 * @param   dev:                        rtu_modbus_device_t 设备
 * @param   over_type:                  OVER_NONE：不开启；OVER_NET：开启over tcp/udp功能
 * @return  int:                        MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_set_over_type(rtu_modbus_device_t dev, modbus_serial_over_type_t over_type)
{
    if ((NULL == dev) || ((OVER_NONE != over_type) && (OVER_NET != over_type)))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    dev->over_type = over_type;
    return MODBUS_RT_EOK;
}

/**
 * @brief   modbus_rtu_set_net:     修改modbus rtu的网络信息
 * @param   dev:                    rtu_modbus_device_t 设备
 * @param   ipaddr:                 设备ip地址，设置为""表示默认为netdev
 * @param   port:                   端口号
 * @param   type:                   socket类型,TCP(SOCK_STREAM)或者UDP(SOCK_DGRAM)
 * @return  int:                    MODBUS_RT_EOK：成功，其他：失败
 */
int modbus_rtu_set_net(rtu_modbus_device_t dev, char *ipaddr, unsigned int port, int type)
{
    if ((NULL == dev) || (OVER_NET != dev->over_type) || (0 >= port) ||
        ((SOCK_STREAM != type) && (SOCK_DGRAM != type)))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    if (NULL != ipaddr)
    {
        int len_ip = strlen(ipaddr);
        memcpy(dev->ip_buf, ipaddr, len_ip);
        dev->ip_buf[len_ip] = 0;
        dev->ipaddr = dev->ip_buf;
    }
    else
    {
        dev->ipaddr = NULL;
    }
    dev->port = port;
    dev->type = type;
    return MODBUS_RT_EOK;
}

/**
 * @brief   modbus_rtu_set_ip:      修改modbus rtu的IP地址
 * @param   dev:                    rtu_modbus_device_t 设备
 * @param   ipaddr:                 需要绑定的IP地址，""表示默认为netdev
 * @return  int:                    MODBUS_RT_EOK：成功，其他：失败
 */
int modbus_rtu_set_ip(rtu_modbus_device_t dev, char *ipaddr)
{
    if ((NULL == dev) || (OVER_NET != dev->over_type))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    if (NULL != ipaddr)
    {
        int len_ip = strlen(ipaddr);
        memcpy(dev->ip_buf, ipaddr, len_ip);
        dev->ip_buf[len_ip] = 0;
        dev->ipaddr = dev->ip_buf;
    }
    else
    {
        dev->ipaddr = NULL;
    }
    return MODBUS_RT_EOK;
}

/**
 * @brief   modbus_rtu_set_port:    修改modbus rtu的通信端口号
 * @param   dev:                    tcp_modbus_device_t设备
 * @param   port:                   端口号
 * @return  int:                    MODBUS_RT_EOK：成功，其他：失败
 */
int modbus_rtu_set_port(rtu_modbus_device_t dev, unsigned int port)
{
    if ((NULL == dev) || (OVER_NET != dev->over_type) || (0 >= port))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    dev->port = port;
    return MODBUS_RT_EOK;
}

/**
 * @brief   modbus_rtu_set_type:    修改modbus rtu的通信类型
 * @param   dev:                    rtu_modbus_device_t 设备
 * @param   type:                   socket类型,TCP(SOCK_STREAM)或者UDP(SOCK_DGRAM)
 * @return  int:                    MODBUS_RT_EOK：成功，其他：失败
 */
int modbus_rtu_set_type(rtu_modbus_device_t dev, int type)
{
    if ((NULL == dev) || (OVER_NET != dev->over_type) || (NULL == dev->data) || ((SOCK_STREAM != type) && (SOCK_DGRAM != type)))
    {
        return -MODBUS_RT_EEMPTY;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    dev->type = type;
    return MODBUS_RT_EOK;
}

#endif

#if MODBUS_RTU_SLAVE_ENABLE
/**
 * @brief   modbus_rtu_set_addr:    修改modbus salve的通信地址
 * @param   dev:                    rtu_modbus_device_t 设备
 * @param   addr:                   modbus slave的地址
 * @return  int:                    MODBUS_RT_EOK：成功，其他：失败
 */
int modbus_rtu_set_addr(rtu_modbus_device_t dev, int addr)
{
    if ((NULL == dev) || (NULL == dev->data) || (MODBUS_SLAVE != dev->mode))
    {
        return -MODBUS_RT_EEMPTY;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    ((rtu_slave_data_t)dev->data)->addr = addr;
    return MODBUS_RT_EOK;
}

/**
 * @brief   modbus_rtu_set_strict:  修改modbus slave的地址匹配功能，注意：只能再开启之前修改
 * @param   dev:                    rtu_modbus_device_t 设备
 * @param   strict:                 地址匹配标识符,0: 不匹配地址；1: 匹配地址
 * @return  int:                    MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_set_strict(rtu_modbus_device_t dev, uint8_t strict)
{
    if ((NULL == dev) || (NULL == dev->data) || (MODBUS_SLAVE != dev->mode) ||
        ((0 != strict) && (1 != strict)))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }

    ((rtu_slave_data_t)dev->data)->slave_strict = strict;
    return MODBUS_RT_EOK;
}

/**
 * @brief   modbus_rtu_add_block:   给modbus slave添加数据
 * @param   dev:                    rtu_modbus_device_t 设备
 * @param   type:                   modbus slave 的寄存器类型，包括CIOLS(0), INPUTS(1), INPUT_REGISTERS(3), REGISTERS(4)四种
 * @param   data_addr:              modbus slave 的地址
 * @param   data:                   modbus slave 添加的数据内容指针
 * @param   nums:                   modbus slave 数据地址长度(注意是地址长度，根据寄存器类型不同，数据的内容和长度不一致)
 * @return  int:                    MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_add_block(rtu_modbus_device_t dev, modbus_register_type_t type, int data_addr, void *data, int nums)
{
    if ((NULL == dev) || (NULL == dev->data))
    {
        return -MODBUS_RT_EINVAL;
    }
    return modbus_slave_add_val(&(((rtu_slave_data_t)dev->data)->util), type, data_addr, data, nums);
}

/**
 * @brief   modbus_rtu_set_pre_ans_callback:    设置slave的应答前回调函数
 * @param   dev:                                rtu_modbus_device_t 设备
 * @param   pre_ans:                            回调函数
 * @return  int:                                MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_set_pre_ans_callback(rtu_modbus_device_t dev, int (*pre_ans)(agile_modbus_t *, int, int, int, int))
{
    if ((NULL == dev) || (NULL == dev->data) || (MODBUS_SLAVE != dev->mode))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    ((rtu_slave_data_t)dev->data)->util.pre_ans_callback = pre_ans;
    return MODBUS_RT_EOK;
}

/**
 * @brief   modbus_rtu_set_done_callback:       设置slave的应答前回调函数
 * @param   dev:                                rtu_modbus_device_t 设备
 * @param   done:                               回调函数
 * @return  int:                                MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_set_done_callback(rtu_modbus_device_t dev, int (*done)(agile_modbus_t *, int, int, int, int))
{
    if ((NULL == dev) || (NULL == dev->data) || (MODBUS_SLAVE != dev->mode))
    {
        return -MODBUS_RT_EINVAL;
    }
    if (0 < dev->status)
    {
        return -MODBUS_RT_ISOPEN;
    }
    ((rtu_slave_data_t)dev->data)->util.done_callback = done;
    return MODBUS_RT_EOK;
}

#if SLAVE_DATA_DEVICE_BINDING
/**
 * @brief   modbus_rtu_set_dev_binding: 开启slave的设备绑定功能
 * @param   dev:                        tcp_modbus_device_t设备
 * @param   flag:                       0：关闭p2p模式；1：开启p2p模式
 * @return  int:                        MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_set_dev_binding(rtu_modbus_device_t dev, int flag)
{
    if ((NULL == dev) || (NULL == dev->data) || (MODBUS_SLAVE != dev->mode))
    {
        return -MODBUS_RT_EINVAL;
    }
    return modbus_slave_util_dev_binding(&(((rtu_slave_data_t)dev->data)->util), flag);
}
#endif

#endif

#if MODBUS_RTU_MASTER_ENABLE
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
/**
 * @brief   modbus_rtu_set_server:              modbus master修改slave端的地址和端口信息
 * @param   dev:                                rtu_modbus_device_t 设备
 * @param   saddr:                              服务端的地址
 * @param   sport:                              服务端端口号
 * @return  int:                                MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_set_server(rtu_modbus_device_t dev, char *saddr, unsigned int sport)
{
    rtu_master_data_t data = NULL;
    if ((NULL == dev) || (NULL == dev->data) || ((SOCK_STREAM != dev->type) && (SOCK_DGRAM != dev->type)) ||
        (NULL == saddr) || (0 >= sport))
    {
        return -MODBUS_RT_EINVAL;
    }

    data = (rtu_master_data_t)dev->data;
    if (SOCK_STREAM == dev->type)
    {
        if (0 < dev->status)
        {
            return -MODBUS_RT_ISOPEN;
        }
        if (MODBUS_RT_EOK != modbus_rt_net_addr2ip(saddr, data->saddr))
        {
            return -MODBUS_RT_HOST_ERROR;
        }
        data->sport = sport;
    }
#if MODBUS_SERIAL_OVER_UDP_ENABLE
    else if (SOCK_DGRAM == dev->type)
    {
        if (MODBUS_RT_EOK != modbus_rt_net_addr2ip(saddr, data->saddr))
        {
            return -MODBUS_RT_HOST_ERROR;
        }
        data->sport = sport;
    }
#endif
    return MODBUS_RT_EOK;
}

/**
 * @brief   modbus_rtu_get_saddr:               modbus rtu master获取slave端设备的IP地址
 *                                              需要注意的是：每次广播都需要重新用modbus_tcp_set_server
 *                                              设置slave的地址为：***************。支持一条excuse指令
 *                                              之后。saddr会自动变为设备IP。下次广播需要重新设置广播ip
 * @param   dev:                                rtu_modbus_device_t设备
 * @param   saddr:                              存储设备的ip地址的空间，建议长度不小于INET_ADDRSTRLEN
 * @return  int:                                MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_get_saddr(rtu_modbus_device_t dev, char *saddr)
{
    rtu_master_data_t data = NULL;
    if ((NULL == dev) || (NULL == dev->data) || ((SOCK_STREAM != dev->type) && (SOCK_DGRAM != dev->type)) ||
        (NULL == saddr))
    {
        return -MODBUS_RT_EINVAL;
    }
    data = (rtu_master_data_t)dev->data;
    int len = strlen(data->saddr);
    memcpy(saddr, data->saddr, len);
    saddr[len] = 0;
    return MODBUS_RT_EOK;
}

#endif

/**
 * @brief   modbus_rtu_excuse_ex:               modbus rtu master特殊执行函数：
 *          主要针对：(0x17)AGILE_MODBUS_FC_WRITE_AND_READ_REGISTERS命令
 *          该函数为阻塞函数，执行完成或者超时后返回(采用信号量机制，不影响其他线程运行)。
 * @param   dev:                                rtu_modbus_device_t 设备
 * @param   slave:                              modbus slave的地址
 * @param   function:                           modbus命令
 * @param   w_addr:                             需要写入数据的寄存器地址
 * @param   w_quantity:                         写入数据的寄存器的数量
 * @param   ptr_w_data:                         需要写入的内容
 * @param   r_addr:                             需要读取入数据的寄存器地址
 * @param   r_quantity:                         读取数据的寄存器的数量
 * @param   ptr_w_data:                         存放读取的内容
 * @return  int:                                MODBUS_RT_EOK：成功，其他：失败
 *
 * @deprecated 该函数已被 modbus_rtu_master_send_cmd 替代，不应再使用。
 *             其实现已被注释，以防止在新的优先级队列模型下产生死锁。
 */
int modbus_rtu_excuse_ex(rtu_modbus_device_t dev, int slave, int function, int w_addr, int w_quantity,
                         void *ptr_w_data, int r_addr, int r_quantity, void *ptr_r_data)
{
    /*
    rtu_master_data_t data = NULL;
    if ((NULL == dev) || (NULL == dev->data) || (1 != dev->status))
    {
        return -MODBUS_RT_EINVAL;
    }
    data = (rtu_master_data_t)dev->data;

    modbus_rt_mutex_lock(&(dev->mutex));
    data->ret = -MODBUS_RT_ERROR;
    data->slave_addr = slave;
    data->data_addr = w_addr;
    data->quantity = w_quantity;
    data->ptr_data = ptr_w_data;
    data->read_addr = r_addr;
    data->read_quantity = r_quantity;
    data->ptr_read_data = ptr_r_data;
    data->function = function;
    modbus_rt_mutex_unlock(&(dev->mutex));

    //    modbus_rt_sem_wait(&(data->completion));
    return data->ret;
    */
    return -MODBUS_RT_ERROR; // 已弃用
}

#if ((MODBUS_P2P_ENABLE) && (MODBUS_P2P_MASTER_ENABLE))

/**
 * @brief   modbus_rtu_master_file_entry:  rtu master 文件读写线程函数
 * @param   parameter: 入口参数，rtu_modbus_device_t设备
 * @return  无
 *
 */
static void modbus_rtu_master_file_entry(void *parameter)
{
    rtu_modbus_device_t dev = (rtu_modbus_device_t)parameter;
    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    modbus_p2p_master_info_t *p2p_info = &g_modbus_p2p_master_info;
    uint8_t *raw_req = p2p_info->raw_req;
    int raw_req_len = p2p_info->raw_req_len;
    int info_len = sizeof(modbus_rt_file_info_t);
    int step = 0;
    while (1)
    {
        raw_req_len = 2;
#if MODBUS_P2P_SEND_ENABLE
#if (!MODBUS_P2P_RECV_ENABLE)
        if (MODBUS_READ == p2p_info->dir)
        {
            data->ret = -MODBUS_RT_EINVAL;
            modbus_rt_sem_post(&(p2p_info->sem));
            return;
        }
#endif
        if (MODBUS_WRITE == p2p_info->dir)
        {
            switch (step)
            {
            case 0:
            {
                modbus_rt_mutex_lock(&data->cmd_mutex);
                data->ret = -MODBUS_RT_ERROR;
                data->slave_addr = raw_req[0];

                raw_req[raw_req_len++] = ((uint16_t)TRANS_FILE_CMD_START >> 8);
                raw_req[raw_req_len++] = ((uint16_t)TRANS_FILE_CMD_START & 0xFF);
                int nb = sizeof(modbus_rt_file_info_t);
                raw_req[raw_req_len++] = nb >> 8;
                raw_req[raw_req_len++] = nb & 0xFF;
                memcpy(raw_req + raw_req_len, &(p2p_info->file_info), info_len);
                raw_req_len += info_len;
                p2p_info->raw_req_len = raw_req_len;
                data->function = raw_req[1];
                modbus_rt_mutex_unlock(&data->cmd_mutex);
                // 等待执行完毕
                // modbus_rt_sem_wait(&(data->completion));
                if (MODBUS_RT_EOK != data->ret)
                {
                    modbus_rt_sem_post(&(p2p_info->sem));
                    return;
                }
                step = 1;
            }
            break;
            case 1:
            {
                modbus_rt_mutex_lock(&data->cmd_mutex);
                data->ret = -MODBUS_RT_ERROR;
                data->slave_addr = raw_req[0];

                raw_req[raw_req_len++] = ((uint16_t)TRANS_FILE_CMD_DATA >> 8);
                raw_req[raw_req_len++] = ((uint16_t)TRANS_FILE_CMD_DATA & 0xFF);
                int nb_pos = raw_req_len;
                raw_req_len += 3;
                int recv_bytes = modbus_rt_file_read_file(p2p_info->fd, raw_req + raw_req_len, P2P_SLAVE_BUF_LEN);
                raw_req_len += recv_bytes;
                p2p_info->write_file_size += recv_bytes;
                int nb = recv_bytes + 1;
                raw_req[nb_pos] = nb >> 8;
                raw_req[nb_pos + 1] = nb & 0xFF;
                if (P2P_SLAVE_BUF_LEN > recv_bytes)
                {
                    raw_req[nb_pos + 2] = TRANS_FILE_FLAG_END;
                    step = 2;
                }
                else
                {
                    raw_req[nb_pos + 2] = TRANS_FILE_FLAG_NOT_END;
                }
                p2p_info->raw_req_len = raw_req_len;
                data->function = raw_req[1];
                modbus_rt_mutex_unlock(&data->cmd_mutex);
                // 等待执行完毕
                // modbus_rt_sem_wait(&(data->completion));
                if (MODBUS_RT_EOK != data->ret)
                {
                    modbus_rt_sem_post(&(p2p_info->sem));
                    return;
                }
            }
            break;
            default:
            {
                modbus_rt_sem_post(&(p2p_info->sem));
                return;
            }
            break;
            }
        }
#endif
#if MODBUS_P2P_RECV_ENABLE
#if (!MODBUS_P2P_SEND_ENABLE)
        if (MODBUS_WRITE == p2p_info->dir)
        {
            data->ret = -MODBUS_RT_EINVAL;
            modbus_rt_sem_post(&(p2p_info->sem));
            return;
        }
#endif
        if (MODBUS_READ == p2p_info->dir)
        {
            switch (step)
            {
            case 0:
            {
                modbus_rt_mutex_lock(&data->cmd_mutex);
                data->ret = -MODBUS_RT_ERROR;
                data->slave_addr = raw_req[0];

                raw_req[raw_req_len++] = ((uint16_t)TRANS_FILE_CMD_START >> 8);
                raw_req[raw_req_len++] = ((uint16_t)TRANS_FILE_CMD_START & 0xFF);
                int nb = sizeof(modbus_rt_file_info_t);
                raw_req[raw_req_len++] = nb >> 8;
                raw_req[raw_req_len++] = nb & 0xFF;
                memcpy(raw_req + raw_req_len, &(p2p_info->file_info), info_len);
                raw_req_len += info_len;
                p2p_info->raw_req_len = raw_req_len;
                data->function = raw_req[1];
                modbus_rt_mutex_unlock(&data->cmd_mutex);
                // 等待执行完毕
                // modbus_rt_sem_wait(&(data->completion));
                if (MODBUS_RT_EOK != data->ret)
                {
                    modbus_rt_sem_post(&(p2p_info->sem));
                    return;
                }

                uint16_t cmd_recv = ((uint16_t)(raw_req[2] << 8)) + raw_req[3];
                uint16_t len_recv = ((uint16_t)(raw_req[4] << 8)) + raw_req[5];
                if ((TRANS_FILE_CMD_START != cmd_recv) || (nb != len_recv))
                {
                    data->ret = -MODBUS_RT_ERROR;
                    modbus_rt_sem_post(&(p2p_info->sem));
                    return;
                }
                p2p_info->fd = modbus_rt_file_wb_open(p2p_info->file_name);
                if (0 >= p2p_info->fd)
                {
                    data->ret = -MODBUS_RT_ERROR;
                    modbus_rt_sem_post(&(p2p_info->sem));
                    return;
                }
                memcpy(&(p2p_info->file_info), &(raw_req[6]), len_recv);
                p2p_info->read_file_serial = 0;
                step = 1;
            }
            break;
            case 1:
            {
                modbus_rt_mutex_lock(&data->cmd_mutex);
                data->ret = -MODBUS_RT_ERROR;
                data->slave_addr = raw_req[0];

                p2p_info->read_file_serial++;
                raw_req[raw_req_len++] = ((uint16_t)TRANS_FILE_CMD_DATA >> 8);
                raw_req[raw_req_len++] = ((uint16_t)TRANS_FILE_CMD_DATA & 0xFF);
                int len = 0x0002;
                raw_req[raw_req_len++] = len >> 8;
                raw_req[raw_req_len++] = len & 0xFF;
                raw_req[raw_req_len++] = (p2p_info->read_file_serial) >> 8;
                raw_req[raw_req_len++] = (p2p_info->read_file_serial) & 0xFF;

                p2p_info->raw_req_len = raw_req_len;
                data->function = raw_req[1];
                modbus_rt_mutex_unlock(&data->cmd_mutex);
                // 等待执行完毕
                // modbus_rt_sem_wait(&(data->completion));
                if (MODBUS_RT_EOK != data->ret)
                {
                    modbus_rt_sem_post(&(p2p_info->sem));
                    return;
                }
                uint16_t cmd_recv = ((uint16_t)(raw_req[2] << 8)) + raw_req[3];
                uint16_t len_recv = ((uint16_t)(raw_req[4] << 8)) + raw_req[5];
                if ((TRANS_FILE_CMD_DATA != cmd_recv) || (0 >= p2p_info->fd))
                {
                    data->ret = -MODBUS_RT_ERROR;
                    modbus_rt_sem_post(&(p2p_info->sem));
                    return;
                }
                int flag = raw_req[6];
                int file_len = len_recv - 1;
                data->ret = modbus_rt_file_write_file(p2p_info->fd, &(raw_req[7]), file_len);
                if (0 >= data->ret)
                {
                    data->ret = -MODBUS_RT_ERROR;
                    modbus_rt_sem_post(&(p2p_info->sem));
                    return;
                }
                else
                {
                    data->ret = MODBUS_RT_EOK;
                }
                p2p_info->write_file_size += file_len;
                if (0 == flag)
                {
                    step = 2;
                }
            }
            break;
            default:
            {
                modbus_rt_sem_post(&(p2p_info->sem));
                return;
            }
            break;
            }
        }
#endif
    }
}

/**
 * @brief   modbus_rtu_excuse_file:  modbus rtu master的文件上传和下载函数
 *          该函数为阻塞函数，执行完成或者超时后返回(采用信号量机制，不影响其他线程运行)。
 *
 * @param   dev:                rtu_modbus_device_t 设备
 * @param   slave:              modbus slave的地址
 * @param   dir:                MODBUS_WRITE（传输文件到slave设备）， MODBUS_READ（从slave设备中读取文件）；
 * @param   file_dev:           slave设备中的文件名称（绝对路径）
 * @param   file_master:        master设备中的文件名称（绝对路径）
 * @return  int:                MODBUS_RT_EOK：成功，其他：失败
 *
 */
int modbus_rtu_excuse_file(rtu_modbus_device_t dev, int slave, modbus_excuse_dir_t dir, char *file_dev, char *file_master)
{
    int ret = 0;
    if ((NULL == dev) || (NULL == dev->data) || ((MODBUS_READ != dir) && (MODBUS_WRITE != dir)) ||
        (1 != dev->status))
    {
        return -MODBUS_RT_EINVAL;
    }
    rtu_master_data_t data = (rtu_master_data_t)dev->data;
#if MODBUS_P2P_SEND_ENABLE
#if (!MODBUS_P2P_RECV_ENABLE)
    if (MODBUS_READ == dir)
    {
        return -MODBUS_RT_EINVAL;
    }
#endif
    if (MODBUS_WRITE == dir)
    {
        modbus_p2p_master_info_t *p2p_info = &g_modbus_p2p_master_info;
        if (0 < p2p_info->fd)
        {
            modbus_rt_file_close(p2p_info->fd);
            p2p_info->fd = 0;
        }

        p2p_info->dir = MODBUS_WRITE;
        p2p_info->fd = modbus_rt_file_get_info(file_dev, file_master, &(p2p_info->file_info));
        if (0 >= p2p_info->fd)
        {
            return -MODBUS_RT_EIO;
        }
        p2p_info->raw_req[0] = slave;
        p2p_info->raw_req[1] = AGILE_MODBUS_FC_TRANS_FILE;

        p2p_info->write_file_size = 0;
        ret = modbus_rt_sem_init(&(p2p_info->sem));
        if (MODBUS_RT_EOK != ret)
        {
            modbus_rt_file_close(p2p_info->fd);
            p2p_info->fd = 0;
            return -MODBUS_RT_ENOMEM;
        }
        char str_name[MODBUS_RT_NAME_MAX] = {0};
        sprintf(str_name, "s%d", p2p_info->fd);
        p2p_info->thread = modbus_rt_thread_init(str_name, modbus_rtu_master_file_entry,
                                                 dev, MODBUS_THREAD_STACK_SIZE, MODBUS_THREAD_PRIO, 10);
        if (NULL != p2p_info->thread)
        {
            modbus_rt_thread_startup(p2p_info->thread);
        }
        else
        {
            modbus_rt_file_close(p2p_info->fd);
            p2p_info->fd = 0;
            modbus_rt_sem_destroy(&(p2p_info->sem));
            return -MODBUS_RT_EINVAL;
        }
        // modbus_rt_sem_wait(&(p2p_info->sem));

        if (0 < p2p_info->fd)
        {
            modbus_rt_file_close(p2p_info->fd);
            p2p_info->fd = 0;
        }
        modbus_rt_sem_destroy(&(p2p_info->sem));
        modbus_rt_thread_destroy(p2p_info->thread);
        if (MODBUS_RT_EOK != data->ret)
        {
            return data->ret;
        }
        return MODBUS_RT_EOK;
    }
#endif
#if MODBUS_P2P_RECV_ENABLE
#if (!MODBUS_P2P_SEND_ENABLE)
    if (MODBUS_WRITE == dir)
    {
        return -MODBUS_RT_EINVAL;
    }
#endif
    if (MODBUS_READ == dir)
    {
        modbus_p2p_master_info_t *p2p_info = &g_modbus_p2p_master_info;
        if (0 < p2p_info->fd)
        {
            modbus_rt_file_close(p2p_info->fd);
            p2p_info->fd = 0;
        }
        if (NULL == file_dev)
        {
            return -MODBUS_RT_EINVAL;
        }
        p2p_info->dir = MODBUS_READ;
        int len_file = strlen(file_dev);
        memcpy(p2p_info->file_info.file_name, file_dev, len_file);
        p2p_info->file_info.file_name[len_file] = 0;
        if (NULL != file_master)
        {
            len_file = strlen(file_master);
            memcpy(p2p_info->file_name, file_master, len_file);
            p2p_info->file_name[len_file] = 0;
        }
        else
        {
            memcpy(p2p_info->file_name, file_dev, len_file);
            p2p_info->file_name[len_file] = 0;
        }

        p2p_info->raw_req[0] = slave;
        p2p_info->raw_req[1] = AGILE_MODBUS_FC_READ_FILE;

        p2p_info->write_file_size = 0;
        ret = modbus_rt_sem_init(&(p2p_info->sem));
        if (MODBUS_RT_EOK != ret)
        {
            p2p_info->fd = 0;
            return -MODBUS_RT_ENOMEM;
        }
        char str_name[MODBUS_RT_NAME_MAX] = {0};
        sprintf(str_name, "r%d", p2p_info->fd);
        p2p_info->thread = modbus_rt_thread_init(str_name, modbus_rtu_master_file_entry,
                                                 dev, MODBUS_THREAD_STACK_SIZE, MODBUS_THREAD_PRIO, 10);
        if (NULL != p2p_info->thread)
        {
            modbus_rt_thread_startup(p2p_info->thread);
        }
        else
        {
            modbus_rt_sem_destroy(&(p2p_info->sem));
            return -MODBUS_RT_EINVAL;
        }
        // modbus_rt_sem_wait(&(p2p_info->sem));
        if (0 < p2p_info->fd)
        {
            modbus_rt_file_close(p2p_info->fd);
            p2p_info->fd = 0;
        }
        modbus_rt_sem_destroy(&(p2p_info->sem));
        modbus_rt_thread_destroy(p2p_info->thread);
        if (MODBUS_RT_EOK != data->ret)
        {
            return data->ret;
        }
        return MODBUS_RT_EOK;
    }
#endif
    return MODBUS_RT_EOK;
}
#endif

#endif
#if 0
int modbus_rtu_master_send_cmd(rtu_modbus_device_t dev, modbus_cmd_t *cmd, int timeout)
{
    rtu_master_data_t data = NULL;
    if ((NULL == dev) || (NULL == dev->data) || (1 != dev->status) || (NULL == cmd))
    {
        return -MODBUS_RT_EINVAL;
    }

    data = (rtu_master_data_t)dev->data;

    if (modbus_rt_sem_init(&cmd->completion_sem) != MODBUS_RT_EOK)
    {
        return -MODBUS_RT_ENOMEM;
    }

    modbus_rt_mutex_lock(&data->cmd_mutex);

    cmd->status = MODBUS_CMD_STATE_WAITING;
    printf("[MODBUS DEBUG] >>> 即将添加命令到优先级队列 (prio=%d)...\n", cmd->list_node.prio);

    plist_add(&cmd->list_node, &data->cmd_queue);

    printf("[MODBUS DEBUG]     命令已成功添加到队列.\n");

    /*
     * 每次添加新命令时都必须释放信号量。
     * 这可以确保如果多个命令被快速连续地添加到队列中，
     * 工作线程能够连续处理它们，而不会在处理完一个后就错误地阻塞。
     * 信号量的计数值将确保工作线程在队列清空之前不会休眠。
     */
    modbus_rt_sem_post(&data->cmd_sem);

    printf("[MODBUS DEBUG]     cmd_sem 信号量已释放.\n");

    modbus_rt_mutex_unlock(&data->cmd_mutex);
#if 0
    if (modbus_rt_sem_wait(&cmd->completion_sem, timeout) != MODBUS_RT_EOK)
    {
        int ret = -MODBUS_RT_ETIMEOUT_APP;
        /* 超时发生，我们必须检查命令的当前状态以决定如何安全地继续。*/
        modbus_rt_mutex_lock(&data->cmd_mutex);
        /* 情况1：命令仍在等待队列中。
         * 我们可以安全地将其移除并标记为取消。*/
        if (cmd->status == MODBUS_CMD_STATE_WAITING)
        {
            plist_del(&cmd->list_node, &data->cmd_queue);
            cmd->status = MODBUS_CMD_STATE_CANCELLED;
        }
        /* 情况2：命令已经被工作线程认领并正在运行。
         * 我们不能移除它，只能等待它完成。
         * 尽管对调用者来说是超时，但我们必须等待信号量以避免内存泄漏。*/
        else if (cmd->status == MODBUS_CMD_STATE_RUNNING)
        {
            /* 必须在解锁后等待，以避免死锁 */
            modbus_rt_mutex_unlock(&data->cmd_mutex);
            modbus_rt_sem_wait(&cmd->completion_sem, MODBUS_RT_WAIT_FOREVER);
            modbus_rt_mutex_lock(&data->cmd_mutex);
        }
        /* 其他情况 (COMPLETE 或 CANCELLED) 理论上不应发生，但作为安全措施，
         * 我们假设命令已处理完毕。*/

        modbus_rt_mutex_unlock(&data->cmd_mutex);

        /* 仅在超时且命令未被执行时返回超时错误 */
        if (cmd->status == MODBUS_CMD_STATE_CANCELLED)
        {
             modbus_rt_sem_destroy(&cmd->completion_sem);
             return ret;
        }

        /* 对于其他情况（例如，它在我们标记为取消之前就运行了），
         * 我们已经等待了完成信号，所以现在可以安全地销毁信号量。*/
    }
    modbus_rt_sem_destroy(&cmd->completion_sem);
#else
    /* 等待指令完成，带有超时机制 */
    if (modbus_rt_sem_wait(&cmd->completion_sem, timeout) != MODBUS_RT_EOK)
    {
        /* 等待超时。现在需要安全地处理这种情况。*/
        int final_result = -MODBUS_RT_ETIMEOUT_APP; // 默认返回应用层超时错误

        modbus_rt_mutex_lock(&data->cmd_mutex);

        if (cmd->status == MODBUS_CMD_STATE_WAITING)
        {
            /* 情况 1: 指令从未被工作线程处理。
             * 我们可以安全地将它从队列中移除并取消。*/
            plist_del(&cmd->list_node, &data->cmd_queue);
            cmd->status = MODBUS_CMD_STATE_CANCELLED;
            modbus_rt_mutex_unlock(&data->cmd_mutex);

            /* 清理并返回超时错误 */
            modbus_rt_sem_destroy(&cmd->completion_sem);
            return final_result;
        }

        /*
         * 情况 2: 指令正在运行 (RUNNING) 或刚刚完成 (COMPLETE)。
         * 我们必须解锁互斥锁，以允许工作线程可以完成它的操作（特别是 post 信号量）。
         */
        modbus_rt_mutex_unlock(&data->cmd_mutex);

        /*
         * 关键：我们必须无条件地等待指令最终完成，以防止调用者释放正在被使用的内存。
         * 如果指令已经完成，这个等待会立即返回。
         * 如果指令正在运行，我们会等待它结束。
         */
        modbus_rt_sem_wait(&cmd->completion_sem, MODBUS_RT_WAIT_FOREVER);

        /* 指令现在肯定已经完成，我们可以安全地返回它的真实结果 */
    }

    /* 指令正常完成或"延迟"完成后，销毁信号量并返回结果 */
    modbus_rt_sem_destroy(&cmd->completion_sem);
#endif


    return cmd->result;
}
#endif
#if 0
int modbus_rtu_master_send_cmd(rtu_modbus_device_t dev, modbus_cmd_t *cmd, int timeout)
{
    rtu_master_data_t data = NULL;
    if ((NULL == dev) || (NULL == dev->data) || (1 != dev->status) || (NULL == cmd))
    {
        return -MODBUS_RT_EINVAL;
    }

    data = (rtu_master_data_t)dev->data;

    if (modbus_rt_sem_init(&cmd->completion_sem) != MODBUS_RT_EOK)
    {
        return -MODBUS_RT_ENOMEM;
    }

    if (modbus_rt_sem_init(&cmd->caller_ready_sem) != MODBUS_RT_EOK)
    {
        modbus_rt_sem_destroy(&cmd->completion_sem);
        return -MODBUS_RT_ENOMEM;
    }
    modbus_rt_mutex_lock(&data->cmd_mutex);

    cmd->status = MODBUS_CMD_STATE_WAITING;
     printf("[MODBUS DEBUG] >>> 即将添加命令到优先级队列 (prio=%d)...\n", cmd->list_node.prio);
    plist_add(&cmd->list_node, &data->cmd_queue);
     printf("[MODBUS DEBUG]     命令已成功添加到队列.\n");

    modbus_rt_sem_post(&data->cmd_sem);
     printf("[MODBUS DEBUG]     cmd_sem 信号量已释放.\n");

    modbus_rt_mutex_unlock(&data->cmd_mutex);

    if (timeout == 0)
    {
        // 异步调用: 调用者不等待，将状态设为DETACHED，所有权完全转移给工作线程
        modbus_rt_mutex_lock(&data->cmd_mutex);
        cmd->status = MODBUS_CMD_STATE_DETACHED;
        modbus_rt_mutex_unlock(&data->cmd_mutex);
        // 返回特定代码，告知应用层"已成功入队，异步处理"
        return -MODBUS_RT_ETIMEOUT_APP;
    }


    /* 关键改动 1：先发一个"我准备好了"的信号 */
    modbus_rt_sem_post(&cmd->caller_ready_sem);

    /* 等待指令完成，带有超时机制 */
    printf("[MODBUS DEBUG]     completion_sem 信号量等待.\n");
    if (modbus_rt_sem_wait(&cmd->completion_sem, timeout) != MODBUS_RT_EOK)
    {
        /* 超时发生，执行"脱离"操作，所有权转移给工作者线程 */
        modbus_rt_mutex_lock(&data->cmd_mutex);

        // 无论命令当前是 WAITING 还是 RUNNING，都将其标记为 DETACHED
        // 工作者线程或destroy函数会看到这个状态并负责清理
        // 关键：在超时后，再次检查命令是否恰好已完成，防止竞态
        if (cmd->status != MODBUS_CMD_STATE_COMPLETE)
        {
            cmd->status = MODBUS_CMD_STATE_DETACHED;
        }

        modbus_rt_mutex_unlock(&data->cmd_mutex);

        /* 立即返回应用层超时错误。
         * **不**销毁信号量，**不**释放 cmd 内存。
         * 上层应用(master_request)必须根据此返回值，避免释放cmd。
         */
        return -MODBUS_RT_ETIMEOUT_APP;
    }

    /* 指令正常完成，销毁信号量并返回结果 */
    modbus_rt_sem_destroy(&cmd->completion_sem);
    modbus_rt_sem_destroy(&cmd->caller_ready_sem);
    return cmd->result;
}
#endif
#if LAST_VERSION
int modbus_rtu_master_send_cmd(rtu_modbus_device_t dev, modbus_cmd_t *cmd, int timeout)
{
    rtu_master_data_t data = NULL;
    if ((NULL == dev) || (NULL == dev->data) || (NULL == cmd))
    {
        return -MODBUS_RT_EINVAL;
    }

    data = (rtu_master_data_t)dev->data;

    //    if (modbus_rt_sem_init(&cmd->completion_sem) != MODBUS_RT_EOK)
    //    {
    //        return -MODBUS_RT_ENOMEM;
    //    }
    //    if (modbus_rt_sem_init(&cmd->caller_ready_sem) != MODBUS_RT_EOK)
    //    {
    //        modbus_rt_sem_destroy(&cmd->completion_sem);
    //        return -MODBUS_RT_ENOMEM;
    //    }

    /* ---------- 仅同步调用(timeout>0)才需要信号量 ---------- */
    bool is_detached = (timeout == 0);

    if (!is_detached)
    {
        if (modbus_rt_sem_init(&cmd->completion_sem) != MODBUS_RT_EOK)
            return -MODBUS_RT_ENOMEM;

        if (modbus_rt_sem_init(&cmd->caller_ready_sem) != MODBUS_RT_EOK)
        {
            modbus_rt_sem_destroy(&cmd->completion_sem);
            return -MODBUS_RT_ENOMEM;
        }
    }
    else
    {
        /* 异步包：不给 OS 申请 semaphore，指针置 NULL */
        cmd->completion_sem.sem = NULL;
        cmd->caller_ready_sem.sem = NULL;
    }

    // --- 关键修复：在加锁保护下，检查设备状态并添加命令 ---
    modbus_rt_mutex_lock(&dev->state_mutex);
    modbus_rt_mutex_lock(&data->cmd_mutex);

    // 检查设备是否已开始关闭
    if (dev->dev_state != MODBUS_DEV_STATE_ACTIVE)
    {
        // 如果设备已关闭，则拒绝新命令，解锁并清理资源后返回
        modbus_rt_mutex_unlock(&data->cmd_mutex);
        modbus_rt_mutex_unlock(&dev->state_mutex);

        modbus_rt_sem_destroy(&cmd->completion_sem);
        modbus_rt_sem_destroy(&cmd->caller_ready_sem);
        return -MODBUS_RT_EPERM; // 返回一个明确的“已关闭”错误码
    }

    // 设置命令状态并添加到队列
    //    bool is_detached = (timeout == 0);
    cmd->status = is_detached ? MODBUS_CMD_STATE_DETACHED : MODBUS_CMD_STATE_WAITING;
    //    printf("[MODBUS DEBUG] >>> 即将添加命令到优先级队列 (prio=%d)...\n", cmd->list_node.prio);
    plist_add(&cmd->list_node, &data->cmd_queue);
    //    printf("[MODBUS DEBUG]     命令已成功添加到队列.\n");
    // 解锁
    modbus_rt_mutex_unlock(&data->cmd_mutex);
    modbus_rt_mutex_unlock(&dev->state_mutex);

    // 唤醒工作线程
    //    modbus_rt_sem_post(&data->cmd_sem);
    //    modbus_rt_mq_sem_post(&data->cmd_mq_sem); // 只有成功塞入 token 的命令才可能滞留在链表；
    /* ---------- 唤醒工作线程 ---------- */
    if (modbus_rt_mq_sem_post(&data->cmd_mq_sem) != MODBUS_RT_EOK) /* <—— 新增判错 */
    {
        /* 回滚：token 没送进去，说明 backlog 已到上限 */
        modbus_rt_mutex_lock(&data->cmd_mutex);
        plist_del(&cmd->list_node, &data->cmd_queue);
        modbus_rt_mutex_unlock(&data->cmd_mutex);

        modbus_rt_sem_destroy(&cmd->completion_sem);
        modbus_rt_sem_destroy(&cmd->caller_ready_sem);
        modbus_rt_free(cmd);
#if LC_LOG_STAT_EN
        lc_stat_inc_cmd_free();
#endif
        return -MODBUS_RT_EBUSY; /* 直接告诉调用者：队列已满 */
    }

    //    printf("[MODBUS DEBUG]     cmd_mq_sem 信号量已释放.\n");
    // --- 后续的同步/异步处理逻辑保持不变 ---
    if (is_detached)
    {
        return -MODBUS_RT_ETIMEOUT_APP;
    }

    modbus_rt_sem_post(&cmd->caller_ready_sem);
    //    printf("[MODBUS DEBUG]     completion_sem 信号量等待.\n");
    if (modbus_rt_sem_wait(&cmd->completion_sem, timeout) != MODBUS_RT_EOK)
    {
        modbus_rt_mutex_lock(&data->cmd_mutex);
        //        if (cmd->status != MODBUS_CMD_STATE_COMPLETE)
        {
            cmd->status = MODBUS_CMD_STATE_DETACHED;
        }
        modbus_rt_mutex_unlock(&data->cmd_mutex);
        return -MODBUS_RT_ETIMEOUT_APP;
    }

    modbus_rt_sem_destroy(&cmd->completion_sem);
    modbus_rt_sem_destroy(&cmd->caller_ready_sem);

    return cmd->result;
}
#else
int modbus_rtu_master_send_cmd(rtu_modbus_device_t dev,
                               modbus_cmd_t *cmd,
                               int timeout)
{
    if (!dev || !cmd || dev->mode != MODBUS_MASTER)
        return -MODBUS_RT_EINVAL;

    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    bool async = (timeout == 0);

    /* ---------- 初始化同步资源 ---------- */
    if (modbus_rt_sem_init(&cmd->completion_sem) != MODBUS_RT_EOK)
    {
        return -MODBUS_RT_ENOMEM;
    }
    if (modbus_rt_sem_init(&cmd->caller_ready_sem) != MODBUS_RT_EOK)
    {
        modbus_rt_sem_destroy(&cmd->completion_sem);
        return -MODBUS_RT_ENOMEM;
    }

    cmd->refcnt = async ? 1 : 2;
    cmd->status = async ? MODBUS_CMD_STATE_DETACHED
                        : MODBUS_CMD_STATE_WAITING;
    cmd->result = -MODBUS_RT_ERROR;

    /* ---------- 入队 ---------- */
    modbus_rt_mutex_lock(&dev->state_mutex);
    if (dev->dev_state != MODBUS_DEV_STATE_ACTIVE)
    {
        modbus_rt_mutex_unlock(&dev->state_mutex);
        cmd_cleanup(cmd);
        return -MODBUS_RT_EPERM;
    }

    modbus_rt_mutex_lock(&data->cmd_mutex);
    plist_add(&cmd->list_node, &data->cmd_queue);
    modbus_rt_mutex_unlock(&data->cmd_mutex);
    modbus_rt_mutex_unlock(&dev->state_mutex);

    /* ---------- 令牌投递，失败即回滚 ---------- */
    if (modbus_rt_mq_sem_post(&data->cmd_mq_sem) != MODBUS_RT_EOK)
    {
        modbus_rt_mutex_lock(&data->cmd_mutex);
        plist_del(&cmd->list_node, &data->cmd_queue);
        modbus_rt_mutex_unlock(&data->cmd_mutex);
        cmd_cleanup(cmd);
        return -MODBUS_RT_EBUSY;
    }

    /* ---------- 告知线程：调用者状态已确定 ---------- */
    modbus_rt_sem_post(&cmd->caller_ready_sem);

    /* ---------- 异步直接返回 ---------- */
    if (async)
    {
        return -MODBUS_RT_ETIMEOUT_APP;
    }

    /* ---------- 同步等待 ---------- */
    if (modbus_rt_sem_wait(&cmd->completion_sem, timeout) != MODBUS_RT_EOK)
    { /* 超时：放弃调用者引用，改为 DETACHED */
        cmd->status = MODBUS_CMD_STATE_DETACHED;
        if (ref_dec_and_fetch(&cmd->refcnt, 1) == 0)
        {
            cmd_cleanup(cmd);
        }
        return -MODBUS_RT_ETIMEOUT_APP;
    }

    /* ---------- 正常返回 ---------- */
    int rc = cmd->result;
    if (ref_dec_and_fetch(&cmd->refcnt, 1) == 0)
    {
        cmd_cleanup(cmd);
    }
    return rc;
}
#endif

/**
 * @brief   安全地清空（冲洗）Modbus Master的命令队列
 * @details 此函数会遍历队列中所有待处理的命令，将其标记为已取消，
 *          并唤醒正在等待这些命令完成的调用者线程。
 *          这是一个线程安全的操作，它通过获取 state_mutex 来确保
 *          不会与 modbus_rtu_destroy() 函数发生竞态条件。
 * @param   dev                 rtu_modbus_device_t设备
 * @return  int                 返回被取消的命令数量，或在设备状态不正确时返回错误码
 */
#if 0
int modbus_rtu_master_flush_queue(rtu_modbus_device_t dev)
{
    rtu_master_data_t data = NULL;
    int cancelled_count = 0;

    if ((NULL == dev) || (NULL == dev->data) || (MODBUS_MASTER != dev->mode))
    {
        return -MODBUS_RT_EINVAL;
    }

    data = (rtu_master_data_t)dev->data;

    // 步骤 1: 获取生命周期锁，这是防止与 destroy() 冲突的关键
    modbus_rt_mutex_lock(&dev->state_mutex);

    // 步骤 2: 检查设备状态。如果设备不在活动状态，说明正在关闭或已关闭，此时不允许清空队列
    if (dev->dev_state != MODBUS_DEV_STATE_ACTIVE)
    {
        modbus_rt_mutex_unlock(&dev->state_mutex);
        // 返回一个明确的错误码，表示操作不允许
        return -MODBUS_RT_EPERM; // (建议在 modbus_rt.h 中定义 EPERM, e.g., -9)
    }

    // 到这里，我们持有 state_mutex 并且确认设备是 ACTIVE 的。
    // 这意味着 destroy() 函数不可能在此时进入它的核心清理逻辑。

    // 步骤 3: 锁住命令队列以进行安全操作
    modbus_rt_mutex_lock(&data->cmd_mutex);

    modbus_cmd_t *cmd, *n;
    list_for_each_entry_safe(cmd, n, &data->cmd_queue.node_list, list_node.node_list)
    {
        if (cmd->status == MODBUS_CMD_STATE_WAITING)
        {
            plist_del(&cmd->list_node, &data->cmd_queue);

            cmd->result = -MODBUS_RT_ECANCEL; // (建议在 modbus_rt.h 中定义 ECANCEL, e.g., -8)
            cmd->status = MODBUS_CMD_STATE_COMPLETE;  // 标记为已完成（以取消告终）

            modbus_rt_sem_post(&cmd->completion_sem);

            cancelled_count++;
        }
    }

    // 步骤 4: 按照与加锁相反的顺序，释放锁
    modbus_rt_mutex_unlock(&data->cmd_mutex);
    modbus_rt_mutex_unlock(&dev->state_mutex);

    if (cancelled_count > 0)
    {
        printf("[MODBUS INFO] 从队列中冲刷并取消了 %d 条命令。\n", cancelled_count);
    }

    return cancelled_count;
}
#endif
#if LAST_VERSION
int modbus_rtu_master_flush_queue(rtu_modbus_device_t dev)
{
    rtu_master_data_t data = NULL;
    int cancelled_count = 0;

    if ((NULL == dev) || (NULL == dev->data) || (MODBUS_MASTER != dev->mode))
    {
        return -MODBUS_RT_EINVAL;
    }

    data = (rtu_master_data_t)dev->data;

    modbus_rt_mutex_lock(&dev->state_mutex);

    if (dev->dev_state != MODBUS_DEV_STATE_ACTIVE)
    {
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return -MODBUS_RT_EPERM;
    }

    modbus_rt_mutex_lock(&data->cmd_mutex);

    modbus_cmd_t *cmd, *n;
    list_for_each_entry_safe(cmd, n, &data->cmd_queue.node_list, list_node.node_list)
    {
        // 从队列中移除该命令
        plist_del(&cmd->list_node, &data->cmd_queue);

        if (cmd->status == MODBUS_CMD_STATE_DETACHED)
        {
            // 异步命令 (DETACHED): 没有等待者，flush函数必须负责清理所有资源。
            modbus_rt_sem_destroy(&cmd->completion_sem);
            modbus_rt_sem_destroy(&cmd->caller_ready_sem);
            printf("[FLUSH]  Attempting to free cmd @ %p\n", cmd); // 打印地址
            modbus_rt_free(cmd);
#if LC_LOG_STAT_EN
            lc_stat_inc_cmd_free();
#endif
        }
        else // 状态必定是 WAITING
        {
            // 同步命令 (WAITING): 有一个等待者，必须唤醒它并让它自己清理。
            cmd->result = -MODBUS_RT_ECANCEL;
            cmd->status = MODBUS_CMD_STATE_COMPLETE;
            modbus_rt_sem_post(&cmd->completion_sem);
        }

        cancelled_count++;
    }

    modbus_rt_mutex_unlock(&data->cmd_mutex);
    modbus_rt_mutex_unlock(&dev->state_mutex);

    if (cancelled_count > 0)
    {
        printf("[MODBUS INFO] 取消的命令数量：%d\n", cancelled_count);
#if LC_LOG_STAT_EN
        lc_stat_inc_flush(cancelled_count);
#endif
    }

    return cancelled_count;
}
#else
int modbus_rtu_master_flush_queue(rtu_modbus_device_t dev)
{
    if (!dev || dev->mode != MODBUS_MASTER)
        return -MODBUS_RT_EINVAL;

    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    int cancelled = 0;

    modbus_rt_mutex_lock(&dev->state_mutex);
    if (dev->dev_state != MODBUS_DEV_STATE_ACTIVE)
    {
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return -MODBUS_RT_EPERM;
    }

    modbus_rt_mutex_lock(&data->cmd_mutex);
    modbus_cmd_t *cmd, *tmp;
    list_for_each_entry_safe(cmd, tmp,
                             &data->cmd_queue.node_list,
                             list_node.node_list)
    {
        plist_del(&cmd->list_node, &data->cmd_queue);
        cancelled++;

        cmd->result = -MODBUS_RT_ECANCEL;

        if (cmd->status == MODBUS_CMD_STATE_WAITING) /* 同步 */
        {
            cmd->status = MODBUS_CMD_STATE_COMPLETE;
            modbus_rt_sem_post(&cmd->caller_ready_sem); /* 1 号握手 */
            modbus_rt_sem_post(&cmd->completion_sem);   /* 2 号唤醒 */
        }
        /* DETACHED(异步) 不需要唤醒 */

        if (ref_dec_and_fetch(&cmd->refcnt, 1) == 0)
            cmd_cleanup(cmd);
    }
    modbus_rt_mutex_unlock(&data->cmd_mutex);
    modbus_rt_mutex_unlock(&dev->state_mutex);

#if LC_LOG_STAT_EN
    if (cancelled)
        lc_stat_inc_flush(cancelled);
#endif
    return cancelled;
}
#endif
#if MODBUS_RTU_SLAVE_ENABLE
int modbus_rtu_slave_poll(rtu_modbus_device_t dev)
{
    if (dev == NULL || dev->mode != MODBUS_SLAVE)
    {
        return -MODBUS_RT_EINVAL;
    }

    if (OVER_NONE == dev->over_type)
    {
        rtu_slave_data_t data = (rtu_slave_data_t)dev->data;
        int send_len;
        int read_len = modbus_rt_serial_receive(dev->serial, dev->ctx->read_buf, dev->ctx->read_bufsz, MODBUS_RTU_TIME_OUT, dev->byte_timeout);
        if (read_len <= 0)
        {
            return read_len; // 返回超时或错误
        }
#if MODBUS_ASCII_SLAVE_ENABLE
        if (dev->ascii_flag)
        {
            modbus_ascii2rtu(dev->ctx->read_buf, &read_len);
        }
#endif
        send_len = agile_modbus_slave_handle(dev->ctx, read_len, data->slave_strict, agile_modbus_slave_util_callback, &data->util, NULL);
        if (send_len > 0)
        {
#if MODBUS_ASCII_SLAVE_ENABLE
            if (dev->ascii_flag)
            {
                modbus_rtu2ascii(dev->ctx->send_buf, &send_len);
            }
#endif
            modbus_rt_serial_send(dev->serial, dev->ctx->send_buf, send_len);
        }
    }
    else if (OVER_NET == dev->over_type)
    {
        // 网络模式不支持轮询
        return -MODBUS_RT_EINVAL;
    }

    return MODBUS_RT_EOK;
}
#endif

#if MODBUS_RTU_MASTER_ENABLE
#if 0
int modbus_rtu_master_poll(rtu_modbus_device_t dev)
{
    if (dev == NULL || dev->mode != MODBUS_MASTER)
    {
        return -MODBUS_RT_EINVAL;
    }

    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    modbus_cmd_t *cmd = NULL;

    /* 在用户轮询模式下，非阻塞地检查是否有命令。
       在库线程模式下，信号量已由 entry 函数等待，此处会直接通过。*/

    /* 取出命令 */
    modbus_rt_mutex_lock(&data->cmd_mutex);
    if (!plist_head_empty(&data->cmd_queue))
    {
        cmd = list_entry(data->cmd_queue.node_list.next, modbus_cmd_t, list_node.node_list);

        /* 命令现在在 send_cmd 中被取消和移除。
         * poll 函数不再需要处理 CANCELLED 状态。*/

        // 认领命令并更新状态
        cmd->status = MODBUS_CMD_STATE_RUNNING;
        plist_del(&cmd->list_node, &data->cmd_queue);
    }
    modbus_rt_mutex_unlock(&data->cmd_mutex);

    if (cmd == NULL)
    {
        return MODBUS_RT_EOK; // 队列为空
    }

    /* 复制参数 */
    data->slave_addr = cmd->slave_addr;
    data->function = cmd->function;
    data->data_addr = cmd->w_addr;
    data->quantity = cmd->w_quantity;
    data->ptr_data = cmd->ptr_w_data;
    data->read_addr = cmd->r_addr;
    data->read_quantity = cmd->r_quantity;
    data->ptr_read_data = cmd->ptr_r_data;
    data->ret = -MODBUS_RT_ERROR;

    if (OVER_NONE == dev->over_type)
    {
        data->ret = modbus_rtu_master_excuse_run(dev);
    }
    else if (OVER_NET == dev->over_type)
    {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        // 如果是TCP模式且socket断开，尝试重连
        if ((SOCK_STREAM == dev->type) && (0 >= dev->sock))
        {
            dev->sock = modbus_rt_tcp_client_init(NULL, dev->port, data->saddr, data->sport);
            if (0 >= dev->sock)
            {
                cmd->result = -MODBUS_RT_REMOTE;
                modbus_rt_sem_post(&cmd->completion_sem); // 通知调用者
                return cmd->result;
            }
        }
        data->ret = modbus_rtu_master_excuse_run(dev);
#else
        data->ret = -MODBUS_RT_EINVAL;
#endif
    }

    cmd->result = data->ret;
    cmd->status = MODBUS_CMD_STATE_COMPLETE;

    // 如果注册了结果回调函数，则调用它
    if (data->result_handler)
    {
        data->result_handler(cmd, data->result_handler_data);
    }

    // 通知调用者执行完毕
    modbus_rt_sem_post(&cmd->completion_sem);

    return data->ret;
}
#endif
#if 0
int modbus_rtu_master_poll(rtu_modbus_device_t dev)
{
    if (dev == NULL || dev->mode != MODBUS_MASTER)
    {
        return -MODBUS_RT_EINVAL;
    }

    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    modbus_cmd_t *cmd = NULL;
    int result = MODBUS_RT_EOK;

    // 快速检查队列是否为空 - 避免不必要的锁操作
    if (plist_head_empty(&data->cmd_queue))
    {
        return MODBUS_RT_EOK;
    }

    // 检查设备状态
    modbus_rt_mutex_lock(&dev->state_mutex);
    if (dev->dev_state != MODBUS_DEV_STATE_ACTIVE)
    {
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return -MODBUS_RT_ERROR;
    }
    // 增加活动命令计数
    dev->active_cmd_count++;
    modbus_rt_mutex_unlock(&dev->state_mutex);

    /* 取出命令 */
    modbus_rt_mutex_lock(&data->cmd_mutex);
    if (!plist_head_empty(&data->cmd_queue))
    {
        cmd = list_entry(data->cmd_queue.node_list.next, modbus_cmd_t, list_node.node_list);

        // 认领命令并更新状态
        cmd->status = MODBUS_CMD_STATE_RUNNING;
        plist_del(&cmd->list_node, &data->cmd_queue);
    }
    modbus_rt_mutex_unlock(&data->cmd_mutex);

    if (cmd == NULL)
    {
        // 减少活动命令计数
        modbus_rt_mutex_lock(&dev->state_mutex);
        dev->active_cmd_count--;
        if (dev->dev_state == MODBUS_DEV_STATE_CLOSING && dev->active_cmd_count == 0)
        {
            modbus_rt_sem_post(&dev->all_cmd_done);
        }
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return MODBUS_RT_EOK; // 队列为空
    }

    /* 复制参数 */
    data->slave_addr = cmd->slave_addr;
    data->function = cmd->function;
    data->data_addr = cmd->w_addr;
    data->quantity = cmd->w_quantity;
    data->ptr_data = cmd->ptr_w_data;
    data->read_addr = cmd->r_addr;
    data->read_quantity = cmd->r_quantity;
    data->ptr_read_data = cmd->ptr_r_data;
    data->ret = -MODBUS_RT_ERROR;

    if (OVER_NONE == dev->over_type)
    {
        data->ret = modbus_rtu_master_excuse_run(dev);
    }
    else if (OVER_NET == dev->over_type)
    {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        // 如果是TCP模式且socket断开，尝试重连
        if ((SOCK_STREAM == dev->type) && (0 >= dev->sock))
        {
            dev->sock = modbus_rt_tcp_client_init(NULL, dev->port, data->saddr, data->sport);
            if (0 >= dev->sock)
            {
                cmd->result = -MODBUS_RT_REMOTE;
                modbus_rt_sem_post(&cmd->completion_sem); // 通知调用者

                // 减少活动命令计数
                modbus_rt_mutex_lock(&dev->state_mutex);
                dev->active_cmd_count--;
                if (dev->dev_state == MODBUS_DEV_STATE_CLOSING && dev->active_cmd_count == 0)
                {
                    modbus_rt_sem_post(&dev->all_cmd_done);
                }
                modbus_rt_mutex_unlock(&dev->state_mutex);

                return cmd->result;
            }
        }
        data->ret = modbus_rtu_master_excuse_run(dev);
#else
        data->ret = -MODBUS_RT_EINVAL;
#endif
    }

    cmd->result = data->ret;
    cmd->status = MODBUS_CMD_STATE_COMPLETE;

    // 如果注册了结果回调函数，则调用它
    if (data->result_handler)
    {
        data->result_handler(cmd, data->result_handler_data);
    }

    // 通知调用者执行完毕
    modbus_rt_sem_post(&cmd->completion_sem);

    // 减少活动命令计数
    modbus_rt_mutex_lock(&dev->state_mutex);
    dev->active_cmd_count--;
    if (dev->dev_state == MODBUS_DEV_STATE_CLOSING && dev->active_cmd_count == 0)
    {
        modbus_rt_sem_post(&dev->all_cmd_done);
    }
    modbus_rt_mutex_unlock(&dev->state_mutex);

    return data->ret;
}
#endif
#if 0
int modbus_rtu_master_poll(rtu_modbus_device_t dev)
{
    if (dev == NULL || dev->mode != MODBUS_MASTER)
    {
        return -MODBUS_RT_EINVAL;
    }

    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    modbus_cmd_t *cmd = NULL;
    int result = MODBUS_RT_EOK;

    // 快速检查队列是否为空 - 避免不必要的锁操作
    if (plist_head_empty(&data->cmd_queue))
    {
        return MODBUS_RT_EOK;
    }

    // 检查设备状态
    modbus_rt_mutex_lock(&dev->state_mutex);
    if (dev->dev_state != MODBUS_DEV_STATE_ACTIVE)
    {
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return -MODBUS_RT_ERROR;
    }
    // 增加活动命令计数
    dev->active_cmd_count++;
    modbus_rt_mutex_unlock(&dev->state_mutex);

    /* 取出命令 */
    modbus_rt_mutex_lock(&data->cmd_mutex);
    if (!plist_head_empty(&data->cmd_queue))
    {
        cmd = list_entry(data->cmd_queue.node_list.next, modbus_cmd_t, list_node.node_list);

        // 【第一道检查】检查命令是否在排队时就被调用者抛弃
        if (cmd->status == MODBUS_CMD_STATE_DETACHED)
        {
            plist_del(&cmd->list_node, &data->cmd_queue);
            modbus_rt_mutex_unlock(&data->cmd_mutex);

            // 作为所有权接管方，负责清理这个"孤儿"命令
            modbus_rt_sem_destroy(&cmd->completion_sem);
            modbus_rt_free(cmd);

            // 【关键修复】更新活动计数，否则 destroy 会死等
            modbus_rt_mutex_lock(&dev->state_mutex);
            dev->active_cmd_count--;
            if (dev->dev_state == MODBUS_DEV_STATE_CLOSING && dev->active_cmd_count == 0)
            {
                modbus_rt_sem_post(&dev->all_cmd_done);
            }
            modbus_rt_mutex_unlock(&dev->state_mutex);

            return -MODBUS_RT_ECANCEL; // 返回一个特殊值表示此任务被取消
        }

        // 认领命令并更新状态
        cmd->status = MODBUS_CMD_STATE_RUNNING;
        plist_del(&cmd->list_node, &data->cmd_queue);
    }
    modbus_rt_mutex_unlock(&data->cmd_mutex);

    if (cmd == NULL)
    {
        // 减少活动命令计数
        modbus_rt_mutex_lock(&dev->state_mutex);
        dev->active_cmd_count--;
        if (dev->dev_state == MODBUS_DEV_STATE_CLOSING && dev->active_cmd_count == 0)
        {
            modbus_rt_sem_post(&dev->all_cmd_done);
        }
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return MODBUS_RT_EOK; // 队列为空
    }

    /* 复制参数 */
    data->slave_addr = cmd->slave_addr;
    data->function = cmd->function;
    data->data_addr = cmd->w_addr;
    data->quantity = cmd->w_quantity;
    data->ptr_data = cmd->ptr_w_data;
    data->read_addr = cmd->r_addr;
    data->read_quantity = cmd->r_quantity;
    data->ptr_read_data = cmd->ptr_r_data;
    data->ret = -MODBUS_RT_ERROR;

    if (OVER_NONE == dev->over_type)
    {
        data->ret = modbus_rtu_master_excuse_run(dev);
    }
    else if (OVER_NET == dev->over_type)
    {
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        // 如果是TCP模式且socket断开，尝试重连
        if ((SOCK_STREAM == dev->type) && (0 >= dev->sock))
        {
            dev->sock = modbus_rt_tcp_client_init(NULL, dev->port, data->saddr, data->sport);
            if (0 >= dev->sock)
            {
                cmd->result = -MODBUS_RT_REMOTE;
                modbus_rt_sem_post(&cmd->completion_sem); // 通知调用者

                // 减少活动命令计数
                modbus_rt_mutex_lock(&dev->state_mutex);
                dev->active_cmd_count--;
                if (dev->dev_state == MODBUS_DEV_STATE_CLOSING && dev->active_cmd_count == 0)
                {
                    modbus_rt_sem_post(&dev->all_cmd_done);
                }
                modbus_rt_mutex_unlock(&dev->state_mutex);

                return cmd->result;
            }
        }
        data->ret = modbus_rtu_master_excuse_run(dev);
#else
        data->ret = -MODBUS_RT_EINVAL;
#endif
    }

    cmd->result = data->ret;

    // 【第二道检查】在通知调用者前，再次检查其是否已超时离开
    modbus_rt_mutex_lock(&data->cmd_mutex);
    if (cmd->status == MODBUS_CMD_STATE_DETACHED)
    {
        // 调用者在我们执行期间超时走掉了。
        // 我们（工作者线程）必须负责清理所有资源。
        modbus_rt_mutex_unlock(&data->cmd_mutex);
        modbus_rt_sem_destroy(&cmd->completion_sem);
        modbus_rt_sem_destroy(&cmd->caller_ready_sem);
        modbus_rt_free(cmd);

        // 减少活动命令计数
        modbus_rt_mutex_lock(&dev->state_mutex);
        dev->active_cmd_count--;
        if (dev->dev_state == MODBUS_DEV_STATE_CLOSING && dev->active_cmd_count == 0)
        {
            modbus_rt_sem_post(&dev->all_cmd_done);
        }
        modbus_rt_mutex_unlock(&dev->state_mutex);
    }
    else
    {
        // 调用者仍在等待，正常通知。
        cmd->status = MODBUS_CMD_STATE_COMPLETE;
        modbus_rt_mutex_unlock(&data->cmd_mutex);

        // 如果注册了结果回调函数，则调用它
        if (data->result_handler)
        {
            data->result_handler(cmd, data->result_handler_data);
        }
        /* 关键改动 2：在释放完成信号量之前，先等待调用者的"准备就绪"信号 */
        modbus_rt_sem_wait(&cmd->caller_ready_sem, MODBUS_RT_WAIT_FOREVER);
        // 通知调用者执行完毕
        modbus_rt_sem_post(&cmd->completion_sem);
        printf("[MODBUS DEBUG]     completion_sem 信号量释放.\n");
        // 减少活动命令计数
        modbus_rt_mutex_lock(&dev->state_mutex);
        dev->active_cmd_count--;
        if (dev->dev_state == MODBUS_DEV_STATE_CLOSING && dev->active_cmd_count == 0)
        {
            modbus_rt_sem_post(&dev->all_cmd_done);
        }
        modbus_rt_mutex_unlock(&dev->state_mutex);
    }

    return data->ret;
}
#endif
#endif
#if 0
int modbus_rtu_master_poll(rtu_modbus_device_t dev)
{
    if (dev == NULL || dev->mode != MODBUS_MASTER)
    {
        return -MODBUS_RT_EINVAL;
    }

    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    modbus_cmd_t *cmd = NULL;

    // --- 1. 安全地获取一个待处理的命令 ---
    modbus_rt_mutex_lock(&dev->state_mutex);
    // 检查设备是否正在关闭
    if (dev->dev_state != MODBUS_DEV_STATE_ACTIVE)
    {
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return -MODBUS_RT_ERROR;
    }

    // 从队列中取单
    modbus_rt_mutex_lock(&data->cmd_mutex);
    if (plist_head_empty(&data->cmd_queue))
    {
        // 正常情况下，工作线程由cmd_sem信号量唤醒，队列不应为空。
        // 这是一个防御性检查，以防意外唤醒。
        modbus_rt_mutex_unlock(&data->cmd_mutex);
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return MODBUS_RT_EOK;
    }
    cmd = list_entry(data->cmd_queue.node_list.next, modbus_cmd_t, list_node.node_list);
    plist_del(&cmd->list_node, &data->cmd_queue);
    modbus_rt_mutex_unlock(&data->cmd_mutex);

    // 成功获取命令后，增加活动命令计数
    dev->active_cmd_count++;
    modbus_rt_mutex_unlock(&dev->state_mutex);

    // --- 2. 核心逻辑: 无论如何，先执行命令 ---
    cmd->status = MODBUS_CMD_STATE_RUNNING;

    // 设置执行参数
    data->slave_addr = cmd->slave_addr;
    data->function = cmd->function;
    data->data_addr = cmd->w_addr;
    data->quantity = cmd->w_quantity;
    data->ptr_data = cmd->ptr_w_data;
    data->read_addr = cmd->r_addr;
    data->read_quantity = cmd->r_quantity;
    data->ptr_read_data = cmd->ptr_r_data;

    // 核心执行函数
    cmd->result = modbus_rtu_master_excuse_run(dev);

    // --- 3. 执行后判断: 根据命令状态决定后续路径 ---
    modbus_rt_mutex_lock(&data->cmd_mutex);
    bool is_detached = (cmd->status == MODBUS_CMD_STATE_DETACHED);
    modbus_rt_mutex_unlock(&data->cmd_mutex);

    if (is_detached)
    {
        // 路径 A: DETACHED 状态 (异步调用或同步调用已超时)
        // 工作线程获得所有权。首先销毁不再需要的信号量。
        modbus_rt_sem_destroy(&cmd->completion_sem);
        modbus_rt_sem_destroy(&cmd->caller_ready_sem);

        // 然后，调用回调函数（如果已注册）。
        // 回调函数的责任被简化为：只处理结果，并释放cmd对象。
        if (data->result_handler)
        {
            data->result_handler(cmd, data->result_handler_data);
        }
        else
        {
            // 安全网：如果没有注册回调，为防止内存泄漏，库必须自己释放cmd对象。
            modbus_rt_free(cmd);
        }
    }
    else
    {
        // 路径 B: 同步调用且未超时
        // 执行 "同步握手" 将结果安全地通知给等待的调用者。
        cmd->status = MODBUS_CMD_STATE_COMPLETE;

        // 步骤 1: 等待调用者发出 "准备好接收" 的信号。
        modbus_rt_sem_wait(&cmd->caller_ready_sem, MODBUS_RT_WAIT_FOREVER);

        // 步骤 2: 发送 "执行完成" 信号。
        modbus_rt_sem_post(&cmd->completion_sem);
        printf("[MODBUS DEBUG]     completion_sem 信号量释放.\n");
    }

    // --- 4. 任务完成: 更新活动计数并检查是否需要触发关机完成信号 ---
    modbus_rt_mutex_lock(&dev->state_mutex);
    dev->active_cmd_count--;
    if (dev->dev_state == MODBUS_DEV_STATE_CLOSING && dev->active_cmd_count == 0)
    {
        // 如果设备正在关闭，并且这是最后一个活动命令，
        // 则通知 modbus_rtu_destroy() 函数可以继续进行清理了。
        modbus_rt_sem_post(&dev->all_cmd_done);
    }
    modbus_rt_mutex_unlock(&dev->state_mutex);

    return cmd->result;
}
#endif
/* 完整的 modbus_rtu_master_poll 函数 */
#if LAST_VERSION
int modbus_rtu_master_poll(rtu_modbus_device_t dev)
{
    if (dev == NULL || dev->mode != MODBUS_MASTER)
    {
        return -MODBUS_RT_EINVAL;
    }

    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    modbus_cmd_t *cmd = NULL;

    // --- 1. 安全地获取一个待处理的命令 ---
    modbus_rt_mutex_lock(&dev->state_mutex);
    if (dev->dev_state != MODBUS_DEV_STATE_ACTIVE)
    {
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return -MODBUS_RT_ERROR;
    }

    modbus_rt_mutex_lock(&data->cmd_mutex);
    if (plist_head_empty(&data->cmd_queue))
    {
        modbus_rt_mutex_unlock(&data->cmd_mutex);
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return MODBUS_RT_EOK;
    }
    cmd = list_entry(data->cmd_queue.node_list.next, modbus_cmd_t, list_node.node_list);
    plist_del(&cmd->list_node, &data->cmd_queue);
    modbus_rt_mutex_unlock(&data->cmd_mutex);

    dev->active_cmd_count++;
    modbus_rt_mutex_unlock(&dev->state_mutex);

    // --- 2. 核心逻辑: 无论如何，先执行命令 ---
    /* 记录原始状态，避免异步(DETACHED)命令被错误覆盖 */
    bool was_detached = (cmd->status == MODBUS_CMD_STATE_DETACHED);

    /* 只有非异步命令才需要设置为 RUNNING */
    if (!was_detached)
    {
        cmd->status = MODBUS_CMD_STATE_RUNNING;
    }

    data->slave_addr = cmd->slave_addr;
    data->function = cmd->function;
    data->data_addr = cmd->w_addr;
    data->quantity = cmd->w_quantity;
    data->ptr_data = cmd->ptr_w_data;
    data->read_addr = cmd->r_addr;
    data->read_quantity = cmd->r_quantity;
    data->ptr_read_data = cmd->ptr_r_data;

    cmd->result = modbus_rtu_master_excuse_run(dev);

    data->ret = cmd->result;

    // --- 3. 执行后判断: 根据命令状态决定后续路径 ---
    // --- 3. 执行后判断: 根据最初记录的状态决定后续路径 ---
    //    bool is_detached = was_detached;
    //    bool is_detached = (cmd->status == MODBUS_CMD_STATE_DETACHED);
    // **关键修改点 1**: 总是先调用回调函数，让其作为观察者处理结果
    if (data->result_handler)
    {
        data->result_handler(cmd, data->result_handler_data);
    }
    bool is_detached = (cmd->status == MODBUS_CMD_STATE_DETACHED);
    //     **关键修改点 2**: 根据状态决定是释放资源还是唤醒调用者
    if (is_detached)
    {
        // 路径 A: DETACHED 状态 (异步调用 或 同步调用已超时)
        // 工作线程是所有者，负责清理所有资源。
        modbus_rt_sem_destroy(&cmd->completion_sem);
        modbus_rt_sem_destroy(&cmd->caller_ready_sem);
        printf("[POLL]   Attempting to free cmd @ %p\n", cmd); // 打印地址
        modbus_rt_free(cmd);
#if LC_LOG_STAT_EN
        lc_stat_inc_cmd_free();
#endif
    }
    else
    {
        // 路径 B: 同步调用且未超时
        // 调用方是所有者，工作线程只负责完成握手。
        cmd->status = MODBUS_CMD_STATE_COMPLETE;
        modbus_rt_sem_wait(&cmd->caller_ready_sem, MODBUS_RT_WAIT_FOREVER);
        modbus_rt_sem_post(&cmd->completion_sem);
    }

    // --- 4. 任务完成: 更新活动计数 ---
    modbus_rt_mutex_lock(&dev->state_mutex);
    dev->active_cmd_count--;
    if (dev->dev_state == MODBUS_DEV_STATE_CLOSING && dev->active_cmd_count == 0)
    {
        modbus_rt_sem_post(&dev->all_cmd_done);
    }
    modbus_rt_mutex_unlock(&dev->state_mutex);

    return data->ret;
}
#else
int modbus_rtu_master_poll(rtu_modbus_device_t dev)
{
    if (!dev || dev->mode != MODBUS_MASTER)
    {
        return -MODBUS_RT_EINVAL;
    }
    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    modbus_cmd_t *cmd = NULL;

    /* ----- 设备状态 + 取队列 ----- */
    modbus_rt_mutex_lock(&dev->state_mutex);
    if (dev->dev_state != MODBUS_DEV_STATE_ACTIVE)
    {
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return -MODBUS_RT_ERROR;
    }

    modbus_rt_mutex_lock(&data->cmd_mutex);
    if (plist_head_empty(&data->cmd_queue))
    {
        modbus_rt_mutex_unlock(&data->cmd_mutex);
        modbus_rt_mutex_unlock(&dev->state_mutex);
        return MODBUS_RT_EEMPTY;
    }
    cmd = list_entry(data->cmd_queue.node_list.next,
                     modbus_cmd_t, list_node.node_list);
    plist_del(&cmd->list_node, &data->cmd_queue);
    dev->active_cmd_count++;
    modbus_rt_mutex_unlock(&data->cmd_mutex);
    modbus_rt_mutex_unlock(&dev->state_mutex);

    cmd->status = MODBUS_CMD_STATE_RUNNING;

    /* ----- 执行事务 ----- */
    data->slave_addr = cmd->slave_addr;
    data->function = cmd->function;
    data->data_addr = cmd->w_addr;
    data->quantity = cmd->w_quantity;
    data->ptr_data = cmd->ptr_w_data;
    data->read_addr = cmd->r_addr;
    data->read_quantity = cmd->r_quantity;
    data->ptr_read_data = cmd->ptr_r_data;

    cmd->result = modbus_rtu_master_excuse_run(dev);

    if (data->result_handler)
    {
        data->result_handler(cmd, cmd->user_data);
    }
    cmd->status = MODBUS_CMD_STATE_COMPLETE;

    /* ----- 双握手：先等 caller_ready，再给 completion ----- */
    modbus_rt_sem_wait(&cmd->caller_ready_sem, MODBUS_RT_WAIT_FOREVER);
    int new_ref = ref_dec_and_fetch(&cmd->refcnt, 1);
    if (new_ref == 1)
    {
        modbus_rt_sem_post(&cmd->completion_sem);
    }
    else /* new_ref == 0 */
    {
        cmd_cleanup(cmd);
    }
    /* ----- 更新活动计数 / 关机握手 ----- */
    modbus_rt_mutex_lock(&dev->state_mutex);
    dev->active_cmd_count--;
    if (dev->dev_state == MODBUS_DEV_STATE_CLOSING &&
        dev->active_cmd_count == 0)
    {
        modbus_rt_sem_post(&dev->all_cmd_done);
    }

    modbus_rt_mutex_unlock(&dev->state_mutex);

    return cmd->result;
}
#endif
int modbus_rtu_set_thread_owner(rtu_modbus_device_t dev, int owner)
{
    if (!dev)
        return -MODBUS_RT_EINVAL;
    if (dev->status > 0)
        return -MODBUS_RT_ISOPEN; // 必须在open之前调用
    dev->thread_owner = (owner != 0);
    return MODBUS_RT_EOK;
}
int modbus_rtu_set_master_result_handler(rtu_modbus_device_t dev, void (*handler)(modbus_cmd_t *cmd, void *user_data), void *user_data)
{
    if (dev == NULL || dev->mode != MODBUS_MASTER)
    {
        return -MODBUS_RT_EINVAL;
    }

    rtu_master_data_t data = (rtu_master_data_t)dev->data;
    data->result_handler = handler;
    data->result_handler_data = user_data;

    return MODBUS_RT_EOK;
}

#endif
