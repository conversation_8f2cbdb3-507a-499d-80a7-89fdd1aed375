/**
 ******************************************************************************
 * @file      testYX.c
 * @brief     C Source file of testYX.c.
 * @details   This file including all API functions's
 *            implement of testYX.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <stdlib.h>
#include <ttylib.h>
#include <stm32f2xx_can.h>
#include <gpio.h>
#include <dmnLib.h>
#include <shell.h>
#include <taskLib.h>
#include <stdio.h>
#include <sxlib.h>
#include <string.h>
#include <trace.h>
#include <maths.h>
#include "..\ccu\charge\ccuChargeMain.h"
#include "..\ccu\charge\imd.h"
#include "..\ccu\bsn\deviceState.h"
#include "..\ccu\bsn\sample.h"
#include "..\ccu\inc\bms.h"
#include "..\ccu\bsn\io.h"
#include "..\ccu\para\para.h"
#include "..\ccu\pcu\pcuMain.h"
#include "..\ccu\tcu\tcuMain.h"
#include "..\ccu\lib\ccuLib.h"
#include "flash_rw.h"
#include "inc/filter.h"
#include "../ccu/charge/LiquidCoolingMain.h"
#include "../bsp/modbus/modbus_rt/modbus_rtu.h"
/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
uint8 chargePauseFlg(uint8 wr, uint8 flag);
/*-----------------------------------------------------------------------------
 Section: 读遥信信号-----ryx
 ----------------------------------------------------------------------------*/
static void rYx(void)
{
    printf("\n--------------------------遥信值-------------------------------\n\n");
    printf("k1遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_K1));
    printf("k2遥信值\t\t%d\n", Get_ValidDiSigal(SXIO_IN_K2));
    printf("熔断器遥信值\t\t%d\n", Get_ValidDiSigal(SXIO_IN_RDQ));
    printf("照明灯遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_CDQ));
    printf("电磁锁遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_DCS));
    printf("急停遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_JT));
    printf("门禁遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_DOOR));
    printf("辅助电源遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_FY));
    printf("水浸遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_CCU_SJ));
    printf("模式1遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_MODE1));
    printf("模式2遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_MODE2));
    printf("模式3遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_MODE3));
    printf("模式4遥信值\t\t%d \n", Get_ValidDiSigal(SXIO_IN_MODE4));
    printf("低压辅助电源状态\t\t%d \n", Get_ValidDiSigal(SXIO_IN_FY));
    printf("电池反接反馈\t\t%d \n", Get_ValidDiSigal(SXIO_IN_RECON));
    printf("倾倒检测反馈\t\t%d \n", Get_ValidDiSigal(SXIO_IN_QD));
}

SHELL_CMD(
    ryx, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)rYx,
    "ryx \r\t\t\t\t 读遥信值\n");

/*-----------------------------------------------------------------------------
 Section: 读遥测采样值-----ryc
 ----------------------------------------------------------------------------*/
static void do_ryc(void)
{
    printf("\n--------------------------采样值-------------------------------\n\n");
    printf("CC1的采样值\t\t%d \n", Get_SigleSampleVal(eADChannel_CC1, 0));
    printf("温度1的采样值\t\t%d \n", Get_SigleSampleVal(eADChannel_GunTemp1, 0));
    printf("温度2的采样值\t\t%d \n", Get_SigleSampleVal(eADChannel_GunTemp2, 0));
    printf("外电压采样值\t\t%d\n", Get_SigleSampleVal(e8209_Voltage, eType_8209_Out));
    printf("内电压采样值\t\t%d\n", Get_SigleSampleVal(e8209_Voltage, eType_8209_In));
    printf("电流采样值\t\t%d \n", Get_SigleSampleVal(e8209_Current_A, eType_8209_In));
    //    printf("ad 反接值\t\t%d \n",Get_SigleSampleVal(eADChannel_ReverseConnect,0));
    printf("电池 反接：\t\t%d \n", gpio_read(SXIO_IN_RECON));

    printf("\n--------------------------测量值-------------------------------\n\n");
    printf("CC1的电压\t\t%f\tV \n", Get_Cc1Vol());
    printf("枪温1的温度\t\t%f\t℃ \n", Get_DCTemp(eADChannel_GunTemp1) / 10.0);
    printf("枪温2的温度\t\t%f\t℃ \n", Get_DCTemp(eADChannel_GunTemp2) / 10.0);
    printf("外侧电压\t\t%d.%d\tV \n", Get_K1K2OutsideVol() / 10, Get_K1K2OutsideVol() % 10);
    printf("内侧电压\t\t%d.%d\tV \n", Get_K1K2InsideVol() / 10, Get_K1K2InsideVol() % 10);
    printf("母线电流\t\t%d.%d[%d.%d]\tA \n", Get_K1K2Current() / 1000, Get_K1K2Current() % 1000,
           Get_LineCurr() / 1000, Get_LineCurr() % 1000);
    printf("辅助电源状态\t\t%d\t \n", Get_SwitchState(SXIO_IN_FY));
}

SHELL_CMD(
    ryc, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_ryc,
    "ryc \r\t\t\t\t 读采样值\n");

/*-----------------------------------------------------------------------------
 Section: 读充电输出需求数据-----rvi
 ----------------------------------------------------------------------------*/
static void do_rvi(void)
{
    BCL_DATA strBCL;
    BHM_DATA strBHM;
    BCP_DATA strBCP;
    BCS_DATA strBCS;

    Get_BMS_Data(BMS_PGN_BCL, (void *)&strBCL);
    Get_BMS_Data(BMS_PGN_BHM, (void *)&strBHM);
    Get_BMS_Data(BMS_PGN_BCP, (void *)&strBCP);
    Get_BMS_Data(BMS_PGN_BCS, (void *)&strBCS);

    printf("当前状态:\t\t");

    switch (Get_WorkState())
    {
    case CCU_WORK_STATE_FREE:
        printf("空闲状态\n");
        break;
    case CCU_WORK_STATE_READY:
        printf("准备状态\n");
        break;
    case CCU_WORK_STATE_SHAKE_HAND:
        printf("握手状态\n");
        break;
    case CCU_WORK_STATE_IMD:
        printf("绝缘状态\n");
        break;
    case CCU_WORK_STATE_RELEASE_01:
        printf("泄放01状态\n");
        break;
    case CCU_WORK_STATE_RECOGNIZE:
        printf("辨识状态\n");
        break;
    case CCU_WORK_STATE_CONFIG:
        printf("配置状态\n");
        break;
    case CCU_WORK_STATE_PRE_CHARGE:
        printf("预充状态\n");
        break;
    case CCU_WORK_STATE_CHARGING:
        printf("充电状态\n");
        break;
    case CCU_WORK_STATE_CHARGE_PAUSE:
        printf("暂停状态\n");
        break;
    case CCU_WORK_STATE_CHARGE_STOP:
        printf("停止状态\n");
        break;
    case CCU_WORK_STATE_RELEASE_02:
        printf("泄放02状态\n");
        break;
    case CCU_WORK_STATE_STOP_FINISH:
        printf("停止完成状态\n");
        break;
    default:
        printf("异常状态\n");
        break;
    }
    printf("BMS状态:\t\t");
    switch (Get_BmsStage())
    {
    case BMS_STAGE_FREE:
        printf("空闲阶段\n");
        break;
    case BMS_STAGE_SHAKEHAND:
        printf("充电握手阶段\n");
        break;
    case BMS_STAGE_RECOGNIZE:
        printf("充电辨识阶段\n");
        break;
    case BMS_STAGE_PARACONFIG:
        printf("充电参数配置阶段\n");
        break;
    case BMS_STAGE_CHARGING:
        printf("充电阶段\n");
        break;
    case BMS_STAGE_STOP:
        printf("充电结束阶段\n ");
        break;
    case BMS_STAGE_STOPFINISH:
        printf("充电结束完成阶段\n ");
        break;
    default:
        printf("异常状态\n");
        break;
    }
    printf("TCU状态:\t\t");
    switch (Get_TcuStage())
    {
    case TCU_STAGE_MATCH_VER:
        printf("匹配-版本校验阶段\n");
        break;
    case TCU_STAGE_MATCH_PARA:
        printf("匹配-参数配置阶段\n");
        break;
    case TCU_STAGE_RUN_FREE:
        printf("运行-Free阶段\n");
        break;
    case TCU_STAGE_RUN_STARTING:
        printf("运行-启动中阶段\n");
        break;
    case TCU_STAGE_RUN_CHARGING:
        printf("运行-充电中阶段\n");
        break;
    case TCU_STAGE_RUN_STOPPING:
        printf("运行-停止中阶段\n");
        break;
    case TCU_STAGE_RUN_STOP_FINISH:
        printf("运行-停止完成阶段\n");
        break;
    }
    printf("PCU状态:\t\t");
    switch (Get_PcuStage())
    {
    case ePcuCtrlStage_Free:
        printf("PCU任务-待机阶段\n");
        break;
    case ePcuCtrlStage_QStart:
        printf("PCU任务-快速启动\n");
        break;
    case ePcuCtrlStage_QStartWait:
        printf("PCU任务-等待快速启动成功\n");
        break;
    case ePcuCtrlStage_WaitK1K2On_1:
        printf("PCU任务-等待K1K2闭合\n");
        break;
    case ePcuCtrlStage_ImdAdjPara:
        printf("PCU任务-绝缘参数调整\n");
        break;
    case ePcuCtrlStage_MidStop:
        printf("PCU任务-绝缘停机\n");
        break;
    case ePcuCtrlStage_SStart:
        printf("PCU任务-软起开机\n");
        break;
    case ePcuCtrlStage_SStartWs:
        printf("PCU任务-等待软起成功\n");
        break;
    case ePcuCtrlStage_WaitK1K2On_2:
        printf("PCU任务-等待K1K2闭合\n");
        break;
    case ePcuCtrlStage_AdjPara:
        printf("PCU任务-充电参数调整\n");
        break;
    case ePcuCtrlStage_Pause:
        printf("PCU任务-充电暂停\n");
        break;
    case ePcuCtrlStage_Stop:
        printf("PCU任务-停止充电\n");
        break;
    }
    printf("绝缘状态: %d\t\t\n", Get_Imd_Stage());
    printf("VIN启动标识: %d\t\t\n", Get_TcuPlugAndPlay());

    if (CHARGE_MODE_AUTO == Get_ChargeMode())
    {
        printf("\n当前充电模式为\t\t自动充电\n\n");
        printf("BMS数据:\n"
               "BHM最高允许充电总电压\t%d\n"
               "BCP当前电池电压\t\t%d\n"
               "BCS测量电压\t\t%d\n"
               "BCL需求电压\t\t%d\n"
               "BCL需求电流\t\t%d\n",
               TwoUint8ToUint16(strBHM.highlestTotalVoltage),
               TwoUint8ToUint16(strBCP.totalVoltage),
               TwoUint8ToUint16(strBCS.voltageMeasuredValue),
               TwoUint8ToUint16(strBCL.voltageDemand),
               (TwoUint8ToUint16(strBCL.currentDemand) == 0) ? 0 : ((Get_HighestCurrentLimit() - TwoUint8ToUint16(strBCL.currentDemand)) * 100));
    }
    else
    {
        printf("\n当前充电模式为\t\t手动充电\n\n");
        printf("手动设定数据:\n"
               "手动设定电压\t\t%d\n"
               "手动设定电流\t\t%d\n",
               Get_ManualVol(), Get_ManualCur());
    }

    printf("\n发给PCU数据:\n"
           "需求电压\t\t%d\n"
           "需求电流\t\t%d\n"
           "电池电压\t\t%d\n",
           Get_DemondVol(), Get_DemondCur(), Get_BatteryVol());
}

SHELL_CMD(
    rvi, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_rvi,
    "rvi \r\t\t\t\t 读取需求输出电压电流\n");

/*-----------------------------------------------------------------------------
 Section: 手动设定电压电流-----wvi
 ----------------------------------------------------------------------------*/
static void do_wvi(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    uint32 vol = 0;
    uint32 cur = 0;

    if (argc > 1)
    {
        sscanf(argv[1], "%d", &vol);
    }

    if (argc > 2)
    {
        sscanf(argv[2], "%d", &cur);
    }

    printf("手动设定的电压是 %dV, 手动设定的电流是 %d.%dA \n", vol / 10, cur / 1000, cur % 1000);

    Set_ManualCur(cur);
    Set_ManualVol(vol);
    return;
}

SHELL_CMD(
    wvi, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_wvi,
    "wvi [V][I] \r\t\t\t\t 设定手动电压电流 V-0.1/bit  I-0.001/bit\n");

/*-----------------------------------------------------------------------------
 Section: 显示故障信息-----rerr
 ----------------------------------------------------------------------------*/

void show_CcuErrEnableList(void)
{
    printf("故障码------故障名---------------------------------------------------故障状态--------使能状态\n\n");

    printf("  %d  紧急停止故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_EmergencyStop,
           Check_ErrType(eErrType_EmergencyStop), Get_EnableFlag(eErrType_EmergencyStop));
    printf("  %d  控制导引故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_GunConnectErr,
           Check_ErrType(eErrType_GunConnectErr), Get_EnableFlag(eErrType_GunConnectErr));
    printf("  %d  交流断路器故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_ACBreakerErr,
           Check_ErrType(eErrType_ACBreakerErr), Get_EnableFlag(eErrType_ACBreakerErr));
    printf("  %d  CCU门禁故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_CcuDoorOpenErr,
           Check_ErrType(eErrType_CcuDoorOpenErr), Get_EnableFlag(eErrType_CcuDoorOpenErr));
    printf("  %d  交流接触器故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_AcJCQErr,
           Check_ErrType(eErrType_AcJCQErr), Get_EnableFlag(eErrType_AcJCQErr));
    printf("  %d  PCU门禁故障\t\t\t\t\t\t\t%d\t\t%d \n\n", eErrType_PcuDoorOpenErr,
           Check_ErrType(eErrType_PcuDoorOpenErr), Get_EnableFlag(eErrType_PcuDoorOpenErr));
    printf("  %d  PCU急停故障\t\t\t\t\t\t\t%d\t\t%d \n\n", eErrType_PcuEmergencyStop,
           Check_ErrType(eErrType_PcuEmergencyStop), Get_EnableFlag(eErrType_PcuEmergencyStop));

    printf("  %d  与TCU通讯超时故障\t\t\t\t\t\t%d\t\t%d\n", eErrType_ComErrWithTCU,
           Check_ErrType(eErrType_ComErrWithTCU), Get_EnableFlag(eErrType_ComErrWithTCU));
    printf("  %d  与BMS通讯超时故障\t\t\t\t\t\t%d\t\t%d\n", eErrType_ComErrWithBMS,
           Check_ErrType(eErrType_ComErrWithBMS), Get_EnableFlag(eErrType_ComErrWithBMS));
    printf("  %d  直流母线输出电压过压故障\t\t\t\t\t\t%d\t\t%d\n", eErrType_OutputVolOverLimit,
           Check_ErrType(eErrType_OutputVolOverLimit), Get_EnableFlag(eErrType_OutputVolOverLimit));
    printf("  %d  直流母线输出电压欠压故障\t\t\t\t\t\t%d\t\t%d\n", eErrType_OutputVolLessLimit,
           Check_ErrType(eErrType_OutputVolLessLimit), Get_EnableFlag(eErrType_OutputVolLessLimit));
    printf("  %d  BSM中过电流故障\t\t\t\t\t\t\t%d\t\t%d\n", eErrType_OutputCurOverLimitBSM,
           Check_ErrType(eErrType_OutputCurOverLimitBSM), Get_EnableFlag(eErrType_OutputCurOverLimitBSM));
    printf("  %d  BSM中过温故障\t\t\t\t\t\t\t%d\t\t%d\n", eErrType_TempOverLimitBSM,
           Check_ErrType(eErrType_OutputCurOverLimitBSM), Get_EnableFlag(eErrType_OutputCurOverLimitBSM));
    printf("  %d  停止完成帧确认超时\t\t\t\t\t\t%d\t\t%d\n", eErrType_StopFinishAckTimeOutTcu,
           Check_ErrType(eErrType_StopFinishAckTimeOutTcu), Get_EnableFlag(eErrType_StopFinishAckTimeOutTcu));
    printf("  %d  绝缘监测故障\t\t\t\t\t\t\t%d\t\t%d\n", eErrType_ImdErr,
           Check_ErrType(eErrType_ImdErr), Get_EnableFlag(eErrType_ImdErr));
    printf("  %d  电池反接故障\t\t\t\t\t\t\t%d\t\t%d\n", eErrType_BatteryReverseConnect,
           Check_ErrType(eErrType_BatteryReverseConnect), Get_EnableFlag(eErrType_BatteryReverseConnect));
    printf("  %d  BMS高压继电器故障\t\t \t\t\t\t%d\t\t%d\n", eErrType_K5K6ErrBMS,
           Check_ErrType(eErrType_K5K6ErrBMS), Get_EnableFlag(eErrType_K5K6ErrBMS));
    printf("  %d  直流母线输出电流过流故障\t\t\t\t\t\t%d\t\t%d\n", eErrType_OutputCurOverLimit,
           Check_ErrType(eErrType_OutputCurOverLimit), Get_EnableFlag(eErrType_OutputCurOverLimit));
    printf("  %d  直流母线输出熔断器故障\t\t\t\t\t\t%d\t\t%d\n", eErrType_FuseProtectorErr,
           Check_ErrType(eErrType_FuseProtectorErr), Get_EnableFlag(eErrType_FuseProtectorErr));
    printf("  %d  桩过温故障\t\t\t\t\t\t\t%d\t\t%d\n", eErrType_PileTempOverLimitErr,
           Check_ErrType(eErrType_PileTempOverLimitErr), Get_EnableFlag(eErrType_PileTempOverLimitErr));
    printf("  %d  烟雾报警故障\t\t\t\t\t\t\t%d\t\t%d\n", eErrType_SmokeErr,
           Check_ErrType(eErrType_SmokeErr), Get_EnableFlag(eErrType_SmokeErr));
    printf("  %d  输入过压\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_InputVolOverLimit,
           Check_ErrType(eErrType_InputVolOverLimit), Get_EnableFlag(eErrType_InputVolOverLimit));
    printf("  %d  输入欠压\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_InputVolLessLimit,
           Check_ErrType(eErrType_InputVolLessLimit), Get_EnableFlag(eErrType_InputVolLessLimit));
    printf("  %d  充电模块故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleErr,
           Check_ErrType(eErrType_PowerModuleErr), Get_EnableFlag(eErrType_PowerModuleErr));
    printf("  %d  直流母线输出接触器故障\t\t\t\t\t\t%d\t\t%d \n", eErrType_DCMainContactorErr1,
           Check_ErrType(eErrType_DCMainContactorErr1), Get_EnableFlag(eErrType_DCMainContactorErr1));
    printf("  %d  充电接口电子锁故障\t\t\t\t\t\t%d\t\t%d \n", eErrType_ElecLockErr,
           Check_ErrType(eErrType_ElecLockErr), Get_EnableFlag(eErrType_ElecLockErr));
    printf("  %d  充电机风扇故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PileFanErr,
           Check_ErrType(eErrType_PileFanErr), Get_EnableFlag(eErrType_PileFanErr));
    printf("  %d  枪过温故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_GunTempOverLimitErr,
           Check_ErrType(eErrType_GunTempOverLimitErr), Get_EnableFlag(eErrType_GunTempOverLimitErr));
    printf("  %d  TCU其它故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_TCUOtherErr,
           Check_ErrType(eErrType_TCUOtherErr), Get_EnableFlag(eErrType_TCUOtherErr));
    printf("  %d  直流输出接触器粘连故障\t\t\t\t\t\t%d\t\t%d \n", eErrType_DCMainContactorSynechia,
           Check_ErrType(eErrType_DCMainContactorSynechia), Get_EnableFlag(eErrType_DCMainContactorSynechia));
    printf("  %d  泄放回路告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_ReleaseErr,
           Check_ErrType(eErrType_ReleaseErr), Get_EnableFlag(eErrType_ReleaseErr));
    printf("  %d  辅助电源故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_AssistPowerErr,
           Check_ErrType(eErrType_AssistPowerErr), Get_EnableFlag(eErrType_AssistPowerErr));
    printf("  %d  并联接触器粘连故障\t\t\t\t\t\t%d\t\t%d \n", eErrType_MultipleContactorSynechia,
           Check_ErrType(eErrType_MultipleContactorSynechia), Get_EnableFlag(eErrType_MultipleContactorSynechia));
    printf("  %d  直流输出接触器K1故障\t\t\t\t\t\t%d\t\t%d \n", eErrType_K1Err,
           Check_ErrType(eErrType_K1Err), Get_EnableFlag(eErrType_K1Err));
    printf("  %d  直流输出接触器K2故障\t\t\t\t\t\t%d\t\t%d\n", eErrType_K2Err,
           Check_ErrType(eErrType_K2Err), Get_EnableFlag(eErrType_K2Err));
    printf("  %d  BMS异常停机错误\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_BmsFaultStopErr /*eErrType_InputCurOverLimit*/,
           Check_ErrType(eErrType_BmsFaultStopErr /*eErrType_InputCurOverLimit*/), Get_EnableFlag(eErrType_BmsFaultStopErr /*eErrType_InputCurOverLimit*/));
    printf("  %d  直流输出短路故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_OutputShortCut,
           Check_ErrType(eErrType_OutputShortCut), Get_EnableFlag(eErrType_OutputShortCut));
    printf("  %d  最高允许充电电压小于充电机最小输出电压\t\t\t\t%d\t\t%d \n", eErrType_BHMVolErr,
           Check_ErrType(eErrType_BHMVolErr), Get_EnableFlag(eErrType_BHMVolErr));

    printf("  %d  K1K2外侧电压>= 10V\t\t\t\t\t\t%d\t\t%d \n", eErrType_K1K2OutsideVolErr1,
           Check_ErrType(eErrType_K1K2OutsideVolErr1), Get_EnableFlag(eErrType_K1K2OutsideVolErr1));
    printf("  %d  启动充电前K1K2外侧电压与BCP中电压值误差>=±百分之5\t\t%d\t\t%d \n", eErrType_BCPVolErr1,
           Check_ErrType(eErrType_BCPVolErr1), Get_EnableFlag(eErrType_BCPVolErr1));
    printf("  %d  外侧电压小于充电机最小输出电压\t\t\t\t\t%d\t\t%d \n", eErrType_K1K2OutsideVolErr2,
           Check_ErrType(eErrType_K1K2OutsideVolErr2), Get_EnableFlag(eErrType_K1K2OutsideVolErr2));
    printf("  %d  外侧电压大于充电机最大输出电压\t\t\t\t\t%d\t\t%d \n", eErrType_K1K2OutsideVolErr3,
           Check_ErrType(eErrType_K1K2OutsideVolErr3), Get_EnableFlag(eErrType_K1K2OutsideVolErr3));
    printf("  %d  电池端电压大于最高允许充电总电压\t\t\t\t\t%d\t\t%d \n", eErrType_BatteryVolErr,
           Check_ErrType(eErrType_BatteryVolErr), Get_EnableFlag(eErrType_BatteryVolErr));
    printf("  %d  BRM数据项异常\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_BRMErr,
           Check_ErrType(eErrType_BRMErr), Get_EnableFlag(eErrType_BRMErr));
    printf("  %d  BCP数据项异常\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_BCPErr,
           Check_ErrType(eErrType_BCPErr), Get_EnableFlag(eErrType_BCPErr));
    printf("  %d  充电暂停超时\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_ChargePauseTimeout,
           Check_ErrType(eErrType_ChargePauseTimeout), Get_EnableFlag(eErrType_ChargePauseTimeout));
    printf("  %d  无空余模块可用\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_NoMod,
           Check_ErrType(eErrType_NoMod), Get_EnableFlag(eErrType_NoMod));
    printf("  %d  负荷控制开关合法性校验失败\t\t\t\t\t%d\t\t%d \n", eErrType_LoadCtrlSwitch,
           Check_ErrType(eErrType_LoadCtrlSwitch), Get_EnableFlag(eErrType_LoadCtrlSwitch));
    printf("  %d  绝缘检测仪通信超时\t\t\t\t\t\t%d\t\t%d \n", eErrType_ComErrWithIMD,
           Check_ErrType(eErrType_ComErrWithIMD), Get_EnableFlag(eErrType_ComErrWithIMD));
    printf("  %d  PCU通信超时\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_ComErrWithPCU,
           Check_ErrType(eErrType_ComErrWithPCU), Get_EnableFlag(eErrType_ComErrWithPCU));
    printf("  %d  充电柜过温故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_CabTempOverErr,
           Check_ErrType(eErrType_CabTempOverErr), Get_EnableFlag(eErrType_CabTempOverErr));
    printf("  %d  PCU终止充电       \t\t\t\t\t\t%d\t\t%d \n", eErrType_PcuStopCharge,
           Check_ErrType(eErrType_PcuStopCharge), Get_EnableFlag(eErrType_PcuStopCharge));
    printf("  %d  PCU禁止充电故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PcuForbidCharge,
           Check_ErrType(eErrType_PcuForbidCharge), Get_EnableFlag(eErrType_PcuForbidCharge));
    printf("  %d  抄表故障\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_ReadMeterErr,
           Check_ErrType(eErrType_ReadMeterErr), Get_EnableFlag(eErrType_ReadMeterErr));
    printf("  %d  输入缺相\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_InputOpenphase,
           Check_ErrType(eErrType_InputOpenphase), Get_EnableFlag(eErrType_InputOpenphase));
    printf("  %d  BSM中SOC过高\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_SOCTooHighBSM,
           Check_ErrType(eErrType_SOCTooHighBSM), Get_EnableFlag(eErrType_SOCTooHighBSM));
    printf("  %d  BSM中SOC过低\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_SOCTooLowBSM,
           Check_ErrType(eErrType_SOCTooLowBSM), Get_EnableFlag(eErrType_SOCTooLowBSM));
    printf("  %d  BSM中绝缘异常\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_ImdErrBSM,
           Check_ErrType(eErrType_ImdErrBSM), Get_EnableFlag(eErrType_ImdErrBSM));
    printf("  %d  BSM中导引异常\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PhyConErrBSM,
           Check_ErrType(eErrType_PhyConErrBSM), Get_EnableFlag(eErrType_PhyConErrBSM));
    printf("  %d  BSM中电池单体电压过高\t\t\t\t\t\t%d\t\t%d \n", eErrType_PhySingleVoltooHighBSM,
           Check_ErrType(eErrType_PhySingleVoltooHighBSM), Get_EnableFlag(eErrType_PhySingleVoltooHighBSM));
    printf("  %d  BSM中电池单体电压过低\t\t\t\t\t\t%d\t\t%d \n", eErrType_PhySingleVoltooLowBSM,
           Check_ErrType(eErrType_PhySingleVoltooLowBSM), Get_EnableFlag(eErrType_PhySingleVoltooLowBSM));
    printf("  %d  启动完成帧确认超时\t\t\t\t\t\t%d\t\t%d \n", eErrType_StartFinishAckTimeOutTcu,
           Check_ErrType(eErrType_StartFinishAckTimeOutTcu), Get_EnableFlag(eErrType_StartFinishAckTimeOutTcu));
    printf("  %d  模块开关机异常\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_ModuleOnOffErr,
           Check_ErrType(eErrType_ModuleOnOffErr), Get_EnableFlag(eErrType_ModuleOnOffErr));
    printf("  %d  BRO准备就绪后取消\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_BROErr,
           Check_ErrType(eErrType_BROErr), Get_EnableFlag(eErrType_BROErr));
    printf("  %d  软启动超时             \t\t\t\t\t\t%d\t\t%d \n", eErrType_SStartTimeOut,
           Check_ErrType(eErrType_SStartTimeOut), Get_EnableFlag(eErrType_SStartTimeOut));
    printf("  %d  水浸故障\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_WaterLoggingErr,
           Check_ErrType(eErrType_WaterLoggingErr), Get_EnableFlag(eErrType_WaterLoggingErr));
    printf("  %d  PCU其它故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PCUOtherErr,
           Check_ErrType(eErrType_PCUOtherErr), Get_EnableFlag(eErrType_PCUOtherErr));
    printf("  %d  开关模块故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_SwErr,
           Check_ErrType(eErrType_SwErr), Get_EnableFlag(eErrType_SwErr));
    printf("  %d  加热器故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_HearterErr,
           Check_ErrType(eErrType_HearterErr), Get_EnableFlag(eErrType_HearterErr));
    printf("  %d  CCU遥信遥测报文超时\t\t\t\t\t\t%d\t\t%d \n", eErrType_CcuYxYcTimeoutErr,
           Check_ErrType(eErrType_CcuYxYcTimeoutErr), Get_EnableFlag(eErrType_CcuYxYcTimeoutErr));
    printf("  %d  散热风扇故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_FanErr,
           Check_ErrType(eErrType_FanErr), Get_EnableFlag(eErrType_FanErr));
    printf("  %d  散热器故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_SrqErr,
           Check_ErrType(eErrType_SrqErr), Get_EnableFlag(eErrType_SrqErr));
    printf("  %d  快速启动超时\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_QStartTimeOut,
           Check_ErrType(eErrType_QStartTimeOut), Get_EnableFlag(eErrType_QStartTimeOut));
    printf("  %d  绝缘检测超时\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_ImdTimeOut,
           Check_ErrType(eErrType_ImdTimeOut), Get_EnableFlag(eErrType_ImdTimeOut));
    printf("  %d  外侧电压小于200V\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_K1K2OutsideVolErr4,
           Check_ErrType(eErrType_K1K2OutsideVolErr4), Get_EnableFlag(eErrType_K1K2OutsideVolErr4));
    printf("  %d  避雷器故障\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_BlqErr,
           Check_ErrType(eErrType_BlqErr), Get_EnableFlag(eErrType_BlqErr));
    //    printf("  %d  即插即充应答超时\t\t\t\t\t\t\t%d\t\t%d \n",eErrType_PlugAndPlayAckTimeout,
    //                   Check_ErrType(eErrType_PlugAndPlayAckTimeout),Get_EnableFlag(eErrType_PlugAndPlayAckTimeout));
    //    printf("  %d  即插即充应答失败\t\t\t\t\t\t\t%d\t\t%d \n",eErrType_PlugAndPlayAckErr,
    //                       Check_ErrType(eErrType_PlugAndPlayAckErr),Get_EnableFlag(eErrType_PlugAndPlayAckErr));
    printf("  %d  即插即充应答超时\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PlugAndPlayAckTimeout,
           Check_ErrType(eErrType_PlugAndPlayAckTimeout), Get_EnableFlag(eErrType_PlugAndPlayAckTimeout));
    printf("  %d  车辆鉴权失败\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PlugAndPlayAckErr,
           Check_ErrType(eErrType_PlugAndPlayAckErr), Get_EnableFlag(eErrType_PlugAndPlayAckErr));
    printf("  %d  非法VIN\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_TcuCarVinIllegalityErr,
           Check_ErrType(eErrType_TcuCarVinIllegalityErr), Get_EnableFlag(eErrType_TcuCarVinIllegalityErr));
    printf("  %d  即插即充鉴权失败\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_TcuCarVinConfirmFailErr,
           Check_ErrType(eErrType_TcuCarVinConfirmFailErr), Get_EnableFlag(eErrType_TcuCarVinConfirmFailErr));
    printf("  %d  接收鉴权帧超时\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_TcuCarVinConfirmTimeoutErr,
           Check_ErrType(eErrType_TcuCarVinConfirmTimeoutErr), Get_EnableFlag(eErrType_TcuCarVinConfirmTimeoutErr));

    printf("  %d  TCU鉴权帧超时\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_CarVinConfirmTimeoutErr,
           Check_ErrType(eErrType_CarVinConfirmTimeoutErr), Get_EnableFlag(eErrType_CarVinConfirmTimeoutErr));
    printf("  %d  鉴权VIN码与车辆VIN不一致\t\t\t\t\t\t%d\t\t%d \n", eErrType_VinInconformityErr,
           Check_ErrType(eErrType_VinInconformityErr), Get_EnableFlag(eErrType_VinInconformityErr));
    printf("  %d  TCU与平台鉴权失败\t\t\t\t\t\t%d\t\t%d \n", eErrType_TcuCarVinOtherErr,
           Check_ErrType(eErrType_TcuCarVinOtherErr), Get_EnableFlag(eErrType_TcuCarVinOtherErr));
    printf("  %d  输出电流大于车辆需求总电流\t\t\t\t\t%d\t\t%d \n", eErrType_BusCurOverCarLimite,
           Check_ErrType(eErrType_BusCurOverCarLimite), Get_EnableFlag(eErrType_BusCurOverCarLimite));
    printf("  %d  输出电压大于车辆需求总电压\t\t\t\t\t%d\t\t%d \n", eErrType_BusVolOverCarLimite,
           Check_ErrType(eErrType_BusVolOverCarLimite), Get_EnableFlag(eErrType_BusVolOverCarLimite));
    printf("  %d  输出电压大于最大允许输出电压\t\t\t\t\t%d\t\t%d \n", eErrType_OutputOverMaxAllowOutputVol,
           Check_ErrType(eErrType_OutputOverMaxAllowOutputVol), Get_EnableFlag(eErrType_OutputOverMaxAllowOutputVol));
    printf("  %d  输出电压大于最大允许充电电压\t\t\t\t\t%d\t\t%d \n", eErrType_OutputOverMaxAllowChargeVol,
           Check_ErrType(eErrType_OutputOverMaxAllowChargeVol), Get_EnableFlag(eErrType_OutputOverMaxAllowChargeVol));
    printf("  %d  输出电压大于需求充电电压\t\t\t\t\t\t%d\t\t%d \n", eErrType_OutputOverDemandVol,
           Check_ErrType(eErrType_OutputOverDemandVol), Get_EnableFlag(eErrType_OutputOverDemandVol));
    printf("  %d  需求电压大于最大允许充电电压\t\t\t\t\t%d\t\t%d \n", eErrType_DemandOverMaxAllowChargeVol,
           Check_ErrType(eErrType_DemandOverMaxAllowChargeVol), Get_EnableFlag(eErrType_DemandOverMaxAllowChargeVol));

    printf("  %d  输出电流大于需求电流\t\t\t\t\t\t%d\t\t%d \n", eErrType_OutputOverDemandCur,
           Check_ErrType(eErrType_OutputOverDemandCur), Get_EnableFlag(eErrType_OutputOverDemandCur));
    printf("  %d  输出电流大于最大允许充电电流\t\t\t\t\t%d\t\t%d \n", eErrType_OutputOverMaxChargeCur,
           Check_ErrType(eErrType_OutputOverMaxChargeCur), Get_EnableFlag(eErrType_OutputOverMaxChargeCur));
    printf("  %d  输出电流大于最大允许输出电流\t\t\t\t\t%d\t\t%d \n", eErrType_OutputOverMaxAllowOutputCur,
           Check_ErrType(eErrType_OutputOverMaxAllowOutputCur), Get_EnableFlag(eErrType_OutputOverMaxAllowOutputCur));
    printf("  %d  需求电流大于最大允许充电电流\t\t\t\t\t%d\t\t%d \n", eErrType_DemandOverMaxAllowChargeCur,
           Check_ErrType(eErrType_DemandOverMaxAllowChargeCur), Get_EnableFlag(eErrType_DemandOverMaxAllowChargeCur));
    printf("  %d  PCU与模块通讯超时\t\t\t\t\t\t%d\t\t%d \n", eErrType_PcuAndMouileTimeout,
           Check_ErrType(eErrType_PcuAndMouileTimeout), Get_EnableFlag(eErrType_PcuAndMouileTimeout));
    printf("  %d  PCU与PCU通讯超时(并柜)\t\t\t\t\t\t%d\t\t%d \n", eErrType_PcuAndPcuTimeout,
           Check_ErrType(eErrType_PcuAndPcuTimeout), Get_EnableFlag(eErrType_PcuAndPcuTimeout));
    printf("  %d  PCU与环境信息模块通讯超时\t\t\t\t\t%d\t\t%d \n", eErrType_PcuAndEcuTimeout,
           Check_ErrType(eErrType_PcuAndEcuTimeout), Get_EnableFlag(eErrType_PcuAndEcuTimeout));
    printf("  %d  PCU与开关模块通讯超时\t\t\t\t\t\t%d\t\t%d \n", eErrType_PcuAndSwcuTimeout,
           Check_ErrType(eErrType_PcuAndSwcuTimeout), Get_EnableFlag(eErrType_PcuAndSwcuTimeout));
    printf("  %d  PCU与CCU通讯超时\t\t\t\t\t\t\t%d\t\t%d \n\n", eErrType_PcuAndCcuTimeout,
           Check_ErrType(eErrType_PcuAndCcuTimeout), Get_EnableFlag(eErrType_PcuAndCcuTimeout));
    printf("  %d  最高允许充电电压大于充电机最大输出电压\t\t\t\t%d\t\t%d \n", eErrType_BHMVolErr1,
           Check_ErrType(eErrType_BHMVolErr1), Get_EnableFlag(eErrType_BHMVolErr1));

    printf("  %d  倾倒故障\t\t\t\t%d\t\t%d \n", eErrType_DropDownErr1,
           Check_ErrType(eErrType_DropDownErr1), Get_EnableFlag(eErrType_DropDownErr1));
    printf("  %d  PE故障\t\t\t\t%d\t\t%d \n", eErrType_PEErr,
           Check_ErrType(eErrType_PEErr), Get_EnableFlag(eErrType_PEErr));
    printf("  %d  PCU水浸故障\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PcuWaterFault,
           Check_ErrType(eErrType_PcuWaterFault), Get_EnableFlag(eErrType_PcuWaterFault));
    printf("  %d  需求电压小于充电机最小输出电压故障\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_DemandLowMinAllowOutputVol,
           Check_ErrType(eErrType_DemandLowMinAllowOutputVol), Get_EnableFlag(eErrType_DemandLowMinAllowOutputVol));
    printf("  %d  充电参数不匹配\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_ChargeParaNoMatch,
           Check_ErrType(eErrType_ChargeParaNoMatch), Get_EnableFlag(eErrType_ChargeParaNoMatch));
    printf("  %d  TCU故障\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_TCU,
           Check_ErrType(eErrType_TCU), Get_EnableFlag(eErrType_TCU));
    printf("  %d  车桩双方枪数信息不一致故障\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_EVGunMismatch,
           Check_ErrType(eErrType_EVGunMismatch), Get_EnableFlag(eErrType_EVGunMismatch));

    printf("  %d  充电模块风扇告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleFanAlarm,
           Check_ErrType(eErrType_PowerModuleFanAlarm), Get_EnableFlag(eErrType_PowerModuleFanAlarm));
    printf("  %d  充电模块过温告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleTempAlarm,
           Check_ErrType(eErrType_PowerModuleTempAlarm), Get_EnableFlag(eErrType_PowerModuleTempAlarm));
    printf("  %d  充电模块交流输入告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleACInAlarm,
           Check_ErrType(eErrType_PowerModuleACInAlarm), Get_EnableFlag(eErrType_PowerModuleACInAlarm));
    printf("  %d  充电模块输出短路告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleOutShortCutAlarm,
           Check_ErrType(eErrType_PowerModuleOutShortCutAlarm), Get_EnableFlag(eErrType_PowerModuleOutShortCutAlarm));
    printf("  %d  充电模块输出过流告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleOutCurOverAlarm,
           Check_ErrType(eErrType_PowerModuleOutCurOverAlarm), Get_EnableFlag(eErrType_PowerModuleOutCurOverAlarm));
    printf("  %d  充电模块输出过压告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleOutVolOverAlarm,
           Check_ErrType(eErrType_PowerModuleOutVolOverAlarm), Get_EnableFlag(eErrType_PowerModuleOutVolOverAlarm));
    printf("  %d  充电模块输出欠压告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleOutVolLessAlarm,
           Check_ErrType(eErrType_PowerModuleOutVolLessAlarm), Get_EnableFlag(eErrType_PowerModuleOutVolLessAlarm));
    printf("  %d  充电模块输入过压告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleInVolOverAlarm,
           Check_ErrType(eErrType_PowerModuleInVolOverAlarm), Get_EnableFlag(eErrType_PowerModuleInVolOverAlarm));
    printf("  %d  充电模块输入欠压告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleInVolLessAlarm,
           Check_ErrType(eErrType_PowerModuleInVolLessAlarm), Get_EnableFlag(eErrType_PowerModuleInVolLessAlarm));
    printf("  %d  充电模块输入缺相告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleInOpenPhaseAlarm,
           Check_ErrType(eErrType_PowerModuleInOpenPhaseAlarm), Get_EnableFlag(eErrType_PowerModuleInOpenPhaseAlarm));
    printf("  %d  充电模块通信告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleComAlarm,
           Check_ErrType(eErrType_PowerModuleComAlarm), Get_EnableFlag(eErrType_PowerModuleComAlarm));
    printf("  %d  充电模块泄放告警(个别故障)\t\t\t\t\t%d\t\t%d \n", eErrType_PowerModuleXfAlarm,
           Check_ErrType(eErrType_PowerModuleXfAlarm), Get_EnableFlag(eErrType_PowerModuleXfAlarm));
    printf("  %d  绝缘监测告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_ImdAlarm,
           Check_ErrType(eErrType_ImdAlarm), Get_EnableFlag(eErrType_ImdAlarm));
    printf("  %d  枪未归位告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_GunNoHoming,
           Check_ErrType(eErrType_GunNoHoming), Get_EnableFlag(eErrType_GunNoHoming));
    printf("  %d  桩过温告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PileTempOverLimitAlarm,
           Check_ErrType(eErrType_PileTempOverLimitAlarm), Get_EnableFlag(eErrType_PileTempOverLimitAlarm));
    printf("  %d  枪过温告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_GunTempOverLimitAlarm,
           Check_ErrType(eErrType_GunTempOverLimitAlarm), Get_EnableFlag(eErrType_GunTempOverLimitAlarm));
    printf("  %d  工作中\t\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_Working,
           Check_ErrType(eErrType_Working), Get_EnableFlag(eErrType_Working));
    printf("  %d  版本校验未完成\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_VerCheckUnfinished,
           Check_ErrType(eErrType_VerCheckUnfinished), Get_EnableFlag(eErrType_VerCheckUnfinished));
    printf("  %d  下发参数未完成\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_SetParaUnfinished,
           Check_ErrType(eErrType_SetParaUnfinished), Get_EnableFlag(eErrType_SetParaUnfinished));
    printf("  %d  PCU过温告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PcuTempOverLimitAlarm,
           Check_ErrType(eErrType_PcuTempOverLimitAlarm), Get_EnableFlag(eErrType_PcuTempOverLimitAlarm));
    printf("  %d  避雷器告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_BlqAlarm,
           Check_ErrType(eErrType_BlqAlarm), Get_EnableFlag(eErrType_BlqAlarm));
    printf("  %d  风机告警    \t\t\t\t\t\t\t%d\t\t%d \n", eErrType_FanAlarm,
           Check_ErrType(eErrType_FanAlarm), Get_EnableFlag(eErrType_FanAlarm));
    printf("  %d  加热部件告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_HearterAlarm,
           Check_ErrType(eErrType_HearterAlarm), Get_EnableFlag(eErrType_HearterAlarm));
    printf("  %d  输入过压告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_InVolOverAlarm,
           Check_ErrType(eErrType_InVolOverAlarm), Get_EnableFlag(eErrType_InVolOverAlarm));
    printf("  %d  输入欠压告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_InVolLessAlarm,
           Check_ErrType(eErrType_InVolLessAlarm), Get_EnableFlag(eErrType_InVolLessAlarm));
    printf("  %d  输入缺相告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_InOpenPhaseAlarm,
           Check_ErrType(eErrType_InOpenPhaseAlarm), Get_EnableFlag(eErrType_InOpenPhaseAlarm));
    printf("  %d  开关模块告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_SwAlarm,
           Check_ErrType(eErrType_SwAlarm), Get_EnableFlag(eErrType_SwAlarm));
    printf("  %d  PCU通讯告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PcuComAlarm,
           Check_ErrType(eErrType_PcuComAlarm), Get_EnableFlag(eErrType_PcuComAlarm));
    printf("  %d  PCU其它告警\t\t\t\t\t\t\t%d\t\t%d \n", eErrType_PcuOtherAlarm,
           Check_ErrType(eErrType_PcuOtherAlarm), Get_EnableFlag(eErrType_PcuOtherAlarm));
    printf("  %d  环境信息模块上送故障\t\t\t\t\t\t%d\t\t%d \n", eErrType_HjxxAlm,
           Check_ErrType(eErrType_HjxxAlm), Get_EnableFlag(eErrType_HjxxAlm));
}
void show_CcucfgEnableList(void)
{
    printf("配置号------配置项-------------------------------------------------------------------使能状态\n\n");
    printf("  %d   CTS发送使能\t\t\t\t\t\t\t\t\t%d \n",
           eParaFmt_CfgCTSSendEnable, Get_EnableFlag(eParaFmt_CfgCTSSendEnable));
    printf("  %d   拔枪再充电使能\t\t\t\t\t\t\t\t\t%d \n",
           eParaFmt_CfgGunOffReChargeEnable, Get_EnableFlag(eParaFmt_CfgGunOffReChargeEnable));
    printf("  %d   外部绝缘检使能\t\t\t\t\t\t\t\t\t%d \n",
           eParaFmt_CfgExImdEnable, Get_EnableFlag(eParaFmt_CfgExImdEnable));
    printf("  %d   分体机配置使能\t\t\t\t\t\t\t\t\t%d \n",
           eParaFmt_CfgFissionEnable, Get_EnableFlag(eParaFmt_CfgFissionEnable));
    printf("  %d   多枪机配置使能\t\t\t\t\t\t\t\t\t%d \n",
           eParaFmt_CfgMultiJunctorEnable, Get_EnableFlag(eParaFmt_CfgMultiJunctorEnable));
    printf("  %d   测试卡配置使能\t\t\t\t\t\t\t\t\t%d \n",
           eParaFmt_CfgTestCardEnable, Get_EnableFlag(eParaFmt_CfgTestCardEnable));
    printf("  %d   泄放使能\t\t\t\t\t\t\t\t\t\t%d \n",
           eParaFmt_XfOpenEnable, Get_EnableFlag(eParaFmt_XfOpenEnable));
    printf("  %d   欧标使能\t\t\t\t\t\t\t\t\t\t%d \n",
           eParaFmt_EuropeEnable, Get_EnableFlag(eParaFmt_EuropeEnable));
    printf("\n\n");
}

static void do_rerr(void)
{
    show_CcuErrEnableList();
}

SHELL_CMD(
    rerr, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_rerr,
    "rerr \r\t\t\t\t 读故障信息\n");

static void do_errTest(cmd_tbl_t *cmdtp, uint32 argc, const uint8 *argv[])
{
    int format = 0;
    int data = 0;
    if (argc == 3)
    {
        sscanf((char *)argv[1], "%d", &format);
        sscanf((char *)argv[2], "%d", &data);

        if (format > 1)
        {
            printf("参数格式错误\n");
            return;
        }
        if ((data < eErrType_PowerModuleFanAlarm) || ((data > eErrType_SwAlarm) && (data < eErrType_ComErrWithTCU)) || ((data > eErrType_K1K2OutsideVolErr4) && (data < eErrType_EmergencyStop)) || (data > eErrType_AcJCQErr))
        {
            printf("不在故障代码范围\n");
            return;
        }
        if (0 == format)
        {
            Clr_ErrType(data);
        }
        else
        {
            Set_ErrType(data);
        }
    }
    else
    {
        printf("测试故障码参数数量错误\n");
    }
    //	show_ErrEnableList();
}

SHELL_CMD(
    errTest, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_errTest,
    "errTest [format][val]\r\t\t\t\t 测试故障或消除故障\n"
    "                     \r\t\t\t\t format 0-消除故障故障，1--------设置故障  \n"
    "                     \r\t\t\t\t val----------------------------故障编码 \n"

);

/*-----------------------------------------------------------------------------
 Section: 显示参数-----ccu_rp
 ----------------------------------------------------------------------------*/
static void do_ccu_rp(void)
{
    SAMPLE_PARA strSamplePara;
    CHARGE_PARA strChargePara;
    OPERATE_PARA strOperatePara;
    CONFIG_PARA strConfigPara;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
    Get_PilePara((void *)&strSamplePara, eParaType_SamplePara);
    Get_PilePara((void *)&strOperatePara, eParaType_OperatePara);
    Get_PilePara((void *)&strConfigPara, eParaType_ConfigPara);

    printf("\n--------------------------充电参数-------------------------------\n");
    printf("-----------枪号-------------------\t\t%d\t\n", (Get_CcuCfgAddr() + 1));
    printf("编号<1>----额定充电功率----------保留1位小数\t%f\tkW\n", TwoUint8ToUint16(strChargePara.maxPower) / 10.0);
    printf("编号<2>----模块类型--------------------\t\t%d\t\n", strChargePara.moduleType);
    printf("编号<3>----模块恒功率电压下限------保留1位小数\t%f\tV\n", TwoUint8ToUint16(strChargePara.pwrVoltMin) / 10.0);
    printf("编号<4>----模块个数--------------------\t\t%d\t\n", strChargePara.moduleCnt);
    printf("编号<5>----最大输出电压----------保留1位小数\t%f\tV\n", TwoUint8ToUint16(strChargePara.maxOutputVoltage) / 10.0);
    printf("编号<6>----最小输出电压----------保留1位小数\t%f\tV\n", TwoUint8ToUint16(strChargePara.minOutputVoltage) / 10.0);
    printf("编号<7>----最大输出电流----------保留3位小数\t%f\tA\n", FourUint8ToUint32(strChargePara.maxOutputCurrent) / 1000.0);
    printf("编号<8>----最小输出电流----------保留3位小数\t%f\tA\n", FourUint8ToUint32(strChargePara.minOutputCurrent) / 1000.0);
    printf("编号<9>----交流输入过压限值-------保留1位小数\t%f\tV\n", TwoUint8ToUint16(strChargePara.AC_InputOverVoltageLimit) / 10.0);
    printf("编号<10>---交流输入欠压限值-------保留1位小数\t%f\tV\n", TwoUint8ToUint16(strChargePara.AC_InputLessVoltageLimit) / 10.0);
    printf("编号<11>---直流输出过压限值-------保留1位小数\t%f\tV\n", TwoUint8ToUint16(strChargePara.DC_OutputOverVoltageLimit) / 10.0);
    printf("编号<12>---直流输出欠压限值-------保留1位小数\t%f\tV\n", TwoUint8ToUint16(strChargePara.DC_OutputLessVoltageLimit) / 10.0);
    printf("编号<13>---直流输出过流限值-------保留3位小数\t%f\tA\n", FourUint8ToUint32(strChargePara.DC_OutputOverCurrentLimit) / 1000.0);
    printf("编号<14>---设定充电功率----------保留1位小数\t%f\tkW\n", TwoUint8ToUint16(strChargePara.chargePower) / 10.0);
    printf("编号<15>---设定线路电阻----------保留1位小数\t%f\tmΩ\n", TwoUint8ToUint16(strChargePara.lineRes) / 10.0);
    printf("编号<16>---输出保护时间----------保留1位小数\t%f\tS\n", TwoUint8ToUint16(strChargePara.ProtTime) / 10.0);
    printf("编号<17>---输出5A时间----------\t%d\tMs\n", TwoUint8ToUint16(strChargePara.A_5Time));
    printf("编号<18>---电磁锁解锁电压----------\t%f\tV\n", TwoUint8ToUint16(strChargePara.Out_Elc) / 10.0);
    printf("编号<19>---是否启用日志功能----------\t%d\t\n", strChargePara.Enablelog);
    printf("编号<20>---枪温采样电阻值----------\t%d\t\n", TwoUint8ToUint16(strChargePara.Sample_K));
    printf("编号<21>---外侧电压阈值----------\t%d\t\n", TwoUint8ToUint16(strChargePara.OutVol));
    printf("编号<22>---是否开启液冷----------\t%d\t\n", strChargePara.EnableYL);
    printf("编号<23>---小功率设备启用0-禁用，1-启用----------\t%d\t\n", strChargePara.Ver);
    printf("编号<24>---欧标绝缘检测测试周期----------\t%d ms\t\n", FourUint8ToUint32(strChargePara.imdchecktime));
    printf("编号<25>---是否支持并充     ----------\t%d \t\n", strChargePara.IsDual);
    printf("编号<26>---并充配对地址     ----------\t%x \t\n", strChargePara.Dual_addr);
    printf("\n--------------------------运营参数-------------------------------\n");
    printf("编号<31>----厂商编码------------10进制\t\t%d \n", FourUint8ToUint32(strOperatePara.factoryCode));
    printf("编号<32>----区域编码------------10进制\t\t%d \n", FourUint8ToUint32(strOperatePara.areaCode));
    printf("编号<33>----设备型号------------10进制\t\t%d \n", TwoUint8ToUint16(strOperatePara.deviceType));
    printf("编号<34>----生产年份------------10进制\t\t%d \n", TwoUint8ToUint16(strOperatePara.produceYear));
    printf("编号<35>----生产批次------------10进制\t\t%d \n", TwoUint8ToUint16(strOperatePara.produceBatch));
    printf("编号<36>----生产序号------------10进制\t\t%d \n", FourUint8ToUint32(strOperatePara.produceSerial));
    printf("编号<37>----硬件版本------------16进制\t\t%X \n", TwoUint8ToUint16(strOperatePara.hardVer));
    printf("编号<38>----软件版本------------16进制\t\t%X \n", ThreeUint8ToUint32(strOperatePara.softVer));
    printf("编号<39>----软件日期------------16进制\t\t%X \n", FourUint8ToUint32(strOperatePara.softDate));
    printf("编号<40>----设备编号------------字符串\t\t%s\t%d\n", strOperatePara.chargerNO, strlen(strOperatePara.chargerNO));
    printf("编号<41>----设备名称------------字符串\t\t%s\t%d\n", strOperatePara.devName, strlen(strOperatePara.devName));
    printf("编号<42>----CCU名称-------------字符串\t\t%s\t%d\n", strOperatePara.ccuName, strlen(strOperatePara.ccuName));
    printf("编号<43>----PCU名称-------------字符串\t\t%s\t%d\n", strOperatePara.pcuName, strlen(strOperatePara.pcuName));
    printf("编号<44>----SW名称--------------字符串\t\t%s\t%d\n", strOperatePara.swName, strlen(strOperatePara.swName));

    printf("\n--------------------------采样参数-------------------------------\n");
    printf("\n---------------------采样参数系数禁止写入-------------------------\n");
    printf("编号<51>----枪温1参数------------K\t\t%f\tB\t%f\n", strSamplePara.AD_GunTemp1.k, strSamplePara.AD_GunTemp1.b);
    printf("编号<52>----枪温2参数------------K\t\t%f\tB\t%f\n", strSamplePara.AD_GunTemp2.k, strSamplePara.AD_GunTemp2.b);
    printf("编号<53>----辅源电压-------------K\t\t%f\n", strSamplePara.AD_AssistPower);
    printf("编号<54>----导引校准-------------K\t\t%f\n", strSamplePara.AD_CC1);
    printf("编号<55>----外侧电压-------------K\t\t%f\tB\t%f\n", strSamplePara.Rn8209_OutVol.k, strSamplePara.Rn8209_OutVol.b);
    printf("编号<56>----内侧电压-------------K\t\t%f\tB\t%f\n", strSamplePara.Rn8209_InVol.k, strSamplePara.Rn8209_InVol.b);
    printf("编号<57>----母线电流-------------K\t\t%f\tB\t%f\n", strSamplePara.Rn8209_Cur.k, strSamplePara.Rn8209_Cur.b);
    printf("编号<58>----绝缘ON_P-------------K\t\t%f\tB\t%f\n", strSamplePara.imdOnP.k, strSamplePara.imdOnP.b);
    printf("编号<59>----绝缘ON_N-------------K\t\t%f\tB\t%f\n", strSamplePara.imdOnN.k, strSamplePara.imdOnN.b);
    printf("编号<60>----绝缘Off_P------------K\t\t%f\tB\t%f\n", strSamplePara.imdOffP.k, strSamplePara.imdOffP.b);
    printf("编号<61>----绝缘Off_N------------K\t\t%f\tB\t%f\n", strSamplePara.imdOffN.k, strSamplePara.imdOffN.b);
    printf("\n---------------------采样参数系数禁止写入-------------------------\n");
    printf("编号<62>----绝缘范围-------------低电压故障:%d\t高电压故障:%d\t低电压告警:%d\t高电压告警:%d\n",
           strSamplePara.insulate.faultL, strSamplePara.insulate.faultH,
           strSamplePara.insulate.alarmL, strSamplePara.insulate.alarmH);
    printf("编号<63>----方向参数设置-------------急停方向(0:常闭,1：常开):%d\t倾倒方向:%d\n",
           strSamplePara.DirParam.jtdir, strSamplePara.DirParam.qddir);

    printf("\n--------------------------配置参数-------------------------------\n");
    printf("编号<71>----故障检测使能(1)------16进制\t\t");
    print_buf(0, strConfigPara.errCheckEnable, sizeof(strConfigPara.errCheckEnable));
    printf("编号<71>----故障检测使能(2)------16进制\t\t");
    print_buf(0, strConfigPara.err2CheckEnable, sizeof(strConfigPara.err2CheckEnable));
    printf("编号<72>----严重故障检测使能(1)--16进制\t\t");
    print_buf(0, strConfigPara.majorErrCheckEnable, sizeof(strConfigPara.majorErrCheckEnable));
    printf("编号<73>----告警检测使能(1)------16进制\t\t");
    print_buf(0, strConfigPara.allarmCheckEnable, sizeof(strConfigPara.allarmCheckEnable));
    printf("编号<74>----CTS发送使能(1)-------10进制\t\t%d \n", strConfigPara.ctsSendEnable);
    printf("编号<75>----插枪再充电使能(1)----10进制\t\t%d \n", strConfigPara.GunOffReChargeEnable);
    printf("编号<76>----外部绝缘使能(1)------10进制\t\t%d \n", strConfigPara.exImdSlect);
    printf("编号<77>----一体(0)/分体机(1)----10进制\t\t%d \n", strConfigPara.fissionEnable);
    printf("编号<78>----单枪(0)/多枪机(1)----10进制\t\t%d \n", strConfigPara.multiJunctorEnable);
    printf("编号<79>----测试卡使能-----------10进制\t\t%d \n", strConfigPara.testCardEnable);
    printf("编号<80>----PCU与CCU超时---------单位秒\t\t%d \n", strConfigPara.pcuAndCcuOvertime);
    printf("编号<81>----PCU与充电模块超时----单位秒\t\t%d \n", strConfigPara.pcuAndMoudleOvertime);
    printf("编号<82>----PCU与开关模块超时----单位秒\t\t%d \n", strConfigPara.pcuAndSwOvertime);
    printf("编号<83>----CCU与TCU超时---------单位秒\t\t%d \n", strConfigPara.ccuAndTcuOvertime);
    printf("编号<84>----CCU与PCU遥信周期-----单位秒\t\t%d \n", strConfigPara.ccuAndPcuYxCycle);
    printf("编号<85>----CCU与PCU遥测周期-----单位秒\t\t%d \n", strConfigPara.ccuAndPcuYcCycle);
    printf("编号<86>----CCU与TCU遥信周期-----单位秒\t\t%d \n", strConfigPara.ccuAndTcuYxCycle);
    printf("编号<87>----CCU与TCU遥测周期-----单位秒\t\t%d \n", strConfigPara.ccuAndTcuYcCycle);
    printf("编号<88>----一桩单充(0)/多充(1)--10进制\t\t%d \n", strConfigPara.onePiLeMulCharge);
    printf("编号<89>----泄放功能使能(1)------10进制\t\t%d \n", strConfigPara.xfOpenEnable);
    printf("编号<90>----欧标使能(1)------10进制\t\t%d \n", strConfigPara.europeEnable);
    printf("编号<91>----TCU协议转换(1--ykc 0---国网 2- 小桔)------10进制\t\t%d \n", strConfigPara.platform_convert);
    printf("编号<92>----电池反接故障使能(1)------10进制\t\t%d \n", Get_EnableFlag(eErrType_BatteryReverseConnect));
    printf("编号<93>----PCU与CCU超时---------单位秒\t\t%d \n", strConfigPara.ccuAndCcuOvertime);
    printf("编号<94>----默认参数配置\t设备类型--%d\t功率--%fkW\n",
           strConfigPara.onePiLeMulCharge ? 2 : strConfigPara.multiJunctorEnable,
           TwoUint8ToUint16(strChargePara.maxPower) / 10.0);
}

SHELL_CMD(
    ccu_rp, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_ccu_rp,
    "ccu_rp \r\t\t\t\t 读参数\n");

/*-----------------------------------------------------------------------------
 Section: 设置充电参数-----wpara
 ----------------------------------------------------------------------------*/
static void do_ccu_wp(cmd_tbl_t *cmdtp, uint32 argc, const uint8 *argv[])
{
    int format = 0;
    uint32 id = 0;
    int data[4] = {0};
    int int_data[2] = {0};
    float f_data[2] = {0.0};
    uint8 para[32] = {0};
    uint8 ret = 0;
    if (argc >= 3)
    {
        sscanf((char *)argv[1], "%d", &format);
        sscanf((char *)argv[1], "%x", &id);

        if (((format > eparaFmt_ChargePara) && (format <= eParaFmt_LineRes)) || ((format >= eParaFmt_CfgCTSSendEnable) && (format <= eParaFmt_CcuAndCcuTimeout)) || ((format > eparaFmt_OperatePara) && (format <= eparaFmt_OperateProduceSerialPara)) || (format == eParaFmt_imdtime))
        {
            if (argc == 3)
            {
                sscanf((char *)argv[2], "%d", data);
                Uint32ToFourUint8(para, data[0]);
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (format == eParaFmt_BatteryReverseEnable)
        {
            if (argc == 3)
            {
                sscanf((char *)argv[2], "%d", &data[0]);

                switch (data[0])
                {
                case 0:
                    Clr_CcuEnableFlag(eErrType_BatteryReverseConnect);
                    break;
                case 1:
                    Set_CcuEnableFlag(eErrType_BatteryReverseConnect);
                    break;
                default:
                    printf("设置失败\n");
                    break;
                }
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (format == eparaFmt_OperateHardVerPara)
        {
            if (argc == 3)
            {
                sscanf((char *)argv[2], "%4X", para);
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (format == eparaFmt_OperateSoftVerPara)
        {
            if (argc == 3)
            {
                sscanf((char *)argv[2], "%6X", para);
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (format == eparaFmt_OperateSoftDatePara)
        {
            if (argc == 3)
            {
                sscanf((char *)argv[2], "%8X", para);
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if ((format >= eparaFmt_OperateChargerNOPara) && (format <= eparaFmt_OperateSwcuName))
        {
            if (argc == 3)
            {
                memcpy(para, argv[2], sizeof(para));
                // sscanf((char *)argv[2], "%8X", para);
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if ((format == eparaFmt_SampleRn8209_CurPara) || (format == eparaFmt_SampleRn8209_OutVolPara) || (format == eparaFmt_SampleRn8209_InVolPara) || (format == eparaFmt_SampleAD_GunTemp1Para) || (format == eparaFmt_SampleAD_GunTemp2Para))
        {
            if (argc >= 4)
            {
                sscanf((char *)argv[2], "%d", &int_data[0]); // k
                sscanf((char *)argv[3], "%d", &int_data[1]); // b

                SAMPLE_PARA strSamplePara;
                Get_PilePara((void *)&strSamplePara, eParaType_SamplePara);

                f_data[0] = int_data[0] / 1000000.0;
                f_data[1] = int_data[1] / 1000.0;

                switch (format)
                {
                case eparaFmt_SampleRn8209_CurPara:
                {
                    strSamplePara.Rn8209_Cur.k = f_data[0];
                    strSamplePara.Rn8209_Cur.b = f_data[1];

                    printf("Rn8209_Cur.k=%f,Rn8209_Cur.b=%f\n",
                           strSamplePara.Rn8209_Cur.k,
                           strSamplePara.Rn8209_Cur.b);
                    Set_Para(format,
                             (uint8 *)&strSamplePara.Rn8209_Cur);
                }
                break;
                case eparaFmt_SampleRn8209_OutVolPara:
                {
                    strSamplePara.Rn8209_OutVol.k = f_data[0];
                    strSamplePara.Rn8209_OutVol.b = f_data[1];

                    printf("Rn8209_OutVol.k=%f,Rn8209_OutVol.b=%f\n",
                           strSamplePara.Rn8209_OutVol.k,
                           strSamplePara.Rn8209_OutVol.b);
                    Set_Para(format,
                             (uint8 *)&strSamplePara.Rn8209_OutVol);
                }
                break;
                case eparaFmt_SampleRn8209_InVolPara:
                {
                    strSamplePara.Rn8209_InVol.k = f_data[0];
                    strSamplePara.Rn8209_InVol.b = f_data[1];

                    printf("Rn8209_InVol.k=%f,Rn8209_InVol.b=%f\n",
                           strSamplePara.Rn8209_InVol.k,
                           strSamplePara.Rn8209_InVol.b);
                    Set_Para(format,
                             (uint8 *)&strSamplePara.Rn8209_InVol);
                }
                break;
                case eparaFmt_SampleAD_GunTemp1Para:
                {
                    strSamplePara.AD_GunTemp1.k = f_data[0];
                    strSamplePara.AD_GunTemp1.b = f_data[1];

                    printf("AD_GunTemp1.k=%f,AD_GunTemp1.b=%f\n",
                           strSamplePara.AD_GunTemp1.k,
                           strSamplePara.AD_GunTemp1.b);
                    Set_Para(format,
                             (uint8 *)&strSamplePara.AD_GunTemp1);
                }
                break;
                case eparaFmt_SampleAD_GunTemp2Para:
                {
                    strSamplePara.AD_GunTemp2.k = f_data[0];
                    strSamplePara.AD_GunTemp2.b = f_data[1];

                    printf("AD_GunTemp2.k=%f,AD_GunTemp2.b=%f\n",
                           strSamplePara.AD_GunTemp2.k,
                           strSamplePara.AD_GunTemp2.b);
                    Set_Para(format,
                             (uint8 *)&strSamplePara.AD_GunTemp2);
                }
                break;
                default:
                    break;
                }

                return;
            }
        }
        else if ((format == eparaFmt_SampleimdOnPPara) || (format == eparaFmt_SampleimdOnNPara) || (format == eparaFmt_SampleimdOffPPara) || (format == eparaFmt_SampleimdOffNPara))
        {
            if (argc >= 4)
            {
                sscanf((char *)argv[2], "%d", &int_data[0]); // K
                sscanf((char *)argv[3], "%d", &int_data[1]); // B

                SAMPLE_PARA strSamplePara;
                Get_PilePara((void *)&strSamplePara, eParaType_SamplePara);

                f_data[0] = int_data[0] / 1000000.0;
                f_data[1] = int_data[1] / 1000000.0;

                switch (format)
                {
                case eparaFmt_SampleimdOnPPara:
                {
                    strSamplePara.imdOnP.k = f_data[0];
                    strSamplePara.imdOnP.b = f_data[1];

                    printf("imdOnP.k=%f,imdOnP.b=%f\n", strSamplePara.imdOnP.k, strSamplePara.imdOnP.b);
                    Set_Para(format, (uint8 *)&strSamplePara.imdOnP);
                }
                break;
                case eparaFmt_SampleimdOnNPara:
                {
                    strSamplePara.imdOnN.k = f_data[0];
                    strSamplePara.imdOnN.b = f_data[1];

                    printf("imdOnN.k=%f,imdOnN.b=%f\n", strSamplePara.imdOnN.k, strSamplePara.imdOnN.b);
                    Set_Para(format, (uint8 *)&strSamplePara.imdOnN);
                }
                break;
                case eparaFmt_SampleimdOffPPara:
                {
                    strSamplePara.imdOffP.k = f_data[0];
                    strSamplePara.imdOffP.b = f_data[1];

                    printf("imdOffP.k=%f,imdOffP.b=%f\n", strSamplePara.imdOffP.k, strSamplePara.imdOffP.b);
                    Set_Para(format, (uint8 *)&strSamplePara.imdOffP);
                }
                break;
                case eparaFmt_SampleimdOffNPara:
                {
                    strSamplePara.imdOffN.k = f_data[0];
                    strSamplePara.imdOffN.b = f_data[1];

                    printf("imdOffN.k=%f,imdOffN.b=%f\n", strSamplePara.imdOffN.k, strSamplePara.imdOffN.b);
                    Set_Para(format, (uint8 *)&strSamplePara.imdOffN);
                }
                break;
                default:
                    break;
                }

                return;
            }
        }
        else if (format == eparaFmt_SampleInsulatePara)
        {
            if (argc == 6)
            {
                sscanf((char *)argv[2], "%d", data);
                sscanf((char *)argv[3], "%d", &data[1]);
                sscanf((char *)argv[4], "%d", &data[2]);
                sscanf((char *)argv[5], "%d", &data[3]);
                Uint16ToTwoUint8(para, data[0]);
                Uint16ToTwoUint8(&para[2], data[1]);
                Uint16ToTwoUint8(&para[4], data[2]);
                Uint16ToTwoUint8(&para[6], data[3]);
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (format == eparaFmt_DirParamPara)
        {

            if (argc >= 4)
            {
                SAMPLE_PARA strSamplePara;
                Get_PilePara((void *)&strSamplePara, eParaType_SamplePara);
                sscanf((char *)argv[2], "%d", &data[0]);
                sscanf((char *)argv[3], "%d", &data[1]);
                strSamplePara.DirParam.jtdir = data[0];
                strSamplePara.DirParam.qddir = data[1];
                printf("jtdir=%d,qddir=%d\n", strSamplePara.DirParam.jtdir, strSamplePara.DirParam.qddir);
                Set_Para(format, (uint8 *)&strSamplePara.DirParam);
                return;
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (format == eparaFmt_CfgerrCheckEnablePara)
        {
            if (argc == 6)
            {
                sscanf((char *)argv[2], "%8X", para);
                sscanf((char *)argv[3], "%8X", &para[4]);
                sscanf((char *)argv[4], "%8X", &para[8]);
                sscanf((char *)argv[5], "%6X", &para[12]);

                print_buf(0, para, sizeof(para));
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (format == eparaFmt_CfgmajorErrCheckEnablePara)
        {
            if (argc == 3)
            {
                sscanf((char *)argv[2], "%4X", para);
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (format == eparaFmt_CfgallarmCheckEnablePara)
        {
            if (argc == 3)
            {
                sscanf((char *)argv[2], "%8X", para);
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (format == eParaFmt_DefaultConfig)
        {
            if (argc == 4)
            {
                sscanf((char *)argv[2], "%d", para);
                sscanf((char *)argv[3], "%d", &para[1]);
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (format == eParaFmt_ProtTime || format == eParaFmt_5_ATime || format == eParaFmt_Out_Elc || format == eParaFmt_Enablelog || format == eParaFmt_Sample_K || format == eParaFmt_OutVol || format == eParaFmt_YL || format == eParaFmt_Ver || format == eParaFmt_isdual || format == eParaFmt_dualaddr)

        {
            if (argc == 3)
            {
                sscanf((char *)argv[2], "%d", &para[0]);
            }
            else
            {
                printf("参数数量错误\n");
                return;
            }
        }
        else if (0xff == id)
        {
            uint8 data[5] = {0};

            if (argc > 4)
            {
                sscanf((char *)argv[2], "%d", &para[0]);
                sscanf((char *)argv[3], "%d", &para[1]);
                sscanf((char *)argv[4], "%d", &para[2]);

                if (para[2] > 500)
                {
                    printf("失败!\n");
                    printf("参数功率错误---<%d>---KW!\n", para[2]);
                    return;
                }

                if ((1 == para[0]) && (1 == para[1]))
                {
                    data[0] = 3;
                    data[1] = (para[2] * 10) & 0xff;
                    data[2] = (para[2] * 10) >> 8;
                    Set_ParaOprateSrc(OporateType_Manual);
                    Set_Para(0xFFFF, data);
                    printf("成功!\n");
                    printf("<一体单枪>---功率---<%dKW>\n", para[2]);
                }
                else if ((1 == para[0]) && (2 == para[1]))
                {
                    data[0] = 2;
                    data[1] = (para[2] * 10) & 0xff;
                    data[2] = (para[2] * 10) >> 8;
                    Set_ParaOprateSrc(OporateType_Manual);
                    Set_Para(0xFFFF, data);
                    printf("成功!\n");
                    printf("<一体双枪>---功率---<%dKW>\n", para[2]);
                }
                else if ((2 == para[0]) && (2 == para[1]))
                {
                    data[0] = 1;
                    data[1] = (para[2] * 10) & 0xff;
                    data[2] = (para[2] * 10) >> 8;
                    Set_Para(0xFFFF, data);
                    Set_ParaOprateSrc(OporateType_Manual);
                    printf("成功!\n");
                    printf("<分体双桩双枪>---功率---<%dKW>\n", para[2]);
                }
                else if ((2 == para[0]) && (4 == para[1]))
                {
                    data[0] = 2;
                    data[1] = (para[2] * 10) & 0xff;
                    data[2] = (para[2] * 10) >> 8;
                    Set_Para(0xFFFF, data);
                    Set_ParaOprateSrc(OporateType_Manual);
                    printf("成功!\n");
                    printf("<分体双桩四枪>---功率---<%dKW>\n", para[2]);
                }
                else if ((4 == para[0]) && (4 == para[1]))
                {
                    data[0] = 1;
                    data[1] = (para[2] * 10) & 0xff;
                    data[2] = (para[2] * 10) >> 8;
                    Set_Para(0xFFFF, data);
                    Set_ParaOprateSrc(OporateType_Manual);
                    printf("成功!\n");
                    printf("<分体四桩四枪>---功率---<%dKW>\n", para[2]);
                }
                else
                {
                    printf("失败!\n");
                    printf("设备不存在---<%d>---<%d>!\n", para[0], para[1]);
                }
            }
            else
            {
                printf("失败!\n");
                printf("参数数量错误\n");
            }
            return;
        }
        else
        {
            printf("参数系数错误\n");
            return;
        }
        Set_ParaOprateSrc(OporateType_Manual);
        Set_Para(format, para);
    }
    else
    {
        printf("参数数量错误\n");
        return;
    }
}

SHELL_CMD(
    ccu_wp, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_ccu_wp,
    "ccu_wp [format][Val] \r\t\t\t\t 设置充电参数\n"
    "                    \r\t\t\t\t 1-----------充电功率\t\t0.1kW/bit\n"
    "                    \r\t\t\t\t 2-----------模块类型 \n"
    "                    \r\t\t\t\t 3-----------单模块功率\t\t0.1kW/bit\n"
    "                    \r\t\t\t\t 4-----------模块个数\n"
    "                    \r\t\t\t\t 5-----------最大输出电压\t0.1V/bit\n"
    "                    \r\t\t\t\t 6-----------最小输出电压\t0.1V/bit\n"
    "                    \r\t\t\t\t 7-----------最大输出电流\t0.001A/bit\n"
    "                    \r\t\t\t\t 8-----------最小输出电流\t0.001A/bit\n"
    "                    \r\t\t\t\t 9-----------交流输入过压限值\t0.1V/bit\n"
    "                    \r\t\t\t\t 10----------交流输入欠压限值\t0.1V/bit\n"
    "                    \r\t\t\t\t 11----------直流输出过压限值\t0.1V/bit\n"
    "                    \r\t\t\t\t 12----------直流输出欠压限值\t0.1V/bit\n"
    "                    \r\t\t\t\t 13----------直流输出过流限值\t0.001A/bit\n");

/*-----------------------------------------------------------------------------
 Section: 采样校准-----sample
 ----------------------------------------------------------------------------*/
static void
do_Sample(cmd_tbl_t *cmdtp, uint32 argc, const uint32 *argv[])
{
    uint32 cmd = 0;
    uint32 type = 0;
    uint8 channel = 0;
    uint32 val = 0;
    uint32 val2 = 0;
    if (argc > 3)
    {
        sscanf((char *)argv[1], "%d", &channel);
        sscanf((char *)argv[2], "%d", &type);
        if (channel == eADChannel_GunTemp1 && 0 == type)
            printf("校准-枪温1      %d \n", type);
        if (channel == eADChannel_GunTemp2 && 0 == type)
            printf("校准-枪温2      %d \n", type);
        if (channel == eADChannel_CC1 && 0 == type)
            printf("校准-CC1        %d \n", type);
        if (channel == e8209_Current_A && eType_8209_In == type)
            printf("校准-母线电流      %d \n", channel);
        if (channel == e8209_Voltage && eType_8209_In == type)
            printf("校准-内测电压      %d \n", channel);
        if (channel == e8209_Voltage && eType_8209_Out == type)
            printf("校准-外侧电压      %d \n", channel);
        if (channel == e8209_Current_B && eType_8209_Imd == type)
            printf("校准-绝缘检测 KE,KP,KN ON %d \n", channel);
        if (channel == e8209_Voltage && eType_8209_Imd == type)
            printf("校准-绝缘检测KE,ON;KP,KN OFF %d \n", channel);

        sscanf((char *)argv[3], "%d", &cmd);
        printf("校准命令 cmd = %d \n", cmd);

        if (cmd == 0)
            printf("初始化         %d \n", cmd);
        else if (cmd == 1)
            printf("Offset校正  %d \n", cmd);
        else
            printf("校准             %d \n", cmd);

        sscanf((char *)argv[4], "%d", &val);
        printf("校准值 val = %d \n", val);
        sscanf((char *)argv[5], "%d", &val2);
        printf("校准值 val2 = %d \n", val2);
        Adjust_Sample(cmd, channel, type, val, val2);
    }
}

SHELL_CMD(
    sample, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_Sample,
    "sample [channel][type][cmd][val][val2] \r\t\t\t\t 采样校准,绝缘检测阶段用到val2\n"
    "                   \r\t\t\t\t [channel][type]:  [0][0]-枪温1     [1][0]-枪温2       [2][0]-CC1      [3]-母线电流\n"
    "                   \r\t\t\t\t       [34][1]-母线电流  [35][1]-内侧电压  [36][2]-外侧电压\n"
    "                   \r\t\t\t\t      cmd:  [0]-初始化    [1]-offset校正  [2]-校准\n"
    "                   \r\t\t\t\t      val:  温度：0.1℃/bit  电压:0.1V/bit  电流:0.001A/bit\n");

static uint8 manual_cc1vol = 0;

uint8 Ret_Manual_Cc1Vol(void)
{
    return manual_cc1vol;
}

void Set_Manual_Cc1Vol(uint8 dat)
{
    manual_cc1vol = dat;
}

static void start(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    uint32 mode = 0;
    uint32 vol = 0;
    uint32 cur = 0;
    if (argc > 1)
    {
        sscanf(argv[1], "%d", &mode);
    }
    else
    {
        printf("参数个数不够\n");
        return;
    }
    if ((enumPhyConVol_4V != Get_PhyConVol() && (mode != 2)) /*&&
           TRUE == Get_EnableFlag(eErrType_EmergencyStop)*/
    )
    {
        printf("导引电压不对\n");
        return;
    }
    if (DEVICE_STATE_MAJOR_FAULT == Get_DeviceState() ||
        DEVICE_STATE_FAULT == Get_DeviceState())
    {
        printf("设备故障了\n");
        return;
    }
    if (TRUE == Check_ErrType(eErrType_GunTempOverLimitAlarm))
    {
        printf("枪过温告警\n");
        return;
    }
    if (CCU_WORK_STATE_FREE != Get_WorkState())
    {
        printf("设备状态不对\n");
        return;
    }
    if (mode == 1)
    {
        if (argc > 3)
        {
            sscanf(argv[2], "%d", &vol);
            sscanf(argv[3], "%d", &cur);
            printf("启动方式:%s 启动的电压: %d.%dV, 启动电流:%d.%dA \n", mode == 0 ? "自动" : "手动", vol / 10, vol % 10, cur / 1000, cur % 1000);
            Set_ChargeMode(mode);
            Set_ChargeActFlag(eActFlag_On);
            Set_ManualCur(cur);
            Set_ManualVol(vol);
        }
        else
        {
            printf("参数个数不够\n");
            return;
        }
    }
    else if (mode == 0)
    {
        Set_ChargeMode(mode);
        Set_ChargeActFlag(eActFlag_On);
        printf("启动方式:%s\n", "自动");
    }
    else if (mode == 2)
    {
        if (argc == 3)
        {
            sscanf(argv[2], "%d", &vol);
            if ((vol > 128) || ((vol < 112) && (vol > 68)) || ((vol < 52) && (vol > 48)) || (vol < 32))
            {
                Set_Manual_Cc1Vol(0);
            }
            else
            {
                Set_Manual_Cc1Vol(vol);
            }
        }
        return;
    }
    else
    {
        printf("启动方式错误\n");
        return;
    }
}

SHELL_CMD(
    start, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)start,
    "start [mode][vol][cur] \r\t\t\t\t mode启动充电 0-自动充电  1-手动充电2-设定CC1值\n"
    "                  \r\t\t\t\t vol 手动电压 保留1位小数  [自动方式无该参数],设定CC1时范围为0~120对应电压为0V~12V\n"
    "                  \r\t\t\t\t cur 手动电流 保留3位小数  [自动方式无该参数]\n");

static void stop(void)
{
    Set_StopSrc(eChargeStopFlag_Manual);
    Set_ChargeActFlag(eActFlag_Off);
    Set_ManualCur(0);
    Set_ManualVol(0);
    Set_Manual_Cc1Vol(0);
}

SHELL_CMD(
    stop, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)stop,
    "stop \r\t\t\t\t 停止充电\n");

static uint8 cc1_imitate_flag = FALSE;

void set_Cc1ImitateFlag(uint8 flag)
{
    cc1_imitate_flag = flag;
}

uint8 get_Cc1ImitateFlag(void)
{
    return cc1_imitate_flag;
}

uint8 k1K2TestFlag(uint8 wr, uint8 flag)
{
    static uint8 k1k2testflag = 0;
    if (wr)
    {
        k1k2testflag = flag;
    }
    return k1k2testflag;
}

uint8 k3K4TestFlag(uint8 wr, uint8 flag)
{
    static uint8 k3k4testflag = 0;
    if (wr)
    {
        k3k4testflag = flag;
    }
    return k3k4testflag;
}

static void cc1_imitate(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    if ((0 == strcmp(argv[1], "fault")) && (argc == 2))
    {
        set_Cc1ImitateFlag(TRUE);
        printf("模拟CC1故障.\n");
        return;
    }
    else if ((0 == strcmp(argv[1], "normal")) && (argc == 2))
    {
        set_Cc1ImitateFlag(FALSE);
        printf("恢复模拟CC1故障.\n");
        return;
    }
    else
    {
        printf("模拟CC1故障正常命令错误\n");
        return;
    }
}

SHELL_CMD(
    cc1, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)cc1_imitate,
    "cc1 \r\t\t\t\t 模拟CC1故障正常\n");

static void do_yk(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    if (0 == strcmp(argv[1], "zkon"))
    {
        ZK_ON;
        printf("总控打开\n");
    }
    else if (0 == strcmp(argv[1], "zkoff"))
    {
        ZK_OFF;
        printf("总控关闭\n");
    }
    else if (0 == strcmp(argv[1], "pcanon"))
    {
        P5V_CTRL_ON;
        printf("5V总控打开\n");
    }
    else if (0 == strcmp(argv[1], "pcanoff"))
    {
        P5V_CTRL_OFF;
        printf("5V总控关闭\n");
    }
    else if (0 == strcmp(argv[1], "k1k2on"))
    {
        k1K2TestFlag(TRUE, TRUE);
        K1_K2_ON;
        printf("K1K2闭合\n");
    }
    else if (0 == strcmp(argv[1], "k1k2off"))
    {
        k1K2TestFlag(TRUE, FALSE);
        K1_K2_OFF;
        printf("K1K2断开\n");
    }
    else if (0 == strcmp(argv[1], "k1on"))
    {
        k1K2TestFlag(TRUE, TRUE);
        K1_ON;
        printf("K1闭合\n");
    }
    else if (0 == strcmp(argv[1], "k1off"))
    {
        k1K2TestFlag(TRUE, FALSE);
        K1_OFF;
        printf("K1断开\n");
    }
    else if (0 == strcmp(argv[1], "k1test"))
    {
        uint8 test = 50;
        k1K2TestFlag(TRUE, TRUE);
        while (test--)
        {
            K1_ON;
            taskDelay(3000);
            printf("K1_ON FLAG = %d\n", Get_ValidDiSigal(SXIO_IN_K1));
            taskDelay(2000);
            K1_OFF;
            taskDelay(3000);
            printf("K1_OFF FLAG = %d\n", Get_ValidDiSigal(SXIO_IN_K1));
            taskDelay(2000);
        }
        k1K2TestFlag(TRUE, FALSE);
    }
    else if (0 == strcmp(argv[1], "k2on"))
    {
        k1K2TestFlag(TRUE, TRUE);
        K2_ON;
        printf("K2闭合\n");
    }
    else if (0 == strcmp(argv[1], "k2off"))
    {
        k1K2TestFlag(TRUE, FALSE);
        K2_OFF;
        printf("K2断开\n");
    }
    else if (0 == strcmp(argv[1], "k2test"))
    {
        uint8 test = 50;
        k1K2TestFlag(TRUE, TRUE);
        while (test--)
        {
            K2_ON;
            taskDelay(3000);
            printf("K2_ON FLAG = %d\n", Get_ValidDiSigal(SXIO_IN_K2));
            taskDelay(2000);
            K2_OFF;
            taskDelay(3000);
            printf("K2_OFF FLAG = %d\n", Get_ValidDiSigal(SXIO_IN_K2));
            taskDelay(2000);
        }
        k1K2TestFlag(TRUE, FALSE);
    }
    else if (0 == strcmp(argv[1], "yledon"))
    {
        FAULT_LED_ON;
        printf("告警灯亮\n");
    }
    else if (0 == strcmp(argv[1], "yledoff"))
    {
        FAULT_LED_OFF;
        printf("告警灯灭\n");
    }
    else if (0 == strcmp(argv[1], "rledon"))
    {
        RUN_LED_ON;
        printf("充电灯亮\n");
    }
    else if (0 == strcmp(argv[1], "rledoff"))
    {
        RUN_LED_OFF;
        printf("充电灯灭\n");
    }
    else if (0 == strcmp(argv[1], "gledon"))
    {
        JUNCTOR_LED_ON;
        printf("连接器灯亮\n");
    }
    else if (0 == strcmp(argv[1], "gledoff"))
    {
        JUNCTOR_LED_OFF;
        printf("连接器灯灭\n");
    }
    else if (0 == strcmp(argv[1], "k3k4on"))
    {
        k3K4TestFlag(TRUE, TRUE);
        K3_K4_ON;
        printf("K3K4闭合\n");
    }
    else if (0 == strcmp(argv[1], "k3k4off"))
    {
        k3K4TestFlag(TRUE, FALSE);
        K3_K4_OFF;
        printf("K3K4断开\n");
    }
    else if (0 == strcmp(argv[1], "xfon"))
    {
        RELEASE_ON;
        printf("泄放投入\n");
    }
    else if (0 == strcmp(argv[1], "xfoff"))
    {
        RELEASE_OFF;
        printf("泄放断开\n");
    }
    else if (0 == strcmp(argv[1], "xftest"))
    {
        RELEASE_ON;
        printf("泄放投入\n");
        taskDelay(sysClkRateGet() * 1);
        RELEASE_OFF;
        printf("泄放断开\n");
    }
    else if (0 == strcmp(argv[1], "lighton"))
    {
        LIGHT_ON;
        printf("开灯\n");
    }
    else if (0 == strcmp(argv[1], "lightoff"))
    {
        LIGHT_OFF;
        printf("关灯\n");
    }
    else if (0 == strcmp(argv[1], "qson"))
    {
#ifdef ELECK_CLOCK_CHECK_EN_Test
        Set_ElecClockOperate(sElecClock_Lock);
#else
        ELEC_LOCK_ON;
#endif
        printf("电子锁加锁\n");
    }
    else if (0 == strcmp(argv[1], "qsoff"))
    {
#ifdef ELECK_CLOCK_CHECK_EN_Test
        Set_ElecClockOperate(sElecClock_Unlock);
#else
        ELEC_LOCK_OFF;
#endif
        printf("电子锁解锁\n");
    }
    else if (0 == strcmp(argv[1], "qstest"))
    {
        printf("电子锁测试\n");
        Set_ElecClockOperate(sElecClock_Lock);
        ELEC_LOCK_ON;
        printf("电子锁加锁\n");
        taskDelay(sysClkRateGet() * 60);
        Set_ElecClockOperate(sElecClock_Unlock);
        ELEC_LOCK_OFF;
        printf("电子锁解锁\n");
    }
    else if (0 == strcmp(argv[1], "keon"))
    {
        IMD_KE_ON;
        printf("绝缘检测KE闭合\n");
    }
    else if (0 == strcmp(argv[1], "keoff"))
    {
        IMD_KE_OFF;
        printf("绝缘检测KE断开\n");
    }
    else if (0 == strcmp(argv[1], "kpon"))
    {
        IMD_KP_ON;
        printf("绝缘检测KP闭合\n");
    }
    else if (0 == strcmp(argv[1], "kepon"))
    {
        IMD_KEP_ON;
        printf("绝缘检测KEP闭合\n");
    }
    else if (0 == strcmp(argv[1], "kenon"))
    {
        IMD_KEN_ON;
        printf("绝缘检测KEN闭合\n");
    }
    else if (0 == strcmp(argv[1], "kepoff"))
    {
        IMD_KEP_OFF;
        printf("绝缘检测KEP闭合\n");
    }
    else if (0 == strcmp(argv[1], "kenoff"))
    {
        IMD_KEN_OFF;
        printf("绝缘检测KEN闭合\n");
    }
    else if (0 == strcmp(argv[1], "kpoff"))
    {
        IMD_KP_OFF;
        printf("绝缘检测KP断开\n");
    }
    else if (0 == strcmp(argv[1], "knon"))
    {
        IMD_KN_ON;
        printf("绝缘检测KN闭合\n");
    }
    else if (0 == strcmp(argv[1], "knoff"))
    {
        IMD_KN_OFF;
        printf("绝缘检测KN断开\n");
    }
    else if (0 == strcmp(argv[1], "byon"))
    {
        BY_ON;
        printf("备用继电器闭合\n");
    }
    else if (0 == strcmp(argv[1], "byoff"))
    {
        BY_OFF;
        printf("备用继电器断开\n");
    }
    else if (0 == strcmp(argv[1], "jyk1on"))
    {
        JYK1_ON;
        printf("绝缘检测遥控K1断开\n");
    }
    else if (0 == strcmp(argv[1], "jyk1off"))
    {
        JYK1_OFF;
        printf("绝缘检测遥控K1闭合\n");
    }
    else if (0 == strcmp(argv[1], "jyk2on"))
    {
        JYK2_ON;
        printf("绝缘检测遥控K2断开\n");
    }
    else if (0 == strcmp(argv[1], "jyk2off"))
    {
        JYK2_OFF;
        printf("绝缘检测遥控K2闭合\n");
    }
    else if (0 == strcmp(argv[1], "jyk3on"))
    {
        JYK3_ON;
        printf("绝缘检测遥控K3断开\n");
    }
    else if (0 == strcmp(argv[1], "jyk3off"))
    {
        JYK3_OFF;
        printf("绝缘检测遥控K3闭合\n");
    }
    else if (0 == strcmp(argv[1], "jyk5on"))
    {
        JYK5_ON;
        printf("绝缘检测遥控K5断开\n");
    }
    else if (0 == strcmp(argv[1], "jyk5off"))
    {
        JYK5_OFF;
        printf("绝缘检测遥控K5闭合\n");
    }
    else if (0 == strcmp(argv[1], "fyon"))
    {
        //    	FY_ON;
        FY24_ON;
        printf("低压辅助电源控制闭合\n");
    }
    else if (0 == strcmp(argv[1], "fyoff"))
    {
        //    	FY_OFF;
        FY24_OFF;
        printf("低压辅助电源控制断开\n");
    }
    else if (0 == strcmp(argv[1], "paraon"))
    {
        PARALLEL_CONTACTOR_OPEN;
    }
    else if (0 == strcmp(argv[1], "paraoff"))
    {
        PARALLEL_CONTACTOR_CLOSE;
    }
    else
    {
        printf("Input cmd is invalid\n\nYou can try:\n");

        printf("yk zkon---------------------总控打开\n");
        printf("yk zkoff--------------------总控关闭\n");
        printf("yk k1k2on-------------------K1K2断开\n");
        printf("yk k1k2off----------------- K1K2闭合\n");
        printf("yk k1on---------------------K1断开\n");
        printf("yk k1off--------------------K1闭合\n");
        printf("yk k1test-------------------K1测试\n");
        printf("yk k2on---------------------K2断开\n");
        printf("yk k2off--------------------K2闭合\n");
        printf("yk k2test-------------------K2测试\n");
        printf("yk k3k4on-------------------K3K4闭合\n");
        printf("yk k3k4off------------------K3K4断开\n");
        printf("yk qson---------------------电子锁加锁\n");
        printf("yk qsoff--------------------电子锁解锁\n");
        printf("yk xfon---------------------泄放投入\n");
        printf("yk xfoff--------------------泄放断开\n");
        printf("yk rledon-------------------充电灯亮\n");
        printf("yk rledoff------------------充电灯灭\n");
        printf("yk gledon-------------------连接器灯亮\n");
        printf("yk gledoff------------------连接器灯灭\n");
        printf("yk yledon-------------------告警灯亮\n");
        printf("yk yledoff------------------告警灯灭\n");
        printf("yk keon---------------------KE闭合\n");
        printf("yk keoff--------------------KE断开\n");
        printf("yk kpon---------------------KP闭合\n");
        printf("yk kpoff--------------------KP断开\n");
        printf("yk knon---------------------KN闭合\n");
        printf("yk knoff--------------------KN断开\n");
        printf("yk byon---------------------备用继电器闭合\n");
        printf("yk byoff--------------------备用继电器断开\n");
        printf("yk jyk1on-------------------绝缘检测K1断开\n");
        printf("yk jyk1off------------------绝缘检测K1闭合\n");
        printf("yk jyk2on-------------------绝缘检测K2断开\n");
        printf("yk jyk2off------------------绝缘检测K2闭合\n");
        printf("yk jyk3on-------------------绝缘检测K3断开\n");
        printf("yk jyk3off------------------绝缘检测K3闭合\n");
        printf("yk jyk5on-------------------绝缘检测K5断开\n");
        printf("yk jyk5off------------------绝缘检测K5闭合\n");
        printf("yk fyon-------------------绝缘检测K5闭合\n");
        printf("yk fyoff------------------绝缘检测K5断开\n");
        printf("yk lighton-------------------开灯\n");
        printf("yk lightoff------------------关灯\n");
        printf("yk paraon--------------------并联继电器闭合\n");
        printf("yk paraoff-------------------并联继电器断开\n");
    }
}

SHELL_CMD(
    yk, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_yk,
    "yk [遥控命令] \r\t\t\t\t 遥控测试\n");

static void do_xfTest(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    uint32 time = 0;
    if (argc == 2)
    {
        sscanf(argv[1], "%d", &time);
        RELEASE_ON;
        printf("泄放投入\n");
        taskDelay(time * 1000);
        RELEASE_OFF;
        printf("泄放断开\n");
    }
    else
    {
        printf("泄放测试格式错误\n");
    }
}
SHELL_CMD(
    xfTest, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_xfTest,
    "xfTest [时间] \r\t\t\t\t 遥泄放测试\n");

static void do_byTest(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    uint32 time = 0, interTime = 0;
    if (argc == 3)
    {
        sscanf(argv[1], "%d", &interTime);
        sscanf(argv[2], "%d", &time);
        for (int i = 0; i < time * 1000;)
        {
            BY_ON;
            taskDelay(interTime / 2);
            BY_OFF;
            taskDelay(interTime / 2);
            i += interTime;
        }
    }
    else
    {
        printf("备用端口测试格式错误\n");
    }
}
SHELL_CMD(
    byTest, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_byTest,
    "byTest [周期时间ms][总运行时间S] \r\t\t\t\t 备用端口测试\n");
static void do_imd(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    // Imd_Ctrl();
    uint8 cmd, act, caltype;

    if (argc >= 4)
    {
        cmd = argv[1][0] - '0';

        act = argv[2][0] - '0';

        caltype = argv[3][0] - '0';
    }
    else
    {
        printf("参数个数不够\n");
        return;
    }

    if ((act > IMDCON_KEOFF_KPOFF_KNON) || (cmd > 5) || (argc < 4))
    {
        printf("imd命令格式错误\n");
        printf("%s", cmdtp->usage);
    }
    else
    {
        Imd_Ctrl_CalibrateCon(cmd, act, caltype);
    }
}

SHELL_CMD(
    imd, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_imd,
    "imd [cmd][act][caltype]   \r\t\t\t\t 绝缘检测 cmd = 0~5 act = 0~6.act = 0 关闭三个继电器;\n "
    "                  \r\t\t\t\t cmd = 0~5 根据act打开对应开关,测量数据并打印,计算绝缘,自动完成绝缘测量,外部绝缘使能.\n"
    "                  \r\t\t\t\t cmd = 1 时act表示打开对应继电器;\n"
    "                  \r\t\t\t\t cmd = 2 根据打开继电器测量数据;\n"
    "                  \r\t\t\t\t cmd = 5 时为act=1表示选择外部绝缘，为act=0表示内部绝缘;\n"
    "                  \r\t\t\t\t cmd = 1,caltype表示返回值类型caltype=0平均值,\n"
    "                  \r\t\t\t\t cmd = 1,caltype=1最小值,caltype=2是最大值,caltype=3是中值;\n"
    "                  \r\t\t\t\t act = 0 KE=ON,KP=OFF,KN=OFF;act = 1 KE=ON,KP=ON,KN=ON ;\n"
    "                  \r\t\t\t\t act = 2 KE=ON,KP=OFF,KN=ON ;act = 3 KE=ON,KP=ON,KN=OFF;\n"
    "                  \r\t\t\t\t act = 4 KE=OFF,KP=ON,KN=ON ;act = 5 KE=OFF,KP=ON,KN=OFF;\n"
    "                  \r\t\t\t\t act = 6 KE=OFF,KP=OFF;KN=ON;cmd = 0 KE=OFF,KP=OFF,KN=OFF.\n");

uint8 productTestFlag(uint8 wr, uint8 flag)
{
    static uint8 producttestflag = 0;

    if (wr)
    {
        producttestflag = flag;
    }

    return producttestflag;
}

static void do_productTest(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    productTestFlag(TRUE, TRUE);
    printf("product test !\n");
}

SHELL_CMD(
    productTest, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_productTest,
    "productTest    \r\t\t\t\t 生产测试命令 1启动生产模式，0正常模式\n ");

static void do_BmsOverTest(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{

    const BMS_RECV BMS_RECV_TABLE[] =
        {
            {BMS_PGN_CHM, 0x26},
            {BMS_PGN_BHM, 0x27},
            {BMS_PGN_BRM, 0x20},
            {BMS_PGN_BCP, 0x06},
            {BMS_PGN_BRO, 0x09},
            {BMS_PGN_BCL, 0x10},
            {BMS_PGN_BCS, 0x11},
            {BMS_PGN_BSM, 0x13},
            {BMS_PGN_BMV, 0x15},
            {BMS_PGN_BMT, 0x16},
            {BMS_PGN_BSP, 0x17},
            {BMS_PGN_BST, 0x19},
            {BMS_PGN_BSD, 0x1c},
            {BMS_PGN_BEM, 0x1e},
            {BMS_PGN_BRO_AA, 0xAA},
            {BMS_PGN_CRM, 0x01},
            {BMS_PGN_BRM, 0x02},
            {BMS_PGN_CTS, 0x07},
            {BMS_PGN_CML, 0x08},
            {BMS_PGN_CRO, 0x0a},
            {BMS_PGN_CCS, 0x12},
            {BMS_PGN_CST, 0x1a},
            {BMS_PGN_CSD, 0x1d},
            {BMS_PGN_CEM, 0x1f},
        };
    BMS_RECV *bms_over = NULL;
    uint32 pgn = 0, enable = 0;
    if (argc >= 2)
    {
        sscanf(argv[1], "%x", &pgn);
        sscanf(argv[2], "%d", &enable);
        for (int i = 0; i < ARRAY_SIZE(BMS_RECV_TABLE); i++)
        {
            bms_over = &BMS_RECV_TABLE[i];
            if (bms_over->index == pgn)
            {
                Set_bms_over_pgn(bms_over->pgn);
                Set_bms_enable(enable);
                printf("=======[pgn : %x]====[enable : %d]===============\n", bms_over->pgn, enable);
            }
        }
    }
    else
    {
        Set_bms_enable(FALSE);
        printf("参数不够!!!!\n");
    }
}

SHELL_CMD(
    bmsover, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_BmsOverTest,
    "do_BmsOverTest    \r\t\t\t\t 生产测试命令 1启动生产模式，0正常模式\n ");

static void do_Fault_Test(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{

    uint32 mask = 0, enable = 0;
    if (argc >= 2)
    {
        sscanf(argv[1], "%d", &mask);
        sscanf(argv[2], "%d", &enable);

        if (eErrType_BatteryReverseConnect == mask)
        {
            Set_ReverseConnect(enable);
        }
        else if (eErrType_K1K2OutsideVolErr1 == mask)
        {
            Set_OutSideVol(enable);
        }
        else if (eErrType_SStartTimeOut == mask)
        {
            Set_TimeOut(enable);
        }
        else
        {
            Set_FaultMask(mask, enable);
        }
    }
}

SHELL_CMD(
    fault, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_Fault_Test,
    "do_Fault_Test    \r\t\t\t\t fault[mask][enable],mask : 对应故障编号(520 电池反接 544 外侧大于10V 570 软启动超时) "
    "enable: 0不使能 1使能  \n ");

static void do_BmsOverClrTest(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{

    const BMS_RECV BMS_RECV_TABLE[] =
        {
            {BMS_PGN_CHM, 0x26},
            {BMS_PGN_BHM, 0x27},
            {BMS_PGN_BRM, 0x20},
            {BMS_PGN_BCP, 0x06},
            {BMS_PGN_BRO, 0x09},
            {BMS_PGN_BCL, 0x10},
            {BMS_PGN_BCS, 0x11},
            {BMS_PGN_BSM, 0x13},
            {BMS_PGN_BMV, 0x15},
            {BMS_PGN_BMT, 0x16},
            {BMS_PGN_BSP, 0x17},
            {BMS_PGN_BST, 0x19},
            {BMS_PGN_BSD, 0x1c},
            {BMS_PGN_BEM, 0x1e},
            {BMS_PGN_BRO_AA, 0xAA},
            {BMS_PGN_CRM, 0x01},
            {BMS_PGN_CTS, 0x07},
            {BMS_PGN_CML, 0x08},
            {BMS_PGN_CRO, 0x0a},
            {BMS_PGN_CCS, 0x12},
            {BMS_PGN_CST, 0x1a},
            {BMS_PGN_CSD, 0x1d},
            {BMS_PGN_CEM, 0x1f},
        };
    BMS_RECV *bms_over = NULL;

    for (int i = 0; i < ARRAY_SIZE(BMS_RECV_TABLE); i++)
    {
        bms_over = &BMS_RECV_TABLE[i];
        Set_bms_enable(FALSE);
    }
}

SHELL_CMD(
    bmsoverclr, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_BmsOverClrTest,
    "do_BmsOverClrTest    \r\t\t\t\t 生产测试命令 1启动生产模式，0正常模式\n ");
extern void Imd_Relay_Act(uint8 act);
extern bool Imd_Get_VpVn(uint8 act, uint8 rettype);
extern void Imd_Ctrl_CalculateUnVol(void);

/**
 *  绝缘电阻检测 reg
 */
static void KpKnKeTest(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    /*
     * 1,闭合继电器;
     * 2,采集电压值;
     * */
#if 1
    uint8 flag = 0;
    printf("\n");
    printf("绝缘电阻测量中，请稍后...!\n\n");

    printf("ImdRegTest---闭合---KEON_KPOFF_KNOFF\n");

    Imd_Relay_Act(IMDCON_KEON_KPOFF_KNOFF);

    taskDelay(1500);

    while (1)
    {
        //        taskDelay(1);

        if (Imd_Get_VpVn(IMDCON_KEON_KPOFF_KNOFF, ADFILTER_RET_AVG))
        {
            break;
        }
    }

    printf("\n");
    printf("ImdRegTest---闭合---KEON_KPON_KNON\n");

    Imd_Relay_Act(IMDCON_KEON_KPON_KNON);

    taskDelay(1500);

    while (1)
    {
        //        taskDelay(1);

        if (Imd_Get_VpVn(IMDCON_KEON_KPON_KNON, ADFILTER_RET_AVG))
        {
            break;
        }
    }

    printf("\n");
    printf("ImdRegTest---闭合---KEON_KPOFF_KNON\n");

    Imd_Relay_Act(IMDCON_KEON_KPOFF_KNON);

    taskDelay(1500);

    while (1)
    {
        //        taskDelay(1);

        if (Imd_Get_VpVn(IMDCON_KEON_KPOFF_KNON, ADFILTER_RET_AVG))
        {
            break;
        }
    }

    printf("\n");
    printf("ImdRegTest---闭合---KEON_KPON_KNOFF\n");

    Imd_Relay_Act(IMDCON_KEON_KPON_KNOFF);

    taskDelay(1500);

    while (1)
    {
        //        taskDelay(1);

        if (Imd_Get_VpVn(IMDCON_KEON_KPON_KNOFF, ADFILTER_RET_AVG))
        {
            break;
        }
    }

    printf("\n");
    //    Imd_Ctrl_CalculateUnVol(); //计算打印电阻
    Imd_Ctrl_CalculateUnVolNew();
#else
    Imd_Start();
#endif
}

SHELL_CMD(imdresTest, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)KpKnKeTest,
          "imdresTest  \r\t\t\t\t 绝缘电阻检测\n");

static void do_curTest(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    if (argc == 2)
    {
    }
    else
    {
        printf("命令错误 !\n");
    }
}

SHELL_CMD(
    curTest, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_curTest,
    "curTest    \r\t\t\t\t \n ");

static void do_pauseTest(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    uint8 flag = argv[1][0] - '0';
    if ((argc == 2) && (flag <= 1))
    {
        chargePauseFlg(TRUE, flag);
    }
    else
    {
        printf("充电暂停命令错误 !\n");
    }
}

SHELL_CMD(
    pauseTest, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_pauseTest,
    "pauseTest   \r\t\t\t\t 暂停充电测试 1充电，0暂停充电\n ");

void Set_ChargePower(uint32 dat)
{
    uint8 data[4] = {0};
    printf("接收到了充电输出功率   %d\n", dat);

    Uint32ToFourUint8(data, dat);
    Set_Para(eParaFmt_ChargePower, data);
}

extern TCU_CTRL tcuCtrl;
static void do_pwr(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    uint32 ctrl = 0;
    uint32 pwr = 0;
    CHARGE_PARA strChargePara;
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    pTcuCtrl->powerCtrl = 0x00;
    pTcuCtrl->powerValue = 2400;
    pTcuCtrl->powerCtrlResult = 0x01;
    pTcuCtrl->powerCtrlFailReason = 0x01;
    if (argc > 1)
    {
        sscanf(argv[1], "%d", &ctrl);
        if ((ctrl == 0x01) || (ctrl == 0x02))
        {
            pTcuCtrl->powerCtrl = ctrl;
        }
        else if (ctrl == 0x03)
        {
            if (argc > 2)
            {
                sscanf(argv[2], "%d", &pwr);
                Set_ChargePower(pwr);
                Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
                printf("ctrl = %d,strChargePara.chargePower = %d", ctrl, TwoUint8ToUint16(strChargePara.chargePower));
                return;
            }
        }
    }
    if (argc > 2)
    {
        sscanf(argv[2], "%d", &pwr);
        pTcuCtrl->powerValue = pwr;
    }
    if ((pTcuCtrl->powerCtrl == 0x01) || (pTcuCtrl->powerCtrl == 0x02))
    {
        pTcuCtrl->powerCtrlResult = 0x00;
        pTcuCtrl->powerCtrlFailReason = 0x00;
        printf("powerCtrl =%d,powerValue = %d\n", pTcuCtrl->powerCtrl, pTcuCtrl->powerValue);
    }
    return;
}

SHELL_CMD(
    pwr, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_pwr,
    "pwr F D \r\t\t\t\t 设定功率调节 F-1:绝对值,2百分比  D-0.1kW/bit,3设定CCU输出功率\n");

static void do_clrs(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    Set_WorkState(CCU_WORK_STATE_FREE);
    Set_ChargeActFlag(eActFlag_On);
    Set_BmsStage(BMS_STAGE_FREE);
    return;
}

SHELL_CMD(
    clrs, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_clrs,
    "clrs  \r\t\t\t\t 手动清状态\n");

extern SEND_FLAG Get_BmsSendFlg(uint32 pgn);
extern RECV_FLAG Get_BmsRecvFlag(uint32 pgn);
static void do_bmsf(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{

    printf("发送状态:\n");

    printf("BMS_PGN_CHM:    %d\n", Get_BmsSendFlg(0x002600)); /**< 充电机握手帧 */
    printf("BMS_PGN_CRM:    %d\n", Get_BmsSendFlg(0x000100)); /**< 充电机辨识帧 */
    printf("BMS_PGN_CTS:    %d\n", Get_BmsSendFlg(0x000700)); /**< 充电机发送时间同步信息 */
    printf("BMS_PGN_CML:    %d\n", Get_BmsSendFlg(0x000800)); /**< 充电机最大输出能力 */
    printf("BMS_PGN_CRO:    %d\n", Get_BmsSendFlg(0x000A00)); /**< 充电机输出准备就绪状态 */
    printf("BMS_PGN_CCS:    %d\n", Get_BmsSendFlg(0x001200)); /**< 充电机充电状态 */
    printf("BMS_PGN_CST:    %d\n", Get_BmsSendFlg(0x001A00)); /**< 充电机中止充电 */
    printf("BMS_PGN_CEM:    %d\n", Get_BmsSendFlg(0x001F00)); /**< 充电机错误报文 */
    printf("BMS_PGN_CSD:    %d\n", Get_BmsSendFlg(0x001D00)); /**< 充电机统计数据 */

    printf("接收状态:\n\n");
    printf("BMS_PGN_BHM:    %d\n", Get_BmsRecvFlag(0x002700)); /**< 车辆握手帧 */
    printf("BMS_PGN_BRM:    %d\n", Get_BmsRecvFlag(0x000200)); /**< 车辆和BMS辨识帧 */
    printf("BMS_PGN_BCP:    %d\n", Get_BmsRecvFlag(0x000600)); /**< 动力蓄电池充电参数 */
    printf("BMS_PGN_BRO:    %d\n", Get_BmsRecvFlag(0x000900)); /**< 电池充电准备就绪状态 */
    printf("BMS_PGN_BRO_AA: %d\n", Get_BmsRecvFlag(0x0009AA)); /**< 车辆已准备就绪好(特殊)！*/
    printf("BMS_PGN_BCL:    %d\n", Get_BmsRecvFlag(0x001000)); /**< 电池充电需求 */
    printf("BMS_PGN_BCS:    %d\n", Get_BmsRecvFlag(0x001100)); /**< 电池充电总状态 */
    printf("BMS_PGN_BSM:    %d\n", Get_BmsRecvFlag(0x001300)); /**< 动力蓄电池状态信息 */
    printf("BMS_PGN_BMV:    %d\n", Get_BmsRecvFlag(0x001500)); /**< 单体动力蓄电池电压 */
    printf("BMS_PGN_BMT:    %d\n", Get_BmsRecvFlag(0x001600)); /**< 单体动力蓄电池温度 */
    printf("BMS_PGN_BSP:    %d\n", Get_BmsRecvFlag(0x001700)); /**< 动力蓄电池预留报文 */
    printf("BMS_PGN_BST:    %d\n", Get_BmsRecvFlag(0x001900)); /**< BMS中止充电 */
    printf("BMS_PGN_BSD:    %d\n", Get_BmsRecvFlag(0x001C00)); /**< BMS统计数据 */
    printf("BMS_PGN_BEM:    %d\n", Get_BmsRecvFlag(0x001E00)); /**< BMS错误报文 */

    return;
}

SHELL_CMD(
    bmsf, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_bmsf,
    "bmsf  \r\t\t\t\t BMS收发状态\n");

static void do_pcurr(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    printf("Get_PcuCurr: %d\n", Get_PcuCurr());
    return;
}

SHELL_CMD(
    pcurr, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_pcurr,
    "pcurr  \r\t\t\t\t PCU输出电流\n");

static void do_platRst(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    DELAY_RST_ON;
    printf("Get_PcuCurr: %d\n", Get_PcuCurr());
    return;
}

SHELL_CMD(
    plat_rst, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_platRst,
    "plat_rst  \r\t\t\t\t 平台整机复位\n");
extern void Set_TcuProtocolVer(uint16 tcuProtocolVer);
static void do_TcuProtocolVer(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    uint16 TcuProtocolVer = 0;
    if (argc > 1)
    {
        sscanf((const char *)argv[1], "%x", &TcuProtocolVer);
    }

    Set_TcuProtocolVer(TcuProtocolVer);
}

SHELL_CMD(
    do_tcuver, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_TcuProtocolVer,
    "do_TcuProtocolVer  \r\t\t\t\t 设置TCU版本号 0x0121\n");

extern int master_request_simple(uint8_t slave_addr,
                                 uint8_t function_code,
                                 uint16_t reg_addr,
                                 uint16_t quantity,
                                 void *data_ptr,
                                 modbus_cmd_prio_t prio,
                                 int timeout_ms,
                                 void *user_data);
/**
 * @brief  手动发送 Modbus 命令的 Shell 实现函数 (最终版)
 * @details
 *      一个通用的调试工具，可以通过 shell 发送标准的 Modbus 读/写命令，并指定优先级。
 *      命令格式: lq_send <从机地址> <功能码> <寄存器地址> <数据/数量> <优先级>
 *      所有数值参数均为十六进制。
 *      优先级: 1 (高), 2 (普通), 3 (低)
 */
void do_lq_manual_send(cmd_tbl_t *cmdtp, uint32_t argc, const char *argv[])
{
    // 使用静态缓冲区替代动态分配，避免在调用超时后出现 use-after-free 问题
    static uint8_t manual_read_coil_buf[(100 + 7) / 8];
    static uint16_t manual_read_reg_buf[125];
    uint32_t slave_addr = 0;
    uint32_t func_code = 0;
    uint32_t reg_addr = 0;
    uint32_t value = 0;
    uint32_t prio = 0;

    if (argc < 6)
    {
        printf("Usage: %s <slave_hex> <func_hex> <addr_hex> <val_hex> <prio_hex>\n", argv[0]);
        printf("  Priority: 1=HIGH, 2=NORMAL, 3=LOW\n");
        printf("  Write Example (Slave 0x30, Func 0x06, Addr 0x0A, Val 0xFF00, Prio HIGH):\n");
        printf("    lq_send 30 6 a ff00 1\n");
        printf("  Read Example (Slave 0x30, Func 0x03, Addr 0x1000, Qty 5, Prio LOW):\n");
        printf("    lq_send 30 3 1000 5 3\n");
        return;
    }

    // 解析所有参数
    sscanf(argv[1], "%x", &slave_addr);
    sscanf(argv[2], "%x", &func_code);
    sscanf(argv[3], "%x", &reg_addr);
    sscanf(argv[4], "%x", &value);
    sscanf(argv[5], "%x", &prio); // 解析优先级

    // 校验优先级参数的有效性
    if (prio < MODBUS_CMD_PRIO_HIGH || prio > MODBUS_CMD_PRIO_LOW)
    {
        printf("Error: Invalid priority '%u'. Must be 1 (HIGH), 2 (NORMAL), or 3 (LOW).\n", (unsigned int)prio);
        return;
    }

    printf("Executing: Slave=0x%X, Func=0x%02X, Addr=0x%04X, Val/Qty=0x%X, Prio=%u\n",
           (unsigned int)slave_addr, (unsigned int)func_code, (unsigned int)reg_addr, (unsigned int)value, (unsigned int)prio);

    int rc = -1;
    modbus_cmd_prio_t cmd_prio = (modbus_cmd_prio_t)prio;

    // 根据功能码分别处理
    switch (func_code)
    {
    case AGILE_MODBUS_FC_WRITE_SINGLE_COIL:
    case AGILE_MODBUS_FC_WRITE_SINGLE_REGISTER:
    {
        uint16_t data_to_write = (uint16_t)value;
        rc = master_request_simple((uint8_t)slave_addr,
                                   (uint8_t)func_code,
                                   (uint16_t)reg_addr,
                                   1,
                                   &data_to_write,
                                   cmd_prio, // 使用指定的优先级
                                   2000, NULL);
        break;
    }

    case AGILE_MODBUS_FC_READ_COILS:
    case AGILE_MODBUS_FC_READ_DISCRETE_INPUTS:
    {
        uint16_t quantity = (uint16_t)value;
        if (quantity == 0 || quantity > 2000)
        {
            printf("Error: Invalid quantity for coil read: %u\n", quantity);
            return;
        }
        uint8_t *read_buffer = manual_read_coil_buf;
        memset(read_buffer, 0, (quantity + 7) / 8);

        rc = master_request_simple((uint8_t)slave_addr, (uint8_t)func_code, (uint16_t)reg_addr,
                                   quantity, read_buffer, cmd_prio, 2000, NULL);

        if (rc == MODBUS_RT_EOK)
        {
            printf("Read coils successful. Data (Hex):\n");
            for (int i = 0; i < (quantity + 7) / 8; i++)
            {
                printf("%02X ", read_buffer[i]);
            }
            printf("\n");
        }
        break;
    }

    case AGILE_MODBUS_FC_READ_HOLDING_REGISTERS:
    case AGILE_MODBUS_FC_READ_INPUT_REGISTERS:
    {
        uint16_t quantity = (uint16_t)value;
        if (quantity == 0 || quantity > 125)
        {
            printf("Error: Invalid quantity for register read: %u\n", quantity);
            return;
        }
        uint16_t *read_buffer = manual_read_reg_buf;
        memset(read_buffer, 0, quantity * sizeof(uint16_t));

        rc = master_request_simple((uint8_t)slave_addr, (uint8_t)func_code, (uint16_t)reg_addr,
                                   quantity, read_buffer, cmd_prio, 2000, NULL);

        if (rc == MODBUS_RT_EOK)
        {
            printf("Read registers successful. Data:\n");
            for (int i = 0; i < quantity; i++)
            {
                printf("  Reg[0x%04X] = 0x%04X (%d)\n", (unsigned int)(reg_addr + i), read_buffer[i], read_buffer[i]);
            }
        }
        break;
    }

    default:
        printf("Error: Unsupported function code for this simple tool: 0x%02X\n", (unsigned int)func_code);
        return;
    }

    // 最终结果反馈
    if (rc == MODBUS_RT_EOK)
    {
        printf("Command processed successfully.\n");
    }
    else
    {
        printf("Command failed with error code: %d\n", rc);
    }
}

// 使用 SHELL_CMD 宏将函数注册到 shell
SHELL_CMD(
    lq_send, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_lq_manual_send,
    "lq_send <slave> <func> <addr> <val/qty> <prio>\n"
    "\t- Manually send a Modbus command with specified priority.\n"
    "\t- All values are in hex. Priority is 1 (HIGH), 2 (NORMAL), 3 (LOW).\n");
/*----------------------------testYX.c--------------------------------*/
