/**
 ******************************************************************************
 * @file      ccuChargeMain.c
 * @brief     C Source file of ccuChargeMain.c.
 * @details   This file including all API functions's
 *            implement of ccuChargeMain.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <string.h>
#include <taskLib.h>
#include <dmnLib.h>
#include <stdlib.h>
#include <stdio.h>
#include <sxlib.h>
#include <dccfg.h>
#include <ttylib.h>
#include <trace.h>
#include <maths.h>
#include <ccu/bsn/io.h>
#include "ccuChargeMain.h"
#include <types.h>
#include <bms.h>
#include <ccu/bsn/io.h>
#include <ccu/bsn/sample.h>
#include <ccu\bsn\deviceState.h>
#include <ccu\para\para.h>
#include <ccu\pcu\pcuMain.h>
#include <ccu\tcu\tcuMain.h>
#include <ccu\hmi\dwMain.h>
#include <test.h>
#include "imd.h"
#include "card.h"
#include "ccu/ccuLog/ccuLog.h"
#include "../ccu/charge/LiquidCoolingMain.h"
#include "ccu/charge/imd.h"
#include "ccu/bms/bmsMain.h"
#include "ccu/pcu/pcuMain.h"
#include "ccu/tcu/tcuMain.h"
/*-----------------------------------------------------------------------------
  Section: Type Definitions
----------------------------------------------------------------------------*/
typedef enum ENUM_CHARGE_STOP_TYPE
{
    eChargeStop_Abnormal = 2, /**< 紧急停机 */
    eChargeStop_Normal = 1,   /**< 正常停机 */
} CHARGE_STOP_TYPE;
/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
#define CHARGE_PAUSE (10 * 60 * sysClkRateGet())

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/

CHARGE_CTRL chargeCtrl;

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/

static uint8 s_WorkState = CCU_WORK_STATE_FREE; /**< 工作状态 */
static uint8 s_ChargeMode = CHARGE_MODE_AUTO;   /**< 充电模式 */
static uint8 s_ChargeActFlag = eActFlag_Null;   /**< 启停标记 */
static uint32 s_ManualVol;                      /**< 手动充电电压 */
static uint32 s_ManualCur;                      /**< 手动充电电流 */
static uint8 ReverseConnect = 0;
static uint8 OutSideVol = 0;
static uint8 TimeOut = 0;
static int fd_4851 = 0;
static uint32 DEVICE_STATE_FAULT0_31 = 0;
static uint32 DEVICE_STATE_FAULT32_63 = 0;
static uint32 DEVICE_STATE_FAULT64_95 = 0;
static uint32 DEVICE_STATE_MAJOR_FAULT0_32 = 0;
static uint32 DEVICE_STATE_ALARM0_32 = 0;
static uint16 K1K2OutsideVol = 0;
static uint8 ElecClock = 0;
static uint8 K1K2Outside = 0;
static uint8 insulationCheckInterval = 0;
static EVCCID_SIM_STATE s_EvccidSimState = SIM_NONE;
static bool_e s_evccid_acquired = FALSE;
/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
extern void Set_BmsRecvTimerEnable(uint32 pgn, TIMER_ENABLE enableFlg);

/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
static uint32 Get_ImdTime(void)
{
    uint32 val = 0;
    CHARGE_PARA strChargePara;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
    val = FourUint8ToUint32(strChargePara.imdchecktime);
    return ((0xffffffff == val) ? 3000 : val);
}

uint8 Get_insulationCheckInterval(void)
{
    return insulationCheckInterval;
}

void Set_insulationCheckInterval(uint8 Interval)
{
    insulationCheckInterval = Interval;
}
/**
 ******************************************************************************
 * @brief       Get_K1K2Current
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      当前合法电流
 * @details
 *              规则：
 *              自身采样值大于通道中模块遥测累计值，并且误差大于5%，
 *              则使用通道中模块遥测累计值
 *
 * @note
 *              计算单位mA
 *
 ******************************************************************************
 */
uint32 Get_K1K2Current(void)
{
    uint32 lineCurr = Get_LineCurr();
    uint32 pcuCurr = Get_PcuCurr() * 10;

    // 如果自己采样大于1000A，直接认为异常值，取通道电流
    if (lineCurr > 1000000)
        lineCurr = pcuCurr;

    if ((lineCurr > 0) && (lineCurr > pcuCurr + 1000))
    {
        // 误差大于本身的1.5%
        if ((1000 * (lineCurr - pcuCurr)) > 15 * lineCurr && (1000 * (lineCurr - pcuCurr)) < 80 * lineCurr)
        {
            lineCurr = pcuCurr;
        }
    }

    // trace(TR_DEBUG, "line curr: %d, pcu curr: %d\n", lineCurr, pcuCurr);

    return lineCurr;
}
uint8 Get_WorkState(void)
{
    return s_WorkState;
}

void Set_WorkState(uint8 workState)
{
    if (s_WorkState != workState)
    {
        trace(TR_CHARGE, "CHARGE阶段切换  当前阶段:%d   前一阶段:%d\n", workState,
              s_WorkState);
        s_WorkState = workState;
    }
    return;
}
void reset_chargeActFlag(uint8 step)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    switch (step)
    {
    case 1:
        pChargeCtrl->release01ActTimer = 0;
        break;
    case 2:
        pChargeCtrl->release02ActTimer = 0;
        break;
    default:
        break;
    }
}
void Set_ChargeActFlag(ACT_FLAG actFlag)
{
    s_ChargeActFlag = actFlag;
    return;
}

ACT_FLAG Get_ChargeActFlag(void)
{
    return s_ChargeActFlag;
}

void Set_StopReason(uint32 reason)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    if (0 != pChargeCtrl->stopReason)
    {
        return;
    }

    pChargeCtrl->stopReason = reason;
}

void Set_StopSrc(CHARGE_STOP_SRC src)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    if (0 != pChargeCtrl->stopSrc)
    {
        return;
    }

    pChargeCtrl->stopSrc = src;
}

CHARGE_STOP_SRC Get_StopSrc(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    return pChargeCtrl->stopSrc;
}

uint32 Get_StopReason(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    return pChargeCtrl->stopReason;
}

void Set_ChargeMode(uint8 mode)
{
    s_ChargeMode = mode;
}

uint8 Get_ChargeMode(void)
{
    return s_ChargeMode;
}

void Set_ManualVol(uint32 vol)
{
    s_ManualVol = vol;
}

uint32 Get_ManualVol(void)
{
    return s_ManualVol;
}

void Set_ManualCur(uint32 cur)
{
    s_ManualCur = cur;
}

uint32 Get_ManualCur(void)
{
    return s_ManualCur;
}

uint16 Get_K1K2OutsideVol1(void)
{
    return K1K2OutsideVol;
}

void Set_K1K2OutsideVol1(uint16 Vol)
{
    K1K2OutsideVol = Vol;
}

uint8 Get_K1K2Outside(void)
{
    return K1K2Outside;
}

void Set_K1K2Outside(uint8 K1K2Out)
{
    K1K2Outside = K1K2Out;
}

uint8 Get_ElecClock(void)
{
    return ElecClock;
}

void Set_ElecClock(uint8 Clock)
{
    ElecClock = Clock;
}
bool_e Get_Release01SuccFlag(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    return pChargeCtrl->release01SuccFlag;
}
void Set_ImdStartFlag(bool_e flag)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    pChargeCtrl->imdStartFlag = flag;
}
bool_e Get_ImdStartFlag(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    return pChargeCtrl->imdStartFlag;
}

bool_e Get_ImdSuccFlag(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    return pChargeCtrl->imdFinishFlag;
}

void Set_ImdSuccFlag(bool_e flag)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    pChargeCtrl->imdFinishFlag = flag;
}

bool_e Get_PreChargeSuccFlag(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    return pChargeCtrl->preChargeSuccFlag;
}

void Set_PreChargeSuccFlag(uint8 flag)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    pChargeCtrl->preChargeSuccFlag = flag;
}

bool_e Get_StopFinishFlag(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    return pChargeCtrl->stopFinishFlag;
}

void Set_StopFinishFlag(uint8 flag)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    pChargeCtrl->stopFinishFlag = flag;
}

bool_e Get_Release02FinishFlag(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    return pChargeCtrl->release02FinishFlag;
}

bool_e Get_K3K4FinishFlag(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    return pChargeCtrl->k3k4OffFinishFlag;
}

uint16 Get_CcuChargeTime(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    return (pChargeCtrl->chargeTime + 30) / 60;
}

uint16 Get_CcuChargeEnerge(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    return (uint16)pChargeCtrl->chargePow;
}

uint16 Get_ChargeTime(void)
{
    return Get_TcuChargeTime() ? Get_TcuChargeTime() : Get_CcuChargeTime();
}

uint16 Get_ChargeEnerge(void)
{
    return Get_TcuChargeEnerge() ? Get_TcuChargeEnerge() : Get_CcuChargeEnerge();
}

void Set_ReverseConnect(uint8 mode)
{
    ReverseConnect = mode;
}

uint8 Get_ReverseConnect(void)
{
    return ReverseConnect;
}

void Set_OutSideVol(uint8 mode)
{
    OutSideVol = mode;
}

uint8 Get_OutSideVol(void)
{
    return OutSideVol;
}

void Set_TimeOut(uint8 mode)
{
    TimeOut = mode;
}

uint8 Get_TimeOut(void)
{
    return TimeOut;
}

void Set_FaultMask(PILE_ERROR_TYPE errType, bool e)
{

    switch ((errType >> 8))
    {
    case DEVICE_STATE_ALARM:
        SETBITS(DEVICE_STATE_ALARM0_32, (errType & 0x00ff), e);
        printf("======ALARM======errType :%d======DEVICE_STATE_ALARM0_32 bit======%x=============\n", (uint8)errType, DEVICE_STATE_ALARM0_32);
        break;
    case DEVICE_STATE_FAULT:
        if (0 == (errType & 0x00ff) >> 5)
        {
            SETBITS(DEVICE_STATE_FAULT0_31, (errType & 0x00ff) & 0x1F, e);
            printf("======FAULT======errType :%d=======DEVICE_STATE_FAULT0_31 bit:%x==================\n", (uint8)errType, DEVICE_STATE_FAULT0_31);
        }
        else if (1 == (errType & 0x00ff) >> 5)
        {
            SETBITS(DEVICE_STATE_FAULT32_63, (errType & 0x00ff) & 0x1F, e);
            printf("======FAULT======errType :%d=======DEVICE_STATE_FAULT32_63 bit:%x==================\n", (uint8)errType, DEVICE_STATE_FAULT32_63);
        }
        else if (2 == (errType & 0x00ff) >> 5)
        {
            SETBITS(DEVICE_STATE_FAULT64_95, (errType & 0x00ff) & 0x1F, e);
            printf("======FAULT======errType :%d=======DEVICE_STATE_FAULT32_63 bit:%x==================\n", (uint8)errType, DEVICE_STATE_FAULT32_63);
        }
        break;
    case DEVICE_STATE_MAJOR_FAULT:
        SETBITS(DEVICE_STATE_MAJOR_FAULT0_32, (errType & 0x00ff), e);
        printf("======FAULT======errType :%d=======DEVICE_STATE_MAJOR_FAULT0_32 bit:%x==================\n", (uint8)errType, DEVICE_STATE_MAJOR_FAULT0_32);
        break;
    }
}

uint8 Get_FaultMask(PILE_ERROR_TYPE errType)
{
    switch ((errType >> 8))
    {
    case DEVICE_STATE_ALARM:
        return BITS(DEVICE_STATE_ALARM0_32, (errType & 0x00ff));
        break;
    case DEVICE_STATE_FAULT:
        if (0 == (errType & 0x00ff) >> 5)
        {
            return BITS(DEVICE_STATE_FAULT0_31, (errType & 0x00ff) & 0x1F);
        }
        else if (1 == (errType & 0x00ff) >> 5)
        {
            return BITS(DEVICE_STATE_FAULT32_63, (errType & 0x00ff) & 0x1F);
        }
        else if (2 == (errType & 0x00ff) >> 5)
        {
            return BITS(DEVICE_STATE_FAULT64_95, (errType & 0x00ff) & 0x1F);
        }
        break;
    case DEVICE_STATE_MAJOR_FAULT:
        return BITS(DEVICE_STATE_MAJOR_FAULT0_32, (errType & 0x00ff));
        break;
    }
    return 0;
}
/**
 ******************************************************************************
 * @brief       充电开始时当前tick
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details     在检测到充电开始后，赋值当前tick
 * @note
 ******************************************************************************
 */
uint32 Get_ChargeStartTick(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    return pChargeCtrl->ChargeStartTick;
}

static uint16 Get_ValidVol(uint16 vol)
{
    CHARGE_PARA strChargePara;

    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    if (0 == vol)
    {
        //		return vol;
        return TwoUint8ToUint16(strChargePara.minOutputVoltage);
    }
    else if (vol > TwoUint8ToUint16(strChargePara.maxOutputVoltage))
    {
        return TwoUint8ToUint16(strChargePara.maxOutputVoltage);
    }
    else if (vol < TwoUint8ToUint16(strChargePara.minOutputVoltage))
    {
        return TwoUint8ToUint16(strChargePara.minOutputVoltage);
    }
    else
    {
        return vol;
    }
}

uint16 Get_DemondVol(void)
{
    BCL_DATA strBCL;
    BHM_DATA strBHM;
    BCP_DATA strBCP;
    uint16 Vol = 0;
    CHARGE_PARA strChargePara;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
    Get_BMS_Data(BMS_PGN_BCL, (void *)&strBCL);
    Get_BMS_Data(BMS_PGN_BHM, (void *)&strBHM);
    Get_BMS_Data(BMS_PGN_BCP, (void *)&strBCP);
    uint8 workstate = Get_WorkState();
    if (Get_CcuCfgParaEuropeEnable())
    {
        if ((CHARGE_MODE_MANUAL == Get_ChargeMode()))
        {
            Vol = Get_ManualVol() + (Get_K1K2Current() / 1000.0) * TwoUint8ToUint16(strChargePara.lineRes) / 1000.0; /**< 单位:0.1V/bit*/
            return Get_ValidVol(Vol);
        }
        else if ((workstate >= CCU_WORK_STATE_CONFIG) && (workstate <= CCU_WORK_STATE_CHARGE_PAUSE))
        {
            if (eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BCL))
            {
                //                return *(uint16 *)strBCP.highestVoltage;
                Vol = MIN(TwoUint8ToUint16(strChargePara.maxOutputVoltage),
                          TwoUint8ToUint16(strBHM.highlestTotalVoltage));
                return Get_ValidVol(Vol);
            }
            else
            {
                Vol = (TwoUint8ToUint16(strBCL.voltageDemand) + (Get_K1K2Current() / 1000.0) * TwoUint8ToUint16(strChargePara.lineRes) / 1000.0);
                return Get_ValidVol(Vol);
            }
        }
        else
        {
            // printf("%s--%d--%d\n",__FILE__,__LINE__,workstate);
            return 0;
        }
    }
    if ((CHARGE_MODE_MANUAL == Get_ChargeMode()))
    {

        Vol = Get_ManualVol() + (Get_K1K2Current() / 1000.0) * TwoUint8ToUint16(strChargePara.lineRes) / 1000.0; /**< 单位:0.1V/bit*/

        return Get_ValidVol(Vol);
    }

    if (CCU_WORK_STATE_READY == workstate)
    {
        Vol = 0;
    }
    else if (CCU_WORK_STATE_FREE == workstate ||
             CCU_WORK_STATE_SHAKE_HAND == workstate ||
             CCU_WORK_STATE_RELEASE_01 == workstate ||
             CCU_WORK_STATE_RECOGNIZE == workstate ||
             CCU_WORK_STATE_CONFIG == workstate ||
             CCU_WORK_STATE_CHARGE_STOP == workstate ||
             CCU_WORK_STATE_RELEASE_02 == workstate ||
             CCU_WORK_STATE_STOP_FINISH == workstate)
    {
        Vol = 0;
    }
    else if (CCU_WORK_STATE_CHARGE_PAUSE == workstate)
    {
        Vol = Get_K1K2OutsideVol() - 100;
    }
    else if (CCU_WORK_STATE_IMD == workstate)
    {
        if (Get_BMS_Ver() == 0xA5)
        {
            Vol = TwoUint8ToUint16(strChargePara.minOutputVoltage);
        }
        else
        {
            Vol = MIN(TwoUint8ToUint16(strChargePara.maxOutputVoltage),
                      TwoUint8ToUint16(strBHM.highlestTotalVoltage));
        }
    }
    else if (CCU_WORK_STATE_PRE_CHARGE == workstate)
    {
        // Vol = TwoUint8ToUint16(strBCP.totalVoltage) - 55;
        // 使用外侧电压 - 5.5V，使输出电压接近[-10, -1]V的中间点。
        Vol = Get_K1K2OutsideVol() - 55;
    }
    else if (CCU_WORK_STATE_CHARGING == workstate)
    {
        if ((enumAllowFlag_Forbid == Get_ChargePauseFlg()) || (enumAllowFlag_Forbid == chargeReconnectFlg(FALSE, FALSE))) /**<重连的时候关闭电流*/
        {
            Vol = Get_K1K2OutsideVol();
        }
        else
        {
            Vol = (TwoUint8ToUint16(strBCL.voltageDemand) + (Get_K1K2Current() / 1000.0) * TwoUint8ToUint16(strChargePara.lineRes) / 1000.0);
        }
    }

    return Get_ValidVol(Vol);
}

extern TCU_CTRL tcuCtrl;
static uint32 Get_ValidCur(uint32 cur)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    CHARGE_PARA strChargePara;
    uint32 dmnVol;       // 分辨率0.1V/bit
    uint32 pwr = 0xffff; // 分辨率0.1kW/bit
    uint32 maxCurr, outCurr;

    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    if (CCU_WORK_STATE_CHARGING == Get_WorkState())
    {
        dmnVol = MAX(TwoUint8ToUint16(strChargePara.minOutputVoltage), Get_K1K2OutsideVol()); // Get_K1K2OutsideVol() <= 3300 ? 3300 : Get_K1K2OutsideVol(); //Get_DemondVol();

        if ((pTcuCtrl->powerCtrlResult == 0x00) &&
            ((pTcuCtrl->powerCtrl == 0x01) || (pTcuCtrl->powerCtrl == 0x02) || (pTcuCtrl->powerCtrl == 0x10)) &&
            (dmnVol != 0))
        {
            // 功率调节有效情况下，考虑使用功率调节数据
            if (pTcuCtrl->powerCtrl == 0x01)
            {
                // 绝对值
                pwr = (pTcuCtrl->powerValue > 0) ? pTcuCtrl->powerValue : 0;
            }
            else if (pTcuCtrl->powerCtrl == 0x02)
            {
                // 百分比
                pwr = pTcuCtrl->powerValue * TwoUint8ToUint16(strChargePara.chargePower) / 100; // strChargePara.moduleCnt * TwoUint8ToUint16(strChargePara.singleModulePower) / 100;
            }
            else
            {
                pwr = (pTcuCtrl->powerValue_W > 0) ? pTcuCtrl->powerValue_W : 0;
            }
        }
        if ((pTcuCtrl->powerCtrl == 0x10))
        {
            pwr = MIN(TwoUint8ToUint16(strChargePara.chargePower) * 1000.0, pwr);

            outCurr = MIN(((float)pwr / dmnVol) * 1000, pTcuCtrl->powerValue * 100); /*pTcuCtrl->powerValue 电流*/

            maxCurr = ((float)TwoUint8ToUint16(strChargePara.maxPower) * 10000 / TwoUint8ToUint16(strChargePara.pwrVoltMin)) * 100;
        }
        else
        {
            pwr = MIN(TwoUint8ToUint16(strChargePara.chargePower), pwr);

            outCurr = ((float)pwr * 10000 / dmnVol) * 100;

            maxCurr = ((float)TwoUint8ToUint16(strChargePara.maxPower) * 10000 / TwoUint8ToUint16(strChargePara.pwrVoltMin)) * 100;
        }

        if (outCurr > maxCurr)
        {
            outCurr = maxCurr;
        }

        if (outCurr > FourUint8ToUint32(strChargePara.maxOutputCurrent))
        {
            outCurr = FourUint8ToUint32(strChargePara.maxOutputCurrent);
        }
    }
    else
    {
        outCurr = FourUint8ToUint32(strChargePara.minOutputCurrent);
    }

    if (cur < FourUint8ToUint32(strChargePara.minOutputCurrent))
    {
        return FourUint8ToUint32(strChargePara.minOutputCurrent); // 电流需求小于充电机最小输出电流时，按最小电流输出
    }
    else if (cur > outCurr)
    {
        return outCurr;
    }
    else
    {
        return cur;
    }
}

uint32 Get_DemondCur(void)
{
    uint8 workstate = Get_WorkState();
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    BCL_DATA strBCL;
    uint32 cur = 0;
    CHARGE_PARA strChargePara;
    OPERATE_PARA strOperatePara;
    Get_PilePara((void *)&strOperatePara, eParaType_OperatePara);
    static uint32 lastCur = 0;
    //    static uint32 lastTick = 0;
    Get_BMS_Data(BMS_PGN_BCL, (void *)&strBCL);
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    if (CCU_WORK_STATE_FREE == workstate ||
        CCU_WORK_STATE_READY == workstate ||
        CCU_WORK_STATE_SHAKE_HAND == workstate ||
        CCU_WORK_STATE_RELEASE_01 == workstate ||
        CCU_WORK_STATE_RECOGNIZE == workstate ||
        CCU_WORK_STATE_CONFIG == workstate ||
        CCU_WORK_STATE_PRE_CHARGE == workstate ||
        CCU_WORK_STATE_CHARGE_STOP == workstate ||
        CCU_WORK_STATE_RELEASE_02 == workstate ||
        CCU_WORK_STATE_STOP_FINISH == workstate)
    {
        cur = 0;
    }
    else if ((CCU_WORK_STATE_IMD == workstate) || (CCU_WORK_STATE_CHARGE_PAUSE == workstate))
    {
        //    	cur = FourUint8ToUint32(strChargePara.minOutputCurrent);                    /***绝缘阶段按最小电流输出*/
        cur = 0; /*充电暂停，绝缘无电流需求 20231017*/
    }
    else if (CCU_WORK_STATE_CHARGING == workstate)
    {
        if (CHARGE_MODE_AUTO == Get_ChargeMode())
        {
            //            trace(TR_CHARGE, "Get_ChargeReconnectFlg8 = %d \n",chargeReconnectFlg(FALSE,FALSE));
            if ((enumAllowFlag_Forbid == Get_ChargePauseFlg()) ||
                (enumAllowFlag_Forbid == chargeReconnectFlg(FALSE, FALSE))) /**<重连的时候关闭电流*/
                                                                            //            if(enumAllowFlag_Forbid == Get_ChargePauseFlg())
            {
                //        	    trace(TR_CHARGE, "Get_ChargeReconnectFlg5 = %d \n",chargeReconnectFlg(FALSE,FALSE));
                cur = 0;
            }
            else
            {
                uint32 factoryCode = FourUint8ToUint32(strOperatePara.factoryCode);
                if (HENAN == factoryCode)
                {
                    cur = Get_ValidCur(((4000 - TwoUint8ToUint16(strBCL.currentDemand)) - 100) * 100);
                }
                else
                {
                    cur = Get_ValidCur((4000 - TwoUint8ToUint16(strBCL.currentDemand)) * 100);
                }
            }
        }
        else
        {
            cur = Get_ValidCur(Get_ManualCur());
        }

        /*
         * 首次需求电流大于最小电流，发一半
         * 判实际输出电流是大于实发输出的90%时发实际需求.
         * 500ms内不用实际需求
         * */
        //        if (cur > lastCur)
        //        {
        //            if(abs(tickGet()-lastTick) < 1000)
        //            {
        //                if (Get_K1K2Current() * 100 < lastCur * 65)
        //                {
        //                    cur = lastCur;
        //                }
        //                else
        //                {
        //                    if ((cur - lastCur) / 2 > 12000)
        //                    {
        //                        cur -= (cur - lastCur) / 2;
        //                    }
        //                }
        //            }
        //        }
        //        else
        //        {
        //            lastTick = tickGet();
        //        }
    }
    lastCur = cur;

    return cur;
}
#include <shell.h>
static void do_ctrltest(cmd_tbl_t *cmdtp, uint32 argc, const char *argv[])
{
    uint32 cur = 4000;
    if (argc > 1)
    {
        sscanf(argv[1], "%i", &cur);
    }

    printf("Get_ValidCur = %d!\n", Get_ValidCur(cur));
}

SHELL_CMD(
    ctrltest, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)do_ctrltest,
    "ctrltest    \r\t\t\t\t 功率调节测试\n ");

uint16 Get_BatteryVol(void)
{
    BCS_DATA strBcs;
    BCP_DATA strBcp;
    uint32 vol = 0;
    uint8 workstate = Get_WorkState();
    if (CHARGE_MODE_AUTO == Get_ChargeMode())
    {
#if 1
        if (workstate == CCU_WORK_STATE_PRE_CHARGE)
        {
            Get_BMS_Data(BMS_PGN_BCP, (void *)&strBcp);
            vol = TwoUint8ToUint16(strBcp.totalVoltage);
            trace(TR_CHARGE, "Get_BatteryVol1 = %d\n", vol);
        }
        else if (workstate == CCU_WORK_STATE_IMD)
        {
            vol = Get_DemondVol();
            trace(TR_CHARGE, "Get_BatteryVol2 = %d\n", vol);
        }
        else if (workstate == CCU_WORK_STATE_CHARGE_PAUSE)
        {
            vol = Get_K1K2OutsideVol();
            trace(TR_CHARGE, "Get_BatteryVol32 = %d\n", vol);
        }
        else
        {
            if (eRecvFlag_Yes == Get_BMS_RecvFlg(BMS_PGN_BCS))
            {
                Get_BMS_Data(BMS_PGN_BCS, (void *)&strBcs);
                vol = TwoUint8ToUint16(strBcs.voltageMeasuredValue);
                trace(TR_CHARGE, "Get_BatteryVol3 = %d\n", vol);
            }
            else
            {
                vol = Get_K1K2OutsideVol();
                trace(TR_CHARGE, "Get_BatteryVol4 = %d\n", vol);
            }
        }
#else
        if (eRecvFlag_Yes == Get_BMS_RecvFlg(BMS_PGN_BCS))
        {
            Get_BMS_Data(BMS_PGN_BCS, (void *)&strBcs);
            vol = TwoUint8ToUint16(strBcs.voltageMeasuredValue);
            trace(TR_CHARGE, "Get_BatteryVol1 = %d\n", vol);
        }
        else if (eRecvFlag_Yes == Get_BMS_RecvFlg(BMS_PGN_BCP))
        {
            Get_BMS_Data(BMS_PGN_BCP, (void *)&strBcp);
            vol = TwoUint8ToUint16(strBcp.totalVoltage);
            trace(TR_CHARGE, "Get_BatteryVol2 = %d\n", vol);
        }
        else
        {
            vol = Get_K1K2OutsideVol();
            trace(TR_CHARGE, "Get_BatteryVol3 = %d\n", vol);
        }
#endif
    }
    else
    {
        vol = Get_DemondVol();
    }

    return vol;
}

/**************************************************************************************************/
/**
 ******************************************************************************
 * @brief      充电控制初始化
 * @param[in]   None
 * @param[out]  None
 * @retval
 *
 * @details    调用条件：
 *                   1.任务注册时初始化
 *                   2.接收到启动命令后，初始化
 *			         3.充电停止完成后，初始化
 * @note
 ******************************************************************************
 */
static void
Init_ChargeCtrl(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    memset(pChargeCtrl, 0x00, sizeof(CHARGE_CTRL));

#ifdef ELECK_CLOCK_CHECK_EN
    Set_ElecClockOperate(sElecClock_Unlock);
#else
    ELEC_LOCK_OFF;
#endif

    IMD_KE_OFF;

    IMD_KP_OFF;

    IMD_KN_OFF;

    K1_K2_OFF;
    trace(TR_CHARGE, "K1_K2_OFF1\n");

    K3_K4_OFF; // Init_ChargeCtrl

    Set_BMS_Ver(0x00);

    chargeReconnectFlg(TRUE, enumAllowFlag_Allow);

    trace(TR_CHARGE, "K3k4OFF1\n");

    RELEASE_OFF;

    s_evccid_acquired = FALSE;
    s_EvccidSimState = SIM_NONE;

    return;
}
static void Set_k3k4OffDelay(uint32 delay)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    if ((pChargeCtrl->k3k4OffDelay == 0) || (pChargeCtrl->k3k4OffDelay > delay))
    {
        pChargeCtrl->k3k4OffDelay = delay;
    }
}
void Set_k1k2OffDelay(uint32 delay)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    if ((pChargeCtrl->k1k2OffDelay == 0) || (pChargeCtrl->k1k2OffDelay > delay))
    {
        pChargeCtrl->k1k2OffDelay = delay;
    }
}
/**
 ******************************************************************************
 * @brief       充电过程中检测设备状态
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      TRUE -表示不影响继续执行该状态的动作
 *              FALSE-不能继续执行该状态的动作
 * @details
 * @note        结束充电过程中，有故障仍然可以继续执行
 *
 ******************************************************************************
 */
static bool_e
Check_FaultState(uint8 workState)
{
    // 获取设备状态
    uint8 deviceState = Get_DeviceState();

    if (DEVICE_STATE_FAULT == deviceState ||
        DEVICE_STATE_MAJOR_FAULT == deviceState)
    {
        switch (workState)
        {
        case CCU_WORK_STATE_FREE:
        case CCU_WORK_STATE_READY:
        case CCU_WORK_STATE_SHAKE_HAND:
        case CCU_WORK_STATE_IMD:
        case CCU_WORK_STATE_RELEASE_01:
        case CCU_WORK_STATE_RECOGNIZE:
        case CCU_WORK_STATE_CONFIG:
        case CCU_WORK_STATE_PRE_CHARGE:
        case CCU_WORK_STATE_CHARGING:
        case CCU_WORK_STATE_CHARGE_PAUSE:
        {
            if (DEVICE_STATE_MAJOR_FAULT == deviceState)
            {
                K1_K2_OFF; /*紧急故障直接断*/
                trace(TR_CHARGE, "K1k2OFF103\n");
            }
            /*2023版27930协议规定当检测到充电参数不匹配时，应在发送CML报文后再发送CST报文*/
            if (TRUE != Check_ErrType(eErrType_ChargeParaNoMatch))
            {
                Set_WorkState(CCU_WORK_STATE_CHARGE_STOP);
            }
            else if ((eSendFlag_Yes == Get_BmsSendFlg(BMS_PGN_CML)))
            {
                Set_BmsSendRemainTimer(BMS_PGN_CML, 0);
                Set_WorkState(CCU_WORK_STATE_CHARGE_STOP);
            }
        }
            return FALSE;

        case CCU_WORK_STATE_CHARGE_STOP:
        case CCU_WORK_STATE_RELEASE_02:
        case CCU_WORK_STATE_STOP_FINISH:
            return TRUE;

        default:
            return FALSE;
        }
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief       充电过程中检测导引状态
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      TRUE-表示不影响继续执行该状态的动作
 *              FALSE-不能继续执行该状态的动作
 * @details
 * @note        1.在充电停止状态下，等待BST的时候，检测到导引不等于4V
 *              也应算产生导引故障
 *              2.手动充电条件下不判断导引
 ******************************************************************************
 */
static bool_e
Check_PhyConVol(uint8 workState)
{
    if (CHARGE_MODE_MANUAL == Get_ChargeMode())
    {
        return TRUE;
    }
    //    if(Get_CcuCfgParaEuropeEnable())
    //    {
    //        if(Get_EcPlcCpStatus() == eCpStatus_Unplug)
    //        {
    //            return FALSE;
    //        }
    //        else
    //        {
    //            return TRUE;
    //        }
    //    }
    if (FALSE == Get_EnableFlag(eErrType_GunConnectErr))
    {
        return TRUE;
    }

    if (enumPhyConVol_4V != Get_PhyConVol())
    {
        switch (workState)
        {
        case CCU_WORK_STATE_FREE:
        case CCU_WORK_STATE_READY:
        case CCU_WORK_STATE_SHAKE_HAND:
        case CCU_WORK_STATE_IMD:
        case CCU_WORK_STATE_RELEASE_01:
        case CCU_WORK_STATE_RECOGNIZE:
        case CCU_WORK_STATE_CONFIG:
        case CCU_WORK_STATE_PRE_CHARGE:
        case CCU_WORK_STATE_CHARGING:
        case CCU_WORK_STATE_CHARGE_PAUSE:
        {
            Set_ErrType(eErrType_GunConnectErr);
        }
            return FALSE;

        case CCU_WORK_STATE_CHARGE_STOP:
        case CCU_WORK_STATE_RELEASE_02:
        case CCU_WORK_STATE_STOP_FINISH:
            return TRUE;

        default:
            return FALSE;
        }
    }
    else
    {
        Clr_ErrType(eErrType_GunConnectErr);
    }

    return TRUE;
}

static bool_e Check_ChargeStopFlag(uint8 workState)
{
    if (eActFlag_Off == Get_ChargeActFlag())
    {
        switch (workState)
        {
        case CCU_WORK_STATE_FREE:
        case CCU_WORK_STATE_READY:
        case CCU_WORK_STATE_SHAKE_HAND:
        case CCU_WORK_STATE_IMD:
        case CCU_WORK_STATE_RELEASE_01:
        case CCU_WORK_STATE_RECOGNIZE:
        case CCU_WORK_STATE_CONFIG:
        case CCU_WORK_STATE_PRE_CHARGE:
        case CCU_WORK_STATE_CHARGING:
        case CCU_WORK_STATE_CHARGE_PAUSE:
        {
            Set_WorkState(CCU_WORK_STATE_CHARGE_STOP);
        }
            return FALSE;

        case CCU_WORK_STATE_CHARGE_STOP:
        case CCU_WORK_STATE_RELEASE_02:
        case CCU_WORK_STATE_STOP_FINISH:
            return TRUE;

        default:
            return FALSE;
        }
    }

    return TRUE;
}

bool_e ImdCheck_ChargeDeal(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    if (FALSE == pChargeCtrl->imdStartFlag)
    {
        pChargeCtrl->imdStartFlag = TRUE;
        Imd_Start();
        pChargeCtrl->imdTick = tickGet();
        pChargeCtrl->imdFinishFlag = FALSE;
    }
    else
    {
        if (IMD_FINISH == Get_Imd_Stage()) /**<绝缘检测 完成*/
        {
            pChargeCtrl->imdFinishFlag = TRUE;
        }
        if (abs(tickGet() - pChargeCtrl->imdTick) > (15 * sysClkRateGet()))
        {
            Imd_Err_Ctrl(IMD_CHECK_TIMEOUT); /**绝缘检测超时*/
            pChargeCtrl->imdFinishFlag = TRUE;
        }
    }
    if (TRUE == pChargeCtrl->imdFinishFlag)
    {
        Set_Imd_Stage(IMD_IDLE);
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

static bool_e Check_ChargeMoudleStartFlag(uint8 workState)
{
    bool_e ret = FALSE;
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    if (Get_CcuCfgParaEuropeEnable() && Get_ImdSuccFlag())
    {
        return TRUE;
    }
    if (Get_ChargeMoudleStartUp()) // 模块启动完成
    {
        //	    printf("%s  %d\n",__FILE__,__LINE__);
        K1_K2_ON;
        ret = TRUE;
    }
    if (Get_ImdSuccFlag()) /*PCU模块待机处于3S极限时间，导致Get_ChargeMoudleStartUp()标志被清除，流程不能往下走*/
    {
        ret = TRUE;
    }
    switch (workState)
    {
    case CCU_WORK_STATE_IMD:
        return ret;
    case CCU_WORK_STATE_PRE_CHARGE:
        return ret;
    default:
        return FALSE;
    }
}

static bool_e Check_K1K2OnFlag(uint8 workState)
{
    bool_e ret = FALSE;
    if ((eSwitchState_ON == Get_SwitchState(SXIO_IN_K2)) && (eSwitchState_ON == Get_SwitchState(SXIO_IN_K1)))
    {
        ret = TRUE;
    }
    else
    {
        ret = FALSE;
    }
    switch (workState)
    {
    case CCU_WORK_STATE_IMD:
        return ret;
    case CCU_WORK_STATE_PRE_CHARGE:
        if (Get_ImdSuccFlag() && Get_CcuCfgParaEuropeEnable()) // 欧标中绝缘检测时需关闭K1 K2  后打开K2和预充旁路
        {
            ret = TRUE;
        }
        return ret;
    case CCU_WORK_STATE_CHARGING:
        return ret;
    case CCU_WORK_STATE_CHARGE_PAUSE:
        return ret;
    default:
        return FALSE;
    }
}
static bool_e Check_BmsOvertime(uint8 workState)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    uint8 bmsComState = Get_BmsComState();

    if (Get_CcuCfgParaEuropeEnable() && CHARGE_MODE_MANUAL == Get_ChargeMode()) /*欧标桩*/
    {
        return TRUE;
    }

    if (eComState_TimeOut == bmsComState)
    {
        switch (workState)
        {
        case CCU_WORK_STATE_FREE:
        case CCU_WORK_STATE_READY:
            return FALSE;

        case CCU_WORK_STATE_SHAKE_HAND:
        case CCU_WORK_STATE_IMD:
        case CCU_WORK_STATE_RELEASE_01:
        {
            Set_BmsOvertimeDealFlag(eResult_Succ);
            return FALSE;
        }
        case CCU_WORK_STATE_RECOGNIZE:
        case CCU_WORK_STATE_CONFIG:
        {
            Set_BmsOvertimeDealFlag(eResult_Succ);
            Set_BmsRecvTimerEnable(BMS_PGN_BCL, eTimerEnable_Off);
            Set_BmsRecvTimerEnable(BMS_PGN_BCS, eTimerEnable_Off);
            Set_BmsRecvTimerEnable(BMS_PGN_BMV, eTimerEnable_Off);
            Set_BmsRecvTimerEnable(BMS_PGN_BMT, eTimerEnable_Off);
            Set_BmsRecvTimerEnable(BMS_PGN_BSP, eTimerEnable_Off);
            if (Get_CcuCfgParaEuropeEnable())
            {
                Set_WorkState(CCU_WORK_STATE_CHARGE_STOP);
            }
            else
            {
                Set_WorkState(CCU_WORK_STATE_RECOGNIZE);
            }
            return TRUE;
        }
        case CCU_WORK_STATE_PRE_CHARGE:
        case CCU_WORK_STATE_CHARGING:
        case CCU_WORK_STATE_CHARGE_PAUSE:
        {
            /*
             * 通信超时，10s内断K1K2
             * */
            Set_k1k2OffDelay(9000 / CHARGE_CALL_CYCLE);
            trace(TR_PCU_PROCESS, "Set_k1k2OffDelay1\n");

            if (eSwitchState_OFF == Get_SwitchState(SXIO_IN_K1) &&
                eSwitchState_OFF == Get_SwitchState(SXIO_IN_K2))
            {
                Set_BmsOvertimeDealFlag(eResult_Succ);
                Set_BmsRecvTimerEnable(BMS_PGN_BCL, eTimerEnable_Off);
                Set_BmsRecvTimerEnable(BMS_PGN_BCS, eTimerEnable_Off);
                Set_BmsRecvTimerEnable(BMS_PGN_BMV, eTimerEnable_Off);
                Set_BmsRecvTimerEnable(BMS_PGN_BMT, eTimerEnable_Off);
                Set_BmsRecvTimerEnable(BMS_PGN_BSP, eTimerEnable_Off);
                if (Get_CcuCfgParaEuropeEnable())
                {
                    Set_WorkState(CCU_WORK_STATE_CHARGE_STOP);
                }
                else
                {
                    Set_WorkState(CCU_WORK_STATE_RECOGNIZE);
                }
                Set_PreChargeSuccFlag(FALSE);
                return TRUE;
            }
            return FALSE;
        }
        case CCU_WORK_STATE_CHARGE_STOP:
            /*
             * 通信超时，10s内断K1K2
             * */
            Set_k1k2OffDelay(9000 / CHARGE_CALL_CYCLE);
            trace(TR_PCU_PROCESS, "Set_k1k2OffDelay2\n");
            Set_BmsOvertimeDealFlag(eResult_Succ);
            return TRUE;
        case CCU_WORK_STATE_RELEASE_02:
        case CCU_WORK_STATE_STOP_FINISH:
        {
            Set_BmsOvertimeDealFlag(eResult_Succ);
        }
            return TRUE;

        default:
            return FALSE;
        }
    }

    return TRUE;
}

// todo： 充电暂停状态下，出现通信超时，回到辨识状态，那么还要不要判断充电暂停超时。
static bool_e Check_ChargePause(uint8 workState)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    if (FALSE == Get_EnableFlag(eErrType_ChargePauseTimeout))
    {
        return TRUE;
    }

    if (enumAllowFlag_Forbid == Get_ChargePauseFlg())
    {
        switch (workState)
        {
        case CCU_WORK_STATE_FREE:
        case CCU_WORK_STATE_READY:
        case CCU_WORK_STATE_IMD:
        case CCU_WORK_STATE_SHAKE_HAND:
        case CCU_WORK_STATE_RELEASE_01:
            return TRUE;

        case CCU_WORK_STATE_RECOGNIZE:
        case CCU_WORK_STATE_CONFIG:
        case CCU_WORK_STATE_PRE_CHARGE:
        case CCU_WORK_STATE_CHARGING:
        case CCU_WORK_STATE_CHARGE_PAUSE:
        {
            if (abs(tickGet() - pChargeCtrl->chargePauseTimer) > CHARGE_PAUSE)
            {
                Set_ErrType(eErrType_ChargePauseTimeout);
                Set_WorkState(CCU_WORK_STATE_CHARGE_STOP);
                return FALSE;
            }

            return TRUE;
        }

        case CCU_WORK_STATE_CHARGE_STOP:
        case CCU_WORK_STATE_RELEASE_02:
        case CCU_WORK_STATE_STOP_FINISH:
            return TRUE;

        default:
            return FALSE;
        }
    }
    else
    {
        pChargeCtrl->chargePauseTimer = tickGet();
    }

    return TRUE;
}

static bool_e Check_ChargeStartFlag(uint8 workState)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    if (Get_CcuCfgParaEuropeEnable()) //   欧标需要先打开BMS通讯
    {
        if (eActFlag_On != Get_BMS_StartFlg())
        {
            Start_BMS();
        }
    }
    if (eActFlag_On == Get_ChargeActFlag())
    {
        return TRUE;
    }
    else
    {
        pChargeCtrl->waitPcuWsTick = tickGet();
    }
    return FALSE;
}

static bool_e Check_PcuWorkStateFlag(uint8 workState)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    if ((Get_PcuWorkState() == ePcuWorkState_Free) || (abs(tickGet() - pChargeCtrl->waitPcuWsTick) > 5000))
    {
        return TRUE;
    }
    return FALSE;
}
/**
 ******************************************************************************
 * @brief      检查握手报文.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *             国标2011直接返回true
 *             否则需判是否已经收到CHM
 *
 * @note
 ******************************************************************************
 */
static bool_e Check_BHM_Recv(uint8 workState)
{
    if ((Get_BMS_Ver() == 0xA5) || (eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BHM)))
    {
        return TRUE;
    }

    return FALSE;
}

static bool_e Check_BCP_Recv(uint8 workState)
{
    if (eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BCP))
    {
        return TRUE;
    }

    return FALSE;
}

static bool_e Check_BST_Recv(uint8 workState)
{
    if (CHARGE_MODE_MANUAL == Get_ChargeMode())
    {
        return TRUE;
    }

    if (eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BST))
    {
        Set_WorkState(CCU_WORK_STATE_CHARGE_STOP);
        return FALSE;
    }

    return TRUE;
}

static bool_e Check_BHM_Vol(uint8 workState)
{
    BHM_DATA strBHM;
    CHARGE_PARA strChargePara;
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    /*
     * 国标2011，无需检查CHM相关信息
     * 兼容时先断K3K4，绝缘后重新启动BMS，并且需跳过握手
     *
     * */
    if (Get_CcuCfgParaEuropeEnable())
    {
        return TRUE;
    }

    if (Get_BMS_Ver() == 0xA5)
    {
        K1_K2_OFF;
        trace(TR_CHARGE, "K1_K2_OFF2\n");
        K3_K4_OFF; // Check_BHM_Vol
        trace(TR_CHARGE, "K3k4OFF5\n");
        return TRUE;
    }

    Get_BMS_Data(BMS_PGN_BHM, (void *)&strBHM);
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    if ((TwoUint8ToUint16(strBHM.highlestTotalVoltage) <
         TwoUint8ToUint16(strChargePara.minOutputVoltage)) &&
        (TRUE == Get_EnableFlag(eErrType_BHMVolErr)))
    {
        Set_ErrType(eErrType_BHMVolErr);
        K1_K2_OFF;
        trace(TR_CHARGE, "K1_K2_OFF3\n");
        K3_K4_OFF; // Check_BHM_Vol
        pChargeCtrl->k3k4OffFinishFlag = TRUE;
        trace(TR_CHARGE, "K3k4OFF6\n");
        return FALSE;
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief       获取DC+枪温
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      uint32
 * @details     扩大10倍
 * @note
 *
 ******************************************************************************
 */
static uint16 Get_OutVol(void)
{

    uint16 val = 0;
    CHARGE_PARA strChargePara;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
    val = TwoUint8ToUint16(strChargePara.OutVol);
    return ((val > 0 && val <= 2000) ? val : 600);
}

static bool_e Check_K1K2OutsideVol(uint8 workState)
{
    if (FALSE == Get_EnableFlag(eErrType_K1K2OutsideVolErr1))
    {
        return TRUE;
    }
    // 留1V的裕量（作为误差允许）
    if ((Platform_protocol_Xj == Get_CcuCfgParaPlatform_convert() && Get_K1K2Outside()) || Get_OutSideVol())
    {
        if (Get_OutSideVol())
        {
            if (Get_K1K2OutsideVol() >= 0) /*Get_K1K2OutsideVol1() + 10)*/
            {
                Set_ErrType(eErrType_K1K2OutsideVolErr1);
                return FALSE;
            }
        }
        else
        {
            if (Get_K1K2OutsideVol() > MAX((Get_K1K2OutsideVol1() * 10), Get_OutVol()) + 10) /*Get_K1K2OutsideVol1() + 10)*/
            {
                Set_ErrType(eErrType_K1K2OutsideVolErr1);
                return FALSE;
            }
        }
    }
    else
    {
        if (Get_K1K2OutsideVol() > Get_OutVol() + 10)
        {
            Set_ErrType(eErrType_K1K2OutsideVolErr1);
            return FALSE;
        }
    }

    return TRUE;
}

static bool_e Check_SPN_2829(uint8 workState)
{
    if (0xAA == Get_SPN_2829())
    {
        return TRUE;
    }

    return FALSE;
}

/**
******************************************************************************
* @brief       即插即充中检测车辆合法性
* @param[in]   None
* @param[out]  None
* @retval
*
* @details
*
*
* @note
******************************************************************************
*/
static bool_e Check_VehicleValidate(uint8 workState)
{
    return Tcu_GetVehicleValidate();
}

/**
******************************************************************************
* @brief       Check_VehParams
* @param[in]   None
* @param[out]  None
* @retval
*
* @details    2023版27930协议要求判断两次充电参数是否匹配(检查BCP电池电压值是否在充电机的输出能力范围内)，
* 			    一次是在接收到BCP报文后，一次是在接收到BRO_00的报文后
*
* @note
******************************************************************************
*/
bool_e Check_VehParams(void)
{
    if (Get_CcuCfgParaEuropeEnable())
    {
        return TRUE;
    }

    CHARGE_PARA strChargePara;
    BCP_DATA strBCP;

    memset(&strBCP, 0x00, sizeof(BCP_DATA));
    Get_BMS_Data(BMS_PGN_BCP, (void *)&strBCP);
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    if ((!Get_FaultMask(eErrType_K1K2OutsideVolErr2)) &&
        (TwoUint8ToUint16(strBCP.totalVoltage) <= TwoUint8ToUint16(strChargePara.maxOutputVoltage)) && /**<判断充电参数是否匹配（即BCP中当前电池电压测量值是否在充电机的输出能力范围内*/
        (TwoUint8ToUint16(strBCP.totalVoltage) >= TwoUint8ToUint16(strChargePara.minOutputVoltage)))
    {
        trace(TR_CHARGE, "[%05d]BCP.totalVoltage1 = %d, maxOutputVoltage = %d, minOutputVoltage = %d \n", __LINE__,
              TwoUint8ToUint16(strBCP.totalVoltage), TwoUint8ToUint16(strChargePara.maxOutputVoltage), TwoUint8ToUint16(strChargePara.minOutputVoltage));
        return TRUE;
    }
    else if (Get_FaultMask(eErrType_ChargeParaNoMatch) || (TwoUint8ToUint16(strBCP.totalVoltage) > TwoUint8ToUint16(strChargePara.maxOutputVoltage)) || (TwoUint8ToUint16(strBCP.totalVoltage) < TwoUint8ToUint16(strChargePara.minOutputVoltage)))
    {
        if (FALSE == Get_EnableFlag(eErrType_ChargeParaNoMatch))
        {
            return TRUE;
        }
        trace(TR_CHARGE, "[%05d]BCP.totalVoltage1 = %d, maxOutputVoltage = %d, minOutputVoltage = %d \n", __LINE__,
              TwoUint8ToUint16(strBCP.totalVoltage), TwoUint8ToUint16(strChargePara.maxOutputVoltage), TwoUint8ToUint16(strChargePara.minOutputVoltage));
        Set_ErrType(eErrType_ChargeParaNoMatch);

        return FALSE;
    }
    return FALSE;
}

/**
******************************************************************************
* @brief       Check_BCP_Vol
* @param[in]   None
* @param[out]  None
* @retval
*
* @details     检查BCP电池电压值与外侧电压的允许范围
*
*
* @note
******************************************************************************
*/
static bool_e Check_BCP_Vol(uint8 workState)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    uint32 curOutSideVol = Get_K1K2OutsideVol();
    CHARGE_PARA strChargePara;
    BCP_DATA strBCP;
    if (Get_CcuCfgParaEuropeEnable())
    {
        return TRUE;
    }
    if (eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BCP))
    {
        return FALSE;
    }

    Get_BMS_Data(BMS_PGN_BCP, (void *)&strBCP);
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    if ((!Get_FaultMask(eErrType_K1K2OutsideVolErr2) || !Get_FaultMask(eErrType_K1K2OutsideVolErr3)) && ((curOutSideVol >= (TwoUint8ToUint16(strBCP.totalVoltage) * 95 / 100)) &&
                                                                                                         (curOutSideVol <= (TwoUint8ToUint16(strBCP.totalVoltage) * 105 / 100)) &&
                                                                                                         (curOutSideVol + 50 >= TwoUint8ToUint16(strChargePara.minOutputVoltage)) && /**<考虑5V测量误差*/
                                                                                                         (curOutSideVol <= TwoUint8ToUint16(strChargePara.maxOutputVoltage) + 50)))  /**<考虑5V测量误差*/
    {
        pChargeCtrl->BCP_VolTrueCnt++;

        if (pChargeCtrl->BCP_VolTrueCnt >= 100)
        {
            trace(TR_CHARGE, "[%05d]curOutSideVol1 = %d,BCP.totalVoltage1 = %d , BCP.highestVoltage1 = %d \n", __LINE__,
                  curOutSideVol, TwoUint8ToUint16(strBCP.totalVoltage), TwoUint8ToUint16(strBCP.highestVoltage));
            return TRUE;
        }
    }
    /**************小桔新增**********************/
    else if (Get_FaultMask(eErrType_K1K2OutsideVolErr2) || (curOutSideVol + 50 <= TwoUint8ToUint16(strChargePara.minOutputVoltage)))
    {
        pChargeCtrl->BCP_VolFalse1Cnt++;

        if (Get_FaultMask(eErrType_K1K2OutsideVolErr2) || (pChargeCtrl->BCP_VolFalse1Cnt >= 300))
        {
            if (FALSE == Get_EnableFlag(eErrType_K1K2OutsideVolErr2))
            {
                return TRUE;
            }
            trace(TR_CHARGE, "[%05d]curOutSideVol3 = %d,BCP.totalVoltage3 = %d , minOutputVoltage = %d \n", __LINE__,
                  curOutSideVol, TwoUint8ToUint16(strBCP.totalVoltage), TwoUint8ToUint16(strChargePara.minOutputVoltage));
            Set_ErrType(eErrType_K1K2OutsideVolErr2);

            return FALSE;
        }
    }
    /**************小桔新增**********************/
    else if (Get_FaultMask(eErrType_K1K2OutsideVolErr3) || (curOutSideVol >= TwoUint8ToUint16(strChargePara.maxOutputVoltage) + 50))
    {
        pChargeCtrl->BCP_VolFalse2Cnt++;

        if (Get_FaultMask(eErrType_K1K2OutsideVolErr3) || (pChargeCtrl->BCP_VolFalse2Cnt >= 300))
        {
            if (FALSE == Get_EnableFlag(eErrType_K1K2OutsideVolErr3))
            {
                return TRUE;
            }
            trace(TR_CHARGE, "[%05d]curOutSideVol4 = %d,BCP.totalVoltage4 = %d , maxOutputVoltage = %d \n", __LINE__,
                  curOutSideVol, TwoUint8ToUint16(strBCP.totalVoltage), TwoUint8ToUint16(strChargePara.maxOutputVoltage));
            Set_ErrType(eErrType_K1K2OutsideVolErr3);

            return FALSE;
        }
    } /**************小桔新增**********************/
    else
    {
        pChargeCtrl->BCP_VolFalseCnt++;

        if (pChargeCtrl->BCP_VolFalseCnt >= 300)
        {
            if (FALSE == Get_EnableFlag(eErrType_BCPVolErr1))
            {
                return TRUE;
            }
            trace(TR_CHARGE, "[%05d]curOutSideVol2 = %d,BCP.totalVoltage2 = %d , BCP.highestVoltage2 = %d \n", __LINE__,
                  curOutSideVol, TwoUint8ToUint16(strBCP.totalVoltage), TwoUint8ToUint16(strBCP.highestVoltage));
            Set_ErrType(eErrType_BCPVolErr1);

            return FALSE;
        }
    }

    return FALSE;
}

static uint32 chargeVolCheckTick(uint8 wr)
{
    static uint32 chargevolchecktick = 0;
    if (wr)
    {
        chargevolchecktick = tickGet();
    }
    return chargevolchecktick;
}

// 返回单位0.1秒
#define DEF_PORT_TIME (30) // 3秒
static uint16
Get_VolCurPortTime(void)
{
    uint16 val = 0;
    CHARGE_PARA strChargePara;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    val = TwoUint8ToUint16(strChargePara.ProtTime);
    return ((0xffff == val) ? DEF_PORT_TIME : val);
}

static uint16
Get_A_5PortTime(void)
{
    uint16 val = 0;
    CHARGE_PARA strChargePara;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    val = TwoUint8ToUint16(strChargePara.A_5Time);
    return ((0xffff == val) ? 98 : val);
}

static uint16
Get_Out_Elc(void)
{
    uint16 val = 0;
    CHARGE_PARA strChargePara;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    val = TwoUint8ToUint16(strChargePara.Out_Elc);
    return ((0xffff == val) ? 550 : val);
}

#define CHARGE_CUR_PROT_ACCURACY (1.5)
#define CHARGE_CUR_PROT_OFFSET (1000.0) // 单位:mA
/**
 ******************************************************************************
 * @brief      Check_ChargeCur.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details   检查充电阶段电流的合法性，此时已接收到BCL
 *            电流比较判据
 *            1,采样电流与BCP比较,故障名称:输出电流大于最高允许充电电流
 *            2,采样电流与BCL比较,故障名称:输出电流大于需求电流;
 *            3,采样电流与设定参数比较,故障故障:输出电流大于最大允许输出电流
 *            4,BCL与BCP比较,故障名称:需求电流大于最高允许充电总电流
 * @note
 ******************************************************************************
 */
static bool_e Check_ChargeCur(uint8 workState)
{
    uint32 curOutBusCur = 0;
    uint32 demandCur = 0;
    uint32 highestCur = 0;
    uint16 Plc_Cur = 0;
    float cmpCur = 0;
    BCL_DATA strBCL;
    BCP_DATA strBCP;
    CHARGE_PARA strChargePara;
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    if (CHARGE_MODE_MANUAL == Get_ChargeMode() || eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BHM))
    {
        return TRUE;
    }

    if (FALSE == Get_EnableFlag(eErrType_BusCurOverCarLimite))
    {
        return TRUE;
    }

    if (productTestFlag(FALSE, FALSE) == TRUE) // 电性能测试模式不判断.
    {
        return TRUE;
    }

    curOutBusCur = Get_K1K2Current();

    /**
     *  采样电流大于最大设定输出电流值
     */
    if (curOutBusCur > (FourUint8ToUint32(strChargePara.maxOutputCurrent) * (100.0 + CHARGE_CUR_PROT_ACCURACY) / 100.0 + CHARGE_CUR_PROT_OFFSET)) // 考虑电流测量误差
    {
        trace(TR_CHARGE,
              "curOutBusCur = %d,  strChargePara.maxOutputCurrent = %d \n",
              curOutBusCur,
              FourUint8ToUint32(strChargePara.maxOutputCurrent));
        Set_ErrType(eErrType_OutputOverMaxAllowOutputCur);

        K1_K2_OFF;
        trace(TR_CHARGE, "K1_K2_OFF3\n");
        K3_K4_OFF; // Check_ChargeVol
        pChargeCtrl->k3k4OffFinishFlag = TRUE;
        trace(TR_CHARGE, "K3k4OFF30\n");

        return FALSE;
    }

    if (eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BCP))
    {
        return TRUE;
    }

    Get_BMS_Data(BMS_PGN_BCP, (void *)&strBCP);

    /**
     * 采样电流大于BCP中允许最大充电电流
     */
    if (Get_CcuCfgParaEuropeEnable())
    {
        demandCur = Get_Bcp_HighestCurrent() * 100; // 单位:mA;
    }
    else
    {
        demandCur = (4000 - TwoUint8ToUint16(strBCP.highestCurrent)) * 100; // 单位:mA
    }
    if (curOutBusCur > (demandCur * (100.0 + CHARGE_CUR_PROT_ACCURACY) / 100.0 + CHARGE_CUR_PROT_OFFSET)) // 考虑电流测量误差
    {
        trace(TR_CHARGE, "curOutBusCur = %d,  strBCP.highestCurrent = %d \n",
              curOutBusCur, TwoUint8ToUint16(strBCP.highestCurrent));
        Set_ErrType(eErrType_OutputOverMaxChargeCur);

        K1_K2_OFF;
        trace(TR_CHARGE, "K1_K2_OFF4\n");
        K3_K4_OFF; // Check_ChargeVol
        pChargeCtrl->k3k4OffFinishFlag = TRUE;
        trace(TR_CHARGE, "K3k4OFF31\n");
        return FALSE;
    }

    if (eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BCL))
    {
        return TRUE;
    }

    Get_BMS_Data(BMS_PGN_BCL, (void *)&strBCL);

    /**
     *  采样电流大于BCL需求电流
     */
    //    demandCur = (4000 - TwoUint8ToUint16(strBCL.currentDemand)) * 100; //单位:mA
    /*若手动启动strBCL.currentDemand = 0，则默认为满电流，转换后则取设置电流，如为0，则按最小输出走*/
    demandCur = (4000 - TwoUint8ToUint16(strBCL.currentDemand)) * 100; // 单位:mA
    //    if(4000*100 == demandCur &&  0 == TwoUint8ToUint16(strBCL.currentDemand))
    //    {
    //        demandCur = 4000*100;
    //    }
    //    if( demandCur )
    //    {
    //        demandCur = FourUint8ToUint32(strChargePara.minOutputCurrent);
    //    }
    demandCur = (demandCur < FourUint8ToUint32(strChargePara.minOutputCurrent)) ? FourUint8ToUint32(strChargePara.minOutputCurrent) : demandCur;
    //    demandCur = (demandCur > 0 && demandCur < 400000)?demandCur:FourUint8ToUint32(strChargePara.maxOutputCurrent); //单位:mA
    /*若手动启动strBCL.currentDemand = 0，则默认为满电流，转换后则取设置电流，如为0，则按最小输出走*/

    cmpCur = demandCur * (100.0 + CHARGE_CUR_PROT_ACCURACY) / 100.0 + CHARGE_CUR_PROT_OFFSET;

    if (curOutBusCur > cmpCur) // 考虑电流测量误差
    {
        pChargeCtrl->chargeCurOverBCLCnt++;
        if (pChargeCtrl->chargeCurOverBCLCnt >= sysClkRateGet() * Get_VolCurPortTime() / (10 * CHARGE_CALL_CYCLE)) //
        {
            trace(TR_CHARGE, "curOutBusCur = %d,  strBCL.currentDemand = %d,demandCur : %d,cmpCur : %f\n",
                  curOutBusCur, TwoUint8ToUint16(strBCL.currentDemand), demandCur, cmpCur);

            Set_ErrType(eErrType_BusCurOverCarLimite);

            K1_K2_OFF;
            trace(TR_CHARGE, "K1_K2_OFF5\n");
            K3_K4_OFF; // Check_ChargeVol
            pChargeCtrl->k3k4OffFinishFlag = TRUE;
            pChargeCtrl->chargeCurOverBCLCnt = 0;
            trace(TR_CHARGE, "K3k4OFF32\n");

            return FALSE;
        }
    }
    else
    {
        pChargeCtrl->chargeCurOverBCLCnt = 0;
    }

    /**
     *  BCL需求电流超过BCP最高允许充电电流
     */
    demandCur = (4000 - TwoUint8ToUint16(strBCL.currentDemand)) * 100; // 单位:mA
    if (Get_CcuCfgParaEuropeEnable())
    {
        highestCur = Get_Bcp_HighestCurrent() * 100; // 单位:mA;
    }
    else
    {
        highestCur = (4000 - TwoUint8ToUint16(strBCP.highestCurrent)) * 100; // 单位:mA
    }

    if (demandCur > highestCur)
    {
        trace(TR_CHARGE,
              "strBCL.currentDemand = %d,  strBCP.highestCurrent = %d \n",
              TwoUint8ToUint16(strBCL.currentDemand),
              TwoUint8ToUint16(strBCP.highestCurrent));
        Set_ErrType(eErrType_DemandOverMaxAllowChargeCur);

        K1_K2_OFF;
        trace(TR_CHARGE, "K1_K2_OFF6\n");
        K3_K4_OFF; // Check_ChargeVol
        pChargeCtrl->k3k4OffFinishFlag = TRUE;
        trace(TR_CHARGE, "K3k4OFF33\n");
        return FALSE;
    }

    return TRUE;
}

/**
******************************************************************************
* @brief       Check_ChargeVol
* @param[in]   None
* @param[out]  None
* @retval
*
* @details     检查各阶段充电电压的合法性，此时已接收到BCP
*              电压比较判据:
*              1,采样电压与BHM比较;故障名称:输出电压大于最高允许充电总电压
*              2,采样电压与BCP比较,故障名称:输出电压大于最高允许充电总电压
*              3,采样电压与设定参数,故障名称:输出电压大于最大允许输出电压
*              4,采样电压与BCL比较, 故障名称：输出电压大于需求电压
*              5,BCL与最高允许充电总电压比较,故障名称: 需求电压大于车辆最高允许充电总电压
*              6,BCL与充电机最小输出电压比较,故障名称: 需求电压小于充电机最小输出电压;
* @note
******************************************************************************
*/
static bool_e Check_ChargeVol(uint8 workState)
{
    uint32 maxAllowedVol = 0;
    uint32 curOutSideVol = 0;
    uint32 minOutputVoltage = 0;
    BHM_DATA strBHM;
    BCP_DATA strBCP;
    BCL_DATA strBCL;
    CHARGE_PARA strChargePara;
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
    if (Get_CcuCfgParaEuropeEnable())
    {
        return TRUE;
    }
    if (CHARGE_MODE_MANUAL == Get_ChargeMode() || eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BHM))
    {
        return TRUE;
    }

    if (FALSE == Get_EnableFlag(eErrType_BatteryVolErr))
    {
        return TRUE;
    }

    if (eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BCP))
    {
        return TRUE;
    }

    Get_BMS_Data(BMS_PGN_BCP, (void *)&strBCP);
    curOutSideVol = Get_K1K2OutsideVol();

    /* 国标2011没有CHM，用BCP */
    if (Get_BMS_Ver() == 0xA5)
    {
        maxAllowedVol = TwoUint8ToUint16(strBCP.highestVoltage);
    }
    else
    {
        //        Get_BMS_Data(BMS_PGN_BHM, (void *) &strBHM);
        //        maxAllowedVol = MIN(TwoUint8ToUint16(strBHM.highlestTotalVoltage),
        //                TwoUint8ToUint16(strBCP.highestVoltage));
        /*2023版国标27930协议规定BHM中的电压仅用于充电机进行绝缘自检时输出电缆绝缘监测电压依据，
        因此不用将BCP中的最高允许充电总电压和BHM中的电压相比较*/
        maxAllowedVol = TwoUint8ToUint16(strBCP.highestVoltage);
    }
    minOutputVoltage = (TwoUint8ToUint16(strChargePara.minOutputVoltage) > 50) ? (TwoUint8ToUint16(strChargePara.minOutputVoltage)) : 50;
    if (productTestFlag(FALSE, FALSE) == TRUE) // 电性能测试模式不判断.
    {
        return TRUE;
    }

    if (abs(tickGet() - chargeVolCheckTick(FALSE)) < 10000)
    {
        if (curOutSideVol > (maxAllowedVol * 105 / 100 + 50))
        {
            trace(TR_CHARGE,
                  "curOutSideVol1 = %d,  maxAllowedVol1 = %d,BHM.TotalVoltage = %d,BCP.highestVoltage = %d,OverVoltage = %d, LessVoltage = %d\n",
                  curOutSideVol, maxAllowedVol,
                  TwoUint8ToUint16(strBHM.highlestTotalVoltage),
                  TwoUint8ToUint16(strBCP.highestVoltage),
                  TwoUint8ToUint16(strChargePara.DC_OutputOverVoltageLimit),
                  TwoUint8ToUint16(strChargePara.DC_OutputLessVoltageLimit));
            Set_ErrType(eErrType_BatteryVolErr); /**<电压异常，用电池电压异常表示*/
            K1_K2_OFF;
            trace(TR_CHARGE, "K1_K2_OFF7\n");
            Stop_BMS(FALSE);
            K3_K4_OFF; // Check_ChargeVol
            pChargeCtrl->k3k4OffFinishFlag = TRUE;
            trace(TR_CHARGE, "K3k4OFF7\n");
            return FALSE;
        }
    }
    else
    {
        /**
         *  采样电压与大于BCP最大允许充电电压(BCP与BHM小值)
         */
        if (curOutSideVol > (maxAllowedVol + 50))
        {
            pChargeCtrl->chargeVolOverBCPCnt++;
            if (pChargeCtrl->chargeVolOverBCPCnt >= sysClkRateGet() * Get_VolCurPortTime() / (10 * CHARGE_CALL_CYCLE)) //
            {
                trace(TR_CHARGE,
                      "curOutSideVol2 = %d,  maxAllowedVol1 = %d,BHM.TotalVoltage = %d,BCP.highestVoltage = %d \n",
                      curOutSideVol, maxAllowedVol,
                      TwoUint8ToUint16(strBHM.highlestTotalVoltage),
                      TwoUint8ToUint16(strBCP.highestVoltage));
                Set_ErrType(eErrType_OutputOverMaxAllowChargeVol);

                K1_K2_OFF;
                trace(TR_CHARGE, "K1_K2_OFF8\n");
                K3_K4_OFF; // Check_ChargeVol
                pChargeCtrl->k3k4OffFinishFlag = TRUE;
                pChargeCtrl->chargeVolOverBCPCnt = 0;
                trace(TR_CHARGE, "K3k4OFF8\n");
                return FALSE;
            }
        }
        else
        {
            pChargeCtrl->chargeVolOverBCPCnt = 0;
        }

        /**
         *  采样电压与大于最大设定输出电压
         */
        if (curOutSideVol > (TwoUint8ToUint16(strChargePara.maxOutputVoltage) + 50))
        {
            pChargeCtrl->chargeVolOverSetCnt++;
            if (pChargeCtrl->chargeVolOverSetCnt >= sysClkRateGet() * Get_VolCurPortTime() / (10 * CHARGE_CALL_CYCLE)) //
            {
                trace(TR_CHARGE,
                      "curOutSideVol2 = %d,  strChargePara.maxOutputVoltage = %d ,chargeVolOverSetCnt :%d\n",
                      curOutSideVol,
                      TwoUint8ToUint16(strChargePara.maxOutputVoltage),
                      pChargeCtrl->chargeVolOverSetCnt);
                Set_ErrType(eErrType_OutputOverMaxAllowOutputVol);

                K1_K2_OFF;
                trace(TR_CHARGE, "K1_K2_OFF9\n");
                K3_K4_OFF; // Check_ChargeVol
                pChargeCtrl->k3k4OffFinishFlag = TRUE;
                pChargeCtrl->chargeVolOverSetCnt = 0;
                trace(TR_CHARGE, "K3k4OFF21\n");

                return FALSE;
            }
        }
        else
        {
            pChargeCtrl->chargeVolOverSetCnt = 0;
        }

        if (eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BCL))
        {
            return TRUE;
        }

        Get_BMS_Data(BMS_PGN_BCL, (void *)&strBCL);

        /**
         *  BCL与BCP较
         */
        if (TwoUint8ToUint16(strBCL.voltageDemand) > TwoUint8ToUint16(strBCP.highestVoltage))
        {
            trace(TR_CHARGE,
                  "strBCL.voltageDemand = %d,  strBCP.highestVoltage = %d \n",
                  strBCL.voltageDemand, TwoUint8ToUint16(strBCP.highestVoltage));

            Set_ErrType(eErrType_DemandOverMaxAllowChargeVol);

            K1_K2_OFF;
            trace(TR_CHARGE, "K1_K2_OFF10\n");
            K3_K4_OFF; // Check_ChargeVol
            pChargeCtrl->k3k4OffFinishFlag = TRUE;
            trace(TR_CHARGE, "K3k4OFF22\n");

            return FALSE;
        }

        /**
         *  BCL中电压需求与充电机最小输出电压比较，当电压需求小于充电机最小输出电压时，报故障停止充电
         */
        if (TwoUint8ToUint16(strBCL.voltageDemand) < TwoUint8ToUint16(strChargePara.minOutputVoltage))
        {
            trace(TR_CHARGE,
                  "strBCL.voltageDemand = %d,  strChargePara.minOutputVoltage = %d \n",
                  TwoUint8ToUint16(strBCL.voltageDemand), TwoUint8ToUint16(strChargePara.minOutputVoltage));
            Set_ErrType(eErrType_DemandLowMinAllowOutputVol);

            K1_K2_OFF;
            trace(TR_CHARGE, "K1_K2_OFF11\n");
            K3_K4_OFF; // Check_ChargeVol
            pChargeCtrl->k3k4OffFinishFlag = TRUE;
            trace(TR_CHARGE, "K3k4OFF23\n");

            return FALSE;
        }
    }
    return TRUE;
}
static RESULT Check_ReleaseValue(void)
{
    if (Get_K1K2InsideVol() < 600)
    {
        return eResult_Succ;
    }

    return eResult_NULL;
}

static RESULT Check_PreCharge(void)
{
    trace(TR_CCU_DEBUG, "OutSideVol = %d,InsideVol= %d, DemondVol = %d\n",
          Get_K1K2OutsideVol(), Get_K1K2InsideVol(), Get_DemondVol());

    if (((Get_K1K2OutsideVol() - 100) < Get_K1K2InsideVol()) && ((Get_K1K2OutsideVol() - 10) > Get_K1K2InsideVol()))
    {
        return eResult_Succ;
    }
    return eResult_NULL;
}

/**
 ******************************************************************************
 * @brief       空闲状态清除一些非器件故障,或者非通讯上送过来的故障导致（过程故障）的并且未被清除的故障
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
RESULT Clear_ProcessFault(uint8 workState)
{
    for (int index = eErrType_ComErrWithTCU;
         index <= eErrType_K1K2OutsideVolErr4; index++)
    {
        if ((index == eErrType_ComErrWithBMS) || (index == eErrType_OutputCurOverLimitBSM) || (index == eErrType_TempOverLimitBSM) || (index == eErrType_StopFinishAckTimeOutTcu) || (index == eErrType_ImdErr) || (index == eErrType_BatteryReverseConnect) || (index == eErrType_K5K6ErrBMS) || (index == eErrType_OutputCurOverLimit) || (index == eErrType_PcuBusyTimeout) || (index == eErrType_ReleaseErr) || (index == eErrType_BmsFaultStopErr) || (index == eErrType_BHMVolErr) || (index == eErrType_K1K2OutsideVolErr1) || (index == eErrType_BCPVolErr1) || (index == eErrType_K1K2OutsideVolErr2) || (index == eErrType_K1K2OutsideVolErr3) || (index == eErrType_BatteryVolErr) || (index == eErrType_BRMErr) || (index == eErrType_BCPErr) || (index == eErrType_ChargePauseTimeout) || (index == eErrType_LoadCtrlSwitch) || (index == eErrType_ComErrWithIMD) || (index == eErrType_ComErrWithPCU) || (index == eErrType_SOCTooHighBSM) || (index == eErrType_SOCTooLowBSM) || (index == eErrType_ImdErrBSM) || (index == eErrType_PhyConErrBSM) || (index == eErrType_PhySingleVoltooHighBSM) || (index == eErrType_PhySingleVoltooLowBSM) || (index == eErrType_StartFinishAckTimeOutTcu) || (index == eErrType_ModuleOnOffErr) || (index == eErrType_BROErr) || (index == eErrType_SStartTimeOut) || (index == eErrType_QStartTimeOut) || (index == eErrType_ImdTimeOut) || (index == eErrType_K1K2OutsideVolErr4) || (index == eErrType_PlugAndPlayAckTimeout) || (index == eErrType_PlugAndPlayAckErr) || (index == eErrType_BHMVolErr1) || (index == eErrType_ChargeParaNoMatch) || (index == eErrType_TCU))
        {
            //			printf("err_index1 = %d,Check_ErrType(index) = %d\n",index,Check_ErrType(index));
            if (eErrType_ComErrWithPCU == index) /**<遥控命令帧应答（非周期）产生的超时需要在这里清除，其它帧产生的超时不用*/
            {
                if (TRUE == Check_ErrType(index))
                {
                    for (int i = PGN_YK_QSTART_ACK; i <= PGN_YK_STOP_ACK; i++)
                    {
                        if (Get_PcuErrInfo(i))
                        {
                            Clr_PcuErrInfo(i);
                        }
                    }
                }
            }
            else
            {
                //				if (TRUE == Check_ErrType(index))
                //				{
                //					trace(TR_CCU_DEBUG,"空闲状态清除故障---故障代码--<%d>!\n",index);
                Clr_ErrType(index);
                //				}
            }
        }
    }
    if (TRUE == Check_ErrType(eErrType_GunConnectErr))
    {
        Clr_ErrType(eErrType_GunConnectErr);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief       充电空闲状态（接收启动命令后）
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details     1.初始化充电控制
 *              2.保存充电起始Tick
 *
 * @note
 ******************************************************************************
 */
static void Deal_Free(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    Init_ChargeCtrl();

    Set_WorkState(CCU_WORK_STATE_READY);
    //    Free_Clear_ProcessFault();

    pChargeCtrl->ChargeStartTick = tickGet();

    return;
}

/**
 ******************************************************************************
 * @brief       充电准备状态
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details     1.闭合电子锁，确认电子锁闭合完成后，闭合K3K4
 *              2.如果是手动充电在收到PCU确认可以手动启动的情况下则
 *                直接闭合K1K2进入充电状态（送检调试用）
 *              3.如果是自动充电，则进入握手状态并同时启动BMS任务
 * @note
 ******************************************************************************
 */
static void Deal_Ready(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    if (Get_CcuCfgParaEuropeEnable())
    {
        RELEASE_OFF;                                /**不开泄放的阶段关泄放*/
                                                    //        K3_K4_ON;/*欧标无bms辅助电源供电*/
        taskDelay(50);                              /**<保证K3K4先闭合在启动Bms数据发送*/
        if (CHARGE_MODE_MANUAL == Get_ChargeMode()) //            if (eResult_Succ == Get_SelfcheckStartFlag())
        {
            if (Get_ChargeMoudleStartUp()) /**<开机完成*/
            {
                K1_K2_ON;
                pChargeCtrl->chargeCurOverBCLCnt = 0;
                pChargeCtrl->chargeVolOverBCLCnt = 0;
                pChargeCtrl->chargeVolOverSetCnt = 0;
                pChargeCtrl->chargeVolOverBCPCnt = 0;
                Set_WorkState(CCU_WORK_STATE_CHARGING);
            }
        }
        else
        {
            Set_WorkState(CCU_WORK_STATE_SHAKE_HAND);
            // Start_BMS();
        }
    }
    else
    {
#ifdef ELECK_CLOCK_CHECK_EN
        Set_ElecClockOperate(sElecClock_Lock);
#else
        ELEC_LOCK_ON;
#endif
        RELEASE_OFF; /**不开泄放的阶段关泄放*/
        if (FALSE == Get_EnableFlag(eErrType_ElecLockErr) ||
            eSwitchState_ON == Get_SwitchState(SXIO_IN_DCS))
        {
            K3_K4_ON;
            if (Get_EnableYL() && LQ_PRIO_YKSHELL != Get_LQCore_BussyPrio(0x30) && TRUE != Get_LQCore_enable(0X30, 0x06, START_STOP_CONTROL))

            {
                Set_LQCore_BussyPrio(0X30, LQ_PRIO_YK);
                Set_LQCore_data(0x30, 0x06, START_STOP_CONTROL, 0xFF00, LQ_PRIO_YK, 0xFF00);
                Set_LQCore_enable(0x30, 0x06, START_STOP_CONTROL, TRUE);
            }
            taskDelay(50); /**<保证K3K4先闭合在启动Bms数据发送*/
            if (CHARGE_MODE_MANUAL == Get_ChargeMode())
            {
                if (Get_ChargeMoudleStartUp()) /**<开机完成*/ //            if (eResult_Succ == Get_SelfcheckStartFlag())
                {
                    K1_K2_ON;
                    pChargeCtrl->chargeCurOverBCLCnt = 0;
                    pChargeCtrl->chargeVolOverBCLCnt = 0;
                    pChargeCtrl->chargeVolOverSetCnt = 0;
                    pChargeCtrl->chargeVolOverBCPCnt = 0;
                    Set_WorkState(CCU_WORK_STATE_CHARGING);
                }
            }
            else
            {
                Set_WorkState(CCU_WORK_STATE_SHAKE_HAND);
                Start_BMS();
            }
        }
    }
    return;
}
/**
 ******************************************************************************
 * @brief       充电握手
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 *
 *
 *
 * @note
 ******************************************************************************
 */
static void Deal_ShakeHand(void)
{
    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
    if (Get_CcuCfgParaEuropeEnable())
    {
        if (Get_EcPlcAagVal() != 0)
        {
            Set_WorkState(CCU_WORK_STATE_RECOGNIZE);
        }
    }
    else
    {
        Set_WorkState(CCU_WORK_STATE_IMD);
        // todo: 考虑这个标记是否有存在的必要。
        pChargeCtrl->imdStartFlag = TRUE;
        Set_Imd_Stage(IMD_IDLE);
        RELEASE_OFF; /**不开泄放的阶段关泄放*/
    }
    return;
}

/**
 ******************************************************************************
 * @brief       绝缘检测
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 *
 *
 *
 * @note
 ******************************************************************************
 */
static void Deal_IMD(void)
{
    if (Get_EvccidSimState() == SIM_EVCCID_WAIT)
    {
        Set_ImdSuccFlag(TRUE);
        return;
    }

    CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

    if (FALSE == Get_ImdStartFlag())
    {
        Set_ImdStartFlag(TRUE);
        SAMPLE_PARA strSamplePara;
        Get_PilePara((void *)&strSamplePara, eParaType_SamplePara);
        RELEASE_OFF; /**不开泄放的阶段关泄放*/

        if (TRUE == pChargeCtrl->imdFinishFlag)
        {
            if (ePcuWorkState_Busy != Get_PcuWorkState())
            {
                pChargeCtrl->imdFinishFlag = FALSE;
                pChargeCtrl->imdStartFlag = FALSE;
                Set_Imd_Stage(IMD_IDLE);
                Set_WorkState(CCU_WORK_STATE_RELEASE_01); /**<绝缘检测超时错误 也需要泄放*/
                reset_chargeActFlag(1);
                trace(TR_DEBUG, "IMD_END!\n");
            }
        }
        else
        {
            if (TRUE == Get_EnableFlag(eErrType_ImdErr))
            {
                if (IMD_IDLE == Get_Imd_Stage()) /**<启动绝缘检测*/
                {
                    pChargeCtrl->imdTick = tickGet(); /**启动计时*/
                    Imd_Start();
                    Set_ImdSwOffActFlag();
                    pChargeCtrl->imdFinishFlag = FALSE;
                }
                if (IMD_FINISH == Get_Imd_Stage()) /**<绝缘检测 完成*/
                {
                    pChargeCtrl->imdFinishFlag = TRUE;
                }
                if (abs(tickGet() - pChargeCtrl->imdTick) > (20 * sysClkRateGet()))
                {
                    Imd_Err_Ctrl(IMD_CHECK_ERR); // Imd_Err_Ctrl(IMD_CHECK_TIMEOUT);       /**绝缘检测超时*/
                    pChargeCtrl->imdFinishFlag = TRUE;
                }
            }
            else
            {
                Imd_Err_Ctrl(IMD_NCHECK);
                pChargeCtrl->imdFinishFlag = TRUE;
            }
        }
    }

    /**
     ******************************************************************************
     * @brief       泄放1
     * @param[in]   None
     * @param[out]  None
     * @retval
     * @details
     *
     *
     *
     * @note
     ******************************************************************************
     */
    static void Deal_ReleaseInsualtion(void)
    {
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
        CONFIG_PARA strCfgPara;
        Get_PilePara((void *)&strCfgPara, eParaType_ConfigPara);
        RESULT releaseResult = eResult_NULL;
        static uint8 isActed = FALSE;
        // todo：将release01Timer改成tick计时
        pChargeCtrl->release01Timer += 1;

        if (pChargeCtrl->release01ActTimer > 0)
        {
            pChargeCtrl->release01ActTimer -= 1;
        }

        if (TRUE == Get_EnableFlag(eErrType_ReleaseErr))
        {
            if (pChargeCtrl->release01Timer > (20000 / CHARGE_CALL_CYCLE))
            {
                RELEASE_OFF;
                Set_ErrType(eErrType_ReleaseErr);
                pChargeCtrl->release01ActTimer = 0;
                isActed = FALSE;
                return;
            }

            if (ePcuWorkState_Free == Get_PcuWorkState())
            {
                if ((isActed != TRUE) || (pChargeCtrl->release01ActTimer > 1))
                {
                    if (strCfgPara.xfOpenEnable)
                        RELEASE_ON;
                }

                if ((isActed == FALSE) && (pChargeCtrl->release01ActTimer == 0))
                {
                    pChargeCtrl->release01ActTimer = 1000 / CHARGE_CALL_CYCLE;
                }
                isActed = TRUE;
            }

            releaseResult = Check_ReleaseValue();
            if ((releaseResult != eResult_Succ) && (pChargeCtrl->release01ActTimer == 1))
            {
                RELEASE_OFF;
                pChargeCtrl->release01ActTimer = 0;
            }
        }
        else
        {
            releaseResult = eResult_Succ;
        }
        if (eResult_Succ == releaseResult)
        {
            RELEASE_OFF;
            pChargeCtrl->release01ActTimer = 0;
            isActed = FALSE;
            K1_K2_OFF;
            trace(TR_CHARGE, "K1_K2_OFF11\n");
            /**保证K1K2断开后再进行通讯*/
            for (int i = 0; i < 15; i++)
            {

                if (eSwitchState_OFF == Get_SwitchState(SXIO_IN_K1) && eSwitchState_OFF == Get_SwitchState(SXIO_IN_K2))
                {
                    break;
                }
                taskDelay(10);
            }
            /* 国标2011，需重新打开K3K4，同时启动BMS */
            pChargeCtrl->release01SuccFlag = TRUE;
            if (Get_BMS_Ver() == 0xA5)
            {
                K3_K4_ON;
                Start_BMS();
            }

            Set_WorkState(CCU_WORK_STATE_RECOGNIZE);
        }
        return;
    }

    /**
     ******************************************************************************
     * @brief      .
     * @param[in]  None
     * @param[out] None
     * @retval
     *
     * @details
     *
     * @note
     ******************************************************************************
     */

    static void Deal_Recognize(void)
    {
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

        Set_WorkState(CCU_WORK_STATE_CONFIG);
        pChargeCtrl->BCP_VolFalseCnt = 0;
        pChargeCtrl->BCP_VolTrueCnt = 0;
        /**************小桔新增*****240329*****************/
        pChargeCtrl->BCP_VolFalse1Cnt = 0;
        pChargeCtrl->BCP_VolFalse2Cnt = 0;
        RELEASE_OFF; /**不开泄放的阶段关泄放*/
                     //    Set_BmsRecvFlag(BMS_PGN_BCP, eRecvFlag_No);
                     //    Set_BmsRecvFlag(BMS_PGN_BCS, eRecvFlag_No);
                     //    Set_BmsRecvFlag(BMS_PGN_BCL, eRecvFlag_No);
                     //    Clr_BMS_RecvFlg(BMS_PGN_BCS);
                     //    Clr_BMS_RecvFlg(BMS_PGN_BCL);
        return;
    }

    static void Deal_Config(void)
    {
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

        Clr_ChargeMoudleStartUp(); // TODO -xg 北京入网增加.

        pChargeCtrl->preChargeSuccFlag = FALSE;
        Set_WorkState(CCU_WORK_STATE_PRE_CHARGE);
        if (Get_CcuCfgParaEuropeEnable())
        {
            Set_Imd_Stage(IMD_IDLE);
            pChargeCtrl->imdFinishFlag = FALSE;
            pChargeCtrl->imdStartFlag = FALSE;
            pChargeCtrl->imdTick = tickGet(); /**启动计时*/
        }
        chargeVolCheckTick(TRUE);
        Set_ChargeActFlag(eActFlag_On);
        RELEASE_OFF; /**不开泄放的阶段关泄放*/
        return;
    }

    static void Deal_PreCharge(void)
    {
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

        /* 如果BMS因为模拟获取ID而请求跳过，我们就在此处理 */
        if (Bms_ShouldSkipPrechargeInsulation())
        {
            /*
             * 通过直接置位imdFinishFlag，我们"欺骗"状态机，
             * 让它认为绝缘检测已瞬间完成。这会使得流程跳过
             * 耗时的硬件检测，直接进入后续的预充电继电器闭合阶段。
             */
            if (FALSE == pChargeCtrl->imdFinishFlag) /* 仅在初次进入时置位和打印一次 */
            {
                trace(TR_ALWAYS, "Precharge/IMD check skipped by BMS request.\n");
                pChargeCtrl->imdFinishFlag = TRUE;
            }
        }

        RESULT   = eResult_NULL;
        static bool pre_bypass = FALSE; // 预充旁路    欧标使用
        static uint32 tick = 0;
        if (Get_CcuCfgParaEuropeEnable())
        {
            if (TRUE == pChargeCtrl->imdFinishFlag)
            {
                if (FALSE == pre_bypass)
                {
                    if ((Get_K1K2InsideVol() < 600) && (ePcuWorkState_Busy != Get_PcuWorkState())) // 停机并且内测电压小于60V时  闭合预充旁路
                    {
                        pre_bypass = TRUE;
                        PRE_RELAY_ON;
                        tick = tickGet();
                    }
                    if (abs(tickGet() - tick) > 3 * sysClkRateGet())
                    {
                        Set_ErrType(eErrType_ReleaseErr);
                    }
                }
                else
                {
                    if (abs(tickGet() - tick) > 2 * sysClkRateGet())
                    {
                        K1_K2_ON;
                        taskDelay(20);
                        PRE_RELAY_OFF;
                        Set_Imd_Stage(IMD_IDLE);
                    }
                }
#if 0
            if(Get_K1K2OutsideVol() < 600)
            {
                pChargeCtrl->release01SuccFlag = TRUE;
                RELEASE_OFF;
                pre_bypass = FALSE;
            }
            if(ePcuWorkState_Busy != Get_PcuWorkState())
            {
                if(release_act == FALSE)
                {
                    release_act = TRUE;
                    RELEASE_ON;
                    pChargeCtrl->release01ActTimer = tickGet();
                }
            }
            else
            {
                pChargeCtrl->release01ActTimer = tickGet();
            }
            if(abs(tickGet() - pChargeCtrl->release01ActTimer) > sysClkRateGet())
            {
                pChargeCtrl->release01SuccFlag = FALSE;
                RELEASE_OFF;
                Set_ErrType(eErrType_ReleaseErr);
                release_act = FALSE;
            }
#endif
            }
            else
            {
                pre_bypass = FALSE;

                pChargeCtrl->release01SuccFlag = FALSE;
                pChargeCtrl->release01ActTimer = tickGet();
                tick = tickGet();
                if (TRUE == Get_EnableFlag(eErrType_ImdErr))
                {
                    if (IMD_IDLE == Get_Imd_Stage()) /**<启动绝缘检测*/
                    {
                        pChargeCtrl->imdTick = tickGet(); /**启动计时*/
                        Imd_Start();
                        Set_ImdSwOffActFlag();
                        pChargeCtrl->imdFinishFlag = FALSE;
                    }
                    if (IMD_FINISH == Get_Imd_Stage()) /**<绝缘检测 完成*/
                    {
                        pChargeCtrl->imdFinishFlag = TRUE;
                        K1_K2_OFF;
                        trace(TR_CHARGE, "K1_K2_OFF12\n");
                    }
                    if (abs(tickGet() - pChargeCtrl->imdTick) > (15 * sysClkRateGet()))
                    {
                        Imd_Err_Ctrl(IMD_CHECK_ERR); // Imd_Err_Ctrl(IMD_CHECK_TIMEOUT);       /**绝缘检测超时*/
                        pChargeCtrl->imdFinishFlag = TRUE;
                    }
                }
                else
                {
                    Imd_Err_Ctrl(IMD_NCHECK);
                    pChargeCtrl->imdFinishFlag = TRUE;
                }
            }
            if (TRUE == pre_bypass)
            {
                if ((eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BCL)) && (eSwitchState_ON == Get_SwitchState(SXIO_IN_K2)) && (eSwitchState_ON == Get_SwitchState(SXIO_IN_K1)))
                {
                    pre_bypass = FALSE;
                    Set_WorkState(CCU_WORK_STATE_CHARGING);
                    pChargeCtrl->release01SuccFlag = FALSE;
                    pChargeCtrl->imdFinishFlag = FALSE;
                    pChargeCtrl->imdStartFlag = FALSE;
                    trace(TR_CHARGE, "CCU_WORK_STATE_CHARGING!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n");
                }
                //            else  if(abs(tickGet() - tick) > 5*sysClkRateGet())
                //            {
                //                Set_ErrType(eErrType_K1Err);
                //            }
            }
        }
        else
        {
            RELEASE_OFF; /**不开泄放的阶段关泄放*/
            if (TRUE != pChargeCtrl->preChargeSuccFlag)
            {
                chargeReconnectFlg(TRUE, enumAllowFlag_Allow);
                pChargeCtrl->preChargeSuccFlag = TRUE;
            }
            else
            {
                if (eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BCS) &&
                    eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BCL))
                {
                    pChargeCtrl->chargeCurOverBCLCnt = 0;
                    pChargeCtrl->chargeVolOverBCLCnt = 0;
                    pChargeCtrl->chargeVolOverSetCnt = 0;
                    pChargeCtrl->chargeVolOverBCPCnt = 0;
                    Set_WorkState(CCU_WORK_STATE_CHARGING);

                    /* CCU开始统计充电时长和充电电量，以备不时只需  */
                    pChargeCtrl->chargeTick = tickGet();
                    pChargeCtrl->chargeTime = 0;
                    pChargeCtrl->chargePow = 0.0;
                    pChargeCtrl->imdFinishFlag = FALSE;
                    pChargeCtrl->imdStartFlag = FALSE;
                    trace(TR_CHARGE, "CCU_WORK_STATE_CHARGING!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n");
                }
            }
        }
        return;
    }

    static void Deal_Charging(void)
    {
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
        uint32 ticks;
        static bool imd_check = FALSE;
        static bool imd_time = FALSE;
        RELEASE_OFF; /**不开泄放的阶段关泄放*/
        if (enumAllowFlag_Forbid == Get_ChargePauseFlg())
        {
#if 0
        Set_k1k2OffDelay(80 / CHARGE_CALL_CYCLE);
        if (eSwitchState_OFF == Get_SwitchState(SXIO_IN_K1) &&
            eSwitchState_OFF == Get_SwitchState(SXIO_IN_K2))
        {
            Set_WorkState(CCU_WORK_STATE_CHARGE_PAUSE);
        }
#else
            Set_WorkState(CCU_WORK_STATE_CHARGE_PAUSE);
#endif
            return;
        }

        ticks = abs(tickGet() - pChargeCtrl->chargeTick);
        if (ticks > sysClkRateGet())
        {
            pChargeCtrl->chargeTick += 1000;
            pChargeCtrl->chargeTime += 1;
            pChargeCtrl->chargePow += (Get_K1K2OutsideVol() / 1000.0) * (Get_K1K2Current() / 1000.0) / 3600.0;
        }
        if (Get_CcuCfgParaEuropeEnable() && CHARGE_MODE_AUTO == Get_ChargeMode())
        {
            if (abs(tickGet() - pChargeCtrl->imdTick) > Get_ImdTime())
            {
                trace(TR_IMD, "pChargeCtrl->imdTick : %d,imdtime : %d\n", pChargeCtrl->imdTick, Get_ImdTime());
                pChargeCtrl->imdTick = tickGet();
                Set_insulationCheckInterval(TRUE);
                imd_time = TRUE;
            }

            if (imd_time || (FALSE == imd_check && Get_Imdstartstate())) // 5*60*sysClkRateGet()
            {
                imd_check = TRUE;
                imd_time = FALSE;
                pChargeCtrl->imdFinishFlag = FALSE;
                pChargeCtrl->imdStartFlag = FALSE;
            }
            if (TRUE == imd_check)
            {
                if (ImdCheck_ChargeDeal())
                {
                    imd_check = FALSE;
                    Set_insulationCheckInterval(FALSE);
                    Set_Imdstartstate(FALSE);
                    trace(TR_IMD, "imdtick : %d\n", tickGet());
                }
            }
        }
        uint8 deviceState = Get_DeviceState();

        if (DEVICE_STATE_MAJOR_FAULT == deviceState)
        {
            K1_K2_OFF; /*紧急故障直接断*/
            trace(TR_CHARGE, "K1k2OFF103\n");
        }
        return;
    }

    static void Deal_ChargePause(void)
    {
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
        RELEASE_OFF; /**不开泄放的阶段关泄放*/
        if (enumAllowFlag_Allow == Get_ChargePauseFlg())
        {
            pChargeCtrl->k1k2OffDelay = 0;
#if 1
            //        //todo : 将 TwoUint8ToUint16(strBCL.voltageDemand) 替换成 Get_DemondVol函数
            //        if (((Get_K1K2OutsideVol() - 100) < Get_K1K2InsideVol())
            //                && ((Get_K1K2OutsideVol() - 10) > Get_K1K2InsideVol()))
            //        {
            //            K1_K2_ON;
            pChargeCtrl->chargeCurOverBCLCnt = 0;
            pChargeCtrl->chargeVolOverBCLCnt = 0;
            pChargeCtrl->chargeVolOverSetCnt = 0;
            pChargeCtrl->chargeVolOverBCPCnt = 0;
            Set_WorkState(CCU_WORK_STATE_CHARGING);
//        }
#else
            pChargeCtrl->preChargeSuccFlag = FALSE;
            Set_WorkState(CCU_WORK_STATE_PRE_CHARGE);
            chargeVolCheckTick(TRUE);
            pChargeCtrl->stopTick = 0;
#endif
        }
        return;
    }

    static void Deal_ChargeStop(void)
    {
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
        CHARGE_STOP_TYPE stopType = 0;
        RELEASE_OFF; /**不开泄放的阶段关泄放*/
        Set_ChargeActFlag(eActFlag_Off);
        Set_k3k4OffDelay(12 * sysClkRateGet() / CHARGE_CALL_CYCLE);
        if (DEVICE_STATE_MAJOR_FAULT == Get_DeviceState()) /**<紧急故障时100ms内断开K3K4*/
        {
            if (TRUE == Check_ErrType(eErrType_GunConnectErr))
            {
                if (Get_K1K2Current() <= 5000)
                {
                    K1_K2_OFF;
                    trace(TR_CHARGE, "K1_K2_OFF13\n");
                    Set_k1k2OffDelay(0);
                }
                else
                {
                    Set_k1k2OffDelay(22 / CHARGE_CALL_CYCLE); // 暂定修改。协议要求50ms超时时间
                    printf("50ms延时继电器\n");
                }
            }
            else
            {
                stopType = eChargeStop_Abnormal;
                K1_K2_OFF;
                trace(TR_CHARGE, "K1_K2_OFF14\n");
                if ((TRUE != Check_ErrType(eErrType_GunConnectErr)) && /**<导引断开时不能关闭K3K4*/
                    (TRUE != Check_ErrType(eErrType_CcuDoorOpenErr)) &&
                    (TRUE != Check_ErrType(eErrType_PcuDoorOpenErr)) &&
                    (TRUE != Check_ErrType(eErrType_EmergencyStop)) &&
                    (TRUE != Check_ErrType(eErrType_PcuEmergencyStop)))

                {
                    Set_StopFinishFlag(TRUE);
                    taskDelay(80);
                    Stop_BMS(FALSE);
                    K3_K4_OFF; // Deal_ChargeStop
                    pChargeCtrl->k3k4OffFinishFlag = TRUE;
#ifdef ELECK_CLOCK_CHECK_EN
                    taskDelay(10);
                    Set_ElecClockOperate(sElecClock_Unlock);
#else
                    ELEC_LOCK_OFF;
#endif
                    Set_BMS_Ver(0x00);
                    trace(TR_CHARGE, "K3k4OFF2\n");
                }
            }
        }
        else if (DEVICE_STATE_FAULT == Get_DeviceState())
        {
            stopType = eChargeStop_Normal;
            if (TRUE == Check_ErrType(eErrType_BROErr))
            {
                Stop_BMS(FALSE);
                K3_K4_OFF; // Deal_ChargeStop
                pChargeCtrl->k3k4OffFinishFlag = TRUE;
#ifdef ELECK_CLOCK_CHECK_EN
                Set_ElecClockOperate(sElecClock_Unlock);
#else
                ELEC_LOCK_OFF;
#endif
                trace(TR_CHARGE, "K3k4OFF9\n");
            }
        }
        else if (eComState_TimeOut == Get_BmsComState())
        {
            stopType = eChargeStop_Normal;
        }
        else if (eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BST))
        {
            if (CHARGE_MODE_AUTO == Get_ChargeMode())
            {
                if (enumPhyConVol_4V != Get_PhyConVol())
                {
                    //                K1_K2_OFF;
                    if (Get_K1K2Current() <= 5000)
                    {
                        K1_K2_OFF;
                        trace(TR_CHARGE, "K1_K2_OFF15\n");
                        Set_k1k2OffDelay(0);
                    }
                    else
                    {
                        Set_k1k2OffDelay(25 / CHARGE_CALL_CYCLE); // 暂定修改。协议要求50ms超时时间
                        printf("50ms延时继电器\n");
                    }
                    //                stopType = eChargeStop_Abnormal;
                }
                else
                {
                    stopType = eChargeStop_Normal;
                }
            }
            // 手动充电条件下不要判导引，正常停机
            else
            {
                stopType = eChargeStop_Normal;
            }
        }
        else
        {
            stopType = eChargeStop_Normal;
        }
        if (eChargeStop_Normal == stopType)
        {
            if (TRUE == Check_ErrType(eErrType_ComErrWithBMS)) /**<BMS超时故障不用100ms断开K1K2*/
            {
                Set_k1k2OffDelay(1000 / CHARGE_CALL_CYCLE);
            }
            else
            {
                if (Get_K1K2Current() <= 4500)
                {
                    K1_K2_OFF;
                    trace(TR_PCU_PROCESS, "Set_k1k2OffDelay3\n");
                }
                else
                {
                    Set_k1k2OffDelay(Get_A_5PortTime() / CHARGE_CALL_CYCLE); /*85*/
                    trace(TR_PCU_PROCESS, "Set_k1k2OffDelay4\n");
                }
            }
        }
        else if (eChargeStop_Abnormal == stopType)
        {
            K1_K2_OFF;
            trace(TR_CHARGE, "K1_K2_OFF16\n");
            Set_k1k2OffDelay(0);
        }
        if (Get_EnableYL() && LQ_PRIO_YKSHELL != Get_LQCore_BussyPrio(0x30) && TRUE != Get_LQCore_enable(0X30, 0x06, START_STOP_CONTROL))
        {
            Set_LQCore_data(0x30, 0x06, START_STOP_CONTROL, 0x0000, LQ_PRIO_YK, 0x0000);
            Set_LQCore_enable(0x30, 0x06, START_STOP_CONTROL, TRUE);
            Set_LQCore_BussyPrio(0X30, LQ_PRIO_YK);
        }
        if (eSwitchState_OFF == Get_SwitchState(SXIO_IN_K1) &&
            eSwitchState_OFF == Get_SwitchState(SXIO_IN_K2))
        {
            if ((Get_K1K2OutsideVol() < 600) || (abs(tickGet() - pChargeCtrl->chargeTick) > 3000))
            {
                trace(TR_CHARGE, "进入泄放2流程1 ,外侧电压为：%d\n", Get_K1K2OutsideVol());
                Set_WorkState(CCU_WORK_STATE_RELEASE_02);
                reset_chargeActFlag(2);
                if (TRUE == Check_ErrType(eErrType_ComErrWithPCU))
                {
                    pChargeCtrl->release02Delay = 10000 / CHARGE_CALL_CYCLE;
                }
            }
        }
        else
        {
            if ((TRUE == Check_ErrType(eErrType_K1Err)) || (TRUE == Check_ErrType(eErrType_K2Err)))
            {
                // todo：假如  出现关机失败， K1K2也出现异常了，可能会出现死等的情况，怎么避免。
                if (Get_K1K2OutsideVol() < 100 && Get_K1K2InsideVol() < 600)
                {
                    trace(TR_CHARGE, "进入泄放2流程2 ,外侧电压为：%d\n", Get_K1K2OutsideVol());
                    Set_WorkState(CCU_WORK_STATE_RELEASE_02);
                    reset_chargeActFlag(2);
                }
            }
        }

        return;
    }

    uint32 reconnectFinishTick(uint8 wr, uint32 tick)
    {
        static uint32 reconnectfinishtick = 0;
        if (wr)
        {
            reconnectfinishtick = tick;
        }
        return reconnectfinishtick;
    }
    /**
     ******************************************************************************
     * @brief     充电结束泄放 .
     * @param[in]  None
     * @param[out] None
     * @retval
     *
     * @details
     *
     * @note
     ******************************************************************************
     */
    static void Deal_ReleaseChargeStop(void)
    {
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
        CONFIG_PARA strCfgPara;
        Get_PilePara((void *)&strCfgPara, eParaType_ConfigPara);
        static uint8 isActed = FALSE;

        if (pChargeCtrl->release02Delay > 0)
        {
            pChargeCtrl->release02Delay--;
            return;
        }

        pChargeCtrl->release02Timer += 1;

        if (pChargeCtrl->release02ActTimer > 0)
        {
            pChargeCtrl->release02ActTimer -= 1;
        }

        if (TRUE == Get_EnableFlag(eErrType_ReleaseErr))
        {
            if (pChargeCtrl->release02Timer > (20000 / CHARGE_CALL_CYCLE))
            {
                RELEASE_OFF;
                Set_ErrType(eErrType_ReleaseErr);
                pChargeCtrl->release02FinishFlag = TRUE;
            }
            else
            {
                if (ePcuWorkState_Free == Get_PcuWorkState())
                {
                    if ((isActed != TRUE) || (pChargeCtrl->release02ActTimer > 1))
                    {
                        if (strCfgPara.xfOpenEnable)
                            RELEASE_ON;
                    }

                    if ((isActed == FALSE) && (pChargeCtrl->release02ActTimer == 0))
                    {
                        pChargeCtrl->release02ActTimer = 1000 / CHARGE_CALL_CYCLE;
                    }
                    isActed = TRUE;
                }

                if (Check_ReleaseValue() == eResult_Succ)
                {
                    RELEASE_OFF;
                    pChargeCtrl->release02FinishFlag = TRUE;
                }
                else
                {
                    if (pChargeCtrl->release02ActTimer == 1)
                    {
                        RELEASE_OFF;
                        pChargeCtrl->release02ActTimer = 0;
                    }
                }
            }
        }
        else
        {
            pChargeCtrl->release02FinishFlag = TRUE;
        }

        if (TRUE == pChargeCtrl->release02FinishFlag)
        {
            RELEASE_OFF;
            trace(TR_CHARGE, "泄放完成  tick = %d\n", tickGet());
            isActed = FALSE;
            reconnectFinishTick(TRUE, tickGet());
            Set_StopFinishFlag(FALSE);
            Set_WorkState(CCU_WORK_STATE_STOP_FINISH);
            pChargeCtrl->clrDelay = 4000 / CHARGE_CALL_CYCLE;
        }

        return;
    }

    const PILE_ERROR_TYPE CLR_ERR_TYPE_MAP[] =
        {
            eErrType_ComErrWithTCU,
            eErrType_ComErrWithBMS,
            eErrType_OutputVolOverLimit,
            eErrType_OutputVolLessLimit,
            eErrType_OutputCurOverLimitBSM,
            eErrType_TempOverLimitBSM,
            /*eErrType_TempOverLimitBSM,*/
            eErrType_StopFinishAckTimeOutTcu,
            eErrType_ImdErr,
            eErrType_BatteryReverseConnect,
            eErrType_K5K6ErrBMS,
            eErrType_OutputCurOverLimit,
            eErrType_FuseProtectorErr,
            /*eErrType_PileTempOverLimitErr,
             eErrType_SmokeErr,*/
            eErrType_InputVolOverLimit,
            eErrType_InputVolLessLimit,
            /*eErrType_PowerModuleErr,*/
            eErrType_PcuBusyTimeout,
            /*eErrType_DCMainContactorErr1,
             * eErrType_ElecLockErr,
             * eErrType_PileFanErr,
             * eErrType_GunTempOverLimitErr*/
            eErrType_TCUOtherErr,
            /*eErrType_DCMainContactorSynechia,*/
            eErrType_ReleaseErr,
            /*eErrType_AssistPowerErr,
             * eErrType_MultipleContactorSynechia ,*/
            /*eErrType_K1Err,
             * eErrType_K2Err ,*/
            eErrType_BmsFaultStopErr,
            eErrType_OutputShortCut,
            eErrType_BHMVolErr,
            eErrType_BHMVolErr1,
            //   eErrType_PEErr,
            eErrType_K1K2OutsideVolErr1,
            eErrType_BCPVolErr1,
            eErrType_K1K2OutsideVolErr2,
            eErrType_K1K2OutsideVolErr3,
            eErrType_BatteryVolErr,
            eErrType_BRMErr,
            eErrType_BCPErr,
            eErrType_ChargePauseTimeout,
            /*eErrType_NoFreePowerModule,*/
            eErrType_LoadCtrlSwitch,
            eErrType_ComErrWithIMD,
            /*eErrType_ComErrWithPCU,
             * eErrType_CabTempOverErr,
             * eErrType_PcuStopCharge,
             * eErrType_PcuForbidCharge,
             * eErrType_ReadMeterErr,
             * eErrType_InputOpenphase,*/
            eErrType_SOCTooHighBSM,
            eErrType_SOCTooLowBSM,
            eErrType_ImdErrBSM,
            eErrType_PhyConErrBSM,
            eErrType_PhySingleVoltooHighBSM,
            eErrType_PhySingleVoltooLowBSM,
            eErrType_StartFinishAckTimeOutTcu,
            eErrType_ModuleOnOffErr,
            eErrType_BROErr,
            eErrType_SStartTimeOut,
            //   eErrType_DropDownErr1,
            /*eErrType_WaterLoggingErr,
             * eErrType_PCUOtherErr,
             * eErrType_SwErr,
             * eErrType_HearterErr,
             * eErrType_CcuYxYcTimeoutErr ,
             * eErrType_FanErr,
             * eErrType_SrqErr,*/
            eErrType_QStartTimeOut,
            eErrType_ImdTimeOut,
            eErrType_K1K2OutsideVolErr4,
            /*eErrType_BlqErr*/
            eErrType_PlugAndPlayAckTimeout,
            eErrType_PlugAndPlayAckErr,
            /* eErrType_EmergencyStop ,*/
            eErrType_GunConnectErr,
            /* eErrType_ACBreakerErr,
             * eErrType_CcuDoorOpenErr,
             * eErrType_AcJCQErr ,
             * eErrType_PcuDoorOpenErr ,
             * eErrType_PowerModuleFanAlarm,
             * eErrType_PowerModuleTempAlarm,
             * eErrType_PowerModuleACInAlarm,
             * eErrType_PowerModuleOutShortCutAlarm,
             * eErrType_PowerModuleOutCurOverAlarm,
             * eErrType_PowerModuleOutVolOverAlarm,
             * eErrType_PowerModuleOutVolLessAlarm,
             * eErrType_PowerModuleInVolOverAlarm ,
             * eErrType_PowerModuleInVolLessAlarm,
             * eErrType_PowerModuleInOpenPhaseAlarm,
             * eErrType_PowerModuleComAlarm,
             * eErrType_PowerModuleXfAlarm,*/
            eErrType_ImdAlarm,
            /* eErrType_GunNoHoming,
             * eErrType_PileTempOverLimitAlarm,
             * eErrType_GunTempOverLimitAlarm,
             * eErrType_Working,*/
            eErrType_VerCheckUnfinished,
            eErrType_SetParaUnfinished,
            /* eErrType_PcuTempOverLimitAlarm,
             * eErrType_BlqAlarm,
             * eErrType_FanAlarm ,
             * eErrType_HearterAlarm,
             * eErrType_InVolOverAlarm,
             * eErrType_InVolLessAlarm,
             * eErrType_InOpenPhaseAlarm,
             * eErrType_SwAlarm ,
             * eErrType_PcuComAlarm,
             * eErrType_PcuOtherAlarm */

            eErrType_TcuCarVinIllegalityErr,
            eErrType_TcuCarVinConfirmFailErr,
            eErrType_TcuCarVinConfirmTimeoutErr,
            eErrType_VinInconformityErr,
            eErrType_TcuCarVinOtherErr,
            eErrType_CarVinConfirmTimeoutErr,

            eErrType_BusCurOverCarLimite,
            eErrType_BusVolOverCarLimite,
            eErrType_OutputOverMaxAllowOutputVol,
            eErrType_OutputOverMaxAllowChargeVol,
            eErrType_OutputOverDemandVol,
            eErrType_DemandOverMaxAllowChargeVol,

            eErrType_OutputOverDemandCur,
            eErrType_OutputOverMaxChargeCur,
            eErrType_OutputOverMaxAllowOutputCur,
            eErrType_DemandOverMaxAllowChargeCur,
            eErrType_DemandLowMinAllowOutputVol,
            eErrType_ChargeParaNoMatch,
            eErrType_TCU,
        };

    static void Deal_StopFinish(void)
    {
        extern void Set_Manual_Cc1Vol(uint8 dat);
        uint8 index = 0;
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;
        static uint32 stopFinishTime = 0;

        RELEASE_OFF; /**不开泄放的阶段关泄放*/
        if (chargeReconnectFinishFlg(FALSE, FALSE))
        {
            if (abs(tickGet() - reconnectFinishTick(FALSE, 0)) > sysClkRateGet())
            {
                Stop_BMS(FALSE);
                K3_K4_OFF; // Deal_StopFinish
                pChargeCtrl->k3k4OffFinishFlag = TRUE;
                trace(TR_CHARGE, "K3k4OFF10 tick = %d\n", tickGet());
            }
        }

        if (eActFlag_On == Get_BMS_StartFlg())
        {
            return;
        }
        // TODO: 0923,可直接关K3K4
        extern uint32 Get_BmsOverTime();
        if (eRecvFlag_Yes != Get_BmsRecvFlag(BMS_PGN_BSD))
        //||    	(Get_BmsOverTime() == BMS_PGN_BSD))
        {
            if ((BMS_STAGE_STOPFINISH == Get_BmsStage()) || (BMS_STAGE_FREE == Get_BmsStage()))
            {
                // trace(TR_CHARGE, "K3k4OFF3\n");
                Stop_BMS(TRUE);
                taskDelay(30);
                K3_K4_OFF; // Deal_StopFinish
                pChargeCtrl->k3k4OffFinishFlag = TRUE;
#ifdef ELECK_CLOCK_CHECK_EN
                taskDelay(10);
                Set_ElecClockOperate(sElecClock_Unlock);
#else
                ELEC_LOCK_OFF;
#endif
            }
        }

        if (pChargeCtrl->clrDelay > 0)
        {
            pChargeCtrl->clrDelay--;

            if ((2000 / CHARGE_CALL_CYCLE) == pChargeCtrl->clrDelay)
            {
                trace(TR_CHARGE, "K3k4OFF4\n");
                K3_K4_OFF; // Deal_StopFinish
                pChargeCtrl->k3k4OffFinishFlag = TRUE;
#ifdef ELECK_CLOCK_CHECK_EN
                taskDelay(10);
                Set_ElecClockOperate(sElecClock_Unlock);
#else
                ELEC_LOCK_OFF;
#endif
            }
        }
        if (pChargeCtrl->clrDelay == 0)
        {
            for (index = 0; index < FCNT(CLR_ERR_TYPE_MAP); index++)
            {
                //        	if(TRUE == Check_ErrType(CLR_ERR_TYPE_MAP[index]))
                //        	{
                Clr_ErrType(CLR_ERR_TYPE_MAP[index]);
                //        	}
            }
            Set_Manual_Cc1Vol(0);
            if (enumPhyConVol_4V != Get_PhyConVol() ||
                CHARGE_MODE_MANUAL == Get_ChargeMode() ||
                FALSE == Get_EnableFlag(eParaFmt_CfgGunOffReChargeEnable))
            {
                chargeReconnectFinishFlg(TRUE, FALSE);
                Set_WorkState(CCU_WORK_STATE_FREE);
                stopFinishTime = 0;
                Set_ReaderResult(eReaderResult_NULL);
                Init_ChargeCtrl();
                Set_StopFinishFlag(TRUE);
                trace(TR_CHARGE, "stop over!!!!\n");
            }
        }
        return;
    }

    typedef bool_e (*PconditionFunc)(uint8 workState);
    typedef void (*PexecuteFunc)(void);
    typedef struct STATE_MANAGE_STRU
    {
        uint8 state;
        PconditionFunc conditionFunc[8];
        PexecuteFunc executeFunc;
    } STATE_MANAGE;

    STATE_MANAGE stateManageTable[] =
        {
            {CCU_WORK_STATE_FREE,
             {Clear_ProcessFault, Check_ChargeStartFlag, Check_FaultState, Check_PhyConVol,
              Check_PcuWorkStateFlag, NULL, NULL, NULL},
             Deal_Free},

            {CCU_WORK_STATE_READY,
             {Check_FaultState, Check_PhyConVol, Check_ChargeStopFlag, NULL, NULL,
              NULL, NULL, NULL},
             Deal_Ready},

            {CCU_WORK_STATE_SHAKE_HAND,
             {Check_FaultState, Check_PhyConVol, Check_BmsOvertime, Check_ChargeStopFlag,
              Check_BHM_Recv, Check_BHM_Vol, Check_K1K2OutsideVol, NULL},
             Deal_ShakeHand},

            {CCU_WORK_STATE_IMD,
             {Check_FaultState, Check_PhyConVol, Check_BmsOvertime, Check_ChargeStopFlag,
              Check_ChargeMoudleStartFlag, Check_K1K2OnFlag, NULL, NULL},
             Deal_IMD},

            {CCU_WORK_STATE_RELEASE_01,
             {Check_FaultState, Check_PhyConVol, Check_BmsOvertime, Check_ChargeStopFlag,
              NULL, NULL, NULL, NULL},
             Deal_ReleaseInsualtion},

            {CCU_WORK_STATE_RECOGNIZE,
             {Check_FaultState, Check_PhyConVol, Check_BmsOvertime, Check_ChargeStopFlag,
              Check_ChargePause, Check_BCP_Recv, NULL, NULL},
             Deal_Recognize},

            {CCU_WORK_STATE_CONFIG,
             {Check_FaultState, Check_PhyConVol, Check_BmsOvertime, Check_ChargeStopFlag,
              Check_ChargePause, Check_SPN_2829, Check_VehicleValidate, Check_BCP_Vol},
             Deal_Config},

            {CCU_WORK_STATE_PRE_CHARGE,
             {Check_FaultState, Check_PhyConVol, Check_BmsOvertime, Check_ChargeStopFlag,
              Check_ChargePause, Check_ChargeVol, Check_ChargeMoudleStartFlag, Check_K1K2OnFlag},
             Deal_PreCharge},

            {CCU_WORK_STATE_CHARGING,
             {Check_FaultState, Check_PhyConVol, Check_BmsOvertime, Check_ChargeStopFlag,
              Check_ChargePause, Check_BST_Recv, Check_ChargeVol, Check_ChargeCur},
             Deal_Charging},

            {CCU_WORK_STATE_CHARGE_PAUSE,
             {Check_FaultState, Check_PhyConVol, Check_BmsOvertime, Check_ChargeStopFlag,
              Check_ChargePause, Check_BST_Recv, Check_ChargeVol, NULL},
             Deal_ChargePause},

            {CCU_WORK_STATE_CHARGE_STOP,
             {Check_BmsOvertime, NULL, NULL, NULL,
              NULL, NULL, NULL, NULL},
             Deal_ChargeStop},

            {CCU_WORK_STATE_RELEASE_02,
             {Check_BmsOvertime, NULL, NULL, NULL,
              NULL, NULL, NULL, NULL},
             Deal_ReleaseChargeStop},

            {CCU_WORK_STATE_STOP_FINISH,
             {Check_FaultState, Check_PhyConVol, Check_BmsOvertime, NULL,
              NULL, NULL, NULL, NULL},
             Deal_StopFinish},
        };

    static void Monitor_GunConnectionState(void)
    {
        static uint8 lastPhyConVol = 0xFF;
        uint8 currentPhyConVol = Get_PhyConVol();

        if (0xFF == lastPhyConVol)
        {
            lastPhyConVol = currentPhyConVol;
            return;
        }

        if ((currentPhyConVol != enumPhyConVol_4V) && (lastPhyConVol == enumPhyConVol_4V))
        {
            if (IsEvccidAcquired())
            {
                trace(TR_ALWAYS, "Gun physically unplugged, resetting EVCCID acquired flag.\n");
                SetEvccidAcquired(FALSE);
                Set_EvccidSimState(SIM_NONE);
            }
        }
        lastPhyConVol = currentPhyConVol;
    }

    static void WorkStateManage(void)
    {
        STATE_MANAGE *pStateManage = NULL;
        PconditionFunc pFunc = NULL;
        uint8 index = 0;
        uint8 tmp = 0;
        uint8 ret = TRUE;

        Monitor_GunConnectionState();

        if (Get_EvccidSimState() == SIM_EVCCID_DONE)
        {
            trace(TR_ALWAYS, "EVCCID Sim: Acquired, stopping charge.");
            Set_StopSrc(eChargeStopFlag_Auto);
            Set_ChargeActFlag(eActFlag_Off);
            s_evccid_acquired = TRUE;
            Set_EvccidSimState(SIM_NONE);
        }

        for (index = 0; index < FCNT(stateManageTable); index++)
        {
            pStateManage = &stateManageTable[index];
            ret = TRUE;

            if (s_WorkState != pStateManage->state)
            {
                continue;
            }

            for (tmp = 0; tmp < FCNT(pStateManage->conditionFunc) && TRUE == ret;
                 tmp++)
            {
                pFunc = pStateManage->conditionFunc[tmp];

                if (NULL == pFunc)
                {
                    continue;
                }

                ret = pFunc(s_WorkState);
            }

            if (TRUE == ret)
            {
                pStateManage->executeFunc();
            }
        }

        return;
    }

    /**
     ******************************************************************************
     * @brief       检测电力输出停止
     * @param[in]   None
     * @param[out]  None
     * @retval
     *
     * @details     需要停止电力输出的地方给k1k2OffDelay赋值,在100ms内电流降到5A以下
     *              或者100ms超时，然后断开K1K2
     *
     * @note
     ******************************************************************************
     */
    static void StopChargeOutput(void)
    {
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

        if (pChargeCtrl->k1k2OffDelay > 0)
        {
            if (Get_K1K2Current() < 4500) /**<   xx.xxx单位0.001A   */
            {
                pChargeCtrl->k1k2OffDelay = 0;
                K1_K2_OFF;
                trace(TR_CHARGE, "K1_K2_OFF17\n");
            }
            else
            {
                pChargeCtrl->k1k2OffDelay--;
                if (0 == pChargeCtrl->k1k2OffDelay)
                {
                    printf("延时时间到!!\n");
                    K1_K2_OFF;
                    trace(TR_CHARGE, "K1_K2_OFF18\n");
                }
            }
        }
    }

    /**
     ******************************************************************************
     * @brief       断K3K4
     * @param[in]   None
     * @param[out]  None
     * @retval
     *
     * @details     需要停止电力输出的地方给k3k4OffDelay赋值,在100ms内电流降到5A以下
     *              或者100ms超时，然后断开K1K2
     *
     * @note
     ******************************************************************************
     */
    static void StopK3K4(void)
    {
        CHARGE_CTRL *pChargeCtrl = &chargeCtrl;

        if (pChargeCtrl->k3k4OffDelay > 0)
        {
            pChargeCtrl->k3k4OffDelay--;
            if (0 == pChargeCtrl->k3k4OffDelay)
            {
                Set_StopFinishFlag(TRUE);
                if (Get_EnableYL() && LQ_PRIO_YKSHELL != Get_LQCore_BussyPrio(0x30) && TRUE != Get_LQCore_enable(0X30, 0x06, START_STOP_CONTROL))
                {
                    Set_LQCore_data(0x30, 0x06, START_STOP_CONTROL, 0x0000, LQ_PRIO_YK, 0x0000);
                    Set_LQCore_enable(0x30, 0x06, START_STOP_CONTROL, TRUE);
                    Set_LQCore_BussyPrio(0X30, LQ_PRIO_YK);
                }
                taskDelay(80);
                Stop_BMS(FALSE);
                K3_K4_OFF; // Deal_ChargeStop
                pChargeCtrl->k3k4OffFinishFlag = TRUE;
                Set_BMS_Ver(0x00);
                trace(TR_CHARGE, "K3k4OFF[%d]\n", __LINE__);
            }
        }
    }

    /**
     ******************************************************************************
     * @brief       检测电力输出停止
     * @param[in]   None
     * @param[out]  None
     * @retval
     *
     * @details     需要停止电力输出的地方给stopChargeDelay赋值,在100ms内电流降到5A以下
     *              或者100ms超时，然后断开K1K2
     *
     * @note
     ******************************************************************************
     */
    static void printfWorkState(void)
    {
        static uint8 lastworkState = 0, lastbmsStage = 0, lastpcuStage = 0, lasttcuStage = 0;
        if ((Get_WorkState() != lastworkState) || (Get_BmsStage() != lastbmsStage) || (lastpcuStage != Get_PcuStage()) || (lasttcuStage != Get_TcuStage()))
        {
            lastworkState = Get_WorkState();
            lastbmsStage = Get_BmsStage();
            lastpcuStage = Get_PcuStage();
            lasttcuStage = Get_TcuStage();
            trace(TR_CHARGE, "ChargeState<%-2d>---BmsStage<%-2d>---PcuStage<%-2d>---TcuStage<%-2d>\n",
                  Get_WorkState(), Get_BmsStage(), Get_PcuStage(), Get_TcuStage());
        }
    }

    void Init_485_1(void)
    {
        fd_4851 = ttyOpen(COM_485_1);

        ttyIoctl(fd_4851, SIO_BAUD_SET, 9600);
        ttyIoctl(fd_4851, SIO_HW_OPTS_SET,
                 (UART_CONFIG_WLEN_8 | UART_CONFIG_STOP_ONE | UART_CONFIG_PAR_EVEN));

        return;
    }
    /**
     ******************************************************************************
     * @brief      充电枪空闲解锁
     * @param[in]   None
     * @param[out]  None
     * @retval
     * @details
     * @note
     ******************************************************************************
     */
    static void ChargeJunctor_UnLock(void)
    {
        static uint32 lastTicks = 0;

        if (abs(tickGet() - lastTicks) > (5 * 60 * sysClkRateGet())) /**< 5分钟 判断是否误锁*/
        {
            lastTicks = tickGet();

            if ((eSwitchState_ON == Get_SwitchState(SXIO_IN_DCS)) && (CCU_WORK_STATE_FREE == Get_WorkState()))
            {
                ELEC_LOCK_ON;
                taskDelay(500);
                ELEC_LOCK_OFF;
            }
        }
    }

    static bool_e ElecClock_Flag = sElecClock_Unlock;

    /**
     ******************************************************************************
     * @brief      .
     * @param[in]  None
     * @param[out] None
     * @retval
     *
     * @details
     *
     * @note
     ******************************************************************************
     */
    void Set_ElecClockOperate(bool_e flag)
    {
        ElecClock_Flag = flag;
    }

    /**
     ******************************************************************************
     * @brief      .
     * @param[in]  None
     * @param[out] None
     * @retval
     *
     * @details
     *
     * @note
     ******************************************************************************
     */
    ElecClockOpr
    Get_ElecClockOperate(void)
    {
        return ElecClock_Flag;
    }

#define UNLOCK_DELAY (1000)
    /**
     ******************************************************************************
     * @brief      ElecClock_Operate.
     * @param[in]  None
     * @param[out] None
     * @retval     电磁锁操作函数.
     *
     * @details
     *
     * @note       解锁条件：
     *             (1)CCU处于空闲状态；
     *             (2)外侧电压小于60V
     ******************************************************************************
     */
    void ElecClock_Operate(void)
    {
        static uint8 Unlock_Cnt = 0;
        static bool_e Unlock_Enable = FALSE; /**< 解锁使能*/
        static uint32 Unlock_Timer = 0;      /**< 解锁定时器*/
        static uint8 Unlock_Step = 0;        /**< 解锁步骤*/
        if (Get_CcuCfgParaEuropeEnable())
        {
            return;
        }

        switch (Get_ElecClockOperate())
        {
        case sElecClock_Unlock:
        {
            if (Get_K1K2OutsideVol() < Get_Out_Elc()) /**< 外侧电压大于60V,不解锁 600*/
            {
                if (0 == Unlock_Cnt)
                {
                    Unlock_Cnt++;
                    ELEC_LOCK_OFF; // 至少解锁一次
                }

                if (eSwitchState_OFF == Get_SwitchState(SXIO_IN_DCS))
                {
                    Unlock_Enable = FALSE;
                    return;
                }

#if 0
                Unlock_Enable++;

                if (Unlock_Enable > 5)
                {
                    ELEC_LOCK_ON;
                    taskDelay(200);
                }

                ELEC_LOCK_OFF;
                taskDelay(200);
#else
                if (FALSE == Unlock_Enable) // TODO -xg 用于解锁步骤初始化
                {
                    Unlock_Enable = TRUE;
                    Unlock_Step = 0;
                    Unlock_Timer = tickGet();
                }

                trace(TR_CHARGE, "断开---DCS---step---<%d>\n", Unlock_Step);

                if (TRUE == Unlock_Enable)
                {
                    switch (Unlock_Step)
                    {
                    case 0: /**< 处理正常解锁*/
                    {
                        ELEC_LOCK_OFF;
                        if (abs(tickGet() - Unlock_Timer) > 2 * sysClkRateGet())
                        {
                            Unlock_Step++;
                            Unlock_Timer = tickGet();
                        }
                    }
                    break;
                    case 1: /**< 处理异常解锁，先闭合，延时200ms，再断开*/
                    {
                        ELEC_LOCK_ON;
                        if (abs(tickGet() - Unlock_Timer) > UNLOCK_DELAY)
                        {
                            Unlock_Step++;
                            Unlock_Timer = tickGet();
                        }
                    }
                    break;
                    case 2:
                    {
                        ELEC_LOCK_OFF;
                        if (abs(tickGet() - Unlock_Timer) > UNLOCK_DELAY)
                        {
                            Unlock_Step--;
                            Unlock_Timer = tickGet();
                        }
                    }
                    break;
                    default:
                    {
                        Unlock_Step = 0;
                        Unlock_Enable = FALSE;
                        trace(TR_CHARGE, "Unlock---DCS---步骤错误---<%d>\n", Unlock_Step);
                    }
                    break;
                    }
                }
#endif
            }
            else
            {
                trace(TR_CHARGE, "断开---DCS---外侧电压---<%d>\n", Get_K1K2OutsideVol());
            }
        }
        break;
        case sElecClock_Lock:
        {
            Unlock_Cnt = 0;

            if (eSwitchState_ON == Get_SwitchState(SXIO_IN_DCS))
            {
                return;
            }

            trace(TR_CHARGE, "闭合---DCS---外侧电压---<%d>---\n", Get_K1K2OutsideVol());

            Unlock_Enable = FALSE;
            ELEC_LOCK_ON;
        }
        break;
        default:
        {
            trace(TR_CHARGE, "错误---DCS---外侧电压---<%d>---\n", Get_K1K2OutsideVol());
        }
        break;
        }
    }

    /**
     ******************************************************************************
     * @brief      充电主控制任务
     * @param[in]   None
     * @param[out]  None
     * @retval
     * @details
     * @note
     ******************************************************************************
     */
    void Charge_Task(void)
    {
        Init_485_1();
        Init_ChargeCtrl();
        dmnTaskRegister(); // 任务注册
        testCardInit();
        uint32 lastTicks = 0;
        uint32 ticksDelay;
        FOREVER
        {
#if 1
            ticksDelay = tickGet() - lastTicks;
            ticksDelay = (ticksDelay > CHARGE_CALL_CYCLE) ? 1 : CHARGE_CALL_CYCLE - ticksDelay;
            taskDelay(ticksDelay);
            lastTicks = tickGet();

            // sysLedRun(0);
#else
            taskDelay(CHARGE_CALL_CYCLE);
#endif
            dmnTaskSigned(); // 任务签到

            TESTLISTEN; /**< 板级测试模式监听 */

            WorkStateManage(); // 工作状态管理

            StopChargeOutput();  // 电力输出停止检测
            StopK3K4();          // K3K4断开检测
            ElecClock_Operate(); // 电磁锁解锁处理
            printfWorkState();
            //        Imd_Server(500, fd_4851);// 绝缘服务
            //        Hmi_Server(50, fd_4851);// 人机交互服务
            //        Card_Server(200);// 刷卡服务
            //        ChargeJunctor_UnLock(); /**< 空闲状态持续解锁*/
        }

        dmnTaskUnRegister(); // 任务注销
        taskDelete(NULL);
    }

    /**
     ******************************************************************************
     * @brief      绝缘检测
     * @param[in]   None
     * @param[out]  None
     * @retval
     * @details
     * @note
     ******************************************************************************
     */
    void Imd_Task(void)
    {
        uint32 dmnTick = 0;
        dmnTaskRegister(); // 任务注册
        taskDelay(100);
        FOREVER
        {
            taskDelay(1);

            if (abs(tickGet() - dmnTick) >= sysClkRateGet())
            {
                dmnTick = tickGet();
                dmnTaskSigned(); // 任务签到
            }
            TESTLISTEN;               /**< 板级测试模式监听 */
            Imd_Server(500, fd_4851); // 绝缘服务
        }

        dmnTaskUnRegister(); // 任务注销
        taskDelete(NULL);
    }

    /*----------------------------chargeMain.c--------------------------------*/

    EVCCID_SIM_STATE Get_EvccidSimState(void)
    {
        return s_EvccidSimState;
    }

    void Set_EvccidSimState(EVCCID_SIM_STATE state)
    {
        if (s_EvccidSimState != state)
        {
            trace(TR_ALWAYS, "EVCCID Sim State Change: %d -> %d\n", s_EvccidSimState, state);
            s_EvccidSimState = state;
        }
    }

    bool_e IsEvccidAcquired(void)
    {
        return s_evccid_acquired;
    }

    static void SetEvccidAcquired(bool_e acquired)
    {
        s_evccid_acquired = acquired;
    }

    static void HandleSimStop(void)
    {
        if (Get_EvccidSimState() == SIM_EVCCID_DONE)
        {
            if (Get_WorkState() < CCU_WORK_STATE_CHARGE_STOP)
            {
                trace(TR_ALWAYS, "EVCCID Sim: EVCCID acquired, stopping charge.\n");
                Set_StopSrc(eChargeStopFlag_Auto);
                Set_ChargeActFlag(eActFlag_Off);
                SetEvccidAcquired(TRUE);
                Set_EvccidSimState(SIM_NONE);
            }
        }
    }

    static void HandleSimStart(void)
    {
        if (Get_CcuCfgParaEuropeEnable() && !IsEvccidAcquired())
        {
            Set_EvccidSimState(SIM_EVCCID_WAIT);
        }
    }

    static bool_e HandleSimImdSkip(void)
    {
        if (Get_CcuCfgParaEuropeEnable() && Get_EvccidSimState() == SIM_EVCCID_WAIT)
        {
            trace(TR_ALWAYS, "EVCCID Sim: Skipping IMD check.\n");
            Set_ImdSuccFlag(TRUE);
            return TRUE;
        }
        return FALSE;
    }
