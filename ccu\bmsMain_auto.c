/**
 ******************************************************************************
 * @file      bmsMain.c
 * @brief     C Source file of bmsMain.c.
 * @details   This file including all API functions's
 *            implement of bmsMain.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <can.h>
#include <string.h>
#include <taskLib.h>
#include <dmnLib.h>
#include <stdlib.h>
#include <stdio.h>
#include <sxlib.h>
#include <trace.h>

#include "bmsMain.h"
#include "bmsDll.h"
#include "bmsSendCtrl.h"
#include "bmsRecvCtrl.h"

#include <bms.h>
#include <ccu\charge\ccuChargeMain.h>
#include <ccu\bsn\deviceState.h>
#include <test.h>
#include <ccu\para\para.h>
#include <maths.h>
#include "../bsn/sample.h"
#include <ccu\tcu\tcuMain.h>
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
typedef void
(*PdealFunc)(void);
typedef struct BMS_STAGE_DEAL_STRU
{
    uint8 stage;
    PdealFunc func;
} BMS_STAGE_DEAL;
/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */
/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
BMS_DATA bmsData; /* BMS数据*/
BMS_CTRL bmsCtrl; /* BMS控制*/

extern uint8 isBHMFirstData;
static EVCCID_SIM_STATE s_EvccidSimState = SIM_NONE;
static bool_e s_evccid_acquired = FALSE;
/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
/////////////////////////////////////////////////////////////////////////////////

uint16
Get_AutoChargeTry(void)
{
    CHARGE_PARA strChargePara;
    Get_PilePara((void *) &strChargePara, eParaType_ChargePara);
    return strChargePara.autocharge_trycnt;
}

uint16
Get_AutoChargeDelay(void)
{
    CHARGE_PARA strChargePara;
    Get_PilePara((void *) &strChargePara, eParaType_ChargePara);
    return strChargePara.autocharge_delay;
}

void
Bms_Init(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    BMS_DATA *pBmsData = &bmsData;


    memset(pBmsCtrl, 0x00, sizeof(BMS_CTRL));
    if (Get_CcuCfgParaEuropeEnable()) //欧标增加
    {
        memset(&pBmsData->strBRM, 0x00, sizeof(BMS_DATA)- MEMBER_SIZE(BMS_DATA,strBHM));
        memset(&pBmsData->strBHM.highlestTotalVoltage[0], 0x00, sizeof(BHM_DATA)-MEMBER_SIZE(BHM_DATA,plcCpStatus));
//        memset(&pBmsData->strBHM.highlestTotalVoltage[0], 0x00, MOFFSET(BHM_DATA,plcCpStatus));
        printf("=======[%s]======[%d]======[%d]=====\n",__FUNCTION__,__LINE__,pBmsData->strBHM.plc_CpStatus.CpStatus);
    }
    else
    {
        memset(pBmsData, 0x00, sizeof(BMS_DATA));
    }
    //默认为启动
    if (DEV_STD_GW == Get_CcuCfgParaEuropeEnable() && Get_EnableAutoCharge())
    {
        pBmsCtrl->tryBACConCnt = Get_AutoChargeTry();
        pBmsCtrl->bac_revc_cnt = 5;
    }

    pBmsData->strBSM.strBatteryCtrl.chargeAllowFlg = enumAllowFlag_Allow;
    pBmsData->strBSM.strBatteryCtrl.reconnect = enumAllowFlag_Allow;
    for(uint8 i = 0; i < 2 ; i++)
    {
        Set_InitPrintf(i,FALSE);
    }
    isBHMFirstData = 1; //第一次接收BHM报文标记
    return;
}

/**
 * @brief 原子递减变量并返回新值（关中断保护）
 * @param p      目标变量指针（需volatile）
 * @param delta  递减量
 * @return       操作后的新值
 */
int atomic_dec_and_fetch(volatile int *p, int delta)
{
    taskLock();      // 关调度器或中断
    int new_val = (*p -= delta);
    taskUnlock();
    return new_val;
}

/**
 * @brief 禁用BMS相关通信（针对欧洲标准GW设备且自动充电使能的情况）
 * @note  当设备为欧洲标准GW且自动充电使能时，关闭BMS_PGN_CDT的发送和接收，并禁用BMS_PGN_BAC的接收。
 */
void DisableBmsComIfEuropeGwAutoChargeEnabled(void)
{
    if (DEV_STD_GW == Get_CcuCfgParaEuropeEnable() && Get_EnableAutoCharge())
    {
        Set_BmsSendRemainTimer(BMS_PGN_CDT, 0);
        Set_BmsSendFlg(BMS_PGN_CDT, eSendFlag_No);
        Set_BmsRecvEnable(BMS_PGN_BAC, eRecvEnable_Off);
        Set_BmsRecvTimerEnable(BMS_PGN_BAC, eTimerEnable_Off);
    }
}



/**
 * @brief 如果BMS_PGN_CDT未在发送状态，则设置其发送定时器
 * @note  当BMS_PGN_CDT的发送标志不是eSendFlag_Yes时，配置其发送定时器参数
 */
void SetupBmsCdtSendTimerIfNotSending(void)
{
    if (DEV_STD_GW == Get_CcuCfgParaEuropeEnable() && Get_EnableAutoCharge())
    {
        printf("======EuropeEnable : %d===========AutoCharge :%d=================\n",Get_CcuCfgParaEuropeEnable(),Get_EnableAutoCharge());
        printf("======BmsSendFlg : %d============\n",Get_BmsSendFlg(BMS_PGN_CDT));
        if (eSendFlag_Yes != Get_BmsSendFlg(BMS_PGN_CDT))
        {
            Set_BmsSendRemainTimer(BMS_PGN_CDT, 200);
            Set_BmsLastSendTimer(BMS_PGN_CDT, 0xFFFF);
            Set_BmsStartTimer(BMS_PGN_CDT);
        }
    }
}

/**
 * @brief 根据BAC连接尝试次数控制BMS通信状态
 * @param pBmsCtrl BMS控制结构体指针
 * @note 当tryBACConCnt减到0时关闭通信，否则检查并启动CDT发送
 */
void HandleBmsComByRetryCount(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    if (atomic_dec_and_fetch(&pBmsCtrl->tryBACConCnt, 1) <= 0)
    {
        if(pBmsCtrl->tryBACConCnt == 0)
        {
            Set_BmsSendRemainTimer(BMS_PGN_CDT, 0);
            Set_BmsSendFlg(BMS_PGN_CDT, eSendFlag_No);
            Set_BmsRecvEnable(BMS_PGN_BAC, eRecvEnable_Off);
            Set_BmsRecvTimerEnable(BMS_PGN_BAC, eTimerEnable_Off);
            printf("重发次数到 :%d\n",pBmsCtrl->tryBACConCnt);
        }
    }
    else if (eSendFlag_Yes != Get_BmsSendFlg(BMS_PGN_CDT))
    {
        SetupBmsCdtSendTimerIfNotSending();
        pBmsCtrl->bac_revc_cnt = 5;
        Set_BmsRecvEnable(BMS_PGN_BAC, eRecvEnable_Off);
        printf("HandleBmsComByRetryCount :%d\n",pBmsCtrl->tryBACConCnt);
        printf("bac_revc_cnt :%d\n",pBmsCtrl->bac_revc_cnt);
    }
}

/**
 * @brief 检测物理连接电压是否从非4V变为4V
 * @return true 当电压从非4V变为4V时返回true
 */
static bool CheckPhyConVolRiseTo4V(void)
{
    static uint8_t lastPhyConVol = 0xFF;
    uint8_t currentPhyConVol = Get_PhyConVol();

    // 初始化记录值
    if (0xFF == lastPhyConVol)
    {
        lastPhyConVol = currentPhyConVol;
        return false;
    }

    // 检测4V上升沿
    bool isRiseTo4V = (currentPhyConVol == enumPhyConVol_4V) &&
                     (lastPhyConVol != enumPhyConVol_4V);

    // 更新记录值
    lastPhyConVol = currentPhyConVol;

    return isRiseTo4V;
}

/**
 * @brief 检测CP状态在PlugIn(1)和DutyOn(2)之间切换的事件
 * @return true 当状态在eCpStatus_PlugIn和eCpStatus_DutyOn之间切换时返回
 */
static bool CheckCpPlugInDutyOnTransition(void)
{
    static eCpStatus lastCpStatus = eCpStatus_Unplug;  // 初始为未插枪状态
    eCpStatus currentCpStatus = Get_EcPlcCpStatus();

    // 首次调用时初始化状态
    if (lastCpStatus == eCpStatus_Unplug)
    {
        lastCpStatus = currentCpStatus;
        return false;
    }

    // 检测PlugIn <-> DutyOn状态切换
    bool isTransition = ((lastCpStatus == eCpStatus_PlugIn &&
                         currentCpStatus == eCpStatus_DutyOn) ||
                        (lastCpStatus == eCpStatus_DutyOn &&
                         currentCpStatus == eCpStatus_PlugIn));

    // 更新状态记录
    lastCpStatus = currentCpStatus;

    return isTransition;
}

uint8
Get_BmsStage(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    return pBmsCtrl->bmsStage;
}

void
Set_BmsStage(uint8 stage)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    if (stage != pBmsCtrl->bmsStage)
    {
        trace(TR_BMS_PROCESS, "BMS阶段切换  当前阶段:%d   前一阶段:%d\n", stage,
                pBmsCtrl->bmsStage);
        pBmsCtrl->bmsStage = stage;
    }
}

/**
 ******************************************************************************
 * @brief      Bms_FreeDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details     1.检测BMS服务启动标记
 *              2.初始化处理
 *              3.置bms阶段为握手阶段
 * @note         空闲阶段处理
 ******************************************************************************
 */
static void
Bms_FreeDeal(void)
{
    trace(TR_BMS_PROCESS, "Bms_FreeDeal()\n");
    can_init(BMS_CHAN, CAN_BAD_250);
    Bms_Init();
    trace(TR_BMS_PROCESS, "Init_OK()\n");
    Set_BmsStage(BMS_STAGE_SHAKEHAND);
    return;
}

/**
 ******************************************************************************
 * @brief       握手阶段处理
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details     1.检测停机标记
 *              2.启动CHM发送（执行一次）
 *
 * @note
 ******************************************************************************
 */
static void
Bms_ShakeHandDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    BMS_DATA *pBmsData = &bmsData;
    //是否外部需要停机

    if (Get_CcuCfgParaEuropeEnable()) //欧标增加
    {
        if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CHM))
        {
            Set_BmsSendRemainTimer(BMS_PGN_CHM, 0xFFFF);
            Set_BmsLastSendTimer(BMS_PGN_CHM, 0xFFFF);
            return;
        }
        if(CheckPhyConVolRiseTo4V())
        {
            if(TCU_STAGE_RUN_FREE  == Get_TcuStage())
            {
                if (DEV_STD_GW == Get_CcuCfgParaEuropeEnable() && Get_EnableAutoCharge())
                {
                    pBmsCtrl->tryBACConCnt = Get_AutoChargeTry();
                    pBmsCtrl->bac_revc_cnt = 5;
                    memset(&pBmsData->strBRM.ec.evccId[1],0,6);
                }
                SetupBmsCdtSendTimerIfNotSending();
                printf("======第一次发送!!!!====\n");
            }
        }
        if(CheckCpPlugInDutyOnTransition())
        {
            if (DEV_STD_GW == Get_CcuCfgParaEuropeEnable() && Get_EnableAutoCharge())
            {
                memcpy(&pBmsData->strBRM.ec.evccId[1], pBmsData->strBAC.mac_address,
                                                            sizeof(pBmsData->strBAC.mac_address));
            }
        }
        if (Get_EcPlcAagVal() != 0)
        {
            Set_BmsStage(BMS_STAGE_RECOGNIZE);
        }
    }
    else
    {
        if (eActFlag_Off == Get_ChargeActFlag())
        {
            Stop_BMS(TRUE);
            return;
        }
        //2011国标，无BHM，直接等绝缘泄放完成
        if (Get_BMS_Ver() != 0xA5)
        {
            //是否通信超时
            if (eComState_Normal != pBmsCtrl->comState)
            {
                Bms_OverTimeDeal();
                return;
            }
            //CHM的发送使能是否打开
            if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CHM))
            {
                Set_BmsSendRemainTimer(BMS_PGN_CHM, 0xFFFF);
                Set_BmsLastSendTimer(BMS_PGN_CHM, 0xFFFF);
                return;
            }
        }
        //第一次泄放完成
        if (TRUE == Get_Release01SuccFlag())
        {
            Set_BmsStage(BMS_STAGE_RECOGNIZE);
        }
    }
    return;
}

/**
 ******************************************************************************
 * @brief      Bms_RecognizeDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note        辨识阶段处理
 ******************************************************************************
 */
static void
Bms_RecognizeDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    isBHMFirstData = 1;
    Set_BmsSendFlg(BMS_PGN_CST, eSendFlag_No);
    Set_BmsSendRemainTimer(BMS_PGN_CST, 0);
    //是否外部需要停机
    if (eActFlag_Off == Get_ChargeActFlag() && !Get_CcuCfgParaEuropeEnable())
    {
    	Set_BmsStage(BMS_STAGE_STOP);
        Stop_BMS(TRUE);
        return;
    }

    if (eComState_Normal != pBmsCtrl->comState)
    {
        Bms_OverTimeDeal();
        return;
    }
    //CRM的发送使能是否打开

    if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CRM))
    {
        Set_BmsSendRemainTimer(BMS_PGN_CRM, 0xFFFF);
        Set_BmsLastSendTimer(BMS_PGN_CRM, 0xFFFF);
        pBmsCtrl->spn_2560 = 0x00;

        Set_BmsSendRemainTimer(BMS_PGN_CHM, 0);

    }
    pBmsCtrl->spn_2830 = 0x00;
    pBmsCtrl->lastBROState = 0x00; /**不清除掉会导致重连时数据终止*/
    return;
}

/**
 ******************************************************************************
 * @brief      Bms_ParaConfigDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note        参数配置阶段处理
 ******************************************************************************
 */
static void
Bms_ParaConfigDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    Set_BmsSendFlg(BMS_PGN_CST, eSendFlag_No);
    Set_BmsSendRemainTimer(BMS_PGN_CST, 0);

    //是否外部需要停机
 
    if (eActFlag_Off == Get_ChargeActFlag())
    {
        if(Get_CcuCfgParaEuropeEnable())
        {
            Set_BmsSendRemainTimer(BMS_PGN_CRO, 0x00);
            Set_BmsStage(BMS_STAGE_STOP);
        }
        else
        {
        	Set_BmsStage(BMS_STAGE_STOP);
            Stop_BMS(TRUE);
            Set_BmsRecvEnable(BMS_PGN_BRO, eRecvEnable_Off);

            Set_BmsRecvEnable(BMS_PGN_BRO_AA, eRecvEnable_Off);
            Set_BmsRecvTimerEnable(BMS_PGN_BRO_AA, eTimerEnable_Off);
            return;
        }
    }

    if (eComState_Normal != pBmsCtrl->comState)
    {
        Bms_OverTimeDeal();
        return;
    }
//    deviceState = Get_DeviceState();
//    if ((DEVICE_STATE_MAJOR_FAULT == deviceState)
//            || (DEVICE_STATE_FAULT == deviceState))
//    {
//        Set_BmsStage(BMS_STAGE_STOP);
//        return;
//    }
    if (Get_CcuCfgParaEuropeEnable())
    {
        if (Get_ImdSuccFlag()&& Get_K1K2InsideVol() < 600)
        {
            pBmsCtrl->spn_2830 = 0xAA;
            if(Check_ErrType(eErrType_ImdAlarm))
            {
                pBmsCtrl->evseIsolationStatus = 0x02;
            }
            else if( Check_ErrType(eErrType_ImdTimeOut)||
                    Check_ErrType(eErrType_ImdErr) )
            {
                pBmsCtrl->evseIsolationStatus = 0x03;
            }
            else
            {
                pBmsCtrl->evseIsolationStatus = 0x01;
            }
        }
    }
    else
    {
        if (TRUE == Get_PreChargeSuccFlag())
        {
            pBmsCtrl->spn_2830 = 0xAA; /**<置CRO发送AA*/
        }
    }
}

/**
 ******************************************************************************
 * @brief       Bms_ChargingDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note        充电阶段处理
 ******************************************************************************
 */
static void
Bms_ChargingDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    Set_BmsSendFlg(BMS_PGN_CST, eSendFlag_No);
    Set_BmsSendRemainTimer(BMS_PGN_CST, 0);

    //是否外部需要停机
    if (eActFlag_Off == Get_ChargeActFlag())
    {
        Set_BmsStage(BMS_STAGE_STOP);
        return;
    }

    if (eComState_Normal != pBmsCtrl->comState)
    {
        Bms_OverTimeDeal();
        return;
    }
}

/**
 ******************************************************************************
 * @brief       Bms_StopDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note        充电结束阶段处理
 ******************************************************************************
 */
static void
Bms_StopDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    Set_BmsSendRemainTimer(BMS_PGN_CRO, 0x00);
    if (eRecvEnable_On != Get_BmsRecvEnable(BMS_PGN_BEM))
    {
        Set_BmsRecvEnable(BMS_PGN_BEM, eRecvEnable_On);
        Set_BmsRecvFlag(BMS_PGN_BEM, eRecvFlag_No);
    }

    if (eComState_Normal != pBmsCtrl->comState)
    {
        Bms_OverTimeDeal();
    }

    if (0xFFFF != Get_BmsSendRemainTimer(BMS_PGN_CST))
    {
        if (eSendFlag_Yes != Get_BmsSendFlg(BMS_PGN_CST))
        {
            /* 已经发送过了，无需重复开启，等超时停止即可 */
            Set_BmsSendRemainTimer(BMS_PGN_CST, 10 * sysClkRateGet());
            Set_BmsLastSendTimer(BMS_PGN_CST, 0xFFFF);
            Set_BmsStartTimer(BMS_PGN_CST);
        }
        Set_BmsRecvEnable(BMS_PGN_BCL, eRecvEnable_Off);
        Set_BmsRecvTimerEnable(BMS_PGN_BCL, eTimerEnable_Off);

        Set_BmsRecvEnable(BMS_PGN_BCS, eRecvEnable_Off);
        Set_BmsRecvTimerEnable(BMS_PGN_BCS, eTimerEnable_Off);

        Set_BmsRecvEnable(BMS_PGN_BSM, eRecvEnable_Off);
        Set_BmsRecvEnable(BMS_PGN_BMV, eRecvEnable_Off);
        Set_BmsRecvEnable(BMS_PGN_BMT, eRecvEnable_Off);
        Set_BmsRecvEnable(BMS_PGN_BSP, eRecvEnable_Off);

        Set_BmsSendRemainTimer(BMS_PGN_CCS, 0);
        if(0x0000 == Get_BmsSendRemainTimer(BMS_PGN_CST))
        {
            Set_BmsStage(BMS_STAGE_STOPFINISH);
        }
    }
}

/**
 ******************************************************************************
 * @brief       Bms_StopFinishDeal
 * @param[in]   None
 * @param[out]  None
 * @retval      None
 *
 * @details
 *
 *
 * @note        充电结束完成阶段处理
 ******************************************************************************
 */
static void
Bms_StopFinishDeal(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;

    if (eComState_Normal != pBmsCtrl->comState)
    {
        Bms_OverTimeDeal();
    }
    if (Get_CcuCfgParaEuropeEnable())
    {
        Set_BmsStage(BMS_STAGE_FREE);
    }
    else
    {
        Stop_BMS(TRUE);
    }
}

const BMS_STAGE_DEAL BMS_STAGE_DEAL_TABLE[] =
    {
        { BMS_STAGE_FREE, 		Bms_FreeDeal },
        { BMS_STAGE_SHAKEHAND, 	Bms_ShakeHandDeal },
        { BMS_STAGE_RECOGNIZE, 	Bms_RecognizeDeal },
        { BMS_STAGE_PARACONFIG, Bms_ParaConfigDeal },
        { BMS_STAGE_CHARGING, 	Bms_ChargingDeal },
        { BMS_STAGE_STOP, 		Bms_StopDeal },
        { BMS_STAGE_STOPFINISH, Bms_StopFinishDeal }
    };

/**
 ******************************************************************************
 * @brief       Bms_StageManage
 * @param[in]   None
 * @param[out]  None
 * @retval
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
static void
Bms_StageManage(void)
{
    BMS_CTRL *pBmsCtrl = &bmsCtrl;
    const BMS_STAGE_DEAL *pStageDeal = NULL;
    uint8 index = 0;

    for (index = 0; index < FCNT(BMS_STAGE_DEAL_TABLE); index++)
    {
        pStageDeal = &BMS_STAGE_DEAL_TABLE[index];

        if (Get_BmsStage() == pStageDeal->stage)
        {
            if (NULL != pStageDeal->func)
            {
                pStageDeal->func();
            }
        }
    }
}
static void ManageEvccidAcquisition(void)
{
    /* --- 触发启动 --- */
    /*
     * 条件: 欧标模式, ID未获取, CCU空闲, 已插枪
     * 动作: 命令CCU开始充电, 并将BMS内部状态切换到等待ID
     */
    if (Get_EnableAutoCharge()&&
//        Get_CcuCfgParaEuropeEnable() &&
        !Bms_IsEvccidAcquired() &&
        Get_WorkState() == CCU_WORK_STATE_FREE &&
        (enumPhyConVol_4V == Get_PhyConVol())
        && eActFlag_On != Get_ChargeActFlag()
        && TCU_STAGE_RUN_FREE  == Get_TcuStage())

    {
        trace(TR_BMS_PROCESS, "BMS: Auto-starting charge for EVCCID acquisition.\n");
        Set_ChargeMode(CHARGE_MODE_AUTO);
        Set_ChargeActFlag(eActFlag_On);
        Set_EvccidSimState(SIM_EVCCID_WAIT); /* 切换到等待状态 */
    }

    /* --- 触发停止 --- */
    /*
     * 条件: BMS内部状态为"完成"
     * 动作: 命令CCU停止充电
     */
    if (Get_EnableAutoCharge()&&Get_EvccidSimState() == SIM_EVCCID_DONE)
    {
        trace(TR_BMS_PROCESS, "BMS: EVCCID acquired, commanding charge stop.\n");
        Bms_HandleEvccidAcquired(); /* 处理完成逻辑 */
    }
}
/**
 ******************************************************************************
 * @brief      Bms_Task
 * @param[in]   None
 * @param[out]  None
 * @retval
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
void
Bms_Task(void)
{
    uint32 dmnTick = 0;
    Bms_Init();
    dmnTaskRegister();

    FOREVER{
    if (abs(tickGet() - dmnTick) >= sysClkRateGet())
    {
        dmnTick = tickGet();
        dmnTaskSigned();
    }

    taskDelay(BMS_CALL_CYCLE);

    TESTLISTEN; /**< 板级测试模式监听 */

//    ManageEvccidAcquisition();

    if (TRUE == Get_StopFinishFlag()&&!Get_CcuCfgParaEuropeEnable())
    {
        Bms_Init();
    }

    if (eActFlag_On != Get_BMS_StartFlg()&&!Get_CcuCfgParaEuropeEnable())
    {
        can_clear(DLL_CHAN);
        continue;
    }

    Dll_RecvServer();

    Dll_OverTimer();

    Bms_RecvTimerManage();

    Bms_StageManage();

    Bms_SendServer();
}

    dmnTaskUnRegister();
    taskDelete(NULL);
}


EVCCID_SIM_STATE Get_EvccidSimState(void)
{
    return s_EvccidSimState;
}

void Set_EvccidSimState(EVCCID_SIM_STATE state)
{
    if (s_EvccidSimState != state)
    {
        trace(TR_BMS_PROCESS, "BMS EVCCID Sim State Change: %d -> %d\n", s_EvccidSimState, state);
        s_EvccidSimState = state;
    }
}

bool_e Bms_ShouldSkipImd(void)
{
    /* If in Euro mode and currently in the simulation process, we should skip the IMD check */
//    return (Get_CcuCfgParaEuropeEnable() && Get_EvccidSimState() == SIM_EVCCID_WAIT);
    return (Get_EvccidSimState() == SIM_EVCCID_WAIT);
}

bool_e Bms_ShouldSkipPrechargeInsulation(void)
{
    /* 在模拟获取ID流程中，跳过预充电绝缘检测 */
//    return (Get_CcuCfgParaEuropeEnable() && Get_EvccidSimState() == SIM_EVCCID_WAIT);
    return (Get_EvccidSimState() == SIM_EVCCID_WAIT);
}

bool_e Bms_IsEvccidAcquired(void)
{
    return s_evccid_acquired;
}

void Bms_HandleEvccidAcquired(void)
{
    Set_StopSrc(eChargeStopFlag_Auto);
    Set_ChargeActFlag(eActFlag_Off);
    s_evccid_acquired = TRUE;
    Set_EvccidSimState(SIM_NONE); /* 重置状态 */
}

void Bms_SetEvccidAcquired(uint8 evccid_acquired)
{
    s_evccid_acquired = evccid_acquired;
}
/*----------------------------bmsMain.c--------------------------------*/
