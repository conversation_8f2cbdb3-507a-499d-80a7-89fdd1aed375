
#ifndef APPLICATIONS_RTT_UART_RTT_UART_H_
#define APPLICATIONS_RTT_UART_RTT_UART_H_

#ifdef __cplusplus
extern "C"
{
#endif
#include <types.h>
#define RT_NULL 0
/* boolean type definitions */
#define RT_TRUE 1  /**< boolean true  */
#define RT_FALSE 0 /**< boolean fails */

/* RT-Thread error code definitions */
#define RT_EOK 0      /**< There is no error */
#define RT_ERROR 1    /**< A generic error happens */
#define RT_ETIMEOUT 2 /**< Timed out */
#define RT_EFULL 3    /**< The resource is full */
#define RT_EEMPTY 4   /**< The resource is empty */
#define RT_ENOMEM 5   /**< No memory */
#define RT_ENOSYS 6   /**< No system */
#define RT_EBUSY 7    /**< Busy */
#define RT_EIO 8      /**< IO error */
#define RT_EINTR 9    /**< Interrupted system call */
#define RT_EINVAL 10  /**< Invalid argument */

// 数据位长度（Word Length）
#define UART_DATA_BITS_5 5
#define UART_DATA_BITS_6 6
#define UART_DATA_BITS_7 7
#define UART_DATA_BITS_8 8

// 校验位（Parity）
#define UART_PARITY_NONE 'N'
#define UART_PARITY_EVEN 'E'
#define UART_PARITY_ODD 'O'

// 停止位（Stop Bits）
#define UART_STOP_BITS_1 1
#define UART_STOP_BITS_2 2

// 流控（Flow Control）
#define UART_FLOW_CONTROL_NONE 0
#define UART_FLOW_CONTROL_RTS_CTS 1

    extern int serial_open(char *devname, int baudrate, int bytesize, char parity, int stopbits, int xonxoff);
    extern int serial_close(int serial);
    extern int serial_send(int serial, uint8_t *buf, int len);
    extern int serial_receive(int serial, uint8_t *buf, int bufsz, int timeout, int bytes_timeout);
    extern int serial_sem_take(int serial_port, uint32_t timeout);
    extern void serial_sem_post(int serial_port);

#ifdef __cplusplus
}
#endif

#endif /* APPLICATIONS_RTT_UART_RTT_UART_H_ */
