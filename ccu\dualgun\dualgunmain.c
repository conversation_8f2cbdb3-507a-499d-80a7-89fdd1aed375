#include <message.h>
#include <pcu/eicu/inc/port.h>
#include "transceiver.h"
#include "can.h"
#include "maths.h"
#include "../pcu/pcuMain.h"
#include "trace.h"
#include <ccu\charge\ccuChargeMain.h>
#include <stdlib.h>
#include "../para/para.h"
#include <dmnLib.h>
#include <shell.h>
#include "dualgun.h"

static void Deal_DualCharge_Independent(int channel);
static void Deal_DualCharge_Insulation(int channel);
static void Deal_DualCharge_Master(int channel);
static void Deal_DualCharge_Slave(int channel);
static void Set_DualchargeMode(int channel, uint8 mode);

// 每个通道的状态转换保护器
static DualChargeStateGuard s_state_guard[DUAL_CHARGE_CHANNEL_NUM] = {{0}};
DUALCHARGE_CTRL s_dualcharge_ctrl[DUAL_CHARGE_CHANNEL_NUM] = {{0}};

const DUALCHARGE_STATUS_HEANDLE Dualcharge_StatusMap[] =
    {
        {eDualChargeStatus_Independent, Deal_DualCharge_Independent},
        {eDualChargeStatus_Insulation, Deal_DualCharge_Insulation},
        {eDualChargeStatus_Master, Deal_DualCharge_Master},
        {eDualChargeStatus_Slave, Deal_DualCharge_Slave},
};

char *
Get_DualchargeModeText(int status)
{
    static DUALTEXT WorkState_Text[] =
        {
            {eDualChargeStatus_Independent, "普通态"},
            {eDualChargeStatus_Insulation, "并充准备态"},
            {eDualChargeStatus_Master, "主机态"},
            {eDualChargeStatus_Slave, "从机态"},
        };

    for (int i = 0; i < ARRAY_SIZE(WorkState_Text); i++)
    {
        if (status == WorkState_Text[i].status)
        {
            return (WorkState_Text[i].text);
        }
    }
    return "未知状态";
}

/** @brief 优先级映射表 */
const PRIO_TBL Prio_Map[] =
    {
        {ID_DUALCHARGE_CTRL, 7}, ///< 控制指令ID对应优先级5 (0x10)
        {ID_DUALCHARGE_TELE, 7}, ///< 应答指令ID对应优先级5
};

/**
 ******************************************************************************
 * @brief      获取指定通道的双充电控制器实例
 * @param[in]  channel 通道号（从0开始）
 * @retval     成功返回控制器指针，失败返回NULL
 * @details    \n
 * | 条件        | 行为                          |\n
 * |-------------|-------------------------------|\n
 * | 通道有效    | 返回s_dualcharge_ctrl数组元素 |\n
 * | 通道越界    | 打印错误并返回NULL            |\n
 * @note       通道范围由DUAL_CHARGE_CHANNEL_NUM定义
 ******************************************************************************
 */
DUALCHARGE_CTRL *Get_DualchargeCtrl(int channel)
{
    if (channel < DUAL_CHARGE_CHANNEL_NUM)
    {
        return &s_dualcharge_ctrl[channel]; // 返回对应通道的控制器实例
    }
    printf("Get_DualchargeCtrl()...error! channel = %d\n", channel); // LCOV_EXCL_LINE
    return NULL;
}

/*-----------------------------------------------------------------------------
 *  17. 结构体初始化函数
 *-----------------------------------------------------------------------------*/
/**
 * 双充控制结构体初始化函数
 */
#if 0
void DualCharge_Ctrl_Init(void)
{
    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

        /* 先使用memset清零所有字段 */
        memset(ctrl, 0, sizeof(DUALCHARGE_CTRL));

        /* 设置版本字段 */
        ctrl->version = DUALCHARGE_VERSION;

        /* 初始化枚举类型字段为特定值 */
        ctrl->mode = eDualChargeStatus_Independent;
        ctrl->spn = SPNID_CHM;
        ctrl->offsidespn = SPNID_CHM;
        ctrl->offsidemode = eDualChargeStatus_Independent;
        ctrl->service = eDualChargeServ_Disallowed;
        ctrl->OffsideService = eDualChargeServ_Disallowed;
        ctrl->operate = eDualChargeOpr_Null;
        ctrl->operateDirection = eDualChargeDir_NULL;
        ctrl->connected = CONN_DISCONNECTED;
        ctrl->offsideconnected = CONN_DISCONNECTED;
        ctrl->work_status = WORK_STANDBY;
        ctrl->status = STATUS_STANDBY;
        ctrl->offsidestatus = STATUS_STANDBY;
        ctrl->reconnect_status = RECONNECT_IDLE;
        ctrl->result_status = RESULT_IDLE;
        ctrl->ctrlCmd = CMD_FAST_IDLE;

        /* 设置调试级别 */
        ctrl->debug_level = 0;

        /* 初始化状态转换管理组 */
        ctrl->mode_transition_in_progress = 0;
        ctrl->flag_cleaning_in_progress = 0;
        ctrl->transition_timeout_count = 0;
        ctrl->sync_failure_count = 0;
        ctrl->state_inconsistency_count = 0;
        ctrl->rejected_transitions = 0;
        ctrl->last_transition_error = DUAL_ERROR_NONE;
        ctrl->command_send_count = 0;
        ctrl->command_resend_count = 0;
        ctrl->transition_success_time = 0;
        ctrl->initiate_timestamp = 0;
        ctrl->offside_service_stable_time = 0;
        ctrl->conversion_completed = 0;

        /* 初始化紧急状态管理组 */
        ctrl->emergency_active = 0;
        ctrl->emergency_type = DUAL_EMERGENCY_NONE;
        ctrl->emergency_start_time = 0;
        ctrl->emergency_retry_count = 0;

        /* 初始化延迟模式变更请求 */
        ctrl->pending_mode_change.active = 0;
        ctrl->pending_mode_change.target_mode = eDualChargeStatus_Independent;
        ctrl->pending_mode_change.timestamp = 0;

        /* 初始化通知状态组 */
        ctrl->service_stable_notified = 0;
    }
}

DUALCHARGE_RET Get_DualCharge_Ctrl(int channel, DUALCHARGE_CTRL_FIELD field)
{
    DUALCHARGE_RET ret = {DUALCHARGE_RET_UNKNOWN, {0}};
    const DUALCHARGE_CTRL* ctrl = Get_DualchargeCtrl(channel);

    if (ctrl == NULL || field >= DUALCHARGE_CTRL_FIELD_COUNT) {
        return ret;
    }

    const FieldMetadata* metadata = &g_field_metadata[field];
    ret.type = metadata->type;

    /* 检查字段状态 */
    if (metadata->flags & FIELD_FLAG_REMOVED) {
        return ret;
    }

    /* 检查版本兼容性 */
    if (ctrl->version < metadata->min_version ||
        (metadata->max_version != 0 && ctrl->version > metadata->max_version)) {
        return ret;
    }

    /* 根据类型安全读取字段值 */
    void* field_ptr = (char*)ctrl + metadata->offset;
    switch (metadata->type) {
        case DUALCHARGE_RET_UINT8:
            ret.value.u8 = *(const uint8_t*)field_ptr;
            break;
        case DUALCHARGE_RET_UINT16:
            ret.value.u16 = *(const uint16_t*)field_ptr;
            break;
        case DUALCHARGE_RET_UINT32:
            ret.value.u32 = *(const uint32_t*)field_ptr;
            break;
        case DUALCHARGE_RET_FLOAT:
            ret.value.f32 = *(const float*)field_ptr;
            break;
        case DUALCHARGE_RET_BOOL:
            ret.value.b = *(const bool*)field_ptr;
            break;
        case DUALCHARGE_RET_DOUBLE:
            ret.value.f64 = *(const double*)field_ptr;
            break;
        case DUALCHARGE_RET_ERROR_TYPE:
            ret.value.error_type = *(const DUAL_ERROR_TYPE*)field_ptr;
            break;
        case DUALCHARGE_RET_EMERGENCY_TYPE:
            ret.value.emergency_type = *(const DUAL_EMERGENCY_TYPE*)field_ptr;
            break;
        case DUALCHARGE_RET_PENDING_MODE:
            if (metadata->size >= sizeof(DUAL_PENDING_MODE_CHANGE)) {
                memcpy(&ret.value.pending_mode, field_ptr, sizeof(DUAL_PENDING_MODE_CHANGE));
            }
            break;
        default:
            break;
    }

    return ret;
}

/**
 * 设置字段值
 */
DUALCHARGE_SET_RESULT Set_DualCharge_Ctrl(DUALCHARGE_CTRL *pCtrl, DUALCHARGE_CTRL_FIELD field,
                                        const DUALCHARGE_RET_VALUE *value, DUALCHARGE_RET_TYPE expected_type)
{
    if (pCtrl == NULL || value == NULL) {
        return DUALCHARGE_SET_NULL_PTR;
    }

    if (field >= DUALCHARGE_CTRL_FIELD_COUNT) {
        return DUALCHARGE_SET_INVALID_FIELD;
    }

    const FieldMetadata* metadata = &g_field_metadata[field];

    /* 检查字段状态 */
    if (metadata->flags & FIELD_FLAG_REMOVED) {
        return DUALCHARGE_SET_REMOVED;
    }

    if (metadata->flags & FIELD_FLAG_READ_ONLY) {
        return DUALCHARGE_SET_READ_ONLY;
    }

    /* 检查数据类型 */
    if (metadata->type != expected_type) {
        return DUALCHARGE_SET_TYPE_MISMATCH;
    }

    /* 检查版本兼容性 */
    if (pCtrl->version < metadata->min_version ||
        (metadata->max_version != 0 && pCtrl->version > metadata->max_version)) {
        return DUALCHARGE_SET_VERSION_MISMATCH;
    }

    /* 对关键字段进行特殊验证 */
    if (metadata->flags & FIELD_FLAG_CRITICAL) {
        if (metadata->type == DUALCHARGE_RET_UINT16) {
            if (strcmp(metadata->unit, "0.1V") == 0 && value->u16 > 1000) {
                return DUALCHARGE_SET_VERIFY_FAILED;
            } else if (strcmp(metadata->unit, "0.01A") == 0 && value->u16 > 10000) {
                return DUALCHARGE_SET_VERIFY_FAILED;
            }
        }
    }

    /* 安全设置字段值 */
    void* field_ptr = (char*)pCtrl + metadata->offset;
    switch (metadata->type) {
        case DUALCHARGE_RET_UINT8:
            *(uint8_t*)field_ptr = value->u8;
            break;
        case DUALCHARGE_RET_UINT16:
            *(uint16_t*)field_ptr = value->u16;
            break;
        case DUALCHARGE_RET_UINT32:
            *(uint32_t*)field_ptr = value->u32;
            break;
        case DUALCHARGE_RET_FLOAT:
            *(float*)field_ptr = value->f32;
            break;
        case DUALCHARGE_RET_BOOL:
            *(bool*)field_ptr = value->b;
            break;
        case DUALCHARGE_RET_DOUBLE:
            *(double*)field_ptr = value->f64;
            break;
        case DUALCHARGE_RET_ERROR_TYPE:
            *(DUAL_ERROR_TYPE*)field_ptr = value->error_type;
            break;
        case DUALCHARGE_RET_EMERGENCY_TYPE:
            *(DUAL_EMERGENCY_TYPE*)field_ptr = value->emergency_type;
            break;
        case DUALCHARGE_RET_PENDING_MODE:
            if (metadata->size >= sizeof(DUAL_PENDING_MODE_CHANGE)) {
                memcpy(field_ptr, &value->pending_mode, sizeof(DUAL_PENDING_MODE_CHANGE));
            } else {
                return DUALCHARGE_SET_TYPE_MISMATCH;
            }
            break;
        default:
            return DUALCHARGE_SET_TYPE_MISMATCH;
    }

    /* 返回状态码 */
    if (metadata->flags & FIELD_FLAG_DEPRECATED) {
        return DUALCHARGE_SET_DEPRECATED;
    }
    if (metadata->flags & FIELD_FLAG_RESERVED) {
        return DUALCHARGE_SET_RESERVED;
    }
    return DUALCHARGE_SET_OK;
}
#else
/**
 ******************************************************************************
 * @brief       获取控制器字段值（类型安全动态访问）
 * @param[in]   channel 通道号
 * @param[in]   field   字段枚举
 * @retval      包含类型和值的联合体
 * @details     \n
 * 1. 通过元数据表动态获取字段偏移量和类型\n
 * 2. 支持uint8/16/32、float、bool类型\n
 * 3. 类型不匹配时返回UNKNOWN类型
 * @warning     调用者必须验证返回值类型与预期一致
 ******************************************************************************
 */

DUALCHARGE_RET Get_DualCharge_Ctrl(int channel, DUALCHARGE_CTRL_FIELD field)
{
    DUALCHARGE_RET ret = {DUALCHARGE_RET_UNKNOWN, {0}};
    const DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
    const FieldMetadata *metadata = Get_Field_Metadata(field);

    if (!ctrl || !metadata)
    {
        return ret;
    }

    ret.type = metadata->type;
    uint8_t *field_address = (uint8_t *)ctrl + metadata->offset;

    switch (metadata->type)
    {
    case DUALCHARGE_RET_UINT8:
        ret.value.u8 = *(uint8_t *)field_address;
        break;
    case DUALCHARGE_RET_UINT16:
        ret.value.u16 = *(uint16_t *)field_address;
        break;
    case DUALCHARGE_RET_UINT32:
        ret.value.u32 = *(uint32_t *)field_address;
        break;
    case DUALCHARGE_RET_ERROR_TYPE:
        ret.value.error_type = *(DUAL_ERROR_TYPE *)field_address;
        break;
    case DUALCHARGE_RET_EMERGENCY_TYPE:
        ret.value.emergency_type = *(DUAL_EMERGENCY_TYPE *)field_address;
        break;
    case DUALCHARGE_RET_PENDING_MODE:
        /* 复制整个结构体 */
        memcpy(&ret.value.pending_mode, field_address, sizeof(DUAL_PENDING_MODE_CHANGE));
        break;
    default:
        ret.type = DUALCHARGE_RET_UNKNOWN;
        break;
    }

    return ret;
}

/**
 ******************************************************************************
 * @brief       设置控制器字段值（带类型校验）
 * @param[in]   pCtrl   控制器指针
 * @param[in]   field   字段枚举
 * @param[in]   value   新值
 * @param[in]   expected_type 预期类型
 * @retval      操作结果（OK/NULL_PTR/INVALID_FIELD/TYPE_MISMATCH）
 * @details     \n
 * 1. 实现写时复制语义\n
 * 2. 支持原子类型安全写入\n
 * 3. 包含完整的错误码体系
 ******************************************************************************
 */
DUALCHARGE_SET_RESULT Set_DualCharge_Ctrl(
    DUALCHARGE_CTRL *ctrl,
    DUALCHARGE_CTRL_FIELD field,
    DUALCHARGE_RET_VALUE value,
    DUALCHARGE_RET_TYPE expected_type)
{
    const FieldMetadata *metadata = Get_Field_Metadata(field);

    if (!ctrl)
    {
        return DUALCHARGE_SET_NULL_PTR;
    }

    if (!metadata)
    {
        return DUALCHARGE_SET_INVALID_FIELD;
    }

    if (metadata->type != expected_type)
    {
        return DUALCHARGE_SET_TYPE_MISMATCH;
    }

    uint8_t *field_address = (uint8_t *)ctrl + metadata->offset;

    switch (expected_type)
    {
    case DUALCHARGE_RET_UINT8:
        *(uint8_t *)field_address = value.u8;
        break;
    case DUALCHARGE_RET_UINT16:
        *(uint16_t *)field_address = value.u16;
        break;
    case DUALCHARGE_RET_UINT32:
        *(uint32_t *)field_address = value.u32;
        break;
    case DUALCHARGE_RET_ERROR_TYPE:
        *(DUAL_ERROR_TYPE *)field_address = value.error_type;
        break;
    case DUALCHARGE_RET_EMERGENCY_TYPE:
        *(DUAL_EMERGENCY_TYPE *)field_address = value.emergency_type;
        break;
    case DUALCHARGE_RET_PENDING_MODE:
        /* 复制整个结构体 */
        memcpy(field_address, &value.pending_mode, sizeof(DUAL_PENDING_MODE_CHANGE));
        break;
    default:
        return DUALCHARGE_SET_INVALID_FIELD;
    }

    return DUALCHARGE_SET_OK;
}
#endif

/**
 ******************************************************************************
 * @brief       批量设置充电参数
 * @param[in]   channel     通道号
 * @param[in]   voltage     电压(mV)
 * @param[in]   current     电流(mA)
 * @param[in]   batVoltage  电池电压(mV)
 * @details     \n
 * 1. 仅当参数变化时更新\n
 * 2. 更新时记录调试轨迹\n
 * 3. 实现最小写入原则
 ******************************************************************************
 */
void setPara_DualCharge(int channel, uint16 voltage, uint16 current, uint16 batVoltage)
{
    DUALCHARGE_CTRL *pCtrl = Get_DualchargeCtrl(channel);
    if (!pCtrl)
    {
        printf("setPara_DualCharge..NULL\n"); // LCOV_EXCL_LINE
        return;
    }
    // 条件更新策略
    if ((pCtrl->voltage != voltage) ||
        (pCtrl->current != current) ||
        (pCtrl->batVoltage != batVoltage))
    {
        pCtrl->voltage = voltage;
        pCtrl->current = current;
        pCtrl->batVoltage = batVoltage;
        // 调试轨迹记录
        printf("ch:%d-需求更新[Vol= %d,Cur= %d,BatVol= %d]\n",
               channel,
               pCtrl->voltage, pCtrl->current, pCtrl->batVoltage);
    }
}

/**
 * @brief 开始状态转换并验证合法性
 * @param channel 通道号
 * @param source_mode 源状态
 * @param target_mode 目标状态
 * @return 转换是否开始成功
 */
static bool Begin_StateTransition(uint8 channel, uint8 source_mode, uint8 target_mode)
{
    if (channel >= DUAL_CHARGE_CHANNEL_NUM)
    {
        DUALGUN_ERROR(TR_CCU_DEBUG, "无效通道: %d\n", channel);
        return FALSE;
    }

    // 状态转换合法性验证
    bool is_valid_transition = FALSE;

    // 相同状态无需转换
    if (source_mode == target_mode)
    {
        if (target_mode == eDualChargeStatus_Independent)
        {
            DUALGUN_MODE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, target_mode,
                             "ch:%d 重新进入独立模式，需特殊处理\n", channel);
            // 继续执行，不直接返回
        }
        else
        {
            return TRUE; // 非独立模式可以直接返回
        }
    }

    // 任何状态都可以转为独立模式（安全回退路径）
    if (target_mode == eDualChargeStatus_Independent)
    {
        is_valid_transition = TRUE;
    }
    // 从独立模式只能转为绝缘模式
    else if (source_mode == eDualChargeStatus_Independent)
    {
        is_valid_transition = (target_mode == eDualChargeStatus_Insulation);
    }
    // 从绝缘模式可以转为独立、主或从模式
    else if (source_mode == eDualChargeStatus_Insulation)
    {
        is_valid_transition = (target_mode == eDualChargeStatus_Independent ||
                               target_mode == eDualChargeStatus_Master ||
                               target_mode == eDualChargeStatus_Slave);
    }
    // 主模式只能转为独立模式
    else if (source_mode == eDualChargeStatus_Master)
    {
        is_valid_transition = (target_mode == eDualChargeStatus_Independent);
    }
    // 从模式只能转为独立模式
    else if (source_mode == eDualChargeStatus_Slave)
    {
        is_valid_transition = (target_mode == eDualChargeStatus_Independent);
    }

    if (!is_valid_transition)
    {

        DUALGUN_MODE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, target_mode,
                         "并充通道[%d]---非法状态转换---<%s->%s>\n", channel,
                         Get_DualchargeModeText(source_mode),
                         Get_DualchargeModeText(target_mode));
        return FALSE;
    }

    // 验证通过，记录转换信息
    DualChargeStateGuard *guard = &s_state_guard[channel];

    guard->channel = channel;
    guard->source_mode = source_mode;
    guard->target_mode = target_mode;
    guard->start_time = tickGet();
    guard->in_progress = TRUE;
    guard->retry_count = 0;

    // 设置期望对端状态
    if (target_mode == eDualChargeStatus_Master)
    {
        guard->expected_remote_state = eDualChargeStatus_Slave;
    }
    else if (target_mode == eDualChargeStatus_Slave)
    {
        guard->expected_remote_state = eDualChargeStatus_Master;
    }
    else
    {
        // 非主从模式不关心对端状态
        guard->expected_remote_state = 0xFF;
    }

    DUALGUN_MODE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, target_mode,
                     "通道[%d] 状态转换开始: %s → %s\n", channel,
                     Get_DualchargeModeText(source_mode),
                     Get_DualchargeModeText(target_mode));

    return TRUE;
}

/**
 * @brief 重置状态守卫结构体
 * @param guard 状态守卫指针
 */
static void Reset_StateGuard(DualChargeStateGuard *guard)
{
    if (guard == NULL)
        return;

    // 保留原始行为 - 主要设置in_progress标志
    guard->in_progress = FALSE;

    // 增强功能部分 - 重置其他字段
    uint8 channel = guard->channel;

    guard->source_mode = eDualChargeStatus_Independent;
    guard->target_mode = eDualChargeStatus_Independent;
    guard->start_time = 0;
    guard->retry_count = 0;
    guard->expected_remote_state = 0xFF; // 0xFF表示不检查远程状态

    guard->channel = channel; // 恢复通道号
}

/**
 * @brief 初始化状态转换保护器
 */
static void Init_DualChargeStateGuard(void)
{
    for (int i = 0; i < DUAL_CHARGE_CHANNEL_NUM; i++)
    {
        /* 保持与原代码完全一致的基本行为 */
        s_state_guard[i].in_progress = FALSE;

        /* 增强功能 - 完整初始化所有字段 */
        DualChargeStateGuard *guard = &s_state_guard[i];

        guard->channel = i;
        guard->source_mode = eDualChargeStatus_Independent;
        guard->target_mode = eDualChargeStatus_Independent;
        guard->start_time = 0;
        guard->retry_count = 0;
        guard->expected_remote_state = 0xFF; // 0xFF表示不检查远程状态

        DUALGUN_DEBUG(TR_CCU_DEBUG, "初始化通道[%d]状态保护器\n", i);
    }
}

/**
 ******************************************************************************
 * @brief       获取CAN消息优先级
 * @param[in]   id  消息ID
 * @retval      优先级值（0-7）
 * @details     \n
 * 1. 实现ID到优先级的映射查找\n
 * 2. 未命中时返回0并记录错误
 ******************************************************************************
 */
uint8 Get_CcuPrio(int id)
{
    for (int i = 0; i < ARRAY_SIZE(Prio_Map); i++)
    {
        if (id == Prio_Map[i].id)
        {
            return Prio_Map[i].prio;
        }
    }
    DUALGUN_INFO(TR_CCU_DEBUG, "Get_CcuPrio()...error! error id [%x]\n", id); // LCOV_EXCL_LINE
    return 0;
}

/**
 ******************************************************************************
 * @brief       构造CAN消息ID
 * @param[out]  ptr     存储ID的指针
 * @param[in]   dev     设备地址
 * @param[in]   id      消息ID
 * @details     \n
 * 1. 自动计算目标地址（A/B切换）\n
 * 2. 设置优先级和协议格式
 ******************************************************************************
 */
void Get_CcuCanId(uint32 *ptr, int dev, int id)
{
    CAN_ID *pId = (CAN_ID *)ptr;
    pId->sa = (uint8)dev;
    //    pId->ps = (DEV_CCU_ADDR_A == Get_CcuAddr()) ? DEV_CCU_B : DEV_CCU_A;
    if (0xff == Get_CcuDualAddr())
    {
        pId->ps = (Get_CCU() ^ 0x01); // 自动计算配对地址（偶数+1，奇数-1）
    }
    else
    {
        pId->ps = (Get_CcuDualAddr());
    }
    pId->pf = id;
    pId->prio = Get_CcuPrio(id);
}

void DualCcuSendForever(void)
{
    //    int dev = (DEV_CCU_ADDR_A == Get_CcuAddr()) ? DEV_CCU_A : DEV_CCU_B;
    int dev = Get_CCU();

    // 遍历所有充电通道（从1开始计数）
    for (int i = DUAL_CHARGE_CHANNEL_01; i < DUAL_CHARGE_CHANNEL_NUM; i++)
    {
        // 应答指令通道配置（逻辑同上）
        if (eEnableFlag_Off == check_send_enable(i, dev, ID_DUALCHARGE_TELE))
        {
            set_send_enable(i, dev, ID_DUALCHARGE_TELE, eEnableFlag_On);
            set_send_startTime(i, dev, ID_DUALCHARGE_TELE, 0);
            set_send_remainTime(i, dev, ID_DUALCHARGE_TELE, SEND_FOREVER);
        }
    }
}

uint8 Is_DualCharge_CtrlCmd(int channel)
{
    // 获取通道控制结构体
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
    if (NULL == ctrl)
    { // 防止空指针异常
        return FALSE;
    }

    // 检查模式状态
    return (Get_DUALCHARGE_CTRL_ctrlCmd(ctrl));
}

/*-----------------------------------------------------------------------------
 *  并充控制函数（带调试追踪）- 修复闭环问题
 *-----------------------------------------------------------------------------*/

/**
 * @brief 启动立即并充模式
 * @param channel 通道号
 * @param cmd 控制命令
 * @param dir 操作方向
 * @param voltage 设定电压（0.1V）
 * @param current 设定电流（0.01A）
 * @param batVoltage 电池电压（0.1V）
 * @note 符合云快充并充协议阶段定义
 */
void Start_DualImdCharge(uint8 channel,
                         uint8 cmd,
                         uint8 dir,
                         uint16 voltage,
                         uint16 current,
                         uint16 batVoltage)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    // 防重入检查（仅允许空闲状态启动）
    if (eDualChargeOpr_ImdStart != ctrl->operate)
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 开始立即并充流程 [CMD:%02X DIR:%d]\n",
                     channel, cmd, dir);

        // 初始化关键参数
        ctrl->operateDirection = dir;
        ctrl->ctrlCmd = cmd;
        ctrl->operate = eDualChargeOpr_ImdStart;
        ctrl->chargeImdReadyFlag = FALSE;
        ctrl->chargeImdFinishFlag = FALSE;
        ctrl->tick = tickGet(); // 记录启动时间戳
        printf("ch:%d-设置并充参数[Vol= %d,Cur= %d,BatVol= %d]\n",
               channel,
               voltage / 10, current / 10, batVoltage / 10);

        // 参数有效性验证
        if (voltage < 1000 || voltage > 12000)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 并充参数无效 [V:%d I:%d]，使用安全默认值\n",
                         channel, voltage, current);

            // 使用安全默认值
            voltage = (voltage < 1000) ? 1000 : (voltage > 10000 ? 4500 : voltage);
            current = (current == 0) ? 30 : current;
        }

        setPara_DualCharge(channel, voltage, current, batVoltage);
        Set_dual_charge_params(voltage, current, batVoltage);
    }
    else
    {
        //        trace(TR_CCU_DEBUG, "ch:%d 并充操作已在进行中，忽略重复请求\n", channel);
    }
}

/**
 * @brief 启动软启动并充模式
 * @param channel 通道号
 * @param cmd 控制命令
 * @param dir 操作方向
 * @param voltage 设定电压（0.1V）
 * @param current 设定电流（0.01A）
 * @param batVoltage 电池电压（0.1V）
 * @note 支持渐进式充电参数调整
 */
void Start_DualSoftCharge(uint8 channel,
                          uint8 cmd,
                          uint8 dir,
                          uint16 voltage,
                          uint16 current,
                          uint16 batVoltage)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    if (eDualChargeOpr_SoftStart != ctrl->operate)
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 开始软启动并充流程 [CMD:%02X DIR:%d]\n",
                     channel, cmd, dir);

        // 初始化软启动参数
        ctrl->operateDirection = dir;
        ctrl->ctrlCmd = cmd;
        ctrl->operate = eDualChargeOpr_SoftStart;
        ctrl->startChargeFlag = FALSE;
        ctrl->startChargeFinishFlag = FALSE;
        ctrl->tick = tickGet(); // 记录启动时间戳 - 添加以确保超时检测正确

        //        DUALGUN_POWER_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, voltage, current,
        //                          "ch:%d 配置软启动参数 [V:%.1fV I:%.2fA]\n",
        //                          channel, voltage / 10.0, current / 10.0);

        trace(TR_CCU_DEBUG, "ch:%d 配置软启动参数 [V:%.1fV I:%.2fA]\n",
              channel, voltage / 10.0, current / 10.0);
        // 参数有效性验证
        if (voltage < 1000 || voltage > 10000 || current == 0)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 软启动参数无效 [V:%d I:%d]，使用安全默认值\n",
                         channel, voltage, current);

            // 使用安全默认值
            voltage = (voltage < 1000) ? 1000 : (voltage > 10000 ? 4500 : voltage);
            current = (current == 0) ? 30 : current;
        }

        setPara_DualCharge(channel, voltage, current, batVoltage);
    }
    else
    {
        DUALGUN_ERROR(TR_CCU_DEBUG, "ch:%d 软启动操作已在进行中，忽略重复请求\n", channel);
    }
}

void stop_DualCharge(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
    if (NULL == ctrl)
    {
        DUALGUN_ERROR(TR_CCU_DEBUG, "stop_DualCharge...error! 无效通道:%d\n", channel);
        return;
    }

    // 检查是否已经是停止操作
    if (eDualChargeOpr_Stop != ctrl->operate)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 触发并充停止流程，当前模式:%s\n",
                     channel, Get_DualchargeModeText(ctrl->mode));

        // 1. 设置停止标志 - 统一的入口点
        ctrl->operate = eDualChargeOpr_Stop;

        // 2. 设置命令类型
        ctrl->ctrlCmd = CMD_RELEASE_MODULE;

        // 3. 记录时间戳用于超时检测
        ctrl->tick = tickGet();

        // 4. 重置充电参数
        setPara_DualCharge(channel, 0x00, 0x00, 0x00);

        // 5. 设置conversion_completed为FALSE，确保状态一致性
        //        ctrl->conversion_completed = FALSE;
    }
    else
    {
        // 如果已经是停止操作，更新时间戳确保不会超时
        if (abs(tickGet() - ctrl->tick) > 10 * sysClkRateGet())
        {
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 停止操作进行时间过长，更新时间戳\n", channel);
            ctrl->tick = tickGet();
        }
    }

    // 不在此处清理其他标志位
    // 不在此处调用Set_DualchargeMode
    // 不在此处调用Enhanced_Clr_DualChargeOperateFlag

    // 标志位清理由Set_DualchargeMode统一处理
}

// 处理并柜启动充电
void Deal_DualChargeStartCharge(uint8 channel,
                                uint8 cmd,
                                uint16 voltage,
                                uint16 current,
                                uint16 batVoltage)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    if (CMD_FAST_START == cmd)
    {
        if (SPNID_CRM_00 == Get_DUALCHARGE_CTRL_offsidespn(ctrl))
        {
            DUALGUN_POWER_LOG(LOG_LEVEL_WARN, TR_CCU_DEBUG, channel, voltage, current,
                              "ch:%d-%-6s并充控制%-6s[----]%-6s[绝缘启动:Vol=%d,Cur=%d,Bat=%d]\n",
                              channel, "**", "**", "**", voltage, current, batVoltage);
            Set_dual_charge_params(voltage, current, batVoltage);
        }
    }
    else if (CMD_SOFT_START == cmd)
    {
        if (SPNID_CRO_00 == Get_DUALCHARGE_CTRL_offsidespn(ctrl))
        {
            DUALGUN_POWER_LOG(LOG_LEVEL_WARN, TR_CCU_DEBUG, channel, voltage, current,
                              "ch:%d-%-6s并充控制%-6s[%--------]%-6s[软启开机:Vol=%d,Cur=%d,Bat=%d]\n",
                              channel, "**", "**", "**", voltage, current, batVoltage);
            Set_dual_charge_params(voltage, current, batVoltage);
        }
    }
}

// 处理并柜充电中
static void
Deal_DualChargeCharging(uint8 channel,
                        uint16 voltage,
                        uint16 current,
                        uint16 batVoltage)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
    uint8 workState = Get_WorkState();
    if ((workState >= CCU_WORK_STATE_CHARGING && workState <= CCU_WORK_STATE_CHARGE_PAUSE) || (SPNID_BCCL == Get_DUALCHARGE_CTRL_offsidespn(ctrl)))
    {
        Set_dual_charge_params(voltage, current, batVoltage);
    }
    else
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 工作状态不匹配当前充电状态 [%d]\n",
                     channel, workState);
    }
}

/**
 * @brief 处理并充停止充电
 * @param channel 通道号
 * @note 实现充电状态机终止逻辑
 */
void Deal_DualChargeStopCharge(uint8 channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
    uint8 workState = Get_WorkState();
    //(workState >= CCU_WORK_STATE_CHARGE_STOP)
    if ((workState <= CCU_WORK_STATE_CHARGE_STOP) ||
        (SPNID_CST == Get_DUALCHARGE_CTRL_spn(ctrl)) ||
        (SPNID_BST == Get_DUALCHARGE_CTRL_offsidespn(ctrl)))
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 执行停机流程 [状态:%d SPN:%02X]\n",
                     channel, workState, Get_DUALCHARGE_CTRL_spn(ctrl));

        Set_ChargeActFlag(eActFlag_Off);
        DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 充电活动标志已关闭\n", channel);
    }
}

/**
 * @brief 增强版标志位清理函数 - 仅供Set_DualchargeMode内部调用
 * @param channel 通道号
 * @return TRUE-成功 FALSE-失败
 * @note 此函数不应在外部直接调用，应通过Set_DualchargeMode或stop_DualCharge触发
 */
static bool Enhanced_Clr_DualChargeOperateFlag(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
    if (NULL == ctrl)
    {
        DUALGUN_ERROR(TR_CCU_DEBUG, "Clr_DualChargeOperateFlag...error! 无效通道:%d\n", channel);
        return FALSE;
    }

    // 检查是否已经在清理中，避免重入
    if (ctrl->flag_cleaning_in_progress)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 标志位清理已在进行中，避免重入\n", channel);
        return FALSE;
    }

    // 设置清理标记，防止清理过程中被打断
    ctrl->flag_cleaning_in_progress = TRUE;

    // 状态转换安全检查
    uint8 current_mode = ctrl->mode;
    DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 开始清理标志位，当前模式:%s\n",
                 channel, Get_DualchargeModeText(current_mode));

    // 1. 清理操作相关标志
    ctrl->chargeImdReadyFlag = FALSE;
    ctrl->chargeImdFinishFlag = FALSE;
    ctrl->startChargeFlag = FALSE;
    ctrl->startChargeFinishFlag = FALSE;
    ctrl->stopChargeFlag = FALSE;
    ctrl->stopChargeFinishFlag = FALSE;

    // 2. 增加超时状态复位 - 根据模式区分处理
    if (current_mode == eDualChargeStatus_Independent)
    {
        // 在独立模式时，清理更多标志
        ctrl->revctrltimeout = FALSE;
        ctrl->revctrlacktimeout = FALSE;
        Set_DUALCHARGE_CTRL_success_flag(ctrl, FALSE);

        // 3. 增加诊断计数器清理
        ctrl->sync_failure_count = 0;

        // 4. 清理延迟的状态变更请求
        ctrl->pending_mode_change.active = FALSE;

        // 5. 清理转换完成标志
        ctrl->conversion_completed = FALSE;
    }
    else if (ctrl->operate == eDualChargeOpr_Stop)
    {
        // 非独立模式且操作为停止时的处理
        // 仅清理必要标志，保留模式转换所需信息
        ctrl->revctrlacktimeout = FALSE;
    }

    // 6. 记录状态清理日志
    DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 完成标志位清理，当前模式: %s\n",
                 channel, Get_DualchargeModeText(ctrl->mode));

    // 清理完成，解除标记
    ctrl->flag_cleaning_in_progress = FALSE;

    return TRUE;
}

/**
 * @brief 记录并充事件
 * @param channel 通道号
 * @param event_type 事件类型
 * @param param1 参数1
 * @param param2 参数2
 * @param param3 参数3
 */
void log_dual_charge_event(int channel, DUAL_EVENT_TYPE event_type,
                           uint8 param1, uint8 param2, uint8 param3)
{
    static const char *event_names[] = {
        "模式变更被拒绝",
        "模式变更回滚",
        "模式变更成功",
        "命令超时",
        "状态不一致",
        "紧急情况已处理",
        "紧急情况已恢复",
        "独立到绝缘转换成功"};

    // 确保事件类型有效
    if (event_type >= DUAL_EVENT_COUNT)
    {
        DUALGUN_ERROR(TR_CCU_DEBUG, "无效事件类型: %d\n", event_type);
        return;
    }

    // 构建日志信息
    trace(TR_CCU_DEBUG, "并充事件[CH:%d]: %s (P1:%d, P2:%d, P3:%d)\n",
          channel, event_names[event_type], param1, param2, param3);

    // 在这里也可以将事件记录到永久存储，如果需要的话
}

/**
 * @brief 获取双充控制器诊断信息
 * @param channel 通道号
 * @return 返回诊断信息文本
 */
const char *Get_DualChargeDiagnostics(int channel)
{
    static char diagnostics[512];
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    if (NULL == ctrl)
    {
        return "控制器不可用";
    }

    // 格式化诊断信息
    snprintf(diagnostics, sizeof(diagnostics),
             "模式: %s, 操作: %d, 方向: %d\n"
             "服务: %d, 对端服务: %d, 对端模式: %s\n"
             "超时计数: %d, 同步失败计数: %d\n"
             "状态不一致计数: %d, 拒绝转换: %d\n"
             "命令发送: %d, 命令重发: %d\n"
             "紧急状态: %d, 紧急类型: %d\n"
             "转换完成标志: %d\n", // 添加转换完成标志的诊断
             Get_DualchargeModeText(ctrl->mode),
             ctrl->operate,
             ctrl->operateDirection,
             ctrl->service,
             ctrl->OffsideService,
             Get_DualchargeModeText(ctrl->offsidemode),
             ctrl->transition_timeout_count,
             ctrl->sync_failure_count,
             ctrl->state_inconsistency_count,
             ctrl->rejected_transitions,
             ctrl->command_send_count,
             ctrl->command_resend_count,
             ctrl->emergency_active,
             ctrl->emergency_type,
             ctrl->conversion_completed);

    return diagnostics;
}

/**
 * @brief 增强的并充紧急情况处理函数
 * @param channel 通道号
 * @param emergency_type 紧急情况类型
 * @details 处理并充系统中的紧急情况，设置紧急状态并触发安全停止流程
 */
void Handle_DualCharge_Emergency(int channel, DUAL_EMERGENCY_TYPE emergency_type)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    if (NULL == ctrl)
    {
        DUALGUN_ERROR(TR_CCU_DEBUG, "Handle_DualCharge_Emergency...error! 无效通道:%d\n", channel);
        return;
    }

    // 检查并记录紧急情况状态
    bool already_in_emergency = ctrl->emergency_active;
    bool same_emergency_type = (already_in_emergency && ctrl->emergency_type == emergency_type);

    // 避免重复处理相同紧急情况
    if (same_emergency_type)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 已处于紧急状态[%d]，跳过重复处理\n",
                     channel, emergency_type);
        return;
    }

    // 记录紧急事件开始
    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 紧急情况处理开始: 类型=%d, 当前模式=%s\n",
                 channel, emergency_type, Get_DualchargeModeText(ctrl->mode));

    // 1. 设置紧急状态标志 - 状态标志生命周期开始
    ctrl->emergency_active = TRUE;
    ctrl->emergency_type = emergency_type;
    ctrl->emergency_start_time = tickGet();
    ctrl->emergency_retry_count = EMERGENCY_COMMAND_RETRY;

    // 2. 设置conversion_completed为FALSE - 确保状态一致性
    if (ctrl->conversion_completed)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 紧急情况下重置转换完成标志\n", channel);
        ctrl->conversion_completed = FALSE;
    }

    // 3. 根据紧急情况类型执行特定硬件操作
    switch (emergency_type)
    {
    case DUAL_EMERGENCY_DEVICE_FAULT:
        // 设备故障需要立即断开硬件
        DUALGUN_WARN(TR_CHARGE, "紧急断开K1_K2: 设备故障\n");
        break;

    case DUAL_EMERGENCY_COMMUNICATION_FAILURE:
        DUALGUN_WARN(TR_CCU_DEBUG, "通信故障，安全关闭并充\n");
        break;

    case DUAL_EMERGENCY_STATE_INCONSISTENCY:
        DUALGUN_WARN(TR_CCU_DEBUG, "状态不一致，尝试恢复一致性\n");

        // 记录不一致计数
        ctrl->state_inconsistency_count++;

        if (ctrl->state_inconsistency_count > MAX_INCONSISTENCY_TOLERANCE)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "状态不一致累计%d次，超过容忍阈值\n",
                         ctrl->state_inconsistency_count);
        }
        break;

    default:
        DUALGUN_WARN(TR_CCU_DEBUG, "未知紧急情况类型: %d\n", emergency_type);
        break;
    }

    // 4. 统一使用stop_DualCharge触发状态流转
    stop_DualCharge(channel);

    // 5. 记录紧急事件
    log_dual_charge_event(channel, DUAL_EVENT_EMERGENCY_HANDLED,
                          ctrl->mode, emergency_type, Get_WorkState());

    // 紧急状态标志的生命周期将在以下两个地方结束:
    // 1. DualCharge_HealthCheck中当条件满足时
    // 2. Check_DualCharge_EmergencyRecovery中当恢复条件满足时
}

/**
 * @brief 紧急情况恢复检查
 * @param channel 通道号
 */
void Check_DualCharge_EmergencyRecovery(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    if (NULL == ctrl || !ctrl->emergency_active)
    {
        return;
    }

    // 检查是否需要重试紧急命令
    if (ctrl->emergency_retry_count > 0)
    {
        ctrl->emergency_retry_count--;

        // 重发紧急命令
        if (ctrl->emergency_type == DUAL_EMERGENCY_DEVICE_FAULT)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "重发紧急停机命令，剩余次数: %d\n",
                         ctrl->emergency_retry_count);
        }
    }

    // 检查紧急情况是否超时
    uint32 emergency_duration = abs(tickGet() - ctrl->emergency_start_time);
    if (emergency_duration > EMERGENCY_TIMEOUT)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 紧急情况处理超时，恢复正常操作\n", channel);

        // 重置紧急状态
        ctrl->emergency_active = FALSE;

        // 如果当前不是独立模式，设置为独立模式
        if (ctrl->mode != eDualChargeStatus_Independent)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "紧急超时后回退到独立模式\n");
            Set_DualchargeMode(channel, eDualChargeStatus_Independent);
        }

        // 记录紧急恢复事件
        log_dual_charge_event(channel, DUAL_EVENT_EMERGENCY_RECOVERED,
                              ctrl->emergency_type, 0, Get_WorkState());
    }
}

/**
 * @brief 更新对端服务状态
 */
static void Update_Service_Status(void)
{
    // 更新对端服务稳定时间
    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
        if (ctrl)
        {
            static uint8 last_offside_service[DUAL_CHARGE_CHANNEL_NUM] = {0};

            // 检测对端服务状态变化
            if (ctrl->OffsideService != last_offside_service[channel])
            {
                // 服务状态变化，更新时间戳
                ctrl->offside_service_stable_time = tickGet();
                last_offside_service[channel] = ctrl->OffsideService;

                // 重置服务稳定通知标志
                ctrl->service_stable_notified = FALSE;

                DUALGUN_SERVICE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, ctrl->OffsideService, "ch:%d 对端服务状态变更为: %d\n",
                                    channel, ctrl->OffsideService);
            }
        }
    }
}

/**
 * @brief 增强的状态一致性检查函数
 * @return TRUE-一致 FALSE-不一致
 */
bool Check_DualCharge_StateConsistency(void)
{
    int channel = DUAL_CHARGE_CHANNEL_01;
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    if (NULL == ctrl)
    {
        return FALSE;
    }

    // 检查是否在标志清理中
    if (ctrl->flag_cleaning_in_progress)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "状态一致性检查：标志清理进行中，跳过检查\n");
        return TRUE; // 暂时返回一致
    }

    // 检查本地状态和对端状态的一致性
    bool consistency = TRUE;

    // 检查主从模式一致性
    if (ctrl->mode == eDualChargeStatus_Master &&
        ctrl->offsidemode != eDualChargeStatus_Slave)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 状态不一致: 本地为主机模式但对端不是从机模式\n", channel);
        consistency = FALSE;
    }
    else if (ctrl->mode == eDualChargeStatus_Slave &&
             ctrl->offsidemode != eDualChargeStatus_Master)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 状态不一致: 本地为从机模式但对端不是主机模式\n", channel);
        consistency = FALSE;
    }

    // 检查服务状态一致性
    if ((ctrl->mode != eDualChargeStatus_Independent) &&
        (ctrl->OffsideService != eDualChargeServ_Allowed))
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 服务状态不一致: 处于非独立模式但对端服务不可用\n", channel);
        consistency = FALSE;
    }

    // 检查转换完成标志与当前状态一致性
    if (ctrl->conversion_completed && ctrl->mode == eDualChargeStatus_Independent)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 标志不一致: 转换完成标志为真但处于独立模式\n", channel);
        ctrl->conversion_completed = FALSE; // 自动修正
        consistency = FALSE;
    }

    // 记录不一致事件
    if (!consistency)
    {
        log_dual_charge_event(channel, DUAL_EVENT_STATE_INCONSISTENCY,
                              ctrl->mode, ctrl->offsidemode, Get_WorkState());

        // 增加不一致计数
        ctrl->state_inconsistency_count++;
    }

    return consistency;
}

/**
 * @brief 执行周期性一致性检查
 */
static void Perform_Consistency_Checks(void)
{
    static uint32 last_consistency_check = 0;

    // 1秒周期的状态一致性检查
    if (abs(tickGet() - last_consistency_check) > sysClkRateGet())
    {
        last_consistency_check = tickGet();

        // 执行状态一致性检查
        for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
        {
            // 只有在非独立模式下才需要检查状态一致性
            DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
            if (ctrl && ctrl->mode != eDualChargeStatus_Independent)
            {
                if (!Check_DualCharge_StateConsistency())
                {
                    // 状态不一致，启动紧急处理
                    Handle_DualCharge_Emergency(channel, DUAL_EMERGENCY_STATE_INCONSISTENCY);
                }
            }

            // 检查紧急情况恢复
            Check_DualCharge_EmergencyRecovery(channel);
        }
    }
}

void DualCharge_HealthCheck(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
    DualChargeStateGuard *guard = &s_state_guard[channel];

    if (NULL == ctrl)
    {
        return;
    }

    // 1. 检查标志位一致性
    bool flags_consistent = TRUE;

    // 1.1 检查状态与操作的一致性
    if (ctrl->mode == eDualChargeStatus_Independent &&
        ctrl->operate != eDualChargeOpr_Null &&
        ctrl->operate != eDualChargeOpr_Stop &&
        ctrl->operate != eDualChargeOpr_ImdStart)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 标志不一致: 独立模式但操作非法 [%d]\n",
                     channel, ctrl->operate);
        flags_consistent = FALSE;
    }

    // 1.2 简化的独立模式下成功标志检查 (增加过渡期判断)
    if (ctrl->mode == eDualChargeStatus_Independent &&
        Get_DUALCHARGE_CTRL_success_flag(ctrl) &&
        !ctrl->mode_transition_in_progress &&
        abs(tickGet() - ctrl->initiate_timestamp) > 3 * sysClkRateGet() &&
        ctrl->operate != eDualChargeOpr_ImdStart)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 独立模式下成功标志长时间为真\n", channel);
        flags_consistent = FALSE;
    }

    // 1.3 紧急状态检查
    if (ctrl->emergency_active &&
        ctrl->mode != eDualChargeStatus_Independent &&
        abs(tickGet() - ctrl->emergency_start_time) > 3 * sysClkRateGet())
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 紧急状态下未回到独立模式\n", channel);
        flags_consistent = FALSE;
    }

    // 1.4 状态保护与当前状态的一致性
    if (guard->in_progress &&
        ctrl->mode != guard->source_mode &&
        ctrl->mode != guard->target_mode)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 状态保护与当前模式不一致\n", channel);
        Reset_StateGuard(guard);
        flags_consistent = FALSE;
    }

    // 2. 修复不一致状态
    if (!flags_consistent)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 检测到标志不一致，执行修复\n", channel);
        log_dual_charge_event(channel, DUAL_EVENT_STATE_INCONSISTENCY,
                              ctrl->mode, ctrl->operate, 0);

        if (ctrl->mode == eDualChargeStatus_Independent)
        {
            // 在独立模式下重置标志，但保留并充初始化场景
            if (ctrl->operate != eDualChargeOpr_ImdStart &&
                ctrl->operate != eDualChargeOpr_Stop)
            {
                // 替换直接调用Enhanced_Clr_DualChargeOperateFlag
                // 使用统一的stop_DualCharge接口
                stop_DualCharge(channel);

                // 直接设置部分关键标志
                Set_DUALCHARGE_CTRL_success_flag(ctrl, FALSE);
                ctrl->conversion_completed = FALSE;
            }

            // 清理紧急状态
            if (ctrl->emergency_active &&
                abs(tickGet() - ctrl->emergency_start_time) > EMERGENCY_TIMEOUT)
            {
                ctrl->emergency_active = FALSE;
                log_dual_charge_event(channel, DUAL_EVENT_EMERGENCY_RECOVERED,
                                      ctrl->emergency_type, 0, 0);
            }
        }
        else if (ctrl->emergency_active)
        {
            // 非独立模式下紧急状态处理
            // 移除直接调用Enhanced_Clr_DualChargeOperateFlag
            // 统一使用stop_DualCharge触发状态流转
            stop_DualCharge(channel);

            // 不直接调用Set_DualchargeMode
            // 由状态处理函数根据operate标志执行状态转换
        }
        else if (ctrl->operate != eDualChargeOpr_Stop)
        {
            // 对于其他非停止中的不一致状态
            stop_DualCharge(channel);
        }
    }

    // 3. 模式转换锁死检查
    if (ctrl->mode_transition_in_progress &&
        abs(tickGet() - ctrl->initiate_timestamp) > 30 * sysClkRateGet())
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 模式转换锁死超时，强制解锁\n", channel);
        ctrl->mode_transition_in_progress = FALSE;
        Reset_StateGuard(guard);

        if (ctrl->mode != eDualChargeStatus_Independent)
        {
            // 使用统一的stop_DualCharge接口
            stop_DualCharge(channel);
            // 不直接调用Set_DualchargeMode
        }
    }
}

/**
 * @brief 状态转换保护函数
 * @details 确保状态转换的安全性，处理超时和异常情况
 */
void Protect_DualCharge_StateTransition(void)
{
    // 遍历所有通道的状态保护
    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        DualChargeStateGuard *guard = &s_state_guard[channel];

        // 仅处理进行中的转换
        if (!guard->in_progress)
        {
            continue;
        }

        DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
        if (NULL == ctrl)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "通道[%d] 获取控制器失败，中止状态转换\n", channel);
            Reset_StateGuard(guard);
            continue;
        }

        // 跳过紧急状态或标志清理中的情况
        if (ctrl->emergency_active || ctrl->flag_cleaning_in_progress)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "通道[%d] 当前处于紧急状态或标志清理中，暂停状态转换\n", channel);
            continue;
        }

        uint8 current_mode = ctrl->mode;
        uint8 target_mode = guard->target_mode;
        uint32 elapsed = tickGet() - guard->start_time;

        // 检查转换是否已完成
        if (current_mode == target_mode)
        {
            // 主从模式特殊处理 - 需要检查对端状态
            if ((current_mode == eDualChargeStatus_Master ||
                 current_mode == eDualChargeStatus_Slave) &&
                guard->expected_remote_state != 0xFF)
            {
                uint8 expected_remote = guard->expected_remote_state;
                uint8 actual_remote = ctrl->offsidemode;

                // 对端状态符合预期，转换完成
                if (actual_remote == expected_remote)
                {
                    DUALGUN_STATE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, current_mode, 0,
                                      "通道[%d] 状态转换完成，对端状态已同步: %s\n",
                                      channel, Get_DualchargeModeText(current_mode));

                    // 完成后设置转换完成标志
                    ctrl->conversion_completed = TRUE;
                    Reset_StateGuard(guard);

                    // 记录成功事件
                    log_dual_charge_event(channel, DUAL_EVENT_MODE_CHANGED_SUCCESS,
                                          guard->source_mode, current_mode, Get_WorkState());
                }
                // 对端状态超时 - 使用统一接口触发回退流程
                else if (elapsed > (15 * sysClkRateGet()))
                {
                    DUALGUN_WARN(TR_CCU_DEBUG, "通道[%d] 对端状态同步超时: 期望=%s, 实际=%s\n",
                                 channel, Get_DualchargeModeText(expected_remote), Get_DualchargeModeText(actual_remote));

                    // 记录同步失败
                    ctrl->sync_failure_count++;

                    // 明确设置转换完成标志为FALSE
                    ctrl->conversion_completed = FALSE;

                    // 统一通过stop_DualCharge触发状态流转
                    stop_DualCharge(channel);
                    Reset_StateGuard(guard);

                    // 记录超时事件
                    log_dual_charge_event(channel, DUAL_EVENT_COMMAND_TIMEOUT,
                                          guard->source_mode, target_mode, Get_WorkState());
                }
            }
            else
            {
                // 非主从模式，转换直接完成
                DUALGUN_STATE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, current_mode, 0,
                                  "通道[%d] 状态转换完成: %s\n",
                                  channel, Get_DualchargeModeText(current_mode));

                // 对于非独立模式，设置转换完成标志
                if (current_mode != eDualChargeStatus_Independent)
                {
                    ctrl->conversion_completed = TRUE;
                    DUALGUN_DEBUG(TR_CCU_DEBUG, "通道[%d] 设置转换完成标志\n", channel);
                }
                else
                {
                    // 独立模式下确保标志为FALSE
                    ctrl->conversion_completed = FALSE;
                }

                // 清理状态保护器
                Reset_StateGuard(guard);

                // 记录成功事件
                log_dual_charge_event(channel, DUAL_EVENT_MODE_CHANGED_SUCCESS,
                                      guard->source_mode, current_mode, Get_WorkState());
            }
        }
        // 转换超时检查
        else if (elapsed > (15 * sysClkRateGet()))
        {
            DUALGUN_ERROR(TR_CCU_DEBUG, "通道[%d] 状态转换超时: %s → %s, 已耗时: %d ms\n",
                          channel, Get_DualchargeModeText(guard->source_mode), Get_DualchargeModeText(target_mode), elapsed);

            // 记录超时次数
            ctrl->transition_timeout_count++;

            // 明确设置转换完成标志为FALSE
            ctrl->conversion_completed = FALSE;

            // 统一通过stop_DualCharge触发状态流转
            stop_DualCharge(channel);
            Reset_StateGuard(guard);

            // 记录超时事件
            log_dual_charge_event(channel, DUAL_EVENT_COMMAND_TIMEOUT,
                                  guard->source_mode, target_mode, Get_WorkState());
        }
        // 转换进行中，检查是否需要重试
        else if (guard->retry_count < MAX_TRANSITION_RETRY &&
                 elapsed > ((1 + guard->retry_count) * sysClkRateGet()))
        {
            guard->retry_count++;
            DUALGUN_WARN(TR_CCU_DEBUG, "通道[%d] 状态转换延迟，重试(%d/%d): %s → %s\n",
                         channel, guard->retry_count, MAX_TRANSITION_RETRY, Get_DualchargeModeText(current_mode), Get_DualchargeModeText(target_mode));

            // 使用延迟模式变更机制
            if (current_mode != target_mode)
            {
                // 记录目标模式

                ctrl->pending_mode_change.active = TRUE;
                ctrl->pending_mode_change.target_mode = target_mode;
                ctrl->pending_mode_change.timestamp = tickGet();

                // 触发状态流转前先设置标志
                if (ctrl->conversion_completed)
                {
                    DUALGUN_DEBUG(TR_CCU_DEBUG, "通道[%d] 重试过程中重置转换完成标志\n", channel);
                    ctrl->conversion_completed = FALSE;
                }

                // 触发状态流转
                stop_DualCharge(channel);
            }
        }
    }

    // 处理延迟的模式变更请求
    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
        if (ctrl && ctrl->pending_mode_change.active)
        {
            // 检查是否达到安全延迟时间且不处于停止操作中
            if (abs(tickGet() - ctrl->pending_mode_change.timestamp) > SAFE_MODE_CHANGE_DELAY &&
                ctrl->operate != eDualChargeOpr_Stop &&
                !ctrl->flag_cleaning_in_progress &&
                !ctrl->emergency_active &&
                !ctrl->mode_transition_in_progress)
            {
                uint8 target_mode = ctrl->pending_mode_change.target_mode;
                DUALGUN_STATE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, target_mode, 0,
                                  "通道[%d] 执行延迟模式切换到: %s\n",
                                  channel, Get_DualchargeModeText(target_mode));

                // 执行模式变更
                Set_DualchargeMode(channel, target_mode);

                // 清除延迟请求 (无论成功与否)
                ctrl->pending_mode_change.active = FALSE;
            }
            else if (abs(tickGet() - ctrl->pending_mode_change.timestamp) > 10 * sysClkRateGet())
            {
                // 延迟请求超时未执行，强制清除
                DUALGUN_WARN(TR_CCU_DEBUG, "通道[%d] 延迟模式切换请求超时未执行，清除请求\n", channel);
                ctrl->pending_mode_change.active = FALSE;
            }
        }
    }
}

/**
 * @brief 设置并充工作模式 - 增强版
 * @param channel 通道号
 * @param mode 目标模式
 */
static void Set_DualchargeMode(int channel, uint8 mode)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
    if (NULL == ctrl)
    {
        DUALGUN_ERROR(TR_CCU_DEBUG, "Set_DualchargeMode...error! 无效通道:%d\n", channel);
        return;
    }

    uint8 current_mode = ctrl->mode;

    // 相同状态无需切换
    if (current_mode == mode)
    {
        // 如果是独立模式，特殊处理：需要执行完整流程确保标志位正确清理
        if (mode == eDualChargeStatus_Independent)
        {
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 重新设置独立模式，执行完整清理流程\n", channel);
            // 继续执行，不返回
        }
        else
        {
            // 非独立模式的相同状态无需切换
            return;
        }
    }

    // 检查是否在紧急状态
    if (ctrl->emergency_active)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 紧急状态中，禁止模式切换\n", channel);
        return;
    }

    // 检查是否在标志清理中
    if (ctrl->flag_cleaning_in_progress)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 标志清理中，禁止模式切换\n", channel);
        return;
    }

    // 记录状态切换请求
    DUALGUN_MODE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, mode,
                     "通道[%d] 请求状态切换: %s → %s\n",
                     channel,
                     Get_DualchargeModeText(current_mode),
                     Get_DualchargeModeText(mode));

    // 从独立模式切换到其他模式时的安全检查
    if (current_mode == eDualChargeStatus_Independent &&
        mode != eDualChargeStatus_Independent)
    {
        // 判断并充初始化场景
        bool is_dual_charge_init =
            (mode == eDualChargeStatus_Insulation) &&    // 目标是绝缘模式
            (ctrl->operate == eDualChargeOpr_ImdStart || // 操作是初始化相关
             ctrl->operate == eDualChargeOpr_SoftStart);

        uint8 workState = Get_WorkState();

        // 充电中非并充初始化场景下禁止切换
        if (workState > CCU_WORK_STATE_READY && !is_dual_charge_init)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "非并充初始化时充电中(%d)禁止切换模式\n", workState);

            // 记录拒绝切换
            ctrl->rejected_transitions++;

            // 添加安全检查失败事件记录
            log_dual_charge_event(channel, DUAL_EVENT_MODE_CHANGE_REJECTED,
                                  current_mode, mode, workState);
            return;
        }

        // 并充初始化场景记录日志
        if (is_dual_charge_init && workState > CCU_WORK_STATE_READY)
        {
            DUALGUN_INFO(TR_CCU_DEBUG, "并充初始化：允许充电状态(%d)下转为绝缘模式\n", workState);
        }

        // 设备故障时禁止切换
        if (IsDeviceFaulted())
        {
            DUALGUN_ERROR(TR_CCU_DEBUG, "设备故障禁止切换模式\n");

            // 设置诊断信息
            ctrl->last_transition_error = DUAL_ERROR_DEVICE_FAULT;
            return;
        }

        // 无车辆连接时禁止切换
        if (ctrl->connected != CONN_CONNECTED)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "未连接车辆禁止切换模式\n");

            // 设置诊断信息
            ctrl->last_transition_error = DUAL_ERROR_NOT_CONNECTED;
            return;
        }

        // 增加对端设备状态检查 - 主从切换前确认对端准备就绪
        if ((mode == eDualChargeStatus_Master || mode == eDualChargeStatus_Slave) &&
            ctrl->OffsideService != eDualChargeServ_Allowed)
        {
            DUALGUN_ERROR(TR_CCU_DEBUG, "对端服务不可用，禁止切换至主从模式\n");

            // 设置诊断信息
            ctrl->last_transition_error = DUAL_ERROR_OFFSIDE_NOT_READY;
            return;
        }
    }

    // 开启状态转换保护（包含验证）
    //    if (!Begin_StateTransition(channel, current_mode, mode))
    //    {
    //        return; // 状态转换验证失败，直接返回
    //    }

    // 记录日志
    DUALGUN_MODE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, mode,
                     "并充通道[%d]---工作状态切换---<%s->%s>\n",
                     channel,
                     Get_DualchargeModeText(current_mode),
                     Get_DualchargeModeText(mode));

    // 安全机制：先暂存旧模式用于恢复
    uint8 previous_mode = ctrl->mode;
    uint8 previous_operate = ctrl->operate;
    uint8 previous_direction = ctrl->operateDirection;

    // 更新模式
    Set_DUALCHARGE_CTRL_mode(ctrl, mode);

    // 确保原子性：状态切换锁定，防止中断
    ctrl->mode_transition_in_progress = TRUE;

    // 独立模式特殊处理
    if (eDualChargeStatus_Independent == mode)
    {
        // 重置操作和方向
        ctrl->operate = eDualChargeOpr_Null;
        ctrl->operateDirection = eDualChargeDir_NULL;

        if (Get_ChargeActFlag() == eActFlag_On)
        {
            Set_ChargeActFlag(eActFlag_Off); // 暂时这样处理，后续将增加状态回退
        }

        if (Get_DualChargeEnableStatus())
        {
            Set_DualChargeEnableStatus(FALSE); // 暂时这样处理，后续将增加状态回退
        }

        // 重新评估服务状态
        bool canProvideService = !IsDeviceFaulted() &&
                                 (CONN_CONNECTED == ctrl->connected) &&
                                 !ctrl->revctrltimeout;

        Set_DUALCHARGE_CTRL_service(ctrl,
                                    canProvideService ? eDualChargeServ_Allowed : eDualChargeServ_Disallowed);

        DUALGUN_SERVICE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, canProvideService ? eDualChargeServ_Allowed : eDualChargeServ_Disallowed,
                            "ch:%d 回到独立模式，服务状态: %s",
                            channel, canProvideService ? "可用" : "不可用\n");

        // 更新命令状态
        ctrl->ctrlCmd = CMD_FAST_IDLE;

        // 调用控制器初始化，确保状态干净
        DualCharge_Ctrl_Init();

        // 独立模式下重置转换完成标志
        ctrl->conversion_completed = FALSE;
    }

    // 清除并柜过程标志 - 增强版，增加错误检查
    if (!Enhanced_Clr_DualChargeOperateFlag(channel))
    {
        // 清理失败，恢复原状态
        DUALGUN_ERROR(TR_CCU_DEBUG, "ch:%d 状态标志清理失败，恢复到原状态: %s\n",
                      channel, Get_DualchargeModeText(previous_mode));

        // 恢复之前的状态
        Set_DUALCHARGE_CTRL_mode(ctrl, previous_mode);
        ctrl->operate = previous_operate;
        ctrl->operateDirection = previous_direction;

        // 记录恢复事件
        log_dual_charge_event(channel, DUAL_EVENT_MODE_CHANGE_ROLLBACK,
                              previous_mode, mode, Get_WorkState());
    }

    // 解除状态切换锁定
    ctrl->mode_transition_in_progress = FALSE;

    // 添加模式变更成功日志
    log_dual_charge_event(channel, DUAL_EVENT_MODE_CHANGED_SUCCESS,
                          previous_mode, mode, Get_WorkState());
}

uint8 Get_DualchargeMode(uint8 channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    if (NULL != ctrl)
    {
        return (ctrl->mode);
    }
    DUALGUN_ERROR(TR_CCU_DEBUG, "Get_DualchargeMode...error! 无效通道:%d\n", channel);
    return 0;
}

/**
 * @brief 检查充电启动条件
 * @return TRUE-条件满足 FALSE-条件不满足
 * @note 包含急停信号检测
 */
uint8 Start_Check_Responder(uint8 channel)
{
    uint8 workState = Get_WorkState();

    if (CCU_WORK_STATE_IMD != workState)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "启动检查失败：工作状态异常 [%d][%s]\n", workState, __FUNCTION__);
        return FALSE;
    }

    if (!Get_ChargeMoudleStartUp())
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "充电模块未就绪\n");
        return FALSE;
    }

    DUALGUN_INFO(TR_CCU_DEBUG, "所有启动条件已满足\n");
    return TRUE;
}

/**
 * @brief 检查预充电启动条件
 * @return TRUE-条件满足 FALSE-条件不满足
 * @note 包含急停信号检测
 */
uint8 Start_PrechargeCheck_Responder(uint8 channel)
{
    uint8 workState = Get_WorkState();

    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    if (CCU_WORK_STATE_PRE_CHARGE != workState)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "启动检查失败：工作状态异常 [%d][%s]\n", workState, __FUNCTION__);
        return FALSE;
    }

    // 欧洲特殊模式处理
    bool pre_bypass = Get_CcuCfgParaEuropeEnable();
    if (pre_bypass && SPNID_BCCL != Get_DUALCHARGE_CTRL_offsidespn(ctrl))
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "欧洲模式跳过检测\n");
        return FALSE;
    }

    if (ePcuWorkState_Busy != Get_PcuWorkState())
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "PCU不处于工作状态!!!\n");
        return FALSE;
    }

    DUALGUN_INFO(TR_CCU_DEBUG, "所有启动条件已满足\n");
    return TRUE;
}

/**
 * @brief 检测立即充电完成状态
 * @param channel 通道号
 * @return TRUE-完成 FALSE-未完成
 * @note 包含超时机制和互锁检测
 */
uint8 Check_StartImdChargeFinish(uint8 channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    // 超时检测（15秒超时）
    uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
    if (timeElapsed >= 45 * sysClkRateGet()) // 25
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 绝缘检测超时 [经过:%d秒]\n",
                     channel, timeElapsed / sysClkRateGet());
        return FALSE;
    }

    //    if (!Get_ImdSuccFlag())
    //    {
    //        // 非错误，只是还未完成
    //        return FALSE;
    //    }

    // 欧洲特殊模式处理
    if (Get_CcuCfgParaEuropeEnable())
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 欧洲模式跳过继电器检测\n", channel);
        return TRUE;
    }

    if (!Get_Release01SuccFlag())
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 泄放失败\n", channel);
        return FALSE;
    }

    DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 充电准备已完成 [SPN:%02X]\n",
                 channel, ctrl->spn);
    return TRUE;
}

/**
 * @brief 检测立即充电完成状态
 * @param channel 通道号
 * @return TRUE-完成 FALSE-未完成
 * @note 包含超时机制和互锁检测
 */
uint8 Check_StartChargeFinish(uint8 channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    uint8 workState = Get_WorkState();

    if (workState < CCU_WORK_STATE_PRE_CHARGE)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "启动检查失败：工作状态异常 [%d][%s]\n", workState, __FUNCTION__);
        return FALSE;
    }

    if (!Get_ChargeMoudleStartUp())
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "充电模块未就绪\n");
        return FALSE;
    }
    // 超时检测（15秒超时）
    uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
    if (timeElapsed >= 45 * sysClkRateGet()) // 15
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 软启动超时 [经过:%d秒]\n",
                     channel, timeElapsed / sysClkRateGet());
        return FALSE;
    }

    DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 充电准备已完成 [SPN:%02X]\n",
                 channel, ctrl->spn);
    return TRUE;
}

/**
 * @brief 检查充电启动条件
 * @return TRUE-条件满足 FALSE-条件不满足
 * @note 包含急停信号检测
 */
uint8 Start_Check_Initiator(void)
{
    uint8 workState = Get_WorkState();

    if (workState < CCU_WORK_STATE_RELEASE_01)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "启动检查失败：工作状态异常 [%d][%s]\n", workState, __FUNCTION__);
        return FALSE;
    }

    //    if (!Get_ImdSuccFlag())
    //    {
    //        return FALSE;
    //    }

    // 欧洲特殊模式处理
    bool pre_bypass = Get_CcuCfgParaEuropeEnable();
    if (pre_bypass)
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "欧洲模式跳过检测\n");
        return TRUE;
    }

    if (!Get_Release01SuccFlag())
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "泄放失败!!!!\n");
        return FALSE;
    }

    DUALGUN_INFO(TR_CCU_DEBUG, "所有启动条件已满足\n");
    return TRUE;
}

/**
 * @brief 检查预充电启动条件
 * @return TRUE-条件满足 FALSE-条件不满足
 * @note 包含急停信号检测
 */
uint8 Start_PrechargeCheck_Initiator(uint8 channel)
{
    uint8 workState = Get_WorkState();

    if (CCU_WORK_STATE_PRE_CHARGE != workState)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "启动检查失败：工作状态异常 [%d][%s]\n", workState, __FUNCTION__);
        return FALSE;
    }

    // 欧洲特殊模式处理
    bool pre_bypass = Get_CcuCfgParaEuropeEnable(); // 需修改
    if (pre_bypass)
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "欧洲模式跳过检测\n");
        return FALSE;
    }

    if (!Get_ChargeMoudleStartUp())
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "充电模块未就绪\n");
        return FALSE;
    }

    if (!Check_Is_K1K2On())
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "继电器未闭合\n");
        return FALSE;
    }

    DUALGUN_INFO(TR_CCU_DEBUG, "所有启动条件已满足\n");
    return TRUE;
}

/**
 * @brief 检测关机完成,进入空闲态
 * @param channel 通道号
 * @return TRUE-完成 FALSE-未完成
 * @note 添加超时检测确保关机流程不会卡住
 */
uint8 Check_DualStopFinish(int channel)
{
    uint8 workState = Get_WorkState();
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    // 添加超时检测
    uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
    if (timeElapsed >= 45 * sysClkRateGet()) // 30
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 停机超时，强制完成 [经过:%d秒]\n",
                     channel, timeElapsed / sysClkRateGet());
        return TRUE;
    }

    if (CCU_WORK_STATE_STOP_FINISH != workState)
    {
        return FALSE;
    }

    return TRUE;
}

/**
 * @brief 独立模式状态处理
 * @param channel 通道号
 * @note 修复了状态转换和错误处理的闭环
 */
static void
Deal_DualCharge_Independent(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    // 是否并柜(响应)?
    if ((eDualChargeOpr_ImdStart == ctrl->operate) && (eDualChargeDir_Responder == ctrl->operateDirection))
    {
        // 并柜未启动且服务允许.
        if ((FALSE == ctrl->chargeImdReadyFlag && FALSE == ctrl->chargeImdFinishFlag) && (eDualChargeServ_Allowed == ctrl->service))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...1\n", channel);

            if (Start_Check_Responder(channel))
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...2\n", channel);
                ctrl->chargeImdReadyFlag = TRUE;
            }
            else
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...30\n", channel);
                uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
                if (timeElapsed >= 45 * sysClkRateGet()) // 30
                {
                    Set_DUALCHARGE_CTRL_service(ctrl, eDualChargeServ_Disallowed);
                    Deal_DualChargeStopCharge(channel);
                    stop_DualCharge(channel);
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...31\n", channel);
                }
                Deal_DualChargeStartCharge(channel, ctrl->ctrlCmd, ctrl->voltage,
                                           ctrl->current, ctrl->batVoltage);
            }
        }
        if ((TRUE == ctrl->chargeImdReadyFlag) && (FALSE == ctrl->chargeImdFinishFlag))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...3\n", channel);
            if (TRUE == Check_StartImdChargeFinish(channel)) // 绝缘结束
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...4\n", channel);
                ctrl->chargeImdFinishFlag = TRUE;
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...5\n", channel);
            }
            else
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...6\n", channel);
                Set_DUALCHARGE_CTRL_success_flag(ctrl, FALSE);
                uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
                if (timeElapsed >= 45 * sysClkRateGet()) // 30
                {
                    Set_DUALCHARGE_CTRL_service(ctrl, eDualChargeServ_Disallowed);
                    Deal_DualChargeStopCharge(channel);
                    stop_DualCharge(channel);
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...7\n", channel);
                }
            }
        }
        if (TRUE == ctrl->chargeImdReadyFlag && TRUE == ctrl->chargeImdFinishFlag)
        {
            Set_DualchargeMode(channel, eDualChargeStatus_Insulation); // 切换状态
            Set_DUALCHARGE_CTRL_success_flag(ctrl, TRUE);
            Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_CRM_AA);
            //            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...8\n", channel);
            trace(TR_CCU_DEBUG, "ch:%d,success_flag ：%d,spn：%d,Deal_DualCharge_Independent...8\n",
                  channel, ctrl->success_flag, ctrl->spn);
        }
    }
    // 是否并柜(发起)?
    else if ((eDualChargeOpr_ImdStart == ctrl->operate) && (eDualChargeDir_Initiator == ctrl->operateDirection))
    {
        // 对侧有服务能力
        if ((FALSE == ctrl->chargeImdReadyFlag && FALSE == ctrl->chargeImdFinishFlag) && (eDualChargeServ_Allowed == ctrl->OffsideService) && (eDualChargeStatus_Independent == ctrl->mode))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...11\n", channel);
            if (Start_Check_Initiator())
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...12\n", channel);
                Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_CRM_00);
                CcuChargeCtrlSend(channel, ctrl->ctrlCmd, ctrl->voltage,
                                  ctrl->current, ctrl->batVoltage); // 打开充电启动命令发送
                ctrl->chargeImdReadyFlag = TRUE;
            }
        }
        // 检测启动完成
        if ((TRUE == ctrl->chargeImdReadyFlag) && (FALSE == ctrl->chargeImdFinishFlag))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...13", channel);
            if (eDualChargeStatus_Insulation == Get_DUALCHARGE_CTRL_offsidemode(ctrl))
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...14\n", channel);
                ctrl->chargeImdFinishFlag = TRUE;
            }
            // 超时检测
            uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
            if (timeElapsed >= 45 * sysClkRateGet()) // 30
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent超时退出...15\n", channel);
                Set_DUALCHARGE_CTRL_service(ctrl, eDualChargeServ_Disallowed);
                stop_DualCharge(channel);
            }
        }
        if ((TRUE == ctrl->chargeImdReadyFlag) && TRUE == ctrl->chargeImdFinishFlag)
        {
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...16\n", channel);
            // SPNID_CRM_AA == Get_DUALCHARGE_CTRL_offsidespn(ctrl) &&
            if (eDualChargeStatus_Insulation == Get_DUALCHARGE_CTRL_offsidemode(ctrl))
            //  && TRUE == Get_DUALCHARGE_CTRL_success_flag(ctrl))
            {
                DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...17\n", channel);
                //                uint8 current_spn = Get_DUALCHARGE_CTRL_offsidespn(ctrl);
                bool current_success = Get_DUALCHARGE_CTRL_success_flag(ctrl);
                Set_DualchargeMode(channel, eDualChargeStatus_Insulation); // 切换状态
                // 确保标志在状态切换后保持一致
                Check_Is_Send2560(TRUE, TRUE);
                //                Set_DUALCHARGE_CTRL_spn(ctrl, current_spn);
                Set_DUALCHARGE_CTRL_success_flag(ctrl, current_success);
            }
        }
    }
    // 异常处理,本侧停机
    else if ((eDualChargeOpr_Stop == ctrl->operate))
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...21\n", channel);
        if (FALSE == ctrl->stopChargeFlag)
        {
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...22\n", channel);
            ctrl->stopChargeFlag = TRUE;
            if (eDualChargeDir_Initiator == ctrl->operateDirection)
            {
                DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...23\n", channel);
                Deal_DualChargeStopCharge(channel); // 暂时使用
                Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_CST);
                CcuChargeCtrlSend(channel, CMD_RELEASE_MODULE, 0, 0, 0);
            }
            else if (eDualChargeDir_Responder == ctrl->operateDirection)
            {
                DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...24\n", channel);
                Deal_DualChargeStopCharge(channel);
                Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_BST);
            }
        }

        if ((TRUE == ctrl->stopChargeFlag) && (FALSE == ctrl->stopChargeFinishFlag))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...25\n", channel);
            if (TRUE == Check_DualStopFinish(channel))
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...26\n", channel);
                if ((eDualChargeDir_Responder == ctrl->operateDirection))
                {
                    Set_DUALCHARGE_CTRL_success_flag(ctrl, FALSE);
                }
                ctrl->stopChargeFinishFlag = TRUE;

                if (eDualChargeDir_Initiator == ctrl->operateDirection)
                {
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...27\n", channel);
                }
            }
        }

        if (TRUE == ctrl->stopChargeFinishFlag && TRUE == ctrl->stopChargeFlag)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...28\n", channel);
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...29\n", channel);
            Set_DualchargeMode(channel, eDualChargeStatus_Independent); // 切换状态
        }
    }
}

/**
 * @brief 绝缘模式状态处理
 * @param channel 通道号
 * @note 修复了状态转换和错误处理的闭环
 */
static void
Deal_DualCharge_Insulation(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    // 是否并柜(响应)?
    if ((eDualChargeOpr_SoftStart == ctrl->operate) && (eDualChargeDir_Responder == ctrl->operateDirection))
    {
        // 并柜未启动且服务允许.
        if ((FALSE == ctrl->startChargeFlag && FALSE == ctrl->startChargeFinishFlag) && (eDualChargeServ_Allowed == ctrl->service))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...1\n", channel);

            if (Start_PrechargeCheck_Responder(channel))
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...2\n", channel);
                ctrl->startChargeFlag = TRUE;
            }
            else
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...\n", channel);
                uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
                if (timeElapsed >= 45 * sysClkRateGet()) // 30
                {
                    Set_DUALCHARGE_CTRL_service(ctrl, eDualChargeServ_Disallowed);
                    Deal_DualChargeStopCharge(channel);
                    stop_DualCharge(channel);
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Independent...31\n", channel);
                }
                Deal_DualChargeStartCharge(channel, ctrl->ctrlCmd, ctrl->voltage,
                                           ctrl->current, ctrl->batVoltage);
            }
        }
        if ((TRUE == ctrl->startChargeFlag) && (FALSE == ctrl->startChargeFinishFlag))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...3\n", channel);
            if (TRUE == Check_StartChargeFinish(channel)) // 预充
            {
                ctrl->startChargeFinishFlag = TRUE;
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...5\n", channel);
            }
            else
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...6\n", channel);
                Set_DUALCHARGE_CTRL_success_flag(ctrl, FALSE);
                uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
                if (timeElapsed >= 45 * sysClkRateGet()) // 30
                {
                    Set_DUALCHARGE_CTRL_service(ctrl, eDualChargeServ_Disallowed);
                    Deal_DualChargeStopCharge(channel);
                    stop_DualCharge(channel);
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...7\n", channel);
                }
            }
        }
        if (TRUE == ctrl->startChargeFlag && TRUE == ctrl->startChargeFinishFlag)
        {
            Set_DUALCHARGE_CTRL_success_flag(ctrl, TRUE);
            if (!Get_CcuCfgParaEuropeEnable())
            {
                Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_CRO_AA);
            }
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...8\n", channel);
            Set_DualchargeMode(channel, eDualChargeStatus_Slave); // 切换状态
            Check_Is_preChargeFlag(TRUE, TRUE);
        }
    }
    // 是否并柜(发起)?
    else if ((eDualChargeOpr_SoftStart == ctrl->operate) && (eDualChargeDir_Initiator == ctrl->operateDirection))
    {
        // 对侧有服务能力
        if ((FALSE == ctrl->startChargeFlag && FALSE == ctrl->startChargeFinishFlag) && (eDualChargeServ_Allowed == ctrl->OffsideService))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...11\n", channel);
            if (Start_PrechargeCheck_Initiator(channel))
            {
                if (Get_CcuCfgParaEuropeEnable())
                {
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...4\n", channel);
                    Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_BCCL);
                    CcuChargeCtrlSend(channel, ctrl->ctrlCmd, ctrl->voltage,
                                      ctrl->current, ctrl->batVoltage); // 打开充电启动命令发送
                }
                else
                {
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...12\n", channel);
                    Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_CRO_00);
                    CcuChargeCtrlSend(channel, ctrl->ctrlCmd, ctrl->voltage,
                                      ctrl->current, ctrl->batVoltage); // 打开充电启动命令发送
                }
                ctrl->startChargeFlag = TRUE;
            }
        }
        // 检测启动完成
        if ((TRUE == ctrl->startChargeFlag) && (FALSE == ctrl->startChargeFinishFlag))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...13\n", channel);
            //            TRUE == Get_DUALCHARGE_CTRL_success_flag(ctrl)
            if (eDualChargeStatus_Slave == Get_DUALCHARGE_CTRL_offsidemode(ctrl))
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...14\n", channel);
                ctrl->startChargeFinishFlag = TRUE;
            }
            // 超时检测
            uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
            if (timeElapsed >= 60 * sysClkRateGet()) // 30
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation超时退出...15\n", channel);
                Set_DUALCHARGE_CTRL_service(ctrl, eDualChargeServ_Disallowed);
                stop_DualCharge(channel);
            }
        }
        if ((TRUE == ctrl->startChargeFlag) && TRUE == ctrl->startChargeFinishFlag)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...16\n", channel);
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...17\n", channel);
            Set_DualchargeMode(channel, eDualChargeStatus_Master); // 切换状态
            Check_Is_preChargeFlag(TRUE, TRUE);
        }
    }
    // 异常处理,本侧停机
    else if ((eDualChargeOpr_Stop == ctrl->operate))
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...21\n", channel);
        if (FALSE == ctrl->stopChargeFlag)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...22\n", channel);
            ctrl->stopChargeFlag = TRUE;
            if (eDualChargeDir_Initiator == ctrl->operateDirection)
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...23\n", channel);
                Deal_DualChargeStopCharge(channel); // 暂时使用
                Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_CST);
                CcuChargeCtrlSend(channel, CMD_RELEASE_MODULE, 0, 0, 0);
            }
            else if (eDualChargeDir_Responder == ctrl->operateDirection)
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...24\n", channel);
                Deal_DualChargeStopCharge(channel);
                if (SPNID_CST != Get_DUALCHARGE_CTRL_offsidespn(ctrl))
                {
                    Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_BST);
                }
            }
        }

        if ((TRUE == ctrl->stopChargeFlag) && (FALSE == ctrl->stopChargeFinishFlag))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...25\n", channel);
            if (TRUE == Check_DualStopFinish(channel)) // 是继续充电还是停机呢？
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...26\n", channel);
                if ((eDualChargeDir_Responder == ctrl->operateDirection))
                {
                    // 清除适当的标志位
                    Set_DUALCHARGE_CTRL_success_flag(ctrl, FALSE);
                }
                ctrl->stopChargeFinishFlag = TRUE;

                if (eDualChargeDir_Initiator == ctrl->operateDirection)
                {
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...27\n", channel);
                }
            }
        }

        if (TRUE == ctrl->stopChargeFinishFlag && TRUE == ctrl->stopChargeFlag)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...28\n", channel);
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Insulation...29\n", channel);
            Set_DualchargeMode(channel, eDualChargeStatus_Independent); // 切换状态
        }
    }
}

/**
 * @brief 主机模式状态处理
 * @param channel 通道号
 * @note 修复了结构不一致问题，统一使用DualCharge结构和状态
 */
static void
Deal_DualCharge_Master(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    if ((eDualChargeOpr_Stop == ctrl->operate) || (eDualChargeServ_Disallowed == ctrl->OffsideService))
    //           || (eDualChargeStatus_Slave != ctrl->offsidemode))
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Master...1\n", channel);
        if (FALSE == ctrl->stopChargeFlag)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Master...2\n", channel);
            Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_CST);
            CcuChargeCtrlSend(channel, CMD_RELEASE_MODULE, 0, 0, 0);
            Deal_DualChargeStopCharge(channel); // 暂时使用
            ctrl->stopChargeFlag = TRUE;
        }

        if ((TRUE == ctrl->stopChargeFlag) && (FALSE == ctrl->stopChargeFinishFlag))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Master...3\n", channel);
            // 对侧不能提供服务或者对侧非从机状态
            if ((eDualChargeServ_Disallowed == ctrl->OffsideService) || (eDualChargeStatus_Slave != ctrl->offsidemode))
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Master...4\n", channel);
                ctrl->stopChargeFinishFlag = TRUE;
            }
            // 超时检测
            uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
            if (timeElapsed >= 45 * sysClkRateGet()) // 30
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Master超时退出...5\n", channel);
                ctrl->stopChargeFinishFlag = TRUE;
            }
        }

        if (TRUE == ctrl->stopChargeFinishFlag && TRUE == ctrl->stopChargeFlag)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Master...6\n", channel);
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Master...7\n", channel);
            Set_DualchargeMode(channel, eDualChargeStatus_Independent); // 切换状态
        }
    }
    else
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Master...8\n", channel);
        DUALGUN_POWER_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, ctrl->voltage, ctrl->current,
                          "ch:%d,Deal_DualCharge_Master...vol=%d,cur=%d,bat=%d\n",
                          channel, ctrl->voltage, ctrl->current, ctrl->batVoltage);

        // 更新时间戳，确保超时检测正确
        uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
        if (timeElapsed > 1 * sysClkRateGet())
        {
            Set_DUALCHARGE_CTRL_tick(ctrl, tickGet());
        }

        // 更新输出电压、电流、电池电压;
        CcuChargeCtrlSend(channel, CMD_MODIFY_PARAM, ctrl->voltage,
                          ctrl->current, ctrl->batVoltage);
    }
}

/**
 * @brief 从机模式状态处理
 * @param channel 通道号
 * @note 修复了结构不一致问题，统一使用DualCharge结构和状态
 */
static void
Deal_DualCharge_Slave(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    // 本侧插枪;
    if ((eDualChargeOpr_Stop == ctrl->operate))
    // || (eDualChargeStatus_Master != ctrl->offsidemode))
    {
        // 本侧停机处理.
        if (FALSE == ctrl->stopChargeFlag)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Slave...1\n", channel);
            ctrl->stopChargeFlag = TRUE;
            Deal_DualChargeStopCharge(channel);
            Set_DUALCHARGE_CTRL_spn(ctrl, SPNID_BST);
        }

        if ((TRUE == ctrl->stopChargeFlag) && (FALSE == ctrl->stopChargeFinishFlag))
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Slave...2\n", channel);
            if (TRUE == Check_DualStopFinish(channel)) // 停机结束?
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Slave...3\n", channel);
                ctrl->stopChargeFinishFlag = TRUE;
            }
            else
            {
                Deal_DualChargeStopCharge(channel);
            }
            // 超时检测
            uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
            if (timeElapsed >= 45 * sysClkRateGet())
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Slave超时退出...4\n", channel);
                ctrl->stopChargeFinishFlag = TRUE;
            }
        }
        if (TRUE == ctrl->stopChargeFinishFlag)
        {
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Slave...5\n", channel);
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Slave...6\n", channel);
            Set_DualchargeMode(channel, eDualChargeStatus_Independent); // 切换状态
        }
    }
    else
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d,Deal_DualCharge_Slave...7\n", channel);
        DUALGUN_POWER_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, ctrl->voltage, ctrl->current,
                          "ch:%d,Deal_DualCharge_Slave...vol=%d,cur=%d,bat=%d\n",
                          channel, ctrl->voltage, ctrl->current, ctrl->batVoltage);

        // 更新时间戳，确保超时检测正确
        uint32 timeElapsed = abs(tickGet() - Get_DUALCHARGE_CTRL_tick(ctrl));
        if (timeElapsed > 1 * sysClkRateGet())
        {
            Set_DUALCHARGE_CTRL_tick(ctrl, tickGet());
        }

        // 刷新输出电压、电流、电池电压;
        Deal_DualChargeCharging(channel, ctrl->voltage, ctrl->current,
                                ctrl->batVoltage);
    }
}

/**
 * @brief 增强版检查并更新服务状态
 * @param channel 通道号
 */
void Enhanced_Check_DualChargeService(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
    if (NULL == ctrl)
    {
        DUALGUN_ERROR(TR_CCU_DEBUG, "ch:%d 获取控制结构失败\n", channel);
        return;
    }

    bool previousServiceState = (ctrl->service == eDualChargeServ_Allowed);
    bool currentServiceAvailable = TRUE;

    // 静态变量，防止日志污染
    static uint8 last_workState[DUAL_CHARGE_CHANNEL_NUM] = {0xFF};
    static uint32 last_service_change_time[DUAL_CHARGE_CHANNEL_NUM] = {0};

    uint8 workState = Get_WorkState();

    // 基础条件检查
    if (IsDeviceFaulted() || CONN_CONNECTED != ctrl->connected || ctrl->revctrltimeout)
    {
        currentServiceAvailable = FALSE;
    }

    // 按模式处理
    if (ctrl->mode == eDualChargeStatus_Independent)
    {
        // 检查是否处于并充初始化阶段
        bool is_dual_charge_init =
            (ctrl->operate == eDualChargeOpr_ImdStart ||
             ctrl->operate == eDualChargeOpr_SoftStart);

        // 独立模式特殊处理 - 充电中不允许服务，除非是并充初始化
        if (ctrl->operateDirection == eDualChargeDir_Responder)
        {
            if (workState > CCU_WORK_STATE_READY && !is_dual_charge_init)
            {
                currentServiceAvailable = FALSE;

                // 避免日志污染
                if (workState != last_workState[channel])
                {
                    DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 独立模式充电中(非并充初始化)，不可服务\n", channel);
                    last_workState[channel] = workState;
                }
            }
        }
        // 发起侧始终保持服务可用
        else if (ctrl->operateDirection == eDualChargeDir_Initiator)
        {
            currentServiceAvailable = TRUE;
        }
        else if (workState > CCU_WORK_STATE_READY && is_dual_charge_init)
        {
            // 并充初始化特殊情况，允许服务
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 虽处于充电状态但正在并充初始化，允许服务\n", channel);
            currentServiceAvailable = TRUE;
        }
    }

    // 增强：检查是否在紧急状态下
    if (ctrl->emergency_active)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 正在紧急状态中，不可服务\n", channel);
        currentServiceAvailable = FALSE;
    }

    // 处理服务状态变化
    if (previousServiceState != currentServiceAvailable)
    {
        last_service_change_time[channel] = tickGet();

        if (previousServiceState && !currentServiceAvailable)
        {
            // 由可用变为不可用
            DUALGUN_SERVICE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, eDualChargeServ_Disallowed,
                                "ch:%d 本侧状态变为不可服务\n", channel);
            Set_DUALCHARGE_CTRL_service(ctrl, eDualChargeServ_Disallowed);

            // 非独立状态下触发停止流程
            if (ctrl->mode != eDualChargeStatus_Independent &&
                ctrl->operate != eDualChargeOpr_Stop)
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 服务变为不可用，触发停止流程 [模式:%d]\n",
                             channel, ctrl->mode);
                stop_DualCharge(channel);
            }
            // 独立状态下如果有操作也需要停止
            else if (ctrl->mode == eDualChargeStatus_Independent &&
                     ctrl->operate != eDualChargeOpr_Null &&
                     ctrl->operate != eDualChargeOpr_Stop)
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 独立状态服务不可用，取消当前操作 [%d]\n",
                             channel, ctrl->operate);
                stop_DualCharge(channel);
            }

            // 重置服务稳定通知标志
            ctrl->service_stable_notified = FALSE;
        }
        else if (!previousServiceState && currentServiceAvailable)
        {
            // 由不可用变为可用
            DUALGUN_SERVICE_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, eDualChargeServ_Allowed,
                                "ch:%d 本侧状态变为可服务\n", channel);
            Set_DUALCHARGE_CTRL_service(ctrl, eDualChargeServ_Allowed);

            // 重置服务稳定通知标志
            ctrl->service_stable_notified = FALSE;
        }
    }
    // 强制状态一致性检查，解决潜在的非原子性问题
    else if (!currentServiceAvailable && ctrl->service == eDualChargeServ_Allowed)
    {
        // 强制修正：服务应不可用但当前标记为可用
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 强制修正服务状态为不可用\n", channel);
        Set_DUALCHARGE_CTRL_service(ctrl, eDualChargeServ_Disallowed);

        // 修改：添加停止双充操作，确保状态一致性
        if (ctrl->mode != eDualChargeStatus_Independent ||
            (ctrl->operate != eDualChargeOpr_Stop && ctrl->operate != eDualChargeOpr_Null))
        {
            stop_DualCharge(channel);
        }

        // 重置服务稳定通知标志
        ctrl->service_stable_notified = FALSE;
    }

    // 增强：检查服务状态稳定性
    if (abs(tickGet() - last_service_change_time[channel]) > MIN_SERVICE_STABLE_TIME)
    {
        // 服务状态已稳定超过最小时间，可以进行进一步操作
        if (ctrl->service == eDualChargeServ_Allowed &&
            !ctrl->service_stable_notified)
        {
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 服务状态已稳定可用\n", channel);
            ctrl->service_stable_notified = TRUE;
        }
    }
    else
    {
        // 服务状态不稳定，重置通知标志
        ctrl->service_stable_notified = FALSE;
    }
}

/**
 * @brief 检测并充需求
 * @param channel 通道号
 * @note 检测对侧服务状态变化，发现不可服务时通过正常流程触发停止
 *       避免使用默认充电参数，参数无效时停止充电流程
 */

static void
Check_DualChargeDemand(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
    static uint8 lastChargeActFlag = eActFlag_Null;
    if (NULL == ctrl)
    {
        DUALGUN_ERROR(TR_CCU_DEBUG, "ch:%d 获取控制结构失败\n", channel);
        return;
    }
    uint8_t workState = Get_WorkState();
    uint16_t vol = 0, cur = 0, batVol = 0;

    // 如果已经处于停止操作中，不执行新的操作
    if (eDualChargeOpr_Stop == ctrl->operate)
    {
        return; // 等待当前停止操作完成
    }
    if (lastChargeActFlag != Get_ChargeActFlag() && eDualChargeOpr_Null != ctrl->operate)
    {
        if (Get_ChargeActFlag() == eActFlag_Off)
        {
            stop_DualCharge(channel);
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 主流程[%d]--->[%d]!!\n",
                         channel, lastChargeActFlag, Get_ChargeActFlag());
            lastChargeActFlag = Get_ChargeActFlag();
            return;
        }
        DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 主流程[%d]--->[%d]!!\n",
                     channel, lastChargeActFlag, Get_ChargeActFlag());
        lastChargeActFlag = Get_ChargeActFlag();
    }
    // 任何模式下，如果对侧突然变为不可服务且不是独立模式，则触发停止流程
    if (ctrl->mode != eDualChargeStatus_Independent &&
        eDualChargeServ_Disallowed == ctrl->OffsideService)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 对侧服务不可用，在模式[%d]下触发停止流程\n",
                     channel, ctrl->mode);
        stop_DualCharge(channel);
        return; // 触发停止后退出，让状态机处理完成剩余流程
    }

    // 如果本侧服务不可用，但不处于停止流程中，可能存在状态不一致
    if (ctrl->service == eDualChargeServ_Disallowed &&
        ctrl->mode != eDualChargeStatus_Independent &&
        ctrl->operate != eDualChargeOpr_Stop)
    {
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 状态不一致: 服务不可用但未停止 [模式:%d 操作:%d]\n",
                     channel, ctrl->mode, ctrl->operate);
        stop_DualCharge(channel);
        return;
    }

    // 根据当前模式处理不同的充电需求
    switch (ctrl->mode)
    {
    case eDualChargeStatus_Independent:
        if (!Get_DualChargeEnableStatus())
        {
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d TCU未启用双充，跳过启动操作\n", channel);
            break; // 只跳过启动操作，不跳过整个函数
        }
        // 独立模式 - 只有当对侧可服务且本侧可服务时才尝试启动并充
        if (eDualChargeServ_Allowed == ctrl->OffsideService &&
            eDualChargeServ_Allowed == ctrl->service)
        {
            // !!! 重要: 独立模式下仅当收到TCU启用指令时才执行启动操作 !!!

            Set_DualChargeImdInitiatedFlag(TRUE);
            if (TRUE == Get_Release01SuccFlag())
            {
                if (!Get_dual_charge_params(&vol, &cur, &batVol))
                {
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 获取充电参数失败，无法启动并充\n", channel);
                    break; // 无法获取参数时不启动并充
                }

                Start_DualImdCharge(channel,
                                    CMD_FAST_START,
                                    eDualChargeDir_Initiator, vol, cur, batVol);
                DUALGUN_POWER_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, vol, cur,
                                  "ch:%d-%-6s并充需求%-6s[-----]%-6s[发起开机]\n",
                                  channel, "**", "**", "**");
            }
            else
            {
                //                DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 泄放未就绪，跳过并充启动", channel);
            }
        }
        // 独立模式 - 先检查对侧服务状态
        else if (eDualChargeServ_Disallowed == ctrl->OffsideService &&
                 ctrl->operate != eDualChargeOpr_Null)
        {
            // 对侧服务不可用，统一使用stop_DualCharge处理
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 独立状态下对侧服务不可用，执行停止流程\n", channel);
            stop_DualCharge(channel);
            return;
        }

        break;

    case eDualChargeStatus_Insulation:
        // !!! 重要: 绝缘检测模式下仅当收到TCU启用指令时才执行启动操作 !!!
        if (!Get_DualChargeEnableStatus())
        {
            break; // 只跳过启动操作，不跳过整个函数
        }

        // 绝缘检测模式 - 检查对侧服务状态
        if (eDualChargeServ_Allowed == ctrl->OffsideService)
        {
            // 对侧可服务时，检查是否可以开始预充电
            if (CCU_WORK_STATE_PRE_CHARGE == workState)
            {

                if (!Get_dual_charge_params(&vol, &cur, &batVol))
                {
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 获取充电参数失败，停止并充\n", channel);
                    stop_DualCharge(channel); // 参数获取失败时停止整个流程
                    return;
                }

                Start_DualSoftCharge(channel,
                                     CMD_SOFT_START,
                                     eDualChargeDir_Initiator, vol, cur, batVol);

                DUALGUN_POWER_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, vol, cur,
                                  "ch:%d-%-6s并充需求%-6s[-----]%-6s[发起开机]\n",
                                  channel, "**", "**", "**");
            }
            else
            {
                //                DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 工作状态非预充电，当前状态:%d", channel, workState);
            }
        }
        // 独立模式 - 先检查对侧服务状态
        else if (eDualChargeServ_Disallowed == ctrl->OffsideService &&
                 ctrl->operate != eDualChargeOpr_Null)
        {
            // 对侧服务不可用，统一使用stop_DualCharge处理
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 独立状态下对侧服务不可用，执行停止流程\n", channel);
            stop_DualCharge(channel);
            return;
        }
        else if (ctrl->operate != eDualChargeOpr_Stop)
        {
            // 对侧服务不可用，且不是独立模式时触发停止
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d 绝缘检测中发现对侧服务不可用，触发停止\n", channel);
            stop_DualCharge(channel);
        }
        break;

    case eDualChargeStatus_Master:
        // 主机模式 - 检查对侧服务状态和对侧模式
        if ((eDualChargeServ_Disallowed == ctrl->OffsideService) ||
            (SPNID_BST == Get_DUALCHARGE_CTRL_offsidespn(ctrl)) || (eDualChargeStatus_Slave != ctrl->offsidemode))

        {
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d-%-6s并充操作%-6s[-----]%-6s[主机停机]\n",
                         channel, "**", "**", "**");
            stop_DualCharge(channel);
        }
        else
        {
            // 正常充电中
            if (!Get_dual_charge_params(&vol, &cur, &batVol))
            {
                DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 主机模式获取充电参数失败，停止充电\n", channel);
                stop_DualCharge(channel);
                return;
            }

            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d-%-6s并充操作%-6s[-----]%-6s[主机充电]\n",
                         channel, "**", "**", "**");

            // 电压安全处理 - 使用安全参数处理函数
            Set_SafeDualChargeParams(channel, vol, cur, batVol);

            // 更新时间戳，用于超时检测
            Set_DUALCHARGE_CTRL_tick(ctrl, tickGet());
        }
        break;

    case eDualChargeStatus_Slave:
        // 从机模式 - 检查本侧服务状态和对侧模式
        if ((eDualChargeServ_Disallowed == ctrl->service) || (SPNID_CST == Get_DUALCHARGE_CTRL_offsidespn(ctrl)))

        //||(eDualChargeStatus_Master != ctrl->offsidemode))

        {
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d-%-6s并充操作%-6s[-----]%-6s[从机停机]\n",
                         channel, "**", "**", "**");
            stop_DualCharge(channel);
        }
        else
        {
            // 正常充电中，可以添加状态监控
            DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d-%-6s并充操作%-6s[-----]%-6s[从机充电中]\n",
                         channel, "**", "**", "**");

            // 更新时间戳，用于超时检测
            Set_DUALCHARGE_CTRL_tick(ctrl, tickGet());
        }
        break;

    default:
        // 未知状态，通过正规停止流程恢复到独立模式
        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 未知状态[%d]，启动停止流程\n",
                     channel, ctrl->mode);
        stop_DualCharge(channel);
        break;
    }
}
#if 0
/**
 * @brief 获取安全的充电电压参数
 * @param channel 通道号
 * @param vol 设定电压(0.1V)
 * @param batVol 电池电压(0.1V)
 * @return 合适的充电电压值(0.1V)
 * @note 基于GB/T 29730标准实现平滑安全的电压选择
 */
uint16_t Get_SafeChargeVoltage(uint8_t channel, uint16_t vol, uint16_t batVol)
{
    uint16_t safeVol = vol;                                        // 默认使用设定电压
    uint16_t busVol = Get_K1K2OutsideVol();                           // 总线实测电压
    static uint16_t lastBusVol[DUAL_CHARGE_CHANNEL_NUM] = {0};     // 上次总线电压
    static uint16_t filteredBusVol[DUAL_CHARGE_CHANNEL_NUM] = {0}; // 滤波后的总线电压
    static uint8_t invalidCount[DUAL_CHARGE_CHANNEL_NUM] = {0};    // 无效测量计数

    // 1. 数据合理性检查
    bool busVolValid = true;

    // 总线电压为0或异常高值(>1000V)视为无效
    if (busVol == 0 || busVol > 10000)
    {
        busVolValid = false;
        invalidCount[channel]++;
        trace(TR_CCU_DEBUG, "ch:%d 总线电压异常 [%d.%dV]，可能测量错误\n",
              channel, busVol / 10, busVol % 10);
    }

    // 总线电压与上次测量值相差过大(>50V)视为可疑
    if (lastBusVol[channel] > 0 &&
        abs(busVol - lastBusVol[channel]) > 500)
    {

        // 如果连续两次测量都出现大幅波动，可能是真实波动
        if (invalidCount[channel] < 2)
        {
            busVolValid = false;
            invalidCount[channel]++;
            trace(TR_CCU_DEBUG, "ch:%d 总线电压波动过大 [%d.%dV -> %d.%dV]\n",
                  channel, lastBusVol[channel] / 10, lastBusVol[channel] % 10,
                  busVol / 10, busVol % 10);
        }
    }
    else
    {
        // 正常波动，重置计数
        invalidCount[channel] = 0;
    }

    // 2. 电压滤波处理 - 使用滑动平均
    if (busVolValid)
    {
        if (filteredBusVol[channel] == 0)
        {
            // 首次有效测量，直接使用
            filteredBusVol[channel] = busVol;
        }
        else
        {
            // 更新滤波值 (75%旧值 + 25%新值)，实现平滑过渡
            filteredBusVol[channel] = (filteredBusVol[channel] * 3 + busVol) / 4;
        }
        lastBusVol[channel] = busVol; // 更新上次有效值
    }

    // 3. 遵循GB/T 29730的电压选择策略
    // 条件1: 有效且较高的总线电压优先用于并充
    if (busVolValid && filteredBusVol[channel] >= 2000)
    {
        // 总线电压高于200V且有效，优先使用
        safeVol = filteredBusVol[channel];
        trace(TR_CCU_DEBUG, "ch:%d 使用滤波总线电压 [%d.%dV]\n",
              channel, safeVol / 10, safeVol % 10);
    }
    // 条件2: 总线电压无效但有电池电压
    else if (!busVolValid && batVol >= 2000)
    {
        // 总线电压无效，使用电池电压作为参考
        safeVol = batVol;
        trace(TR_CCU_DEBUG, "ch:%d 总线电压无效，使用电池电压 [%d.%dV]\n",
              channel, safeVol / 10, safeVol % 10);
    }
    // 条件3: 防范总线电压与设定电压差异过大
    else if (busVolValid && abs(vol - filteredBusVol[channel]) >= 100)
    {
        // 设定电压与总线电压差距大于10V，取平滑过渡值
        // 以每次最多5V的速度向目标靠近
        int16_t volDiff = vol - filteredBusVol[channel];
        int16_t stepVol = (abs(volDiff) > 50) ? 50 * (volDiff > 0 ? 1 : -1) : volDiff;
        safeVol = filteredBusVol[channel] + stepVol;

        trace(TR_CCU_DEBUG, "ch:%d 电压差异大，平滑过渡 [%d.%dV -> %d.%dV], 步长:%d.%dV\n",
              channel, filteredBusVol[channel] / 10, filteredBusVol[channel] % 10,
              vol / 10, vol % 10, abs(stepVol) / 10, abs(stepVol) % 10);
    }

    // 4. 最终安全约束
    // 确保充电电压不超过电池额定电压的1.05倍(GB/T 29730安全要求)
    /*暂时不用*/
//    if (batVol > 0 && safeVol > batVol * 105 / 100)
//    {
//        safeVol = batVol * 105 / 100;
//        trace(TR_CCU_DEBUG, "ch:%d 充电电压超过电池电压+5%%，限制为 [%d.%dV]\n",
//              channel, safeVol / 10, safeVol % 10);
//    }

    return safeVol;
}
#else
/**
 * @brief 获取安全的充电电压参数
 * @param channel 通道号
 * @param vol 设定电压(0.1V)
 * @param batVol 电池电压(0.1V)
 * @param mode 电压选择模式: 0-总线优先, 1-设定值优先, 2-响应侧模式(电池电压参考)
 * @return 合适的充电电压值(0.1V)
 * @note 基于GB/T 29730标准实现平滑安全的电压选择，可选择优先模式
 */
uint16_t Get_SafeChargeVoltage(uint8_t channel, uint16_t vol, uint16_t batVol, uint8_t mode)
{
    uint16_t safeVol = vol;                                        // 默认使用设定电压
    uint16_t busVol = Get_K1K2Outside();                           // 总线实测电压
    static uint16_t lastBusVol[DUAL_CHARGE_CHANNEL_NUM] = {0};     // 上次总线电压
    static uint16_t filteredBusVol[DUAL_CHARGE_CHANNEL_NUM] = {0}; // 滤波后的总线电压
    static uint8_t invalidCount[DUAL_CHARGE_CHANNEL_NUM] = {0};    // 无效测量计数
    static uint16_t lastSetVol[DUAL_CHARGE_CHANNEL_NUM] = {0};     // 上次设定电压
    static uint16_t lastBatVol[DUAL_CHARGE_CHANNEL_NUM] = {0};     // 上次电池电压

    // 检查电池电压合理性
    //    bool batVolValid = (batVol >= 2000 && batVol <= 9000); // 200V-900V范围内视为有效
    bool batVolValid = (batVol >= 0 && batVol <= 15000); // 200V-900V范围内视为有效

    // 1. 数据合理性检查
    bool busVolValid = true;

#if 0 // 暂时去除电压滤波逻辑

    // 总线电压为0或异常高值(>1000V)视为无效
    if (busVol == 0 || busVol > 10000)
    {
        busVolValid = false;
        invalidCount[channel]++;
        DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 总线电压异常 [%d.%dV]，可能测量错误\n",
              channel, busVol / 10, busVol % 10);
    }

    // 总线电压与上次测量值相差过大(>50V)视为可疑
    if (lastBusVol[channel] > 0 &&
        abs(busVol - lastBusVol[channel]) > 500)
    {
        // 如果连续两次测量都出现大幅波动，可能是真实波动
        if (invalidCount[channel] < 2)
        {
            busVolValid = false;
            invalidCount[channel]++;
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 总线电压波动过大 [%d.%dV -> %d.%dV]\n",
                  channel, lastBusVol[channel] / 10, lastBusVol[channel] % 10,
                  busVol / 10, busVol % 10);
        }
    }
    else
    {
        // 正常波动，重置计数
        invalidCount[channel] = 0;
    }

    // 2. 电压滤波处理 - 使用滑动平均
    if (busVolValid)
    {
        if (filteredBusVol[channel] == 0)
        {
            // 首次有效测量，直接使用
            filteredBusVol[channel] = busVol;
        }
        else
        {
            // 更新滤波值 (75%旧值 + 25%新值)，实现平滑过渡
            filteredBusVol[channel] = (filteredBusVol[channel] * 3 + busVol) / 4;
        }
        lastBusVol[channel] = busVol; // 更新上次有效值
    }
#endif
    // 3. 根据模式选择电压策略
    if (mode == 2) // 响应侧模式：电池电压与设定电压结合评估
    {
        // 首先检查电池电压和设定电压的合理性
        //        if (batVolValid && vol >= 1000 && vol <= 10000)
        if (batVolValid && vol >= 0 && vol <= 15000)
        {
            // 计算设定电压与电池电压的差异百分比
            int16_t volDiff = vol - batVol;
            float diffPercent = (float)abs(volDiff) / batVol * 100;

            // 如果设定电压与电池电压差异不大(±5%以内)，优先使用设定电压
            if (diffPercent <= 5.0)
            {
                safeVol = vol;
                DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 响应侧模式：设定电压与电池电压差异小，使用设定电压 [%d.%dV]\n",
                              channel, safeVol / 10, safeVol % 10);
            }
            // 如果差异较大但在合理范围(±10%以内)，采用电池电压与设定电压的加权平均
            else if (diffPercent <= 10.0)
            {
                // 使用电池电压和设定电压的加权平均，趋向于电池电压
                safeVol = (batVol * 3 + vol) / 4;
                DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 响应侧模式：使用电池电压加权 [%d.%dV]\n",
                              channel, safeVol / 10, safeVol % 10);
            }
            // 差异过大，以电池电压为基础，但向设定电压方向偏移一定比例
            else
            {
                // 以电池电压为基础，朝设定电压方向调整最多10%
                int16_t maxAdjust = batVol / 10; // 最大调整量为电池电压的10%
                int16_t adjustment = (volDiff > 0) ? MIN(maxAdjust, volDiff) : MAX(-maxAdjust, volDiff);

                safeVol = batVol + adjustment;

                DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 响应侧模式：电压差异大，基于电池电压调整 [%d.%dV -> %d.%dV]\n",
                              channel, batVol / 10, batVol % 10, safeVol / 10, safeVol % 10);
            }

            // 脉冲需求快速响应：检测电池电压快速变化
            if (lastBatVol[channel] > 0 && abs(batVol - lastBatVol[channel]) > 100)
            {
                // 电池电压变化超过10V，可能是脉冲需求
                DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 检测到脉冲需求，电池电压变化明显 [%d.%dV -> %d.%dV]\n",
                              channel, lastBatVol[channel] / 10, lastBatVol[channel] % 10,
                              batVol / 10, batVol % 10);

                // 迅速跟随电池电压变化，比例更高
                safeVol = (batVol * 4 + vol) / 5; // 更偏向电池电压
            }
        }
        // 电池电压无效但设定电压有效
        else if (vol >= 1000 && vol <= 10000)
        {
            safeVol = vol;
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 响应侧模式：电池电压无效，使用设定电压 [%d.%dV]\n",
                          channel, safeVol / 10, safeVol % 10);
        }
        // 设定电压无效但电池电压有效
        else if (batVolValid)
        {
            safeVol = batVol;
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 响应侧模式：设定电压无效，使用电池电压 [%d.%dV]\n",
                          channel, safeVol / 10, safeVol % 10);
        }
        // 电池电压和设定电压都无效，尝试使用总线电压
        else if (busVolValid && filteredBusVol[channel] >= 2000)
        {
            safeVol = filteredBusVol[channel];
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 响应侧模式：电池和设定电压均无效，使用总线电压 [%d.%dV]\n",
                          channel, safeVol / 10, safeVol % 10);
        }
        // 所有电压源均无效
        else
        {
            safeVol = 4500; // 默认450V
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 响应侧模式：所有电压源均无效，使用默认值 [450.0V]\n", channel);
        }
    }
    else if (mode == 1) // 设定电压优先模式
    {
        // 检查设定电压有效性
        if (vol >= 1000 && vol <= 10000)
        {
            // 设定电压有效，作为基础值
            safeVol = vol;

            // 如果是首次设置或与上次设定值差异过大(>10V)，进行平滑过渡
            if (lastSetVol[channel] > 0 && abs(vol - lastSetVol[channel]) >= 100)
            {
                // 以每次最多5V的速度向目标靠近
                int16_t volDiff = vol - lastSetVol[channel];
                int16_t stepVol = (abs(volDiff) > 50) ? 50 * (volDiff > 0 ? 1 : -1) : volDiff;
                safeVol = lastSetVol[channel] + stepVol;

                DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 设定电压变化大，平滑过渡 [%d.%dV -> %d.%dV], 步长:%d.%dV\n",
                              channel, lastSetVol[channel] / 10, lastSetVol[channel] % 10,
                              vol / 10, vol % 10, abs(stepVol) / 10, abs(stepVol) % 10);
            }

            // 参考电池电压进行二次检验
            if (batVolValid && abs(safeVol - batVol) > batVol * 10 / 100)
            {
                DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 设定电压与电池电压差异超过10%% [设定:%d.%dV 电池:%d.%dV]\n",
                              channel, safeVol / 10, safeVol % 10, batVol / 10, batVol % 10);

                // 如果差异过大，还是采用平滑过渡策略，但更偏向电池电压
                safeVol = (safeVol * 2 + batVol) / 3;
            }

            // 总线实测电压作为参考，防止突变和较大偏差
            if (busVolValid && filteredBusVol[channel] >= 2000)
            {
                // 设定电压与总线电压差异过大(>100V)时进行额外安全检查
                if (abs(safeVol - filteredBusVol[channel]) > 1000)
                {
                    DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 设定电压与总线电压差异过大 [设定:%d.%dV 总线:%d.%dV]\n",
                                 channel, safeVol / 10, safeVol % 10,
                                 filteredBusVol[channel] / 10, filteredBusVol[channel] % 10);

                    // 差异超过100V时，记录警告但仍使用设定电压
                }
            }
        }
        // 设定电压无效，尝试其他电压来源
        else if (batVolValid)
        {
            // 优先使用电池电压
            safeVol = batVol;
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 设定电压无效，使用电池电压 [%d.%dV]\n",
                          channel, safeVol / 10, safeVol % 10);
        }
        else if (busVolValid && filteredBusVol[channel] >= 2000)
        {
            // 其次使用总线电压
            safeVol = filteredBusVol[channel];
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 设定电压和电池电压均无效，使用总线电压 [%d.%dV]\n",
                          channel, safeVol / 10, safeVol % 10);
        }
        else
        {
            // 所有电压来源均无效，使用默认安全电压
            safeVol = 4500; // 默认450V
            DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 所有电压来源均无效，使用默认安全电压 [450.0V]\n", channel);
        }
    }
    else // 总线电压优先模式（默认模式）
    {
        // 条件1: 有效且较高的总线电压优先用于并充
        if (busVolValid && filteredBusVol[channel] >= 2000)
        {
            // 总线电压高于200V且有效，优先使用
            safeVol = filteredBusVol[channel];
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 使用滤波总线电压 [%d.%dV]\n",
                          channel, safeVol / 10, safeVol % 10);
        }
        // 条件2: 总线电压无效但有电池电压
        else if (!busVolValid && batVolValid)
        {
            // 总线电压无效，使用电池电压作为参考
            safeVol = batVol;
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 总线电压无效，使用电池电压 [%d.%dV]\n",
                          channel, safeVol / 10, safeVol % 10);
        }
        // 条件3: 防范总线电压与设定电压差异过大
        else if (busVolValid && abs(vol - filteredBusVol[channel]) >= 100)
        {
            // 设定电压与总线电压差距大于10V，取平滑过渡值
            // 以每次最多5V的速度向目标靠近
            int16_t volDiff = vol - filteredBusVol[channel];
            int16_t stepVol = (abs(volDiff) > 50) ? 50 * (volDiff > 0 ? 1 : -1) : volDiff;
            safeVol = filteredBusVol[channel] + stepVol;

            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 电压差异大，平滑过渡 [%d.%dV -> %d.%dV], 步长:%d.%dV\n",
                          channel, filteredBusVol[channel] / 10, filteredBusVol[channel] % 10,
                          vol / 10, vol % 10, abs(stepVol) / 10, abs(stepVol) % 10);
        }
        else
        {
            // 所有条件都不满足，使用设定电压
            safeVol = vol;
            DUALGUN_DEBUG(TR_CCU_DEBUG, "ch:%d 使用设定电压 [%d.%dV]\n",
                          channel, safeVol / 10, safeVol % 10);
        }
    }

    // 更新状态记录
    lastSetVol[channel] = safeVol;
    if (batVolValid)
    {
        lastBatVol[channel] = batVol;
    }

    // 4. 最终安全约束
    // 确保充电电压不超过电池额定电压的1.05倍(GB/T 29730安全要求)
    //    if (batVolValid && safeVol > batVol * 105 / 100)
    //    {
    //        safeVol = batVol * 105 / 100;
    //        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 充电电压超过电池电压+5%%，限制为 [%d.%dV]",
    //              channel, safeVol / 10, safeVol % 10);
    //    }

    return safeVol;
}
#endif
/**
 * @brief 设置安全充电参数
 * @param channel 通道号
 * @param vol 设定电压(0.1V)
 * @param cur 设定电流(0.01A)
 * @param batVol 电池电压(0.1V)
 * @note 进行安全性检查并设置到控制结构
 */
void Set_SafeDualChargeParams(uint8_t channel, uint16_t vol, uint16_t cur, uint16_t batVol)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    //    // 参数有效性验证
    //    if (vol < 1000 || vol > 10000)
    //    {
    //        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 电压参数无效 [%d.%dV]，使用安全默认值\n",
    //              channel, vol / 10, vol % 10);
    //        vol = (vol < 1000) ? 1000 : (vol > 10000 ? 4500 : vol);
    //    }
    //
    //    if (cur > 5000)
    //    { // 最大500A
    //        DUALGUN_WARN(TR_CCU_DEBUG, "ch:%d 电流参数无效 [%d.%dA]，使用安全默认值\n",
    //              channel, cur / 100, cur % 100);
    //        cur = (cur == 0) ? 1000 : (cur > 500000 ? 10000 : cur); // 默认10A，最大100A
    //    }

    // 获取安全电压
    //    uint16_t safeVol = Get_SafeChargeVoltage(channel, vol, batVol, 2);
    uint16_t safeVol = vol;

    // 更新控制结构参数
    if (NULL != ctrl)
    {
        Set_DUALCHARGE_CTRL_voltage(ctrl, safeVol);
        Set_DUALCHARGE_CTRL_current(ctrl, cur);
        Set_DUALCHARGE_CTRL_batVoltage(ctrl, batVol);

        DUALGUN_POWER_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, safeVol, cur,
                          "ch:%d 设置并充安全参数 电压:%d.%dV 电流:%d.%dA 电池电压:%d.%dV\n",
                          channel, safeVol / 10, safeVol % 10, cur / 10, cur % 10, batVol / 10, batVol % 10);
    }
    else
    {
        DUALGUN_ERROR(TR_CCU_DEBUG, "Set_SafeDualChargeParams...error! 无效通道:%d\n", channel);
    }
}

uint8 Get_DualchargeWorkState(int channel)
{
    DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);

    if (NULL != ctrl)
    {
        return (ctrl->mode);
    }
    printf("Get_DualchargeWorkState...error!\n");
    return 0;
}

/**
 * 初始化DUALCHARGE_CTRL结构体
 * Initialize DUALCHARGE_CTRL Structure
 */
void DualCharge_Ctrl_Init(void)
{
    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        DUALCHARGE_CTRL *ctrl = Get_DualchargeCtrl(channel);
        if (!ctrl)
        {
            continue;
        }

        /* 清空整个结构体为0 */
        memset(ctrl, 0, sizeof(DUALCHARGE_CTRL));

        /* 只为非零初值的枚举类型和特殊值字段赋值 */
        ctrl->tick = tickGet();
        ctrl->lasttick = ctrl->tick;

        /* 基础状态字段初始化 */
        ctrl->mode = eDualChargeStatus_Independent;        // 默认为独立模式
        ctrl->offsidemode = eDualChargeStatus_Independent; // 对端默认也为独立模式
        ctrl->spn = SPNID_CHM;                             // 初始SPN阶段
        ctrl->offsidespn = SPNID_CHM;
        /* 操作控制字段初始化 */
        ctrl->operate = eDualChargeOpr_Null;          // 无操作
        ctrl->operateDirection = eDualChargeDir_NULL; // 无方向

        /* 工作状态字段初始化 */
        ctrl->work_status = WORK_STANDBY; // 待机状态
        ctrl->status = STATUS_STANDBY;
        ctrl->offsidestatus = STATUS_STANDBY;
        ctrl->reconnect_status = RECONNECT_IDLE;
        ctrl->result_status = RESULT_IDLE;
        ctrl->ctrlCmd = CMD_FAST_IDLE;

        /* 紧急状态字段初始化 - 已经是0/NONE，无需赋值 */
        ctrl->emergency_type = DUAL_EMERGENCY_NONE;    // 初始无紧急情况
        ctrl->last_transition_error = DUAL_ERROR_NONE; // 初始无错误

        /* 复合结构体字段初始化 */
        // ctrl->pending_mode_change.active = 0;            // 初始无待处理模式，已经是0
        ctrl->pending_mode_change.target_mode = eDualChargeStatus_Independent; // 默认目标为独立模式
        // ctrl->pending_mode_change.timestamp = 0;         // 已经是0
    }

    /* 日志初始化消息 */
    log_dual_charge_event(0, DUAL_EVENT_MODE_CHANGE_ROLLBACK, 0, 0, 0);
}

/**
 * 字段偏移量验证
 */
void DualCharge_Ctrl_Alignment_Check(void)
{
    /* 验证4字节字段的偏移量 */
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, tick, 0);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, lasttick, 4);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, transition_timeout_count, 8);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, sync_failure_count, 12);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, state_inconsistency_count, 16);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, rejected_transitions, 20);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, command_send_count, 24);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, command_resend_count, 28);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, transition_success_time, 32);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, initiate_timestamp, 36);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, offside_service_stable_time, 40);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, emergency_start_time, 44);

    /* 验证2字节字段的偏移量 */
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, actual_voltage, 48);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, actual_current, 50);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, battery_voltage, 52);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, voltage, 54);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, current, 56);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, batVoltage, 58);

    /* 验证1字节字段的偏移量 */
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, bussy, 60);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, mode, 61);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, spn, 62);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, offsidespn, 63);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, offsidemode, 64);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, service, 65);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, OffsideService, 66);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, operate, 67);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, operateDirection, 68);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, chargeImdReadyFlag, 69);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, chargeImdFinishFlag, 70);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, startChargeFlag, 71);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, startChargeFinishFlag, 72);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, stopChargeFlag, 73);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, stopChargeFinishFlag, 74);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, revctrltimeout, 75);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, revctrlacktimeout, 76);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, success_flag, 77);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, connected, 78);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, offsideconnected, 79);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, work_status, 80);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, status, 81);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, offsidestatus, 82);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, reconnect_status, 83);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, result_status, 84);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, ctrlCmd, 85);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, mode_transition_in_progress, 86);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, flag_cleaning_in_progress, 87);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, conversion_completed, 88);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, emergency_active, 89);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, last_transition_error, 90);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, emergency_type, 91);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, emergency_retry_count, 92);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, service_stable_notified, 93);

    /* 验证复合结构体字段的偏移量 */
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, pending_mode_change, 94);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, pending_mode_change.active, 94);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, pending_mode_change.target_mode, 95);
    STATIC_ASSERT_FIELD_OFFSET(DUALCHARGE_CTRL, pending_mode_change.timestamp, 96);

    /* 验证结构体总大小 */
    _Static_assert(sizeof(DUALCHARGE_CTRL) == 100, "结构体大小不正确 - DUALCHARGE_CTRL size mismatch");
}
/**
 ******************************************************************************
 * @brief       获取CCU地址
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      ccu通信地址
 * @details     BASRADDR+MODE1
 * @note
 ******************************************************************************
 */
uint8 Get_CCU(void)
{
    extern uint8 Get_CcuCfgAddr(void);
    return DEV_CCU_ADDR_A + Get_CcuCfgAddr();
}
/**
 ******************************************************************************
 * @brief       CCU模块初始化
 * @details     \n
 * 1. 执行内存布局验证\n
 * 2. 初始化收发模块
 ******************************************************************************
 */
void DualCcuInit(void)
{
    DualCharge_Ctrl_Alignment_Check(); // 内存布局验证
    DualCharge_Ctrl_Init();            // 结构体初始化
    Ccu_RecvInit();                    // 接收模块初始化
    Ccu_SendInit();                    // 发送模块初始化
}

void Dualcharge_Init(void)
{
    DualCcuInit();
    DualCcuSendForever(); // 并柜服务状态周期发送;
    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        Set_DualchargeMode(channel, eDualChargeStatus_Independent);
    }
}

void Dualcharge_Handle(void)
{
    for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
    {
        // Check_ExtCtypeService(channel);
        Enhanced_Check_DualChargeService(channel); // 检测服务状态
        Check_DualChargeDemand(channel);           // 检测并柜需求
        for (int i = 0; i < ARRAY_SIZE(Dualcharge_StatusMap); i++)
        {
            if (Dualcharge_StatusMap[i].status == Get_DualchargeWorkState(channel))
            {
                Dualcharge_StatusMap[i].func(channel);
            }
        }
    }
}

/**
 * @brief 处理状态转换和保护
 */
void Process_State_Transitions(void)
{
    // 综合状态转换保护 - 解决非原子性和不同步问题
    Protect_DualCharge_StateTransition();
}
/**
 * @brief 处理基本并充逻辑
 */
void Process_Base_DualCharge_Logic(void)
{
    // 原有处理流程
    Dualcharge_Handle();
    dual_transceiver_handle();
}

void DualCharge_Task(void)
{
    taskDelay(sysClkRateGet());
    Dualcharge_Init();
    Init_DualChargeStateGuard();
    dmnTaskRegister(); // 任务注册
    FOREVER
    {
        taskDelay(10);
        dmnTaskSigned(); // 任务签到
        Process_Base_DualCharge_Logic();

        // 新增：1秒周期的状态一致性检查
        // 执行一致性检查
        //        Perform_Consistency_Checks();

        // 综合状态转换保护 - 解决非原子性和不同步问题
        // 处理状态转换
        //        Process_State_Transitions();

        // 新增：更新对端服务稳定时间
        // 更新服务状态
        //        Update_Service_Status();
        //
        //        // 添加：执行健康检查
        //        for (int channel = 0; channel < DUAL_CHARGE_CHANNEL_NUM; channel++)
        //        {
        //            DualCharge_HealthCheck(channel);
        //        }
    }
    dmnTaskUnRegister(); // 任务注销
    taskDelete(NULL);
}

/**
 ******************************************************************************
 * @brief      CCU发送测试命令
 * @param[in]  cmdtp - 命令表指针
 * @param[in]  argc  - 参数个数
 * @param[in]  argv  - 参数值数组
 * @retval     固定返回0
 * @details    通过Shell命令手动激活通道1的控制命令发送，设置发送周期为5秒。
 * @note       仅用于开发调试，正式版本应移除
 ******************************************************************************
 */
void Test_Dualccu(cmd_tbl_t *cmdtp, uint32 argc, const uint8 *argv[])
{
    uint32 mode = 0;
    uint32 vol = 0;
    uint32 cur = 0;
    if (argc > 1)
    {
        sscanf(argv[1], "%d", &mode);
    }
    else
    {
        DUALGUN_WARN(0, "参数个数不够\n");
        return;
    }
    if (TRUE == IsDeviceFaulted())
    {
        DUALGUN_WARN(0, "设备故障了\n");
        return;
    }
    if (CCU_WORK_STATE_FREE != Get_WorkState())
    {
        DUALGUN_WARN(0, "设备状态不对\n");
        return;
    }
    if (mode == 0)
    {
        Set_ChargeMode(mode);
        Set_ChargeActFlag(eActFlag_On);
        Set_DualChargeEnableStatus(TRUE);
        DUALGUN_INFO(0, "启动方式:%s", "自动\n");
    }
    else if (mode == 2)
    {
        if (argc == 3)
        {
            sscanf(argv[2], "%d", &vol);
            if ((vol > 128) || ((vol < 112) && (vol > 68)) || ((vol < 52) && (vol > 48)) || (vol < 32))
            {
                Set_Manual_Cc1Vol(0);
            }
            else
            {
                Set_Manual_Cc1Vol(vol);
            }
        }
        return;
    }
    else
    {
        DUALGUN_WARN(0, "启动方式错误\n");
        return;
    }
}
/* 注册Shell命令 */
SHELL_CMD(dualccu,                          /* 命令名 */
          CFG_MAXARGS,                      /* 最大参数数 */
          (SHELL_CMD_FUNCPTR)Test_Dualccu,  /* 命令处理函数 */
          "ccu_send \r\t\t\t\t CCU并机\n"); /* 帮助信息 */

static void Dualccustop(void)
{
    Set_ChargeActFlag(eActFlag_Off);
    Set_ManualCur(0);
    Set_ManualVol(0);
    Set_Manual_Cc1Vol(0);
    Set_DualChargeEnableStatus(FALSE);
}

SHELL_CMD(
    dualstop, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)Dualccustop,
    "stop \r\t\t\t\t 停止充电\n");
// 添加全局变量，用于手动设置外侧电压值
static uint32 s_ManualOutsideVol = 0;      // 手动设置的外侧电压值，扩大10倍（同Get_K1K2OutsideVol返回值单位）
static bool s_UseManualOutsideVol = FALSE; // 是否使用手动设置的外侧电压

/**
 * @brief 获取是否使用手动设置的外侧电压
 * @return 是否使用手动设置的外侧电压
 */
bool Is_UseManualOutsideVol(void)
{
    return s_UseManualOutsideVol;
}

/**
 * @brief 获取手动设置的外侧电压值
 * @return 手动设置的外侧电压值（扩大10倍）
 */
uint32 Get_ManualOutsideVol(void)
{
    return s_ManualOutsideVol;
}

/**
 * @brief 手动设置外侧电压调试函数
 * @param cmdtp 命令表指针
 * @param argc 参数个数
 * @param argv 参数数组
 * @return 执行结果
 */
void Test_OutsideVol(cmd_tbl_t *cmdtp, uint32 argc, const uint8 *argv[])
{
    uint32 vol = 0;

    if (argc < 2)
    {
        // 无参数，显示当前状态
        if (s_UseManualOutsideVol)
        {
            DUALGUN_INFO(0, "当前使用手动设置的外侧电压: %d.%d V\n",
                         s_ManualOutsideVol / 10, s_ManualOutsideVol % 10);
        }
        else
        {
            DUALGUN_INFO(0, "当前使用实际采样的外侧电压\n");
            DUALGUN_INFO(0, "实际外侧电压: %d.%d V\n",
                         Get_K1K2OutsideVol() / 10, Get_K1K2OutsideVol() % 10);
        }
        DUALGUN_INFO(0, "使用方法:\n");
        DUALGUN_INFO(0, "  outside_vol off      - 关闭手动设置，使用实际采样值\n");
        DUALGUN_INFO(0, "  outside_vol <数值>   - 设置外侧电压值，单位: V\n");
        DUALGUN_INFO(0, "例如: outside_vol 500  - 设置外侧电压为500V\n");
        return;
    }

    // 检查是否是关闭命令
    if (0 == strcmp((const char *)argv[1], "off"))
    {
        s_UseManualOutsideVol = FALSE;
        printf("已关闭手动设置，使用实际采样的外侧电压\n");
        return;
    }

    // 解析电压值参数，单位V
    vol = strtoul((const char *)argv[1], NULL, 10) * 10; // 转换为扩大10倍的值

    // 如果有小数部分
    if (argc >= 3)
    {
        uint32 decimal = strtoul((const char *)argv[2], NULL, 10);
        if (decimal < 10)
        {
            vol += decimal;
        }
    }

    // 设置手动电压值
    s_ManualOutsideVol = vol;
    s_UseManualOutsideVol = TRUE;

    printf("已设置外侧电压为: %d.%d V\n", vol / 10, vol % 10);
}

/* 注册shell命令 */
SHELL_CMD(outside_vol, CFG_MAXARGS, (SHELL_CMD_FUNCPTR)Test_OutsideVol,
          "outside_vol\r\t\t\t\t 手动设置外侧电压\n");
