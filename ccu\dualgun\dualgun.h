#ifndef DUAL_CHARGE_CTRL_H
#define DUAL_CHARGE_CTRL_H

#include "types.h"
#include "stdio.h"
#include <stddef.h>
#include <assert.h>
#include "dualgunlog.h"
/*-----------------------------------------------------------------------------
 *  1. 基础配置定义 - Basic Configuration Definitions
 *-----------------------------------------------------------------------------*/
/**
 * 设备地址定义 - Device Address Definitions
 * 定义了不同设备单元和物理地址，用于通信寻址
 */
#define DEV_CCU_A (0x10)      ///< 主控单元A地址 - Control Unit A Address
#define DEV_CCU_B (0x11)      ///< 主控单元B地址 - Control Unit B Address
#define DEV_CCU_ADDR_A (0x10) ///< CCU_A物理地址 - Physical Address for CCU_A
#define DEV_CCU_ADDR_B (0x11) ///< CCU_B物理地址 - Physical Address for CCU_B

/**
 * 协议标识定义 - Protocol Identifier Definitions
 * 定义了不同通信帧的标识符，用于区分消息类型
 */
#define ID_DUALCHARGE_CTRL (0x05) ///< 并充控制帧标识 - Dual Charge Control Frame ID
#define ID_DUALCHARGE_TELE (0x06) ///< 并充遥信遥测帧标识 - Dual Charge Telemetry Frame ID
#define CHANNEL_CCU (0x00)        ///< CCU通道号 - CCU Channel Number

/**
 * 通道配置 - Channel Configuration
 * 定义了通道相关的常量，支持单通道和多通道配置
 */
#define DUAL_CHARGE_CHANNEL_01 (0x00)  ///< 通道1标识 - Channel 1 Identifier
#define DUAL_CHARGE_CHANNEL_NUM (0x01) ///< 双通道配置 - Number of Dual Charge Channels(同dualgunlog里面的DUAL_CHARGE_CHANNEL_LOG_NUM)

/**
 * 功能开关 - Feature Toggles
 * 控制特定功能的启用与禁用
 */
#define ENHANCED_STATE_MANAGEMENT 1 ///< 增强状态管理 - Enhanced State Management Toggle

#define MAX_TRANSITION_RETRY 3                   // 最大转换重试次数
#define SAFE_MODE_CHANGE_DELAY 50                // 安全模式变更延迟(ms)
#define MIN_SERVICE_STABLE_TIME 1000             // 服务状态最小稳定时间(ms)
#define MAX_INCONSISTENCY_TOLERANCE 5            // 最大状态不一致容忍次数
#define EMERGENCY_COMMAND_RETRY 3                // 紧急命令重试次数
#define EMERGENCY_TIMEOUT (10 * sysClkRateGet()) // 紧急情况超时时间

/*-----------------------------------------------------------------------------
 *  2. 核心枚举类型 - Core Enumeration Types
 *-----------------------------------------------------------------------------*/
/**
 * 控制返回类型 - Control Return Types
 * 定义了各种数据类型的标识，用于类型安全的数据传输
 */
typedef enum __attribute__((packed, aligned(4)))
{
    DUALCHARGE_RET_UNKNOWN = 0,    ///< 未知类型 - Unknown Type
    DUALCHARGE_RET_UINT8,          ///< 8位无符号整数 - 8-bit Unsigned Integer
    DUALCHARGE_RET_UINT16,         ///< 16位无符号整数 - 16-bit Unsigned Integer
    DUALCHARGE_RET_UINT32,         ///< 32位无符号整数 - 32-bit Unsigned Integer
    DUALCHARGE_RET_FLOAT,          ///< 32位浮点数 - 32-bit Floating Point
    DUALCHARGE_RET_BOOL,           ///< 布尔值 - Boolean Value
    DUALCHARGE_RET_CUSTOM,         ///< 自定义类型 - Custom Type
    DUALCHARGE_RET_ERROR_TYPE,     ///< 错误类型 (DUAL_ERROR_TYPE) - Error Type
    DUALCHARGE_RET_EMERGENCY_TYPE, ///< 紧急类型 (DUAL_EMERGENCY_TYPE) - Emergency Type
    DUALCHARGE_RET_PENDING_MODE,   ///< 待处理模式 (DUAL_PENDING_MODE_CHANGE) - Pending Mode
    DUALCHARGE_RET_TYPE_COUNT      ///< 类型总数 - Type Count
} DUALCHARGE_RET_TYPE;

/**
 * 操作指令集 - Operation Command Set
 * 定义了系统支持的各种操作命令
 */
typedef enum __attribute__((packed, aligned(1)))
{
    CMD_FAST_IDLE = 0x00,     ///< 快速空闲 - Fast Idle
    CMD_FAST_START = 0x01,    ///< 快速启动（绝缘检测阶段）- Fast Start (Isolation Detection Phase)
    CMD_STOP_CHARGE = 0x02,   ///< 停止充电（保留模块）- Stop Charging (Reserve Module)
    CMD_SOFT_START = 0x03,    ///< 软启动（预充电阶段）- Soft Start (Pre-charge Phase)
    CMD_SHOW_ADDR = 0x04,     ///< 显示地址信息 - Show Address Information
    CMD_MODIFY_PARAM = 0x05,  ///< 参数修改指令 - Parameter Modification Command
    CMD_RELEASE_MODULE = 0x06 ///< 模块释放指令 - Module Release Command
} DUALCHARGE_OPERATE_CMD;

/**
 * 桩状态机 - Charging Station State Machine
 * 定义了充电桩的各种工作状态
 */
typedef enum __attribute__((packed, aligned(1)))
{
    STATUS_STANDBY = 0x00,          ///< 待机状态 - Standby State
    STATUS_PLUGGED = 0x01,          ///< 充电枪插入 - Charger Plugged In
    STATUS_INSULATING = 0x02,       ///< 绝缘检测中 - Insulation Check In Progress
    STATUS_INSULATION_DONE = 0x03,  ///< 绝缘完成 - Insulation Check Completed
    STATUS_PRE_CHARGING = 0x04,     ///< 预充电阶段 - Pre-charging Phase
    STATUS_PRE_CHARGE_DONE = 0x05,  ///< 预充电完成 - Pre-charging Completed
    STATUS_CHARGING = 0x06,         ///< 充电进行中 - Charging In Progress
    STATUS_STOPPING = 0x07,         ///< 停止过程中 - Stopping In Progress
    STATUS_CHARGE_COMPLETED = 0x08, ///< 充电完成 - Charging Completed
    STATUS_FAULT = 0x09,            ///< 故障状态 - Fault State
    STATUS_DISABLED = 0x0A,         ///< 禁用状态 - Disabled State
    STATUS_PAUSED = 0x0B            ///< 暂停充电状态 - Paused State
} DUALCHARGE_STATUS;

/**
 * 主从模式 - Master-Slave Mode
 * 定义了设备在并充系统中的角色状态
 */
typedef enum __attribute__((packed, aligned(1)))
{
    eDualChargeStatus_Independent = 0x00, ///< 独立运行状态（非并充模式）- Independent Operation Mode
    eDualChargeStatus_Insulation = 0x01,  ///< 绝缘检测状态（并充准备阶段）- Insulation Check State (Dual Charging Preparation)
    eDualChargeStatus_Master = 0x02,      ///< 主机控制状态 - Master Control State
    eDualChargeStatus_Slave = 0x03,       ///< 从机响应状态 - Slave Response State
} E_DUALCHARGE_STATUS;

/**
 * 连接状态 - Connection Status
 * 定义了设备连接状态的枚举
 */
typedef enum __attribute__((packed, aligned(1)))
{
    CONN_DISCONNECTED = 0, ///< 未连接 - Disconnected
    CONN_CONNECTED = 1     ///< 已连接 - Connected
} DUALCHARGE_CONN_STATUS;

/**
 * SPN阶段 - Suspect Parameter Number Phase
 * 定义了SPN的不同阶段
 */
typedef enum __attribute__((packed, aligned(1)))
{
    SPN_PHASE_INIT = 0,     ///< 初始化阶段 - Initialization Phase
    SPN_PHASE_IMD = 1,      ///< 绝缘阶段 - Insulation Measurement Phase
    SPN_PHASE_PRECHARGE = 2 ///< 预充电阶段 - Pre-charge Phase
} DUALCHARGE_SPN_PHASE;

/**
 * 阶段状态 - Phase Status
 * 定义了各执行阶段的状态枚举
 */
typedef enum __attribute__((packed, aligned(1)))
{
    PHASE_PENDING = 0,   ///< 阶段待启动 - Phase Pending
    PHASE_ACTIVE = 1,    ///< 阶段进行中 - Phase Active
    PHASE_COMPLETED = 2, ///< 阶段完成 - Phase Completed
    PHASE_ABORTED = 3    ///< 阶段异常终止 - Phase Aborted
} DUALCHARGE_PHASE_STATUS;

/**
 * 双充电系统工作状态枚举 - Dual Charging System Working Status
 * 定义了整个系统的工作状态
 */
typedef enum __attribute__((packed, aligned(1)))
{
    WORK_STANDBY = 0x00, ///< 待机状态（低功耗模式）- Standby Mode (Low Power)
    WORK_ACTIVE = 0x01,  ///< 工作状态（含自检阶段）- Active Mode (Including Self-Check)
    WORK_FAULT = 0x02    ///< 故障状态（需人工干预）- Fault State (Manual Intervention Required)
} DUALCHARGE_WORK_STATUS;

/**
 * 重连状态 - Reconnection Status
 * 定义了设备重连过程的状态
 */
typedef enum __attribute__((packed, aligned(1)))
{
    RECONNECT_IDLE = 0,       ///< 未重连 - Idle (No Reconnection)
    RECONNECT_ATTEMPTING = 1, ///< 重连尝试中 - Reconnection Attempt In Progress
    RECONNECT_SUCCESS = 2,    ///< 重连成功 - Reconnection Successful
    RECONNECT_FAILED = 3      ///< 重连失败 - Reconnection Failed
} DUALCHARGE_RECONNECT_STATUS;

/**
 * 操作结果状态 - Operation Result Status
 * 定义了操作执行后的结果状态
 */
typedef enum __attribute__((packed, aligned(1)))
{
    RESULT_IDLE = 0x00,    ///< Bit2-3: 00 - 无状态 - No Status
    RESULT_FAIL = 0x01,    ///< Bit2-3: 01 - 失败 - Failed
    RESULT_SUCCESS = 0x02, ///< Bit2-3: 02 - 成功 - Success
} DUALCHARGE_RESULT_STATUS;

/**
 * @brief 并充状态机事件类型 - Dual Charging State Machine Event Types
 * 定义了状态机可能触发的各种事件
 */
typedef enum __attribute__((packed, aligned(1)))
{
    DUAL_EVENT_MODE_CHANGE_REJECTED = 0,          ///< 模式变更被拒绝 - Mode Change Rejected
    DUAL_EVENT_MODE_CHANGE_ROLLBACK,              ///< 模式变更回滚 - Mode Change Rollback
    DUAL_EVENT_MODE_CHANGED_SUCCESS,              ///< 模式变更成功 - Mode Change Success
    DUAL_EVENT_COMMAND_TIMEOUT,                   ///< 命令超时 - Command Timeout
    DUAL_EVENT_STATE_INCONSISTENCY,               ///< 状态不一致 - State Inconsistency
    DUAL_EVENT_EMERGENCY_HANDLED,                 ///< 紧急情况已处理 - Emergency Handled
    DUAL_EVENT_EMERGENCY_RECOVERED,               ///< 紧急情况已恢复 - Emergency Recovered
    DUAL_EVENT_INDEPENDENT_TO_INSULATION_SUCCESS, ///< 独立到绝缘转换成功 - Independent to Insulation Transition Success
    DUAL_EVENT_COUNT                              ///< 事件类型总数 - Event Type Count
} DUAL_EVENT_TYPE;

/**
 * @brief 并充紧急情况类型 - Dual Charging Emergency Types
 * 定义了系统可能遇到的紧急情况类型
 */
typedef enum __attribute__((packed, aligned(1)))
{
    DUAL_EMERGENCY_NONE = 0,              ///< 无紧急情况 - No Emergency
    DUAL_EMERGENCY_DEVICE_FAULT,          ///< 设备故障 - Device Fault
    DUAL_EMERGENCY_COMMUNICATION_FAILURE, ///< 通信故障 - Communication Failure
    DUAL_EMERGENCY_STATE_INCONSISTENCY,   ///< 状态不一致 - State Inconsistency
    DUAL_EMERGENCY_COUNT                  ///< 紧急情况类型总数 - Emergency Type Count
} DUAL_EMERGENCY_TYPE;

/**
 * @brief 新增错误类型 - New Error Types
 * 定义了系统可能出现的错误类型
 */
typedef enum __attribute__((packed, aligned(1)))
{
    DUAL_ERROR_NONE = 0,          ///< 无错误 - No Error
    DUAL_ERROR_DEVICE_FAULT,      ///< 设备故障 - Device Fault
    DUAL_ERROR_NOT_CONNECTED,     ///< 未连接 - Not Connected
    DUAL_ERROR_OFFSIDE_NOT_READY, ///< 对端未准备好 - Remote End Not Ready
    DUAL_ERROR_COUNT              ///< 错误类型总数 - Error Type Count
} DUAL_ERROR_TYPE;

/**
 * @brief 延迟模式变更请求结构 - Delayed Mode Change Request Structure
 * 存储延迟处理的模式变更请求信息
 */
typedef struct
{
    uint8_t active;      ///< 是否激活 - Whether Active (0=inactive, 1=active)
    uint8_t target_mode; ///< 目标模式 - Target Mode
    uint32_t timestamp;  ///< 请求时间戳 - Request Timestamp
} DUAL_PENDING_MODE_CHANGE;

/*
 * 定义服务状态枚举，明确表示服务是否可用
 * Service Status Enumeration - Indicates service availability
 */
typedef enum __attribute__((packed, aligned(1)))
{
    eDualChargeServ_Disallowed = 0x00, ///< 双充不可服务状态 - Dual Charging Service Disallowed
    eDualChargeServ_Allowed = 0x01,    ///< 双充可服务状态 - Dual Charging Service Allowed
} EDUALCHARGE_SERVICE;

/**
 * 设置结果错误码 - Setting Result Error Codes
 * 定义了设置操作可能返回的结果状态
 */
typedef enum __attribute__((packed, aligned(1)))
{
    DUALCHARGE_SET_OK = 0,        ///< 设置成功 - Setting Successful
    DUALCHARGE_SET_NULL_PTR,      ///< 空指针错误 - Null Pointer Error
    DUALCHARGE_SET_INVALID_FIELD, ///< 无效字段 - Invalid Field
    DUALCHARGE_SET_TYPE_MISMATCH  ///< 类型不匹配 - Type Mismatch
} DUALCHARGE_SET_RESULT;

/**
 * SPN（可疑参数编号）状态枚举
 * Suspect Parameter Number Status Enumeration
 * 依据标准：GB/T 27930-2015 电动汽车充电通信协议
 * Based on Standard: GB/T 27930-2015 Electric Vehicle Charging Communication Protocol
 */
typedef enum __attribute__((packed, aligned(1)))
{
    SPNID_CHM = 0,    /**< CHM:    充电握手阶段 - Charging Handshake Phase */
    SPNID_CRM_00 = 1, /**< CRM_00: 绝缘开始阶段 - Insulation Start Phase */
    SPNID_CRM_AA = 2, /**< CRM_AA: 绝缘结束阶段 - Insulation End Phase */
    SPNID_CRO_00 = 3, /**< BRO_AA: 预充开始阶段 - Pre-charging Start Phase */
    SPNID_CRO_AA = 4, /**< CRO_AA: 预充结束阶段 - Pre-charging End Phase */
    SPNID_BCCL = 5,   /**< BCL:    充电开始阶段 - Charging Start Phase */
    SPNID_CST = 6,    /**< CST:    充电终止状态 - Charging Termination Status */
    SPNID_BST = 7     /**< BST:    充电终止状态 - Charging Termination Status */
} SPNID;

/**
 * 双充电操作类型 - Dual Charging Operation Types
 * 定义了可执行的操作类型
 */
typedef enum __attribute__((packed, aligned(1)))
{
    eDualChargeOpr_Null,      ///< 无操作 - No Operation
    eDualChargeOpr_ImdStart,  ///< 启动绝缘 - Start Insulation
    eDualChargeOpr_SoftStart, ///< 启动软起 - Start Soft Start
    eDualChargeOpr_Stop,      ///< 停止双充 - Stop Dual Charging
    eDualChargeOpr_Sync,      ///< 参数同步 - Parameter Synchronization
    eDualChargeOpr_FaultReset ///< 故障复位 - Fault Reset
} E_DUALCHARGE_OPR;

/**
 * 双充电方向类型 - Dual Charging Direction Types
 * 定义了操作的发起方和响应方
 */
typedef enum __attribute__((packed, aligned(1)))
{
    eDualChargeDir_NULL,      ///< 无 - None
    eDualChargeDir_Initiator, ///< 发起方 - Initiator
    eDualChargeDir_Responder, ///< 响应方 - Responder
} E_DUALCHARGE_DIRECTION;

/**
 * 主控制结构体枚举定义 - 枚举值作为字段索引
 * Main Control Structure Enum Definition - Enum Values as Field Index
 */
typedef enum __attribute__((packed, aligned(4)))
{
    /* 4字节字段 */
    DUALCHARGE_CTRL_tick = 0,                    ///< 当前时间戳 - Current Timestamp
    DUALCHARGE_CTRL_lasttick,                    ///< 上次时间戳 - Last Timestamp
    DUALCHARGE_CTRL_transition_timeout_count,    ///< 转换超时计数 - Transition Timeout Counter
    DUALCHARGE_CTRL_sync_failure_count,          ///< 同步失败计数 - Sync Failure Counter
    DUALCHARGE_CTRL_state_inconsistency_count,   ///< 状态不一致计数 - State Inconsistency Counter
    DUALCHARGE_CTRL_rejected_transitions,        ///< 被拒绝的转换请求数 - Rejected Transition Requests Count
    DUALCHARGE_CTRL_command_send_count,          ///< 命令发送计数 - Command Send Counter
    DUALCHARGE_CTRL_command_resend_count,        ///< 命令重发计数 - Command Resend Counter
    DUALCHARGE_CTRL_transition_success_time,     ///< 转换成功时间 - Transition Success Time
    DUALCHARGE_CTRL_initiate_timestamp,          ///< 发起时间戳 - Initiation Timestamp
    DUALCHARGE_CTRL_offside_service_stable_time, ///< 对端服务稳定时间 - Remote Service Stable Time
    DUALCHARGE_CTRL_emergency_start_time,        ///< 紧急情况开始时间 - Emergency Start Time

    /* 2字节字段 */
    DUALCHARGE_CTRL_actual_voltage,  ///< 实际输出电压 - Actual Output Voltage
    DUALCHARGE_CTRL_actual_current,  ///< 实际输出电流 - Actual Output Current
    DUALCHARGE_CTRL_battery_voltage, ///< 电池电压 - Battery Voltage
    DUALCHARGE_CTRL_voltage,         ///< 控制参数-电压 - Control Parameter-Voltage
    DUALCHARGE_CTRL_current,         ///< 控制参数-电流 - Control Parameter-Current
    DUALCHARGE_CTRL_batVoltage,      ///< 控制参数-电池电压 - Control Parameter-Battery Voltage

    /* 1字节字段 */
    DUALCHARGE_CTRL_bussy,                       ///< 并充忙状态 - Dual Charging Busy Status
    DUALCHARGE_CTRL_mode,                        ///< 主从模式 - Master/Slave Mode
    DUALCHARGE_CTRL_spn,                         ///< SPN阶段标识 - SPN Phase Identifier
    DUALCHARGE_CTRL_offsidespn,                  ///< 对侧SPN阶段标识 - Remote SPN Phase Identifier
    DUALCHARGE_CTRL_offsidemode,                 ///< 对侧主从模式 - Remote Master/Slave Mode
    DUALCHARGE_CTRL_service,                     ///< 本侧服务状态 - Local Service Status
    DUALCHARGE_CTRL_OffsideService,              ///< 对侧服务状态 - Remote Service Status
    DUALCHARGE_CTRL_operate,                     ///< 并柜操作 - Dual Cabinet Operation
    DUALCHARGE_CTRL_operateDirection,            ///< 操作方向 - Operation Direction
    DUALCHARGE_CTRL_chargeImdReadyFlag,          ///< 充电准备标志 - Charging Preparation Flag
    DUALCHARGE_CTRL_chargeImdFinishFlag,         ///< 充电准备完成标志 - Charging Preparation Complete Flag
    DUALCHARGE_CTRL_startChargeFlag,             ///< 启动充电标志 - Start Charging Flag
    DUALCHARGE_CTRL_startChargeFinishFlag,       ///< 启动充电结束标志 - Start Charging Completion Flag
    DUALCHARGE_CTRL_stopChargeFlag,              ///< 停止充电标志 - Stop Charging Flag
    DUALCHARGE_CTRL_stopChargeFinishFlag,        ///< 停止充电结束标志 - Stop Charging Completion Flag
    DUALCHARGE_CTRL_revctrltimeout,              ///< 通道超时计数 - Channel Timeout Counter
    DUALCHARGE_CTRL_revctrlacktimeout,           ///< 应答超时计数 - Acknowledgement Timeout Counter
    DUALCHARGE_CTRL_success_flag,                ///< 成功标识 - Success Flag
    DUALCHARGE_CTRL_connected,                   ///< 车辆连接状态 - Vehicle Connection Status
    DUALCHARGE_CTRL_offsideconnected,            ///< 对侧车辆连接状态 - Remote Vehicle Connection Status
    DUALCHARGE_CTRL_work_status,                 ///< 当前工作状态 - Current Working Status
    DUALCHARGE_CTRL_status,                      ///< Recv阶段状态 - Receive Phase Status
    DUALCHARGE_CTRL_offsidestatus,               ///< 对侧阶段状态 - Remote Phase Status
    DUALCHARGE_CTRL_reconnect_status,            ///< 重连状态 - Reconnection Status
    DUALCHARGE_CTRL_result_status,               ///< 操作结果状态 - Operation Result Status
    DUALCHARGE_CTRL_ctrlCmd,                     ///< 控制命令 - Control Command
    DUALCHARGE_CTRL_mode_transition_in_progress, ///< 模式切换进行中标志 - Mode Transition In Progress Flag
    DUALCHARGE_CTRL_flag_cleaning_in_progress,   ///< 标志清理进行中 - Flag Cleaning In Progress
    DUALCHARGE_CTRL_conversion_completed,        ///< 转换完成标志 - Conversion Completed Flag
    DUALCHARGE_CTRL_emergency_active,            ///< 紧急情况激活标志 - Emergency Active Flag
    DUALCHARGE_CTRL_last_transition_error,       ///< 最后转换错误 - Last Transition Error
    DUALCHARGE_CTRL_emergency_type,              ///< 紧急情况类型 - Emergency Type
    DUALCHARGE_CTRL_emergency_retry_count,       ///< 紧急命令重试计数 - Emergency Command Retry Count
    DUALCHARGE_CTRL_service_stable_notified,     ///< 服务稳定通知标志 - Service Stable Notification Flag

    /* 复合结构体字段 */
    DUALCHARGE_CTRL_pending_mode_change, ///< 延迟模式变更请求 - Delayed Mode Change Request

    /* 字段总数 */
    DUALCHARGE_CTRL_FIELD_COUNT ///< 字段总数 - Total Field Count
} DUALCHARGE_CTRL_FIELD;

/*-----------------------------------------------------------------------------
 *  3. 联合体与结构体 - Unions and Structures
 *-----------------------------------------------------------------------------*/
/**
 * 并机状态处理结构 - Parallel State Handler Structure
 * 用于状态回调处理
 */
typedef struct _DUALCHARGE_STATUS_HEANDLE
{
    int status;                ///< 状态标识 - Status Identifier
    void (*func)(int channel); ///< 处理函数 - Handler Function
} DUALCHARGE_STATUS_HEANDLE;

/**
 * 优先级表项结构 - Priority Table Entry Structure
 * 定义ID与优先级的映射
 */
typedef struct _PRIO_TBL
{
    uint8 id;   ///< 标识符 - Identifier
    uint8 prio; ///< 优先级 - Priority
} PRIO_TBL;

/**
 * 文本映射结构 - Text Mapping Structure
 * 定义状态到文本的映射
 */
typedef struct TEXT_STR
{
    int status;    ///< 状态标识 - Status Identifier
    char text[30]; ///< 状态文本 - Status Text
} DUALTEXT;

/**
 * 状态转换保护结构体 - State Transition Protection Structure
 * 保护状态转换过程中的关键信息
 */
typedef struct
{
    uint8 channel;               ///< 通道号 - Channel Number
    uint8 source_mode;           ///< 源状态 - Source State
    uint8 target_mode;           ///< 目标状态 - Target State
    uint32 start_time;           ///< 开始时间 - Start Time
    uint8 in_progress;           ///< 转换进行中 - Transition In Progress
    uint8 retry_count;           ///< 重试次数 - Retry Count
    uint8 expected_remote_state; ///< 期望对端状态 - Expected Remote State
} DualChargeStateGuard;

/**
 * 返回值容器 - Return Value Container
 * 用于存储不同类型的返回值
 */
typedef union
{
    /* 基本数值类型 - Basic Numeric Types */
    uint8_t u8;   ///< 8位无符号整型 - 8-bit Unsigned Integer
    uint16_t u16; ///< 16位无符号整型 - 16-bit Unsigned Integer
    uint32_t u32; ///< 32位无符号整型 - 32-bit Unsigned Integer
    float f32;    ///< 单精度浮点型 - Single Precision Float
    uint8_t b;    ///< 布尔类型 - Boolean Type (changed from bool to uint8_t)
    double f64;   ///< 双精度浮点型 - Double Precision Float

    /* 自定义枚举类型 - Custom Enumeration Types */
    DUAL_ERROR_TYPE error_type;         ///< 错误类型 - Error Type
    DUAL_EMERGENCY_TYPE emergency_type; ///< 紧急类型 - Emergency Type

    /* 自定义结构体类型 - Custom Structure Types */
    DUAL_PENDING_MODE_CHANGE pending_mode; ///< 待处理模式 - Pending Mode
} DUALCHARGE_RET_VALUE;

/**
 * 完整返回结构 - Complete Return Structure
 * 包含类型标识和值容器
 */
typedef struct
{
    DUALCHARGE_RET_TYPE type;   ///< 数据类型标识 - Data Type Identifier
    DUALCHARGE_RET_VALUE value; ///< 数据值容器 - Data Value Container
} DUALCHARGE_RET;

/**
 * 遥控命令帧（1字节对齐）- Remote Control Command Frame (1-byte aligned)
 * 用于发送控制命令
 */
#pragma pack(1)
typedef struct
{
    union
    {
        struct
        {
            uint8_t reconnect_status : 1;   ///< 重连状态（0=未重连，1=重连中）- Reconnection Status
            uint8_t spn_flag : 2;           ///< SPN阶段标识（0=SPN2560，1=SPN2829）- SPN Phase Flag
            uint8_t result_status : 2;      ///< 操作结果（00=失败，11=成功）- Operation Result
            DUALCHARGE_OPERATE_CMD cmd : 3; ///< 操作指令（低4位）- Operation Command (Lower 4 bits)
        };
        uint8_t controlWord; ///< 控制字节整体访问 - Control Byte Full Access
    };
    uint8_t reserved1;        ///< 预留字段（扩展用）- Reserved Field (For Extension)
    uint16_t set_voltage;     ///< 设定电压（0.1V/位）- Set Voltage (0.1V/bit)
    uint16_t set_current;     ///< 设定电流（0.01A/位）- Set Current (0.01A/bit)
    uint16_t battery_voltage; ///< 电池电压（0.1V/位）- Battery Voltage (0.1V/bit)
} RemoteControlCmd;
#pragma pack()

/**
 * 应答帧结构（1字节对齐）- Response Frame Structure (1-byte aligned)
 * 用于发送控制命令应答
 */
#pragma pack(1)
typedef struct
{
    union
    {
        struct
        {
            uint8_t success_flag : 1;       ///< 成功标识 - Success Flag
            uint8_t connected : 1;          ///< 车辆连接状态 - Vehicle Connection Status
            uint8_t reconnect : 1;          ///< 重连状态（低1位）- Reconnection Status (Lower 1 bit)
            uint8_t mode : 2;               ///< 主从模式（低2位）- Master/Slave Mode (Lower 2 bits)
            DUALCHARGE_OPERATE_CMD cmd : 3; ///< 操作指令（高3位）- Operation Command (Upper 3 bits)
        };
        uint8_t controlWord; ///< 控制字节整体访问 - Control Byte Full Access
    };
    union
    {
        struct
        {
            uint8_t spn_id : 3;  ///< SPN标识 - SPN Identifier
            uint8_t service : 1; ///< 服务状态（0=不可服务，1=可服务）- Service Status
            uint8_t status : 4;  ///< 桩阶段（低4位）- Station Phase (Lower 4 bits)
        };
        uint8_t controlWord1; ///< 控制字节整体访问 - Control Byte Full Access
    };
    uint16_t actual_voltage;  ///< 实际输出电压（0.1V/位）- Actual Output Voltage (0.1V/bit)
    uint16_t actual_current;  ///< 实际输出电流（0.01A/位）- Actual Output Current (0.01A/bit)
    uint16_t battery_voltage; ///< 电池电压（0.1V/位）- Battery Voltage (0.1V/bit)
} RemoteControlAck;
#pragma pack()

/**
 * 心跳应答帧结构（1字节对齐）- Heartbeat Response Frame Structure (1-byte aligned)
 * 用于心跳包应答
 */
#pragma pack(1)
typedef struct CCUREVCHRART
{
    uint8 data[8]; ///< 数据字节数组 - Data Byte Array
} CcuRevheart;
#pragma pack()

/*-----------------------------------------------------------------------------
 *  4. 核心控制结构体 - Core Control Structure
 *-----------------------------------------------------------------------------*/
/**
 * 主控制结构体（1字节对齐）- Main Control Structure (1-byte aligned)
 * 包含双充系统的所有控制参数和状态，按字段大小排序
 */
#pragma pack(1)
typedef struct _DUALCHARGE_CTRL
{
    /*---------------------------------------------------
     * 4字节字段组 (uint32_t)
     *---------------------------------------------------*/
    uint32_t tick;                        ///< 当前时间戳（ms）- Current Timestamp (ms)
    uint32_t lasttick;                    ///< 上次时间戳（ms）- Last Timestamp (ms)
    uint32_t transition_timeout_count;    ///< 转换超时计数 - Transition Timeout Counter
    uint32_t sync_failure_count;          ///< 同步失败计数 - Sync Failure Counter
    uint32_t state_inconsistency_count;   ///< 状态不一致计数 - State Inconsistency Counter
    uint32_t rejected_transitions;        ///< 被拒绝的转换请求数 - Rejected Transition Requests Count
    uint32_t command_send_count;          ///< 命令发送计数 - Command Send Counter
    uint32_t command_resend_count;        ///< 命令重发计数 - Command Resend Counter
    uint32_t transition_success_time;     ///< 转换成功时间 - Transition Success Time
    uint32_t initiate_timestamp;          ///< 发起时间戳 - Initiation Timestamp
    uint32_t offside_service_stable_time; ///< 对端服务稳定时间 - Remote Service Stable Time
    uint32_t emergency_start_time;        ///< 紧急情况开始时间 - Emergency Start Time

    /*---------------------------------------------------
     * 2字节字段组 (uint16_t)
     *---------------------------------------------------*/
    uint16_t actual_voltage;  ///< 实际输出电压（0.1V/位）- Actual Output Voltage (0.1V/bit)
    uint16_t actual_current;  ///< 实际输出电流（0.01A/位）- Actual Output Current (0.01A/bit)
    uint16_t battery_voltage; ///< 电池电压（0.1V/位）- Battery Voltage (0.1V/bit)
    uint16_t voltage;         ///< 控制参数-电压 - Control Parameter-Voltage
    uint16_t current;         ///< 控制参数-电流 - Control Parameter-Current
    uint16_t batVoltage;      ///< 控制参数-电池电压 - Control Parameter-Battery Voltage

    /*---------------------------------------------------
     * 1字节字段组 (uint8_t/枚举类型)
     *---------------------------------------------------*/
    /* 基础状态字段 */
    uint8_t bussy;          ///< 并充忙状态 - Dual Charging Busy Status
    uint8_t mode;           ///< 主从模式 - Master/Slave Mode
    uint8_t spn;            ///< SPN阶段标识 - SPN Phase Identifier
    uint8_t offsidespn;     ///< 对侧SPN阶段标识 - Remote SPN Phase Identifier
    uint8_t offsidemode;    ///< 对侧主从模式 - Remote Master/Slave Mode
    uint8_t service;        ///< 本侧服务状态 - Local Service Status
    uint8_t OffsideService; ///< 对侧服务状态 - Remote Service Status

    /* 操作控制字段 */
    uint8_t operate;               ///< 并柜操作 - Dual Cabinet Operation
    uint8_t operateDirection;      ///< 操作方向 - Operation Direction
    uint8_t chargeImdReadyFlag;    ///< 充电准备标志 - Charging Preparation Flag
    uint8_t chargeImdFinishFlag;   ///< 充电准备完成标志 - Charging Preparation Complete Flag
    uint8_t startChargeFlag;       ///< 启动充电标志 - Start Charging Flag
    uint8_t startChargeFinishFlag; ///< 启动充电结束标志 - Start Charging Completion Flag
    uint8_t stopChargeFlag;        ///< 停止充电标志 - Stop Charging Flag
    uint8_t stopChargeFinishFlag;  ///< 停止充电结束标志 - Stop Charging Completion Flag

    /* 通信管理字段 */
    uint8_t revctrltimeout;    ///< 通道超时计数 - Channel Timeout Counter
    uint8_t revctrlacktimeout; ///< 应答超时计数 - Acknowledgement Timeout Counter

    /* 连接状态字段 */
    uint8_t success_flag;     ///< 成功标识 - Success Flag
    uint8_t connected;        ///< 车辆连接状态 - Vehicle Connection Status
    uint8_t offsideconnected; ///< 对侧车辆连接状态 - Remote Vehicle Connection Status

    /* 工作状态字段 */
    uint8_t work_status;      ///< 当前工作状态 - Current Working Status
    uint8_t status;           ///< Recv阶段状态 - Receive Phase Status
    uint8_t offsidestatus;    ///< 对侧阶段状态 - Remote Phase Status
    uint8_t reconnect_status; ///< 重连状态 - Reconnection Status
    uint8_t result_status;    ///< 操作结果状态 - Operation Result Status
    uint8_t ctrlCmd;          ///< 控制命令 - Control Command

    /* 状态转换字段 */
    uint8_t mode_transition_in_progress; ///< 模式切换进行中标志 - Mode Transition In Progress Flag
    uint8_t flag_cleaning_in_progress;   ///< 标志清理进行中 - Flag Cleaning In Progress
    uint8_t conversion_completed;        ///< 转换完成标志 - Conversion Completed Flag

    /* 紧急状态字段 */
    uint8_t emergency_active;      ///< 紧急情况激活标志 - Emergency Active Flag
    uint8_t last_transition_error; ///< 最后转换错误 - Last Transition Error
    uint8_t emergency_type;        ///< 紧急情况类型 - Emergency Type
    uint8_t emergency_retry_count; ///< 紧急命令重试计数 - Emergency Command Retry Count

    /* 通知状态字段 */
    uint8_t service_stable_notified; ///< 服务稳定通知标志 - Service Stable Notification Flag

    /*---------------------------------------------------
     * 复合结构体字段 - 6字节(1+1+4)
     *---------------------------------------------------*/
    struct
    {                        ///< 延迟模式变更请求 - Delayed Mode Change Request
        uint8_t active;      ///< 是否激活 - Whether Active (0=inactive, 1=active)
        uint8_t target_mode; ///< 目标模式 - Target Mode
        uint32_t timestamp;  ///< 请求时间戳 - Request Timestamp
    } pending_mode_change;   ///< DUAL_PENDING_MODE_CHANGE结构体内嵌
} DUALCHARGE_CTRL;
#pragma pack()

/*-----------------------------------------------------------------------------
 *  5. 元数据与全局配置 - Metadata and Global Configuration
 *-----------------------------------------------------------------------------*/
/**
 * 字段元信息 - Field Metadata
 * 定义字段类型和偏移量
 */
typedef struct
{
    DUALCHARGE_RET_TYPE type; ///< 字段数据类型 - Field Data Type
    size_t offset;            ///< 字段偏移量 - Field Offset
} FieldMetadata;

/**
 * 完整的字段元数据表定义 - 使用枚举索引
 * Complete Field Metadata Table Definition - Using Enum Index
 */
static const FieldMetadata g_field_metadata[] = {
    /* 4字节字段 */
    [DUALCHARGE_CTRL_tick] = {
        .type = DUALCHARGE_RET_UINT32,
        .offset = offsetof(DUALCHARGE_CTRL, tick)},
    [DUALCHARGE_CTRL_lasttick] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, lasttick)},
    [DUALCHARGE_CTRL_transition_timeout_count] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, transition_timeout_count)},
    [DUALCHARGE_CTRL_sync_failure_count] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, sync_failure_count)},
    [DUALCHARGE_CTRL_state_inconsistency_count] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, state_inconsistency_count)},
    [DUALCHARGE_CTRL_rejected_transitions] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, rejected_transitions)},
    [DUALCHARGE_CTRL_command_send_count] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, command_send_count)},
    [DUALCHARGE_CTRL_command_resend_count] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, command_resend_count)},
    [DUALCHARGE_CTRL_transition_success_time] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, transition_success_time)},
    [DUALCHARGE_CTRL_initiate_timestamp] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, initiate_timestamp)},
    [DUALCHARGE_CTRL_offside_service_stable_time] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, offside_service_stable_time)},
    [DUALCHARGE_CTRL_emergency_start_time] = {.type = DUALCHARGE_RET_UINT32, .offset = offsetof(DUALCHARGE_CTRL, emergency_start_time)},

    /* 2字节字段 */
    [DUALCHARGE_CTRL_actual_voltage] = {.type = DUALCHARGE_RET_UINT16, .offset = offsetof(DUALCHARGE_CTRL, actual_voltage)},
    [DUALCHARGE_CTRL_actual_current] = {.type = DUALCHARGE_RET_UINT16, .offset = offsetof(DUALCHARGE_CTRL, actual_current)},
    [DUALCHARGE_CTRL_battery_voltage] = {.type = DUALCHARGE_RET_UINT16, .offset = offsetof(DUALCHARGE_CTRL, battery_voltage)},
    [DUALCHARGE_CTRL_voltage] = {.type = DUALCHARGE_RET_UINT16, .offset = offsetof(DUALCHARGE_CTRL, voltage)},
    [DUALCHARGE_CTRL_current] = {.type = DUALCHARGE_RET_UINT16, .offset = offsetof(DUALCHARGE_CTRL, current)},
    [DUALCHARGE_CTRL_batVoltage] = {.type = DUALCHARGE_RET_UINT16, .offset = offsetof(DUALCHARGE_CTRL, batVoltage)},

    /* 1字节字段和枚举类型 */
    [DUALCHARGE_CTRL_bussy] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, bussy)},
    [DUALCHARGE_CTRL_mode] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, mode)},
    [DUALCHARGE_CTRL_spn] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, spn)},
    [DUALCHARGE_CTRL_offsidespn] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, offsidespn)},
    [DUALCHARGE_CTRL_offsidemode] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, offsidemode)},
    [DUALCHARGE_CTRL_service] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, service)},
    [DUALCHARGE_CTRL_OffsideService] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, OffsideService)},
    [DUALCHARGE_CTRL_operate] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, operate)},
    [DUALCHARGE_CTRL_operateDirection] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, operateDirection)},
    [DUALCHARGE_CTRL_chargeImdReadyFlag] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, chargeImdReadyFlag)},
    [DUALCHARGE_CTRL_chargeImdFinishFlag] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, chargeImdFinishFlag)},
    [DUALCHARGE_CTRL_startChargeFlag] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, startChargeFlag)},
    [DUALCHARGE_CTRL_startChargeFinishFlag] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, startChargeFinishFlag)},
    [DUALCHARGE_CTRL_stopChargeFlag] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, stopChargeFlag)},
    [DUALCHARGE_CTRL_stopChargeFinishFlag] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, stopChargeFinishFlag)},
    [DUALCHARGE_CTRL_revctrltimeout] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, revctrltimeout)},
    [DUALCHARGE_CTRL_revctrlacktimeout] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, revctrlacktimeout)},
    [DUALCHARGE_CTRL_success_flag] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, success_flag)},
    [DUALCHARGE_CTRL_connected] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, connected)},
    [DUALCHARGE_CTRL_offsideconnected] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, offsideconnected)},
    [DUALCHARGE_CTRL_work_status] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, work_status)},
    [DUALCHARGE_CTRL_status] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, status)},
    [DUALCHARGE_CTRL_offsidestatus] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, offsidestatus)},
    [DUALCHARGE_CTRL_reconnect_status] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, reconnect_status)},
    [DUALCHARGE_CTRL_result_status] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, result_status)},
    [DUALCHARGE_CTRL_ctrlCmd] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, ctrlCmd)},
    [DUALCHARGE_CTRL_mode_transition_in_progress] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, mode_transition_in_progress)},
    [DUALCHARGE_CTRL_flag_cleaning_in_progress] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, flag_cleaning_in_progress)},
    [DUALCHARGE_CTRL_conversion_completed] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, conversion_completed)},
    [DUALCHARGE_CTRL_emergency_active] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, emergency_active)},
    [DUALCHARGE_CTRL_last_transition_error] = {.type = DUALCHARGE_RET_ERROR_TYPE, .offset = offsetof(DUALCHARGE_CTRL, last_transition_error)},
    [DUALCHARGE_CTRL_emergency_type] = {.type = DUALCHARGE_RET_EMERGENCY_TYPE, .offset = offsetof(DUALCHARGE_CTRL, emergency_type)},
    [DUALCHARGE_CTRL_emergency_retry_count] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, emergency_retry_count)},
    [DUALCHARGE_CTRL_service_stable_notified] = {.type = DUALCHARGE_RET_UINT8, .offset = offsetof(DUALCHARGE_CTRL, service_stable_notified)},

    /* 复合结构体字段 */
    /* 注：对于复合结构体字段，我们需要特殊处理 */
    /* 这里我们创建一个假设的枚举值（如果在实际代码中存在）来映射pending_mode_change字段 */
    [DUALCHARGE_CTRL_pending_mode_change] = {.type = DUALCHARGE_RET_PENDING_MODE, .offset = offsetof(DUALCHARGE_CTRL, pending_mode_change)},

    /* 确保数组大小与枚举总数匹配 */
    [DUALCHARGE_CTRL_FIELD_COUNT] = {.type = DUALCHARGE_RET_UNKNOWN, .offset = 0}};

/**
 * 全局控制实例 - Global Control Instance
 * 定义通道控制实例数组
 */
extern DUALCHARGE_CTRL s_dualcharge_ctrl[DUAL_CHARGE_CHANNEL_NUM];

/**
 * 全局元数据表 - Global Metadata Table
 * 外部引用声明
 */
extern const FieldMetadata g_field_metadata[];

/*-----------------------------------------------------------------------------
 *  6. API 声明 - API Declarations
 *-----------------------------------------------------------------------------*/
/**
 * 获取指定通道的控制结构体指针
 * Get Control Structure Pointer for Specified Channel
 *
 * @param channel 通道号 - Channel Number
 * @return 控制结构体指针 - Control Structure Pointer
 */
DUALCHARGE_CTRL *Get_DualchargeCtrl(int channel);

/**
 * 获取CCU优先级
 * Get CCU Priority
 *
 * @param id CCU标识 - CCU Identifier
 * @return 优先级 - Priority
 */
uint8 Get_CcuPrio(int id);

/**
 * 获取CCU CAN ID
 * Get CCU CAN ID
 *
 * @param ptr 存储ID的指针 - Pointer to Store ID
 * @param dev 设备号 - Device Number
 * @param id 标识号 - Identifier Number
 */
void Get_CcuCanId(uint32 *ptr, int dev, int id);

/**
 * CCU初始化
 * CCU Initialization
 */
void Ccu_Init(void);

/**
 * CCU发送初始化
 * CCU Send Initialization
 */
void Ccu_SendInit(void);

/**
 * CCU接收初始化
 * CCU Receive Initialization
 */
void Ccu_RecvInit(void);

/**
 * 发送充电控制指令
 * Send Charging Control Command
 *
 * @param channel 通道号 - Channel Number
 * @param ctrlCmd 控制命令 - Control Command
 * @param voltage 电压值 - Voltage Value
 * @param current 电流值 - Current Value
 * @param batVoltage 电池电压 - Battery Voltage
 */
void CcuChargeCtrlSend(uint8 channel,
                       uint8 ctrlCmd,
                       uint16 voltage,
                       uint16 current,
                       uint16 batVoltage);

/**
 * 双充控制对齐检查
 * Dual Charge Control Alignment Check
 */
void DualCharge_Ctrl_Alignment_Check(void);

/**
 * 获取双充控制字段值
 * Get Dual Charge Control Field Value
 *
 * @param channel 通道号 - Channel Number
 * @param field 字段标识 - Field Identifier
 * @return 字段返回值 - Field Return Value
 */
DUALCHARGE_RET Get_DualCharge_Ctrl(int channel, DUALCHARGE_CTRL_FIELD field);

/**
 * 设置双充控制字段值
 * Set Dual Charge Control Field Value
 *
 * @param pCtrl 控制结构指针 - Control Structure Pointer
 * @param field 字段标识 - Field Identifier
 * @param value 设置值 - Value to Set
 * @param expected_type 期望类型 - Expected Type
 * @return 设置结果 - Setting Result
 */
DUALCHARGE_SET_RESULT Set_DualCharge_Ctrl(
    DUALCHARGE_CTRL *pCtrl,
    DUALCHARGE_CTRL_FIELD field,
    DUALCHARGE_RET_VALUE value,
    DUALCHARGE_RET_TYPE expected_type);

/**
 * 设置双充参数
 * Set Dual Charge Parameters
 *
 * @param channel 通道号 - Channel Number
 * @param voltage 电压值 - Voltage Value
 * @param current 电流值 - Current Value
 * @param batVoltage 电池电压 - Battery Voltage
 */
void setPara_DualCharge(int channel,
                        uint16 voltage,
                        uint16 current,
                        uint16 batVoltage);

/**
 * 检查是否有双充控制命令
 * Check if There is a Dual Charge Control Command
 *
 * @param channel 通道号 - Channel Number
 * @return 是否有命令 - Whether There is a Command
 */
uint8 Is_DualCharge_CtrlCmd(int channel);

/**
 * 设置安全的双充参数
 * Set Safe Dual Charge Parameters
 *
 * @param channel 通道号 - Channel Number
 * @param vol 电压值 - Voltage Value
 * @param cur 电流值 - Current Value
 * @param batVol 电池电压 - Battery Voltage
 */
void Set_SafeDualChargeParams(uint8_t channel, uint16_t vol, uint16_t cur, uint16_t batVol);

/**
 * 获取安全的充电电压
 * Get Safe Charging Voltage
 *
 * @param channel 通道号 - Channel Number
 * @param vol 电压值 - Voltage Value
 * @param batVol 电池电压 - Battery Voltage
 * @return 安全电压值 - Safe Voltage Value
 */
uint16_t Get_SafeChargeVoltage(uint8_t channel, uint16_t vol, uint16_t batVol, uint8_t mode);

/**
 * 双充控制初始化
 * Dual Charge Control Initialization
 */
void DualCharge_Ctrl_Init(void);

/**
 * 启动双充绝缘检测
 * Start Dual Charging Insulation Detection
 *
 * @param channel 通道号 - Channel Number
 * @param cmd 命令 - Command
 * @param dir 方向 - Direction
 * @param voltage 电压值 - Voltage Value
 * @param current 电流值 - Current Value
 * @param batVoltage 电池电压 - Battery Voltage
 */
void Start_DualImdCharge(uint8 channel,
                         uint8 cmd,
                         uint8 dir,
                         uint16 voltage,
                         uint16 current,
                         uint16 batVoltage);

/**
 * 启动双充软启动
 * Start Dual Charging Soft Start
 *
 * @param channel 通道号 - Channel Number
 * @param cmd 命令 - Command
 * @param dir 方向 - Direction
 * @param voltage 电压值 - Voltage Value
 * @param current 电流值 - Current Value
 * @param batVoltage 电池电压 - Battery Voltage
 */
void Start_DualSoftCharge(uint8 channel,
                          uint8 cmd,
                          uint8 dir,
                          uint16 voltage,
                          uint16 current,
                          uint16 batVoltage);

/**
 * 停止双充
 * Stop Dual Charging
 *
 * @param channel 通道号 - Channel Number
 */
void stop_DualCharge(int channel);

/**
 * 双充任务处理
 * Dual Charging Task Processing
 */
void DualCharge_Task(void);

/**
 * 获取CCU
 * Get CCU
 *
 * @return CCU标识 - CCU Identifier
 */
uint8 Get_CCU(void);

/**
 * 检查是否使用手动外部电压
 * Check if Manual External Voltage is Used
 *
 * @return 是否使用手动外部电压 - Whether Manual External Voltage is Used
 */
bool Is_UseManualOutsideVol(void);

/**
 * 获取手动外部电压
 * Get Manual External Voltage
 *
 * @return 手动外部电压值 - Manual External Voltage Value
 */
uint32 Get_ManualOutsideVol(void);

/*-----------------------------------------------------------------------------
 *  7. 类型安全宏定义 - Type-Safe Macro Definitions
 *-----------------------------------------------------------------------------*/

/**
 * 获取字段元数据函数
 * Get Field Metadata Function
 */
static inline const FieldMetadata *Get_Field_Metadata(DUALCHARGE_CTRL_FIELD field)
{
    if (field >= DUALCHARGE_CTRL_FIELD_COUNT)
    {
        return NULL;
    }
    return &g_field_metadata[field];
}

/**
 * 设置8位字段（类型安全）
 * Set 8-bit Field (Type-Safe)
 */
#define Set_U8(pCtrl, field, val) Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field, (DUALCHARGE_RET_VALUE){.u8 = (val)}, DUALCHARGE_RET_UINT8)

/**
 * 设置16位字段（类型安全）
 * Set 16-bit Field (Type-Safe)
 */
#define Set_U16(pCtrl, field, val) Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field, (DUALCHARGE_RET_VALUE){.u16 = (val)}, DUALCHARGE_RET_UINT16)

/**
 * 设置32位字段（类型安全）
 * Set 32-bit Field (Type-Safe)
 */
#define Set_U32(pCtrl, field, val) Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field, (DUALCHARGE_RET_VALUE){.u32 = (val)}, DUALCHARGE_RET_UINT32)

/**
 * 设置浮点字段（类型安全）
 * Set Float Field (Type-Safe)
 */
#define Set_Float(pCtrl, field, val) Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field, (DUALCHARGE_RET_VALUE){.f32 = (val)}, DUALCHARGE_RET_FLOAT)

/**
 * 设置布尔字段（类型安全）
 * Set Boolean Field (Type-Safe)
 */
#define Set_Bool(pCtrl, field, val) Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field, (DUALCHARGE_RET_VALUE){.b = (val)}, DUALCHARGE_RET_BOOL)

/**
 * 设置错误类型字段（类型安全）
 * Set Error Type Field (Type-Safe)
 */
#define Set_ErrorType(pCtrl, field, val)                \
    Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field, \
                        (DUALCHARGE_RET_VALUE){.error_type = (val)}, DUALCHARGE_RET_ERROR_TYPE)

/**
 * 设置紧急类型字段（类型安全）
 * Set Emergency Type Field (Type-Safe)
 */
#define Set_EmergencyType(pCtrl, field, val)            \
    Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field, \
                        (DUALCHARGE_RET_VALUE){.emergency_type = (val)}, DUALCHARGE_RET_EMERGENCY_TYPE)

/**
 * 设置待处理模式字段（类型安全）
 * Set Pending Mode Field (Type-Safe)
 */
#define Set_PendingMode(pCtrl, field, val)              \
    Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field, \
                        (DUALCHARGE_RET_VALUE){.pending_mode = (val)}, DUALCHARGE_RET_PENDING_MODE)

/**
 * 带错误检查的8位设置宏（返回错误码）
 * 8-bit Setting Macro with Error Checking (Returns Error Code)
 */
#define Set_U8_Checked(pCtrl, field, val, err)                                                \
    do                                                                                        \
    {                                                                                         \
        err = Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field,                             \
                                  (DUALCHARGE_RET_VALUE){.u8 = (val)}, DUALCHARGE_RET_UINT8); \
    } while (0)

/**
 * 带错误检查的16位设置宏（返回错误码）
 * 16-bit Setting Macro with Error Checking (Returns Error Code)
 */
#define Set_U16_Checked(pCtrl, field, val, err)                                                 \
    do                                                                                          \
    {                                                                                           \
        err = Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field,                               \
                                  (DUALCHARGE_RET_VALUE){.u16 = (val)}, DUALCHARGE_RET_UINT16); \
    } while (0)

/**
 * 带错误检查的32位设置宏（返回错误码）
 * 32-bit Setting Macro with Error Checking (Returns Error Code)
 */
#define Set_U32_Checked(pCtrl, field, val, err)                                                \
    do                                                                                         \
    {                                                                                          \
        err = Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field,                              \
                                  (DUALCHARGE_RET_VALUE){.u32 = (val)}, DUALCHARGE_RET_UINT32) \
    } while (0)

/**
 * 带错误检查的浮点设置宏（返回错误码）
 * Float Setting Macro with Error Checking (Returns Error Code)
 */
#define Set_F32_Checked(pCtrl, field, val, err)                                               \
    do                                                                                        \
    {                                                                                         \
        err = Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field,                             \
                                  (DUALCHARGE_RET_VALUE){.f32 = (val)}, DUALCHARGE_RET_FLOAT) \
    } while (0)

/**
 * 带错误检查的布尔设置宏（返回错误码）
 * Boolean Setting Macro with Error Checking (Returns Error Code)
 */
#define Set_Bool_Checked(pCtrl, field, val, err)                                           \
    do                                                                                     \
    {                                                                                      \
        err = Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field,                          \
                                  (DUALCHARGE_RET_VALUE){.b = (val)}, DUALCHARGE_RET_BOOL) \
    } while (0)

/**
 * 带错误检查的错误类型设置宏（返回错误码）
 * Error Type Setting Macro with Error Checking (Returns Error Code)
 */
#define Set_ErrorType_Checked(pCtrl, field, val, err)                                                      \
    do                                                                                                     \
    {                                                                                                      \
        err = Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field,                                          \
                                  (DUALCHARGE_RET_VALUE){.error_type = (val)}, DUALCHARGE_RET_ERROR_TYPE); \
    } while (0)

/**
 * 带错误检查的紧急类型设置宏（返回错误码）
 * Emergency Type Setting Macro with Error Checking (Returns Error Code)
 */
#define Set_EmergencyType_Checked(pCtrl, field, val, err)                                                          \
    do                                                                                                             \
    {                                                                                                              \
        err = Set_DualCharge_Ctrl(pCtrl, DUALCHARGE_CTRL_##field,                                                  \
                                  (DUALCHARGE_RET_VALUE){.emergency_type = (val)}, DUALCHARGE_RET_EMERGENCY_TYPE); \
    } while (0)

/**
 * 新增：设置pending_mode_change各个成员的宏
 * New: Macros for Setting Individual Members of pending_mode_change
 */
#define Set_PendingMode_Active(pCtrl, val) \
    Set_DUALCHARGE_CTRL_pending_mode_change_active(pCtrl, (val))

#define Set_PendingMode_TargetMode(pCtrl, val) \
    Set_DUALCHARGE_CTRL_pending_mode_change_target_mode(pCtrl, (val))

#define Set_PendingMode_Timestamp(pCtrl, val) \
    Set_DUALCHARGE_CTRL_pending_mode_change_timestamp(pCtrl, (val))

/**
 * 新增：带错误检查的pending_mode_change成员设置宏
 * New: Macros with Error Checking for Setting pending_mode_change Members
 */
#define Set_PendingMode_Active_Checked(pCtrl, val, err)                   \
    do                                                                    \
    {                                                                     \
        if (!(pCtrl))                                                     \
        {                                                                 \
            err = DUALCHARGE_SET_NULL_PTR;                                \
        }                                                                 \
        else if ((val) > 1)                                               \
        { /* 简单验证 */                                                  \
            err = DUALCHARGE_SET_INVALID_FIELD;                           \
        }                                                                 \
        else                                                              \
        {                                                                 \
            Set_DUALCHARGE_CTRL_pending_mode_change_active(pCtrl, (val)); \
            err = DUALCHARGE_SET_OK;                                      \
        }                                                                 \
    } while (0)

#define Set_PendingMode_TargetMode_Checked(pCtrl, val, err)                    \
    do                                                                         \
    {                                                                          \
        if (!(pCtrl))                                                          \
        {                                                                      \
            err = DUALCHARGE_SET_NULL_PTR;                                     \
        }                                                                      \
        else if ((val) > eDualChargeStatus_Slave)                              \
        { /* 验证模式值 */                                                     \
            err = DUALCHARGE_SET_INVALID_FIELD;                                \
        }                                                                      \
        else                                                                   \
        {                                                                      \
            Set_DUALCHARGE_CTRL_pending_mode_change_target_mode(pCtrl, (val)); \
            err = DUALCHARGE_SET_OK;                                           \
        }                                                                      \
    } while (0)

#define Set_PendingMode_Timestamp_Checked(pCtrl, val, err)                   \
    do                                                                       \
    {                                                                        \
        if (!(pCtrl))                                                        \
        {                                                                    \
            err = DUALCHARGE_SET_NULL_PTR;                                   \
        }                                                                    \
        else                                                                 \
        {                                                                    \
            Set_DUALCHARGE_CTRL_pending_mode_change_timestamp(pCtrl, (val)); \
            err = DUALCHARGE_SET_OK;                                         \
        }                                                                    \
    } while (0)

/**
 * 新增：方便的一次性设置所有pending_mode_change成员的宏
 * New: Convenient Macro for Setting All pending_mode_change Members at Once
 */
#define Set_PendingMode_All(pCtrl, active, target, timestamp)                      \
    do                                                                             \
    {                                                                              \
        if (pCtrl)                                                                 \
        {                                                                          \
            Set_DUALCHARGE_CTRL_pending_mode_change_active(pCtrl, (active));       \
            Set_DUALCHARGE_CTRL_pending_mode_change_target_mode(pCtrl, (target));  \
            Set_DUALCHARGE_CTRL_pending_mode_change_timestamp(pCtrl, (timestamp)); \
        }                                                                          \
    } while (0)

#define Set_PendingMode_All_Checked(pCtrl, active, target, timestamp, err)         \
    do                                                                             \
    {                                                                              \
        if (!(pCtrl))                                                              \
        {                                                                          \
            err = DUALCHARGE_SET_NULL_PTR;                                         \
        }                                                                          \
        else if ((active) > 1 || (target) > eDualChargeStatus_Slave)               \
        {                                                                          \
            err = DUALCHARGE_SET_INVALID_FIELD;                                    \
        }                                                                          \
        else                                                                       \
        {                                                                          \
            Set_DUALCHARGE_CTRL_pending_mode_change_active(pCtrl, (active));       \
            Set_DUALCHARGE_CTRL_pending_mode_change_target_mode(pCtrl, (target));  \
            Set_DUALCHARGE_CTRL_pending_mode_change_timestamp(pCtrl, (timestamp)); \
            err = DUALCHARGE_SET_OK;                                               \
        }                                                                          \
    } while (0)

/*-----------------------------------------------------------------------------
 *  8. 静态断言验证 - Static Assertion Verification
 *-----------------------------------------------------------------------------*/
/**
 * 结构体成员偏移量断言
 * Structure Member Offset Assertion
 */
#define STATIC_ASSERT_FIELD_OFFSET(type, field, expected_offset) \
    _Static_assert(offsetof(type, field) == (expected_offset),   \
                   "Field offset mismatch: " #type "::" #field)

/**
 * 结构体总大小断言
 * Structure Total Size Assertion
 */
#define STATIC_ASSERT_STRUCT_SIZE(type, expected_size) \
    _Static_assert(sizeof(type) == (expected_size),    \
                   "Struct size mismatch: " #type)

/*-----------------------------------------------------------------------------
 * 元数据表验证（确保与实际布局一致）
 * Metadata Table Verification (Ensure Consistency with Actual Layout)
 *-----------------------------------------------------------------------------*/
#define VERIFY_METADATA(enum_val, field)                                       \
    _Static_assert(                                                            \
        g_field_metadata[enum_val].offset == offsetof(DUALCHARGE_CTRL, field), \
        "Metadata mismatch for " #field)

/*-----------------------------------------------------------------------------
 *  9. 宏生成函数（类型安全）- Macro-Generated Functions (Type-Safe)
 *-----------------------------------------------------------------------------*/

/**
 * 字段列表定义（字段名，类型，偏移量）- 修改后版本
 * Field List Definition (Field Name, Type, Offset) - Modified Version
 */
#define FIELD_LIST                               \
    /* 4字节字段 */                              \
    X(tick, uint32_t, 0)                         \
    X(lasttick, uint32_t, 4)                     \
    X(transition_timeout_count, uint32_t, 8)     \
    X(sync_failure_count, uint32_t, 12)          \
    X(state_inconsistency_count, uint32_t, 16)   \
    X(rejected_transitions, uint32_t, 20)        \
    X(command_send_count, uint32_t, 24)          \
    X(command_resend_count, uint32_t, 28)        \
    X(transition_success_time, uint32_t, 32)     \
    X(initiate_timestamp, uint32_t, 36)          \
    X(offside_service_stable_time, uint32_t, 40) \
    X(emergency_start_time, uint32_t, 44)        \
    /* 2字节字段 */                              \
    X(actual_voltage, uint16_t, 48)              \
    X(actual_current, uint16_t, 50)              \
    X(battery_voltage, uint16_t, 52)             \
    X(voltage, uint16_t, 54)                     \
    X(current, uint16_t, 56)                     \
    X(batVoltage, uint16_t, 58)                  \
    /* 1字节字段 */                              \
    X(bussy, uint8_t, 60)                        \
    X(mode, uint8_t, 61)                         \
    X(spn, uint8_t, 62)                          \
    X(offsidespn, uint8_t, 63)                   \
    X(offsidemode, uint8_t, 64)                  \
    X(service, uint8_t, 65)                      \
    X(OffsideService, uint8_t, 66)               \
    X(operate, uint8_t, 67)                      \
    X(operateDirection, uint8_t, 68)             \
    X(chargeImdReadyFlag, uint8_t, 69)           \
    X(chargeImdFinishFlag, uint8_t, 70)          \
    X(startChargeFlag, uint8_t, 71)              \
    X(startChargeFinishFlag, uint8_t, 72)        \
    X(stopChargeFlag, uint8_t, 73)               \
    X(stopChargeFinishFlag, uint8_t, 74)         \
    X(revctrltimeout, uint8_t, 75)               \
    X(revctrlacktimeout, uint8_t, 76)            \
    X(success_flag, uint8_t, 77)                 \
    X(connected, uint8_t, 78)                    \
    X(offsideconnected, uint8_t, 79)             \
    X(work_status, uint8_t, 80)                  \
    X(status, uint8_t, 81)                       \
    X(offsidestatus, uint8_t, 82)                \
    X(reconnect_status, uint8_t, 83)             \
    X(result_status, uint8_t, 84)                \
    X(ctrlCmd, uint8_t, 85)                      \
    X(mode_transition_in_progress, uint8_t, 86)  \
    X(flag_cleaning_in_progress, uint8_t, 87)    \
    X(conversion_completed, uint8_t, 88)         \
    X(emergency_active, uint8_t, 89)             \
    X(last_transition_error, uint8_t, 90)        \
    X(emergency_type, uint8_t, 91)               \
    X(emergency_retry_count, uint8_t, 92)        \
    X(service_stable_notified, uint8_t, 93)      \
    /* 复合结构体字段 - 仅包含整个结构体 */      \
    X(pending_mode_change, DUAL_PENDING_MODE_CHANGE, 94)
/**
 * 批量定义字段访问器函数宏 - Define Field Accessors Macro
 */
#define DEFINE_FIELD_ACCESSORS(field_enum, type, offset)                                   \
    static inline type Get_DUALCHARGE_CTRL_##field_enum(DUALCHARGE_CTRL *ctrl)             \
    {                                                                                      \
        return *(type *)((uint8_t *)ctrl + offset);                                        \
    }                                                                                      \
    static inline void Set_DUALCHARGE_CTRL_##field_enum(DUALCHARGE_CTRL *ctrl, type value) \
    {                                                                                      \
        *(type *)((uint8_t *)ctrl + offset) = value;                                       \
    }

#define X(field_enum, type, offset) \
    DEFINE_FIELD_ACCESSORS_IF_NOT_STRUCT(field_enum, type, offset)

/**
 * 根据字段类型有条件地定义访问器
 */
#define DEFINE_FIELD_ACCESSORS_IF_NOT_STRUCT(field_enum, type, offset) \
    _DEFINE_IF_NOT_STRUCT_##type(field_enum, type, offset)

/* 为结构体类型特殊处理 - 不生成标准访问器 */
#define _DEFINE_IF_NOT_STRUCT_DUAL_PENDING_MODE_CHANGE(field_enum, type, offset) /* 不生成标准访问器 */

/* 为普通类型生成标准访问器 */
#define _DEFINE_IF_NOT_STRUCT_uint8_t(field_enum, type, offset) DEFINE_FIELD_ACCESSORS(field_enum, type, offset)
#define _DEFINE_IF_NOT_STRUCT_uint16_t(field_enum, type, offset) DEFINE_FIELD_ACCESSORS(field_enum, type, offset)
#define _DEFINE_IF_NOT_STRUCT_uint32_t(field_enum, type, offset) DEFINE_FIELD_ACCESSORS(field_enum, type, offset)

/* 应用到所有字段 */
FIELD_LIST

/* 取消宏定义 */
#undef X
#undef DEFINE_FIELD_ACCESSORS_IF_NOT_STRUCT
#undef _DEFINE_IF_NOT_STRUCT_DUAL_PENDING_MODE_CHANGE
#undef _DEFINE_IF_NOT_STRUCT_uint8_t
#undef _DEFINE_IF_NOT_STRUCT_uint16_t
#undef _DEFINE_IF_NOT_STRUCT_uint32_t

#endif // DUAL_CHARGE_CTRL_H

/*--------------------------End of DUAL_CHARGE_CTRL.h----------------------------*/
