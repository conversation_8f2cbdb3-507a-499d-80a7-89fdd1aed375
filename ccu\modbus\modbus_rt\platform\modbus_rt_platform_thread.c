#include "modbus_rt_platform_thread.h"
#include "modbus_rt_platform_memory.h"
#include "../ccu/charge/lc_log_stat.h"
#include "../ccu/charge/fault_inject.h"
/*
 * VxWorks/FreeRTOS兼容层通常需要定义这个函数指针类型
 * OSFUNCPTR/FUNCPTR 代表一个接受一个整型参数并返回一个整型值的函数指针
 */

#define UNUSED(x) (void)(x)

int modbus_rt_sem_init(modbus_rt_sem_t *m)
{
    /* 用于信令的信号量应初始化为0（空） */
    if (fi_should_fail(FI_SEM))
    {
        return -1;
    }

    m->sem = semBCreate(0);
    if (m->sem == NULL)
    {
        return -1;
    }
    return 0;
}
int modbus_rt_sem_wait(modbus_rt_sem_t *m, uint32_t timeout)
{
    return semTake(m->sem, timeout);
}
int modbus_rt_sem_post(modbus_rt_sem_t *m)
{
    return semGive(m->sem);
}
void modbus_rt_sem_destroy(modbus_rt_sem_t *m)
{
    if (m && m->sem)
    {
        semDelete(m->sem);
        m->sem = NULL;
    }
}

int modbus_rt_mutex_init(modbus_rt_mutex_t *m)
{
    /* 互斥锁是初始化为1（满）的二进制信号量 */
    m->mutex = semBCreate(1);
    if (m->mutex == NULL)
    {
        return -1;
    }
    return 0;
}

int modbus_rt_mutex_lock(modbus_rt_mutex_t *m)
{
    return semTake(m->mutex, (uint32_t)-1);
}

int modbus_rt_mutex_unlock(modbus_rt_mutex_t *m)
{
    return semGive(m->mutex);
}

void modbus_rt_mutex_destroy(modbus_rt_mutex_t *m)
{
    if (m && m->mutex)
    {
        semDelete(m->mutex);
        m->mutex = NULL;
    }
}

modbus_rt_thread_t *modbus_rt_thread_init(const char *name, void (*entry)(void *parameter),
                                          void *parameter, uint32_t stack_size,
                                          uint32_t priority, uint32_t tick)
{
    UNUSED(tick);

    modbus_rt_thread_t *thread = (modbus_rt_thread_t *)modbus_rt_malloc(sizeof(modbus_rt_thread_t));
    if (thread == NULL)
    {
        return NULL;
    }
    memset(thread, 0, sizeof(modbus_rt_thread_t));
    /* taskSpawn签名: (name, priority, stackSize, entryPt, arg) - 5个参数 */
    thread->tid = taskSpawn((const signed char *)name, priority, stack_size,
                            (OSFUNCPTR)entry, (uint32_t)parameter);

    if (thread->tid == 0)
    {
        modbus_rt_free(thread);
        return NULL;
    }

    return thread;
}

void modbus_rt_thread_startup(modbus_rt_thread_t *thread)
{
    /* 在 VxWorks 中，taskSpawn 已经启动线程，这里不需要额外操作 */
    UNUSED(thread);
}

void modbus_rt_thread_destroy(modbus_rt_thread_t *thread)
{
    if (thread != NULL)
    {
        if (thread->tid != 0)
        {
            taskDelete(thread->tid);
        }
        modbus_rt_free(thread);
    }
}
/* --- 新增：基于消息队列的计数信号量实现 (严格遵循用户API) --- */

// 定义一个静态的、非空的虚拟指针"令牌"
static void *const DUMMY_TOKEN = (void *)1;

// 提醒：以下实现严格依据用户提供的函数签名：
// msgQCreate(uint32 msgQLen);
// msgQSend(MSG_Q_ID msgQId, void *pmsg);
// msgQReceive(MSG_Q_ID msgQId, uint32 timeout, void **pmsg);
// msgQDelete(MSG_Q_ID msgQId);

int modbus_rt_mq_sem_init(modbus_rt_mq_sem_t *m)
{
    if (fi_should_fail(FI_SEM))
    {
        return -1;
    }

    m->qid = msgQCreate(MQ_SEM_MAX_MSGS);
    if (m->qid == NULL)
    {
        return -1;
    }
    //    printf("[MODBUS DEBUG] >>> 计数器总数(count=%d)...\n", modbus_rt_mq_sem_get_count(m));
    return 0;
}

int modbus_rt_mq_sem_wait(modbus_rt_mq_sem_t *m, uint32_t timeout)
{
    void *received_token;
    //    printf("[MODBUS DEBUG] >>> 减少计数器开始(count=%d)...\n", modbus_rt_mq_sem_get_count(m));
    // 严格按照用户原型调用，并检查返回值
    if (msgQReceive(m->qid, timeout, &received_token) == OK)
    {
//        printf("[MODBUS DEBUG] >>> 减少计数器(count=%d)...\n", modbus_rt_mq_sem_get_count(m));
#if LC_LOG_STAT_EN
        lc_stat_backlog_add(-1);
#endif
        return 0;
    }
    //    printf("[MODBUS DEBUG] >>> 减少计数器失败(count=%d)...\n", modbus_rt_mq_sem_get_count(m));
    return -1;
}

int modbus_rt_mq_sem_post(modbus_rt_mq_sem_t *m)
{
    // 严格按照用户原型调用，并检查返回值
    if (msgQSend(m->qid, DUMMY_TOKEN) == OK)
    {
//        printf("[MODBUS DEBUG] >>> 添加计数器(count=%d)...\n", modbus_rt_mq_sem_get_count(m));
#if LC_LOG_STAT_EN
        lc_stat_backlog_add(+1);
#endif
        return 0;
    }
    return -1;
}

void modbus_rt_mq_sem_destroy(modbus_rt_mq_sem_t *m)
{
    if (m && m->qid)
    {
        msgQDelete(m->qid);
        m->qid = NULL;
    }
}

int modbus_rt_mq_sem_get_count(modbus_rt_mq_sem_t *m)
{
    if (m == NULL || m->qid == NULL)
    {
        return 0;
    }
    // 直接调用底层的 msgQNumMsgs 函数
    return msgQNumMsgs(m->qid);
}
