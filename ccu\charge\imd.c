/**
 ******************************************************************************
 * @file      imd.c
 * @brief     C Source file of imd.c.
 * @details   This file including all API functions's
 *            implement of imd.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <string.h>
#include <taskLib.h>
#include <dmnLib.h>
#include <stdlib.h>
#include <board\SXDC_4_0_1\SXDC_4_0_1.h>
#include <ttylib.h>
#include <sxlib.h>
#include <stdio.h>

#include <ccu\bsn\deviceState.h>
#include "ccuChargeMain.h"
#include "imd.h"
#include <ccu\bsn\io.h>
#include <filter.h>
#include "ccu\para\para.h"
#include "ccu\bsn\sample.h"
#include <trace.h>
#include "../pcu/pcuMain.h"
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
// #define   IMD_EXTERN_ON

typedef struct IMD_CTRL_STRU
{
    uint8 trySendCnt;   // 尝试发送次数
    uint8 waitDelay;    // 等待延时
    uint8 initFlag;     // 初始化标志
    uint8 modeFlag;     // 恢复出厂标志
    uint8 swOnFlag;     // 接触器闭合标志
    uint8 swOffFlag;    // 接触器断开标志
    uint8 curSendCmd;   // 当前发送CMD
    uint8 imdSwOffFlag; // 绝缘接触器断开标记
    uint8 stage;        // 运行阶段
    uint8 type;         //
    uint32 imdVol;      // 绝缘电压值
    float imdR0;        // 正对地电阻值
    float imdR1;        // 负对地电阻值
    float vP[4];
    float vN[4];
    float Europek;
    uint8 EuropeIdle;
} IMD_CTRL;

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/

static IMD_CTRL imdCtrl;

static uint8 startstate = 0;

/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/

uint8 Get_Imdstartstate(void)
{
    return startstate;
}

void Set_Imdstartstate(uint8 state)
{
    startstate = state;
}

static void Init_Imd(void)
{
    IMD_CTRL *pImdCtrl = &imdCtrl;
    pImdCtrl->trySendCnt = 0;
    pImdCtrl->waitDelay = 0;
    pImdCtrl->initFlag = 0;
    pImdCtrl->modeFlag = 0;
    pImdCtrl->swOnFlag = 0;
    pImdCtrl->swOffFlag = 0;
    pImdCtrl->curSendCmd = 0;
    pImdCtrl->imdSwOffFlag = 0;
    pImdCtrl->imdVol = 0;
    pImdCtrl->imdR0 = 0.0;
    pImdCtrl->imdR1 = 0.0;
    pImdCtrl->swOffFlag = TRUE;

    Set_ImdSwOffFlag(FALSE);
    return;
}

static void Imd_SendCtrl(int fd)
{
    IMD_CTRL *pImdCtrl = &imdCtrl;
    uint8 buf[10] =
        {0};
    uint8 txLen = 0;
    uint16 crc = 0;

    if (pImdCtrl->waitDelay > 0)
    {
        pImdCtrl->waitDelay--;

        if (0 == pImdCtrl->waitDelay)
        {
            pImdCtrl->trySendCnt++;

            if (pImdCtrl->trySendCnt >= IMD_MAX_TRY_CNT)
            {
                if (TRUE == Get_EnableFlag(eErrType_ComErrWithIMD))
                {
                    Set_ErrType(eErrType_ComErrWithIMD);
                }
            }
        }

        return;
    }

    if (FALSE == pImdCtrl->initFlag)
    {
        pImdCtrl->curSendCmd = IMD_CMD_INIT;
    }
    else if (FALSE == pImdCtrl->modeFlag)
    {
        pImdCtrl->curSendCmd = IMD_CMD_MODE;
    }
    else if (FALSE == pImdCtrl->swOnFlag)
    {
        pImdCtrl->curSendCmd = IMD_CMD_SWON;
    }
    else if (FALSE == pImdCtrl->swOffFlag)
    {
        pImdCtrl->curSendCmd = IMD_CMD_SWOFF;
    }
    else
    {
        pImdCtrl->curSendCmd = IMD_CMD_READ;
    }

    buf[txLen++] = DEVICE_ID;
    buf[txLen++] = pImdCtrl->curSendCmd;

    crc = Get_CRC16(buf, 8);
    Uint16ToTwoUint8(buf + 8, crc);
    txLen += 8;

    ttyWrite(fd, (char *)buf, txLen);
    pImdCtrl->waitDelay = IMD_MAX_WAIT_DELAY;

    return;
}

static void
Imd_RecvCtrl(int fd)
{
    IMD_CTRL *pImdCtrl = &imdCtrl;
    uint16 dataLen = 0;
    uint16 offSet = 0;
    uint8 buf[20] =
        {0};

    dataLen = ttyRead(fd, (char *)buf, sizeof(buf));

    if (dataLen > 0)
    {
        print_buf("him:", buf, dataLen);
    }

    while ((dataLen - offSet) >= 10)
    {
        if (DEVICE_ID == buf[offSet] && pImdCtrl->curSendCmd == buf[offSet + 1])
        {
            if (TwoUint8ToUint16(&buf[offSet + 8]) == Get_CRC16(&buf[offSet], 8))
            {
                if (IMD_CMD_INIT == pImdCtrl->curSendCmd)
                {
                    pImdCtrl->initFlag = TRUE;
#ifdef IMD_DEBUG
                    printf("初始化成功\n");
#endif
                }

                else if (IMD_CMD_MODE == pImdCtrl->curSendCmd)
                {
                    pImdCtrl->modeFlag = TRUE;
#ifdef IMD_DEBUG
                    printf("模式设置成功\n");
#endif
                }
                else if (IMD_CMD_SWON == pImdCtrl->curSendCmd)
                {
                    pImdCtrl->swOnFlag = TRUE;
#ifdef IMD_DEBUG
                    printf("接触器闭合完成\n");
#endif
                }
                else if (IMD_CMD_SWOFF == pImdCtrl->curSendCmd)
                {
                    pImdCtrl->swOffFlag = TRUE;
#ifdef IMD_DEBUG
                    printf("接触器断开完成\n");
#endif
                    pImdCtrl->imdSwOffFlag = TRUE;
                }
                else
                {
                    pImdCtrl->imdVol = (buf[offSet + 2] << 8) | buf[offSet + 3];
                    pImdCtrl->imdR0 = 1000.0 * ((buf[offSet + 4] << 8) | buf[offSet + 5]);
                    pImdCtrl->imdR1 = 1000.0 * ((buf[offSet + 6] << 8) | buf[offSet + 7]);
#ifdef IMD_DEBUG
                    printf("imdVol = %x, imdR0 = %f, imdR1 = %f\n",
                           pImdCtrl->imdVol, pImdCtrl->imdR0, pImdCtrl->imdR1);
#endif
                }

                pImdCtrl->waitDelay = 0;
                pImdCtrl->curSendCmd = 0;
                pImdCtrl->trySendCnt = 0;
                break;
            }
        }

        offSet++;
    }
}

/**
 ******************************************************************************
 * @brief     判断电阻值有效性
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details   不换算成电压的计算方式   此公式中Vp Vn都是对应的AD值加偏移量值，没有真正换算成电压
 * @note
 * @note
 ******************************************************************************
 */
// float Imd_Res0_Adj(float k1,
//                    float k2,
//                    float k3,
//                    float iresp,
//                    float iresn,
//                    float *oresp,
//                    float *oresn,
//                    bool  flag)
void Imd_Res0_Adj(float k1,
                  float k2,
                  float k3,
                  float iresp,
                  float iresn,
                  float *oresp,
                  float *oresn,
                  bool flag)
{
    float R;
    uint8 exChangeMark = 0;
    R = ((flag == TRUE) ? R1_R2_R5 : R1_R5);
#if 0
    if(k1 < 1.0)
    {
        k1 = 1.0/k1;
        exChangeMark = 1;
    }
    if(k1 > IMD_OFFSET_MAX)
    {
        R /= (k1-1);
        printf("K1= %f,R = %f,exChangeMark = %d\n",k1,R,exChangeMark);
        if(0 == exChangeMark)
        {
            *oresp = iresp;
            *oresn = iresn;//R
        }
        else
        {
            *oresp = iresp;//R
            *oresn = iresn;
        }
    }
    else
    {
        printf("K1= %f,R = %f,exChangeMark = %d\n",k1,R,exChangeMark);
        *oresp = iresp;
        *oresn = iresn;
    }
#else
    if (k1 < 1.0)
    {
        k1 = 1.0 / k1;
        exChangeMark = 1;
    }
    if (k3 / k2 > 1.75)
    {
        printf("k3/k2= %f,R = %f,exChangeMark = %d\n", k3 / k2, R, exChangeMark);
        *oresp = iresp;
        *oresn = iresn;
    }
    else
    {
        R /= (k1 - 1);
        printf("K1= %f,k3/k2：%f,R = %f,exChangeMark = %d\n", k1, k3 / k2, R, exChangeMark);
        if (0 == exChangeMark)
        {
            *oresp = iresp;
            *oresn = iresn;
        }
        else
        {
            //            *oresp = R;
            *oresp = iresp;
            *oresn = iresn;
        }
    }
#endif
}
void Imd_Res0_Adj_new(float k1,
                      float k2,
                      float k3,
                      double iresp,
                      double iresn,
                      double *oresp,
                      double *oresn,
                      bool flag)
{
    volatile double R = R1_R5;
    ;
    float temp_k1 = k1 - 1;
    uint8 exChangeMark = 0;

    if ((temp_k1 <= 0.1) && (temp_k1 >= -0.1))
    {
        *oresp = iresp;
        *oresn = iresn;
    }
    else if ((k1 >= 6.65) && (iresp > 1000))
    {
        exChangeMark = 1;
        *oresp = iresp;
        *oresn = R / (k1 - 1);
    }
    else if ((k1 <= 0.136) && (iresn > 1000))
    {
        *oresp = R / (1 / k1 - 1);
        *oresn = iresn;
        exChangeMark = 1;
    }

    printf("R = %f,exChangeMark = %d\n", R, exChangeMark);
}
/**
 ******************************************************************************
 * @brief     计算绝缘值
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 * @note      Rp = R1_5*R1_2_5(Vp2*Vn1-Vp1*Vn2)/(Vp1*Vn2*R1_2_5-Vp2*Vn1*R1_5)
 * @note      Rn = R1_5*R1_2_5(Vp3*Vn1-Vp1*Vn3)/(Vp1*Vn3*R1_5-Vp3*Vn1*R1_2_5)
 ******************************************************************************
 */
void Imd_Ctrl_Calculate(void)
{
    IMD_CTRL *pImdCtrl = &imdCtrl;
    double tmp[5];
    tmp[0] = pImdCtrl->vP[2] * pImdCtrl->vN[1];
    tmp[1] = pImdCtrl->vP[1] * pImdCtrl->vN[2];
    tmp[2] = (R1_R2_R5)*tmp[1];
    tmp[3] = (R1_R5)*tmp[0];
    tmp[4] = (R1_R2_R5) * (R1_R5) * (tmp[0] - tmp[1]);
    tmp[4] /= (tmp[2] - tmp[3]);
    if (tmp[4] < 0.0)
        tmp[4] *= -1.0;
    trace(TR_DEBUG,
          "tmp[0] = %f,tmp[1] = %f,tmp[2] = %f,tmp[3] = %f,tmp[4] = %f.\r\n",
          tmp[0], tmp[1], tmp[2], tmp[3], tmp[4]);
    pImdCtrl->imdR0 = (tmp[4]);
    tmp[0] = pImdCtrl->vP[3] * pImdCtrl->vN[1];
    tmp[1] = pImdCtrl->vP[1] * pImdCtrl->vN[3];
    tmp[2] = (R1_R5)*tmp[1];
    tmp[3] = (R1_R2_R5)*tmp[0];
    tmp[4] = (R1_R2_R5) * (R1_R5) * (tmp[0] - tmp[1]);
    tmp[4] /= (tmp[2] - tmp[3]);
    if (tmp[4] < 0.0)
        tmp[4] *= -1.0;
    trace(TR_DEBUG,
          "tmp[0] = %f,tmp[1] = %f,tmp[2] = %f,tmp[3] = %f,tmp[4] = %f.\r\n",
          tmp[0], tmp[1], tmp[2], tmp[3], tmp[4]);
    pImdCtrl->imdR1 = (tmp[4]);

    trace(TR_DEBUG, "vP[0] = %f,vN[0] = %f.\r\n", pImdCtrl->vP[0],
          pImdCtrl->vN[0]);
    trace(TR_DEBUG, "vP[1] = %f,vN[1] = %f.\r\n", pImdCtrl->vP[1],
          pImdCtrl->vN[1]);
    trace(TR_DEBUG, "vP[2] = %f,vN[2] = %f.\r\n", pImdCtrl->vP[2],
          pImdCtrl->vN[2]);
#ifdef IMD_DEBUG
    printf("Rp = %fK.\r\n", pImdCtrl->imdR0);
    printf("Rn = %fK.\r\n", pImdCtrl->imdR1);
#endif
}

/**
 ******************************************************************************
 * @brief     计算绝缘值
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details   不换算成电压的计算方式   此公式中Vp Vn都是对应的AD值加偏移量值，没有真正换算成电压
 * @note      Rp0 = (Vp3*Vn0*R1_2_5-Vp0*Vn3*R1_5)/(Vp0*Vn3-Vp3*Vn0)
 * @note      Rp1 = (Vp2*Vn1*R1_2_5-Vp1*Vn2*R1_5)/(Vp1*Vn2-Vp2*Vn1)
 * @note      Rn0 = (Vp2*Vn0*R1_5-Vp0*Vn2*R1_2_5)/(Vp2*Vn0-Vp0*Vn2)
 * @note      Rn1 = (Vp3*Vn1*R1_5-Vp1*Vn3*R1_2_5)/(Vp1*Vn3-Vp3*Vn1)
 * @note      Rp =  (Vn2*R1_5*Vp3*R1_5-Vn3*R1_2_5*Vp2*R1_2_5)/(Vn3*R1_2_5*(Vp2-Vn2)-Vn2*R1_5*(Vp3-Vn1))
 * @note      Rn =  (Vp2*R1_2_5*Vn1*R1_2_5-Vp1*R1_5*Vn2*R1_5)/(Vp1*R1_5*(Vn2-Vp2)-Vp2*R1_2_5*(Vn1-Vp1))
 * @note      Rp =  (Vn2*R1_5*Vp1*R1_5-Vn1*R1_2_5*Vp2*R1_2_5)/(Vn1*R1_2_5*(Vp2-Vn2)-Vn2*R1_5*(Vp1-Vn1))
 * @note      Rn =  (Vp2*R1_2_5*Vn1*R1_2_5-Vp1*R1_5*Vn2*R1_5)/(Vp1*R1_5*(Vn2-Vp2)-Vp2*R1_2_5*(Vn1-Vp1))
 *
 * @note      Rp =  (Vp1*R1_2_5*Vn3-Vp3*R1_5*Vn1)/(Vp3*Vn1-Vp1*Vn3)
 * @note      Rn =  (Vp1*R1_5*Vn2-Vp2*R1_2_5*Vn1)/(Vp2*Vn1-Vp1*Vn2)
 ******************************************************************************
 */
void Imd_Ctrl_CalculateUnVolNew(void) /**< 不换算成电压的计算电阻电阻   */
{
    IMD_CTRL *pImdCtrl = &imdCtrl;
    double tmp[5] = {0};
    float Rp1, Rn1, Rp, Rn, k;
    if (Get_ImdVol() <= 500)
    {
        printf("=========IMDVOL : ===%d===========[%d]==================\n", Get_ImdVol(), __LINE__);
        tmp[0] = pImdCtrl->vP[2] * pImdCtrl->vN[1]; /**< vP,vN都保存的AD加偏移量  */
        tmp[1] = pImdCtrl->vP[1] * pImdCtrl->vN[2];
        tmp[2] = (R3_R4_R5)*tmp[1];
        tmp[3] = (R3_R5)*tmp[0];
        tmp[4] = (tmp[3] - tmp[2]);
        if (tmp[1] != tmp[0])
        {
            tmp[4] /= (tmp[1] - tmp[0]);
            if (tmp[4] < 0.0)
                tmp[4] *= -1.0;
        }
        else
        {
            tmp[4] = 0.0;
        }
        Rn1 = tmp[4];

        tmp[1] = pImdCtrl->vP[1] * R1_R5;
        if (0 == Rn1)
        {
            tmp[2] = 0.0;
        }
        else
        {
            tmp[2] = pImdCtrl->vN[1] + (pImdCtrl->vN[1] * (R3_R5)) / Rn1 - pImdCtrl->vP[1];
        }
        if (0 != tmp[2])
        {
            tmp[1] /= tmp[2];
            if (tmp[1] < 0.0)
                tmp[1] *= -1.0;
        }
        else
        {
            tmp[1] = 0.0;
        }
        Rp1 = tmp[1];
        pImdCtrl->Europek = pImdCtrl->vP[2] / pImdCtrl->vN[2];
        k = (pImdCtrl->vP[1] / pImdCtrl->vN[1]) / (pImdCtrl->vP[2] / pImdCtrl->vN[2]);
        trace(TR_IMD, "vP[1] = %f,vN[1] = %f.\r\n", pImdCtrl->vP[1],
              pImdCtrl->vN[1]);
        trace(TR_IMD, "vP[2] = %f,vN[2] = %f.\r\n", pImdCtrl->vP[2],
              pImdCtrl->vN[2]);
        trace(TR_IMD, "Rp1 = %fK,Rn1 = %fK.\r\n", Rp1, Rn1);
        trace(TR_IMD, "k1 = %f,k2 = %f,k1/k2 :%f\r\n", pImdCtrl->vP[1] / pImdCtrl->vN[1], pImdCtrl->vP[2] / pImdCtrl->vN[2], k);
    }
    else
    {

        printf("=========IMDVOL : ===%d===========[%d]==================\n", Get_ImdVol(), __LINE__);
        tmp[0] = pImdCtrl->vP[3] * pImdCtrl->vN[2];
        tmp[1] = pImdCtrl->vP[2] * pImdCtrl->vN[3];
        tmp[2] = (R1_R2_R5)*tmp[0];
        tmp[3] = (R1_R5)*tmp[1];
        tmp[4] = tmp[2] - tmp[3];
        if (tmp[1] != tmp[0])
        {
            tmp[4] /= (tmp[1] - tmp[0]);
            if (tmp[4] < 0.0)
                tmp[4] *= -1.0;
        }
        else
        {
            tmp[4] = 0.0;
        }
        Rp1 = tmp[4];

        tmp[1] = pImdCtrl->vN[3] * R3_R4_R5;
        if (0 == Rp1)
        {
            tmp[2] = 0.0;
        }
        else
        {
            tmp[2] = pImdCtrl->vP[3] + (pImdCtrl->vP[3] * (R1_R2_R5)) / Rp1 - pImdCtrl->vN[3];
        }
        if (0 != tmp[2])
        {
            tmp[1] /= tmp[2];
            if (tmp[1] < 0.0)
                tmp[1] *= -1.0;
        }
        else
        {
            tmp[1] = 0.0;
        }
        Rn1 = tmp[1];
        pImdCtrl->Europek = pImdCtrl->vP[2] / pImdCtrl->vN[2];
        k = (pImdCtrl->vP[2] / pImdCtrl->vN[2]) / (pImdCtrl->vP[3] / pImdCtrl->vN[3]);
        trace(TR_IMD, "vP[2] = %f,vN[2] = %f.\r\n", pImdCtrl->vP[2],
              pImdCtrl->vN[2]);
        trace(TR_IMD, "vP[3] = %f,vN[3] = %f.\r\n", pImdCtrl->vP[3],
              pImdCtrl->vN[3]);
        trace(TR_IMD, "Rp1 = %fK,Rn1 = %fK.\r\n", Rp1, Rn1);
        trace(TR_IMD, "k1 = %f,k3 = %f,k3/k2 = %f\r\n", pImdCtrl->vP[2] / pImdCtrl->vN[2], pImdCtrl->vP[3] / pImdCtrl->vN[3], k);
    }
#ifdef IMD_CALCULATE_EXP1
    Imd_Res0_Adj(pImdCtrl->vP[0] / pImdCtrl->vN[0],
                 pImdCtrl->vP[2] / pImdCtrl->vN[2],
                 pImdCtrl->vP[3] / pImdCtrl->vN[3],
                 Rp0,
                 Rn0,
                 &Rp,
                 &Rn,
                 TRUE);
    pImdCtrl->imdR0 = 1000 * Rp;
    pImdCtrl->imdR1 = 1000 * Rn;

#ifdef IMD_DEBUG
    printf("Rp = %fK,Rn = %fK.\r\n", pImdCtrl->imdR0 / 1000.0, pImdCtrl->imdR1 / 1000.0);
#endif
#endif
#ifdef IMD_CALCULATE_EXP2
    Imd_Res0_Adj(pImdCtrl->vP[1] / pImdCtrl->vN[1],
                 pImdCtrl->vP[2] / pImdCtrl->vN[2],
                 pImdCtrl->vP[3] / pImdCtrl->vN[3],
                 Rp1,
                 Rn1,
                 &Rp,
                 &Rn,
                 FALSE);
    pImdCtrl->imdR0 = 1000 * Rp;
    pImdCtrl->imdR1 = 1000 * Rn;
#ifdef IMD_DEBUG
    trace(TR_IMD, "Rp = %fK,Rn = %fK.\r\n", pImdCtrl->imdR0 / 1000.0, pImdCtrl->imdR1 / 1000.0);
#endif

#endif
}
// shanfeng's calc illusion
void Imd_Ctrl_CalculateUnVol_SF(void) /**< 不换算成电压的计算电阻电阻   */
{
    IMD_CTRL *pImdCtrl = &imdCtrl;
    double tmp[10];
    float Rp1, Rn1, Rp, Rn, k;
    if (Get_ImdVol() <= 500)
    {
        printf("=========IMDVOL : ===%d===========[%d]==================\n", Get_ImdVol(), __LINE__);
        memset(tmp, 0, 10 * sizeof(double));
        tmp[0] = pImdCtrl->vP[2] * pImdCtrl->vN[1]; /**< vP,vN都保存的AD加偏移量  */
        tmp[1] = pImdCtrl->vP[1] * pImdCtrl->vN[2];
        tmp[2] = (R1_R2_R5) * (R1_R5) * (tmp[1] - tmp[0]);
        tmp[3] = (R1_R5)*tmp[0];
        tmp[4] = (R1_R2_R5)*tmp[1];
        if (tmp[3] != tmp[4])
        {
            tmp[4] = tmp[2] / (tmp[3] - tmp[4]);
            if (tmp[4] < 0.0)
                tmp[4] *= -1.0;
        }
        else
        {
            tmp[4] = 0.0;
        }
        Rp1 = tmp[4];

        memset(tmp, 0, 10 * sizeof(double));
        tmp[0] = pImdCtrl->vP[2] * pImdCtrl->vN[1]; /**< vP,vN都保存的AD加偏移量  */
        tmp[1] = pImdCtrl->vP[1] * pImdCtrl->vN[2];

        tmp[2] = (R3_R5)*tmp[0];
        tmp[3] = (R3_R4_R5)*tmp[1];
        tmp[4] = (tmp[2] - tmp[3]) * R5 * (R1_R5);

        tmp[5] = pImdCtrl->vN[1] * pImdCtrl->vN[2];
        tmp[6] = (R5 * R4) * tmp[5];
        tmp[7] = (R3_R4_R5 * R5) * tmp[1];
        tmp[8] = ((R3_R5)*R5) * tmp[0];
        tmp[9] = tmp[6] - tmp[7] + tmp[8];

        if (tmp[4] != tmp[9])
        {
            tmp[4] /= tmp[9];
            if (tmp[4] < 0.0)
                tmp[4] *= -1.0;
        }
        else
        {
            tmp[4] = 0.0;
        }
        Rp1 = tmp[4];
        k = (pImdCtrl->vP[1] / pImdCtrl->vN[1]) / (pImdCtrl->vP[2] / pImdCtrl->vN[2]);
        trace(TR_IMD, "vP[1] = %f,vN[1] = %f.\r\n", pImdCtrl->vP[1],
              pImdCtrl->vN[1]);
        trace(TR_IMD, "vP[2] = %f,vN[2] = %f.\r\n", pImdCtrl->vP[2],
              pImdCtrl->vN[2]);
        printf("Rp1 = %fK,Rn1 = %fK.\r\n", Rp1, Rn1);
        printf("k1 = %f,k2 = %f,k1/k2 :%f\r\n", pImdCtrl->vP[1] / pImdCtrl->vN[1], pImdCtrl->vP[2] / pImdCtrl->vN[2], k);
    }
    else
    {

        printf("=========IMDVOL : ===%d===========[%d]==================\n", Get_ImdVol(), __LINE__);
        memset(tmp, 0, 10 * sizeof(double));
        tmp[0] = pImdCtrl->vP[3] * pImdCtrl->vN[1];
        tmp[1] = pImdCtrl->vP[1] * pImdCtrl->vN[3];
        tmp[2] = (R1_R2_R5)*tmp[0];
        tmp[3] = (R1_R5)*tmp[1];
        tmp[4] = tmp[2] - tmp[3];
        if (tmp[1] != tmp[0])
        {
            tmp[4] /= (tmp[1] - tmp[0]);
            if (tmp[4] < 0.0)
                tmp[4] *= -1.0;
        }
        else
        {
            tmp[4] = 0.0;
        }
        Rp1 = tmp[4];

        memset(tmp, 0, 10 * sizeof(double));
        tmp[0] = pImdCtrl->vP[1] * pImdCtrl->vN[3];
        tmp[1] = pImdCtrl->vP[3] * pImdCtrl->vN[1];
        tmp[2] = (R1_R5)*tmp[0];
        tmp[3] = (R1_R2_R5)*tmp[1];
        tmp[4] = R5 * (R3_R4_R5) * (tmp[2] - tmp[3]);
        tmp[5] = (pImdCtrl->vP[1] * pImdCtrl->vP[3]);
        tmp[6] = R5 * (tmp[3] - tmp[0]);
        tmp[7] = R2 * R5 * tmp[5];

        if (tmp[6] != tmp[7])
        {
            tmp[4] /= (tmp[6] - tmp[7]);
            if (tmp[4] < 0.0)
                tmp[4] *= -1.0;
        }
        else
        {
            tmp[4] = 0.0;
        }
        Rn1 = tmp[4];

        k = (pImdCtrl->vP[1] / pImdCtrl->vN[1]) / (pImdCtrl->vP[2] / pImdCtrl->vN[2]);
        trace(TR_IMD, "vP[1] = %f,vN[1] = %f.\r\n", pImdCtrl->vP[1],
              pImdCtrl->vN[1]);
        trace(TR_IMD, "vP[3] = %f,vN[2] = %f.\r\n", pImdCtrl->vP[3],
              pImdCtrl->vN[3]);
        printf("Rp1 = %fK,Rn1 = %fK.\r\n", Rp1, Rn1);
        printf("k1 = %f,k3 = %f,k3/k2 = %f\r\n", pImdCtrl->vP[1] / pImdCtrl->vN[1], pImdCtrl->vP[3] / pImdCtrl->vN[3], k);
    }

#ifdef IMD_CALCULATE_EXP1
    Imd_Res0_Adj(pImdCtrl->vP[0] / pImdCtrl->vN[0],
                 pImdCtrl->vP[2] / pImdCtrl->vN[2],
                 pImdCtrl->vP[3] / pImdCtrl->vN[3],
                 Rp0,
                 Rn0,
                 &Rp,
                 &Rn,
                 TRUE);
    pImdCtrl->imdR0 = 1000 * Rp;
    pImdCtrl->imdR1 = 1000 * Rn;

#ifdef IMD_DEBUG
    printf("Rp = %fK,Rn = %fK.\r\n", pImdCtrl->imdR0 / 1000.0, pImdCtrl->imdR1 / 1000.0);
#endif
#endif
#ifdef IMD_CALCULATE_EXP2
    Imd_Res0_Adj(pImdCtrl->vP[1] / pImdCtrl->vN[1],
                 pImdCtrl->vP[2] / pImdCtrl->vN[2],
                 pImdCtrl->vP[3] / pImdCtrl->vN[3],
                 Rp1,
                 Rn1,
                 &Rp,
                 &Rn,
                 FALSE);
    pImdCtrl->imdR0 = 1000 * Rp;
    pImdCtrl->imdR1 = 1000 * Rn;
#ifdef IMD_DEBUG
    printf("Rp = %fK,Rn = %fK.\r\n", pImdCtrl->imdR0 / 1000.0, pImdCtrl->imdR1 / 1000.0);
#endif

#endif
}
/**
 ******************************************************************************
 * @brief     计算绝缘值
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details   不换算成电压的计算方式   此公式中Vp Vn都是对应的AD值加偏移量值，没有真正换算成电压
 * @note      Rp1 = (Vp2*Vn1*R1_2_5-Vp1*Vn2*R1_5)/(Vp1*Vn2-Vp2*Vn1)
 * @note      Rn1 = (Vp3*Vn1*R3_5-Vp1*Vn3*R3_4_5)/(Vp1*Vn3-Vp3*Vn1)
 ******************************************************************************
 */
void Imd_Ctrl_CalculateUnVol(void) /**< 不换算成电压的计算电阻电阻   */
{
    IMD_CTRL *pImdCtrl = &imdCtrl;

    double tmp[5] = {0};
    float Rp1, Rn1, Rp, Rn, k;

    tmp[0] = pImdCtrl->vP[2] * pImdCtrl->vN[1]; /**< vP,vN都保存的AD加偏移量  */
    tmp[1] = pImdCtrl->vP[1] * pImdCtrl->vN[2];
    tmp[2] = (R1_R2_R5)*tmp[0];
    tmp[3] = (R1_R5)*tmp[1];
    tmp[4] = tmp[2] - tmp[3];
    if (tmp[1] != tmp[0])
    {
        tmp[4] /= (tmp[1] - tmp[0]);
        if (tmp[4] < 0.0)
            tmp[4] *= -1.0;
    }
    else
    {
        tmp[4] = 0.0;
    }
    Rp1 = tmp[4];

    //    trace(TR_DEBUG,
    //             "UnVol tmp[0] = %f,tmp[1] = %f,tmp[2] = %f,tmp[3] = %f,tmp[4] = %f.\r\n",
    //             tmp[0], tmp[1], tmp[2], tmp[3], tmp[4]);

    tmp[0] = pImdCtrl->vP[3] * pImdCtrl->vN[1];
    tmp[1] = pImdCtrl->vP[1] * pImdCtrl->vN[3];
    tmp[2] = (R3_R5)*tmp[0];
    tmp[3] = (R3_R4_R5)*tmp[1];
    tmp[4] = tmp[2] - tmp[3];
    if (tmp[1] != tmp[0])
    {
        tmp[4] /= (tmp[1] - tmp[0]);
        if (tmp[4] < 0.0)
            tmp[4] *= -1.0;
    }
    else
    {
        tmp[4] = 0.0;
    }
    Rn1 = tmp[4];
    //    trace(TR_DEBUG,
    //             "UnVol tmp[0] = %f,tmp[1] = %f,tmp[2] = %f,tmp[3] = %f,tmp[4] = %f.\r\n",
    //             tmp[0], tmp[1], tmp[2], tmp[3], tmp[4]);

    k = (pImdCtrl->vP[3] / pImdCtrl->vN[3]) / (pImdCtrl->vP[2] / pImdCtrl->vN[2]);
    trace(TR_IMD, "vP[0] = %f,vN[0] = %f.\r\n", pImdCtrl->vP[0],
          pImdCtrl->vN[0]);
    trace(TR_IMD, "vP[1] = %f,vN[1] = %f.\r\n", pImdCtrl->vP[1],
          pImdCtrl->vN[1]);
    trace(TR_IMD, "vP[2] = %f,vN[2] = %f.\r\n", pImdCtrl->vP[2],
          pImdCtrl->vN[2]);
    trace(TR_IMD, "vP[3] = %f,vN[2] = %f.\r\n", pImdCtrl->vP[3],
          pImdCtrl->vN[3]);
    printf("Rp1 = %fK,Rn1 = %fK.\r\n", Rp1, Rn1);
    printf("k1 = %f,k2 = %f,k3 = %f,k3/k2 = %f\r\n", pImdCtrl->vP[1] / pImdCtrl->vN[1], pImdCtrl->vP[2] / pImdCtrl->vN[2], pImdCtrl->vP[3] / pImdCtrl->vN[3], k);

#ifdef IMD_CALCULATE_EXP2
    Imd_Res0_Adj(pImdCtrl->vP[1] / pImdCtrl->vN[1],
                 pImdCtrl->vP[2] / pImdCtrl->vN[2],
                 pImdCtrl->vP[3] / pImdCtrl->vN[3],

                 Rp1,
                 Rn1,
                 &Rp,
                 &Rn,
                 FALSE);
    pImdCtrl->imdR0 = 1000 * Rp;
    pImdCtrl->imdR1 = 1000 * Rn;
#ifdef IMD_DEBUG
    printf("Rp = %fK,Rn = %fK.\r\n", pImdCtrl->imdR0 / 1000.0, pImdCtrl->imdR1 / 1000.0);
#endif

#endif
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void Imd_Ctrl_JudgInsulate(void)
{
    uint32 fault_res = 0;
    uint32 alarm_res = 0;
    uint32 res = 0;
    if (Get_ImdVol() > 550)
    {
        fault_res = Get_CcuSampleParaInsulateFaultH();
        alarm_res = Get_CcuSampleParaInsulateAlarmH();
    }
    else
    {
        fault_res = Get_CcuSampleParaInsulateFaultL();
        alarm_res = Get_CcuSampleParaInsulateAlarmL();
    }
    res = Get_ImdRes();
    if (res > alarm_res) //
    {
        Imd_Err_Ctrl(IMD_NONE);
        trace(TR_IMD, "充电机绝缘检测完成\n");
    }
    else if (res > fault_res)
    {
        trace(TR_IMD, "充电机绝缘告警\n");
        Imd_Err_Ctrl(IMD_CHECK_ALARM);
    }
    else
    {
        trace(TR_IMD, "充电机绝缘故障\n");
        Imd_Err_Ctrl(IMD_CHECK_ERR);
    }
    trace(TR_IMD,
          "res/vol = %d,ImdVol = %d,alarmL = %d,alarmH = %d,faultL = %d,faultH = %d!\n",
          res, Get_ImdVol(),
          Get_CcuSampleParaInsulateAlarmL(),
          Get_CcuSampleParaInsulateAlarmH(),
          Get_CcuSampleParaInsulateFaultL(),
          Get_CcuSampleParaInsulateFaultH());
}
/**
 ******************************************************************************
 * @brief     绝缘继电器动作
 * @param[in]   开关继电器操作
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
void Imd_Relay_Act(uint8 act)
{
    switch (act)
    {
    case IMDCON_KEON_KPOFF_KNOFF:
        KE_ON_KP_KN_OFF;
        trace(TR_IMD, "[tick = %d ] KE_ON,KP_OFF,KN_OFF\n", tickGet());
        break;
    case IMDCON_KEON_KPON_KNON:
        KE_KP_KN_ON;
        trace(TR_IMD, "[tick = %d ] KE_ON,KP_ON,KN_ON\n", tickGet());
        break;
    case IMDCON_KEON_KPOFF_KNON:
        KE_KN_ON_KP_OFF;
        trace(TR_IMD, "[tick = %d ] KE_ON,KP_OFF,KN_ON\n", tickGet());
        break;
    case IMDCON_KEON_KPON_KNOFF:
        KE_KP_ON_KN_OFF;
        trace(TR_IMD, "[tick = %d ] KE_ON,KP_ON,KN_OFF\n", tickGet());
        break;
    case IMDCON_KEOFF_KPON_KNON:
        KE_OFF_KP_KN_ON;
        trace(TR_IMD, "[tick = %d ] KE_OFF,KP_ON,KN_ON\n", tickGet());
        break;
    case IMDCON_KEOFF_KPON_KNOFF:
        KE_KN_OFF_KP_ON;
        trace(TR_IMD, "[tick = %d ] KE_OFF,KP_ON,KN_OFF\n", tickGet());
        break;
    case IMDCON_KEOFF_KPOFF_KNON:
        KE_KP_OFF_KN_ON;
        trace(TR_IMD, "[tick = %d ] KE_OFF,KP_OFF,KN_ON\n", tickGet());
        break;
    case IMDCON_KEOFF_KPOFF_KNOFF:
        KE_KP_KN_OFF;
        trace(TR_IMD, "[tick = %d ] KE_OFF,KP_OFF,KN_OFF\n", tickGet());
        break;
    default:
        break;
    }
}

/**
 ******************************************************************************
 * @brief     绝缘继电器动作
 * @param[in]   获取采样值
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
bool Imd_Get_VpVn(uint8 act, uint8 rettype)
{
    IMD_CTRL *pImdCtrl = &imdCtrl;
    float dat[2] = {0};

    if (Get_KpKeKn_Vol(K_OFF, rettype, dat))
    {
        switch (act)
        {
        case IMDCON_KEOFF_KPOFF_KNOFF:
            pImdCtrl->vP[0] = dat[0];
            pImdCtrl->vN[0] = dat[1];
            break;
        case IMDCON_KEON_KPON_KNON:
            pImdCtrl->vP[1] = dat[0];
            pImdCtrl->vN[1] = dat[1];
            break;
        case IMDCON_KEON_KPON_KNOFF: // IMDCON_KEON_KPOFF_KNON:
            pImdCtrl->vP[2] = dat[0];
            pImdCtrl->vN[2] = dat[1];
            break;
        case IMDCON_KEON_KPOFF_KNOFF: // IMDCON_KEON_KPOFF_KNON:////
            pImdCtrl->vP[3] = dat[0];
            pImdCtrl->vN[3] = dat[1];
            break;
        case IMDCON_KEOFF_KPON_KNON:
            pImdCtrl->vP[0] = dat[0];
            pImdCtrl->vN[0] = dat[1];
            break;
        case IMDCON_KEOFF_KPON_KNOFF:
            pImdCtrl->vP[0] = dat[0];
            pImdCtrl->vN[0] = dat[1];
            break;
        case IMDCON_KEOFF_KPOFF_KNON:
            pImdCtrl->vP[0] = dat[0];
            pImdCtrl->vN[0] = dat[1];
            break;
        default:
            break;
        }
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

/**
 ******************************************************************************
 * @brief       获取当前绝缘值
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
uint32 Get_ImdRes(void)
{
    IMD_CTRL *pImdCtrl = &imdCtrl;

    uint32 minR = 0;
    if (Get_EnableFlag(eParaFmt_CfgExImdEnable) == FALSE)
    {
        pImdCtrl->imdVol = Get_K1K2InsideVol() / 10.0;
    }
    if (0 == pImdCtrl->imdVol)
    {
        return 0;
    }
    minR = (pImdCtrl->imdR0 >= pImdCtrl->imdR1) ? pImdCtrl->imdR1 : pImdCtrl->imdR0;
    //    minR *= 1000;
    trace(TR_IMD, "ImdVal = %d,minR = %d,Vol = %d\n", minR / pImdCtrl->imdVol, minR, pImdCtrl->imdVol);
    return minR / pImdCtrl->imdVol;
}
uint32 Get_ImdVol(void)
{
    IMD_CTRL *pImdCtrl = &imdCtrl;

    if (Get_EnableFlag(eParaFmt_CfgExImdEnable) == FALSE)
    {
        pImdCtrl->imdVol = Get_K1K2InsideVol() / 10.0;
    }

    return pImdCtrl->imdVol;
}
/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
/**
 ******************************************************************************
 * @brief       获取绝缘继电器断开标记
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
bool_e Get_ImdSwOffFlag(void)
{
    return imdCtrl.imdSwOffFlag;
}

/**
 ******************************************************************************
 * @brief       设置绝缘继电器断开标记
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
void Set_ImdSwOffFlag(uint8 flag)
{
    imdCtrl.imdSwOffFlag = flag;
}

/**
 ******************************************************************************
 * @brief       置绝缘继电器断开标记
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
void Set_ImdSwOffActFlag(void)
{
    imdCtrl.swOffFlag = FALSE;
}
/**
 ******************************************************************************
 * @brief      绝缘阶段设置
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */

uint8 Get_Imd_Stage(void)
{
    return imdCtrl.stage;
}
/**
 ******************************************************************************
 * @brief      绝缘阶段设置
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */

void Set_Imd_Stage(uint8 stage)
{
    imdCtrl.stage = stage;
}
/**
 ******************************************************************************
 * @brief      绝缘检测开始
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
void Imd_Kopen_Start(void)
{
#ifdef IMD_DEBUG
    printf("充电机启动完成,开始绝缘测量\n");
#endif
    Set_Imd_Stage(IMD_KEKPKN_ON);
    Put_ImdSampleStartFlag(TRUE);
}

/**
 ******************************************************************************
 * @brief      绝缘测量平衡桥算法
 * @param[in]   stage 运行阶段
 * @param[in]   rettype   0平均值,1最小值,2是最大值,3是中值.
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
static void Imd_Ctrl_BalanceBridge_Run(uint8 stage, uint8 rettype, uint32 vol)
{
#if 1
    static uint8 actFlag = FALSE;
    static uint32 lastTick = 0;
    if (IMD_KE_ON_KPKN_OFF == stage)
    {
        if (!actFlag)
        {
            Imd_Relay_Act(IMDCON_KEON_KPOFF_KNOFF);
            lastTick = tickGet();
            actFlag = TRUE;
        }
        else
        {
            if (abs(tickGet() - lastTick) > IMD_RELAY_DELAY)
            {
                if (Get_CcuCfgParaEuropeEnable())
                {
                    while (FALSE == Imd_Get_VpVn(IMDCON_KEON_KPOFF_KNOFF, rettype))
                    {
                        taskDelay(0);
                    }
                    if (vol > 500)
                    {
                        Set_Imd_Stage(IMD_CALCULATE);
                        actFlag = FALSE;
                    }
                    else
                    {
                        Set_Imd_Stage(IMD_KEKPKN_ON);
                        actFlag = FALSE;
                    }
                }
                else
                {
                    if (Imd_Get_VpVn(IMDCON_KEON_KPOFF_KNOFF, rettype))
                    {
                        if (vol > 500)
                        {
                            Set_Imd_Stage(IMD_CALCULATE);
                            actFlag = FALSE;
                        }
                        else
                        {
                            Set_Imd_Stage(IMD_KEKPKN_ON);
                            actFlag = FALSE;
                        }
                    }
                }
            }
        }
    }
    else if (IMD_KEKPKN_ON == stage)
    {
        if (!actFlag)
        {
            Imd_Relay_Act(IMDCON_KEON_KPON_KNON);
            lastTick = tickGet();
            actFlag = TRUE;
        }
        else
        {
            if (abs(tickGet() - lastTick) > IMD_RELAY_DELAY)
            {

                if (Get_CcuCfgParaEuropeEnable())
                {
                    while (FALSE == Imd_Get_VpVn(IMDCON_KEON_KPON_KNON, rettype))
                    {
                        taskDelay(0);
                    }
                    if (vol <= 500)
                    {
                        Set_Imd_Stage(IMD_CALCULATE);
                        actFlag = FALSE;
                    }
                    else
                    {
                        Set_Imd_Stage(IMD_KEKN_ON_KP_OFF);
                        actFlag = FALSE;
                    }
                }
                else
                {
                    if (Imd_Get_VpVn(IMDCON_KEON_KPON_KNON, rettype))
                    {
                        if (vol <= 500)
                        {
                            Set_Imd_Stage(IMD_CALCULATE);
                            actFlag = FALSE;
                        }
                        else
                        {
                            Set_Imd_Stage(IMD_KEKN_ON_KP_OFF);
                            actFlag = FALSE;
                        }
                    }
                }
            }
        }
    }
    else if (IMD_KEKN_ON_KP_OFF == stage)
    {
        if (!actFlag)
        {
            Imd_Relay_Act(IMDCON_KEON_KPOFF_KNON);
            lastTick = tickGet();
            actFlag = TRUE;
        }
        else
        {
            if (abs(tickGet() - lastTick) > IMD_RELAY_DELAY)
            {

                if (Get_CcuCfgParaEuropeEnable())
                {
                    while (FALSE == Imd_Get_VpVn(IMDCON_KEON_KPOFF_KNON, rettype))
                    {
                        taskDelay(0);
                    }
                    Set_Imd_Stage(IMD_KEKP_ON_KN_OFF);
                    actFlag = FALSE;
                }
                else
                {
                    if (Imd_Get_VpVn(IMDCON_KEON_KPOFF_KNON, rettype))
                    {
                        Set_Imd_Stage(IMD_KEKP_ON_KN_OFF);
                        actFlag = FALSE;
                    }
                }
            }
        }
    }
    else if (IMD_KEKP_ON_KN_OFF == stage)
    {
        if (!actFlag)
        {
            Imd_Relay_Act(IMDCON_KEON_KPON_KNOFF);
            lastTick = tickGet();
            actFlag = TRUE;
        }
        else
        {
            if ((abs(tickGet() - lastTick) > IMD_RELAY_DELAY) || Get_Imdstartstate())
            {
                if (Get_CcuCfgParaEuropeEnable())
                {
                    while (FALSE == Imd_Get_VpVn(IMDCON_KEON_KPON_KNOFF, rettype))
                    {
                        taskDelay(0);
                    }
                    if (vol <= 500)
                    {
                        Set_Imd_Stage(IMD_KEKPKN_ON);
                        actFlag = FALSE;
                    }
                    else if (vol > 500)
                    {
                        Set_Imd_Stage(IMD_KE_ON_KPKN_OFF);
                        actFlag = FALSE;
                    }
                    else
                    {
                        Set_Imd_Stage(IMD_CALCULATE);
                        actFlag = FALSE;
                    }
                }
                else
                {
                    if (Imd_Get_VpVn(IMDCON_KEON_KPON_KNOFF, rettype))
                    {
                        if (vol <= 500)
                        {
                            Set_Imd_Stage(IMD_KEKPKN_ON);
                            actFlag = FALSE;
                        }
                        else if (vol > 500)
                        {
                            Set_Imd_Stage(IMD_KE_ON_KPKN_OFF);
                            actFlag = FALSE;
                        }
                        else
                        {
                            Set_Imd_Stage(IMD_CALCULATE);
                            actFlag = FALSE;
                        }
                    }
                }
            }
        }
    }
    else if (IMD_CALCULATE == stage)
    {
        if (!Get_CcuCfgParaEuropeEnable())
        {
            Imd_Relay_Act(IMDCON_KEOFF_KPOFF_KNOFF);
        }
        else
        {
            Imd_Relay_Act(IMDCON_KEON_KPON_KNOFF);
        }
#ifndef IMD_CALCULATE_UNVOL
        Imd_Ctrl_Calculate();
#else
        Imd_Ctrl_CalculateUnVolNew();
        Imd_Ctrl_JudgInsulate();
#endif
        Set_Imd_Stage(IMD_FINISH);
    }
#else
    static uint8 actFlag = FALSE;
    static uint32 lastTick = 0;
    if (IMD_KE_ON_KPKN_OFF == stage)
    {
        if (!actFlag)
        {
            Imd_Relay_Act(IMDCON_KEON_KPOFF_KNOFF);
            lastTick = tickGet();
            actFlag = TRUE;
        }
        else
        {
            if (abs(tickGet() - lastTick) > IMD_RELAY_DELAY)
            {
                if (Imd_Get_VpVn(IMDCON_KEON_KPOFF_KNOFF, rettype))
                {
                    Set_Imd_Stage(IMD_KEKPKN_ON);
                    actFlag = FALSE;
                }
            }
        }
    }
    else if (IMD_KEKPKN_ON == stage)
    {
        if (!actFlag)
        {
            Imd_Relay_Act(IMDCON_KEON_KPON_KNON);
            lastTick = tickGet();
            actFlag = TRUE;
        }
        else
        {
            if (abs(tickGet() - lastTick) > IMD_RELAY_DELAY)
            {
                if (Imd_Get_VpVn(IMDCON_KEON_KPON_KNON, rettype))
                {
                    Set_Imd_Stage(IMD_KEKN_ON_KP_OFF);
                    actFlag = FALSE;
                }
            }
        }
    }
    else if (IMD_KEKN_ON_KP_OFF == stage)
    {
        if (!actFlag)
        {
            Imd_Relay_Act(IMDCON_KEON_KPOFF_KNON);
            lastTick = tickGet();
            actFlag = TRUE;
        }
        else
        {
            if (abs(tickGet() - lastTick) > IMD_RELAY_DELAY)
            {
                if (Imd_Get_VpVn(IMDCON_KEON_KPOFF_KNON, rettype))
                {
                    Set_Imd_Stage(IMD_KEKP_ON_KN_OFF);
                    actFlag = FALSE;
                }
            }
        }
    }
    else if (IMD_KEKP_ON_KN_OFF == stage)
    {
        if (!actFlag)
        {
            Imd_Relay_Act(IMDCON_KEON_KPON_KNOFF);
            lastTick = tickGet();
            actFlag = TRUE;
        }
        else
        {
            if (abs(tickGet() - lastTick) > IMD_RELAY_DELAY)
            {
                if (Imd_Get_VpVn(IMDCON_KEON_KPON_KNOFF, rettype))
                {
                    Set_Imd_Stage(IMD_CALCULATE);
                    actFlag = FALSE;
                }
            }
        }
    }
    else if (IMD_CALCULATE == stage)
    {
        Imd_Relay_Act(IMDCON_KEOFF_KPOFF_KNOFF);
#ifndef IMD_CALCULATE_UNVOL
        Imd_Ctrl_Calculate();
#else
        Imd_Ctrl_CalculateUnVolNew();
        Imd_Ctrl_JudgInsulate();
#endif
        Set_Imd_Stage(IMD_FINISH);
    }
#endif
}

/**
 ******************************************************************************
 * @brief      绝缘测量运行阶段
 * @param[in]   stage 运行阶段
 * @param[in]   rettype   0平均值,1最小值,2是最大值,3是中值.
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
static void Imd_Ctrl_Run(uint8 stage, uint8 rettype, uint16 vol)
{

#ifndef IMD_CALCULATE_VOL_SCALE
    Imd_Ctrl_BalanceBridge_Run(stage, rettype, Get_ImdVol());
#else

#endif
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note  在充电中且自动充电过程中，循环检测
 ******************************************************************************
 */
void EuropeImd(void)
{
    IMD_CTRL *pImdCtrl = &imdCtrl;
    float Europek = 0;
    float k = 0;
    if (Get_CcuCfgParaEuropeEnable() && CHARGE_MODE_AUTO == Get_ChargeMode() && FALSE == Get_Imdstartstate())
    {
        Init_Imd();
        Set_Imd_Stage(IMD_KEKP_ON_KN_OFF);
        if (IMD_KEKP_ON_KN_OFF == Get_Imd_Stage())
        {
            while (FALSE == Imd_Get_VpVn(IMDCON_KEON_KPON_KNOFF, ADFILTER_RET_AVG) && FALSE == Get_Imdstartstate() && FALSE == Get_insulationCheckInterval()) // 使用while循环，缩短时间
            {
                {
                    Europek = pImdCtrl->Europek * 10000.0;
                    k = (pImdCtrl->vP[2] / pImdCtrl->vN[2]) * 10000.0; // k值都放大10000进行计算

                    if (abs(Europek - k) > Europek / 10.0) // 变换比例为10%
                    {
                        Set_Imdstartstate(TRUE);
                        printf("====[%s]======[%d]==[startstate :%d]==[tick : %d]===\n", __FUNCTION__, __LINE__, Get_Imdstartstate(), tickGet());
                    }
                    else
                    {
                        Set_Imdstartstate(FALSE);
                    }
                }
                Set_Imd_Stage(IMD_IDLE);
                taskDelay(0); // 防止任务切换时，任务死循环
            }
        }
    }
}

/**
 ******************************************************************************
 * @brief      绝缘检测控制
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */

static void Imd_Ctrl(void)
{
    //    static uint32 chargeStartTick = 0; //绝缘检测阶段充电机启动tick,
    IMD_CTRL *pImdCtrl = &imdCtrl;
    if (IMD_IDLE == pImdCtrl->stage)
    {
        Clr_ErrType(eErrType_ImdErr);
        Clr_ErrType(eErrType_ComErrWithIMD);
        Clr_ErrType(eErrType_ImdAlarm);
        if (CCU_WORK_STATE_CHARGING == Get_WorkState() && ePcuCtrlStage_AdjPara == Get_PcuStage())
        {
            EuropeImd();
            pImdCtrl->EuropeIdle = TRUE;
        }
        else
        {
            if (TRUE == pImdCtrl->EuropeIdle)
            {
                Imd_Relay_Act(IMDCON_KEOFF_KPOFF_KNOFF);
            }
            pImdCtrl->EuropeIdle = FALSE;
        }
    }
    else if (IMD_START == pImdCtrl->stage)
    {
        Init_Imd();
#if 0

        Set_Imd_Stage(IMD_KEKPKN_ON);
#else
        Set_Imd_Stage(IMD_KEKP_ON_KN_OFF);
#endif
    }
    else if ((pImdCtrl->stage >= IMD_KEKPKN_ON) && (pImdCtrl->stage < IMD_FINISH))
    {
        Imd_Ctrl_Run(pImdCtrl->stage, ADFILTER_RET_AVG, Get_ImdVol());
    }
    else
    {
        taskDelay(10);
    }
}

void Imd_Start(void)
{
    Set_Imd_Stage(IMD_START);
}

/**
 ******************************************************************************
 * @brief      绝缘异常处理
 * @param[in]   None
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */

void Imd_Err_Ctrl(uint8 err)
{
    if (err == IMD_CHARGE_TIMEOUT)
    {
        Set_ErrType(eErrType_QStartTimeOut);
    }
    else if (err == IMD_CHECK_TIMEOUT)
    {
        Set_ErrType(eErrType_ImdTimeOut);
        if (Get_CcuCfgParaEuropeEnable())
        {
            Imd_Relay_Act(IMDCON_KEOFF_KPOFF_KNOFF);
        }
    }
    else if (err == IMD_CHECK_ERR)
    {
        Set_ErrType(eErrType_ImdErr);
        if (Get_CcuCfgParaEuropeEnable())
        {
            Imd_Relay_Act(IMDCON_KEOFF_KPOFF_KNOFF);
        }
    }
    else if (err == IMD_CHECK_ALARM)
    {
        Set_ErrType(eErrType_ImdAlarm);
    }
    else if (err == IMD_VOL_ERR)
    {
        Set_ErrType(eErrType_K1K2OutsideVolErr4);
    }
    else if (err == IMD_NONE) /**<无异常*/
    {
        trace(TR_IMD, "绝缘检测通过.\n");
    }
    else if (err == IMD_NCHECK)
    {
        trace(TR_IMD, "不进行绝缘检测.\n");
    }
    else
    {
        return;
    }
}

///**
// ******************************************************************************
// * @brief      绝缘电阻判断
// * @param[in]   None
// * @param[out]  None
// * @retval
// * @details
// * @note
// ******************************************************************************
// */
//
// void Imd_Res_Check(uint32 res)
//{
//    Set_Imd_Stage(IMD_IDLE);
//               if (res > 100)
//               {
//                   if (res <= 500)
//                   {
//                       Set_ErrType(eErrType_ImdAlarm);
//                       trace(TR_ALARM, "绝缘检测异常%d欧姆./V.\r\n", imd_res);
//                   }
//                   pChargeCtrl->imdSuccFlag = TRUE;
//               }
//               else
//               {
//                   Set_ErrType(eErrType_ImdErr);
//                   trace(TR_ALARM, "绝缘检测故障%d欧姆/V.\r\n", imd_res);
//               }
//               Set_WorkState(CCU_WORK_STATE_RELEASE_01);
//               trace(TR_DEBUG, "IMD_END!\n");
//
//}

/**
 ******************************************************************************
 * @brief     IMD命令操作
 * @param[in]   act 开关操作码
 * @param[in]   cmd 控制命令类型
 * @param[in]   rettype 0平均值,1最小值,2是最大值,3是中值.
 * @param[out]  None
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
void Imd_Ctrl_CalibrateCon(uint8 cmd, uint8 act, uint8 caltype)
{
    IMD_CTRL *pImdCtrl = &imdCtrl;
    if (IMD_TEST_OPEN_RELAY == cmd)
    {
        Imd_Relay_Act(act);
    }
    else if (IMD_TEST_READ_VOL == cmd)
    {
        while (Imd_Get_VpVn(act, caltype) == FALSE)
            ;
        {
            taskDelay(10);
        }
    }
    else if (IMD_TEST_SINGLE_CAL == cmd)
    {
        Imd_Relay_Act(IMDCON_KEOFF_KPOFF_KNOFF);
#ifndef IMD_CALCULATE_UNVOL
        Imd_Ctrl_Calculate();
#else
        Imd_Ctrl_CalculateUnVol();
#endif
    }
    else if (IMD_TEST_ACT == cmd)
    {
        printf("pImdCtrl->stage = %d\n", pImdCtrl->stage);
        if ((pImdCtrl->stage == IMD_IDLE) || (pImdCtrl->stage >= IMD_FINISH))
        {
            Set_Imd_Stage(IMD_KE_ON_KPKN_OFF);
            Put_ImdSampleStartFlag(TRUE);
        }
        while (pImdCtrl->stage != IMD_FINISH)
        {
            Imd_Ctrl_Run(pImdCtrl->stage, caltype, Get_ImdVol());
        }
        Set_Imd_Stage(IMD_IDLE);
    }
    else if (IMD_EX_SET == cmd)
    {
        if (act)
        {
            Set_CcuEnableFlag(eParaFmt_CfgExImdEnable);
        }
        else
        {
            Clr_CcuEnableFlag(eParaFmt_CfgExImdEnable);
        }
    }
    else
    {
        Set_Imd_Stage(IMD_IDLE);
        Imd_Relay_Act(IMDCON_KEOFF_KPOFF_KNOFF);
    }
}
/**
 ******************************************************************************
 * @brief       绝缘控制服务
 * @param[in]   调用周期 ms
 * @param[out]  None
 * @retval
 * @details
 * @note        绝缘与触摸屏共用一路485口，绝缘仅在绝缘阶段使用
 ******************************************************************************
 */
void Imd_Server(uint32 ms, int fd)
{
    Imd_Ctrl(); /**<内部绝缘检测*/
}
