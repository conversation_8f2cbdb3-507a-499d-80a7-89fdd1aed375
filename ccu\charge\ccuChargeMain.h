/**
 ******************************************************************************
 * @file       ccuChargeMain.h
 * @brief      API include file of ccuChargeMain.h.
 * @details    This file including all API functions's declare of ccuChargeMain.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef __CCU_CHARGE_MAIN_H__
#define __CCU_CHARGE_MAIN_H__
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <types.h>
#include <ccu\lib\ccuLib.h>
/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/
#define CCU_WORK_STATE_FREE 0x00         /**< 空闲状态 */
#define CCU_WORK_STATE_READY 0x01        /**< 准备状态 */
#define CCU_WORK_STATE_SHAKE_HAND 0x02   /**< 握手状态 */
#define CCU_WORK_STATE_IMD 0x03          /**< 绝缘状态 */
#define CCU_WORK_STATE_RELEASE_01 0x04   /**< 泄放01状态 */
#define CCU_WORK_STATE_RECOGNIZE 0x05    /**< 辨识状态 */
#define CCU_WORK_STATE_CONFIG 0x06       /**< 配置状态 */
#define CCU_WORK_STATE_PRE_CHARGE 0x07   /**< 预充状态 */
#define CCU_WORK_STATE_CHARGING 0x08     /**< 充电中状态 */
#define CCU_WORK_STATE_CHARGE_PAUSE 0x09 /**< 充电暂停状态 */
#define CCU_WORK_STATE_CHARGE_STOP 0x0A  /**< 停止充电 */
#define CCU_WORK_STATE_RELEASE_02 0x0B   /**< 泄放02状态 */
#define CCU_WORK_STATE_STOP_FINISH 0x0C  /**< 停止完成状态 */

#define CHARGE_MODE_AUTO 0x00   /**< 自动充电  */
#define CHARGE_MODE_MANUAL 0x01 /**< 手动充电  */

#define CHARGE_CALL_CYCLE 10

#define ELECK_CLOCK_CHECK_EN 1

/**< EVCCID获取模拟状态*/
typedef enum
{
    SIM_NONE,        /**< 无模拟或模拟已完成 */
    SIM_EVCCID_WAIT, /**< 等待获取EVCCID */
    SIM_EVCCID_DONE, /**< 获取EVCCID完成,准备停机 */
} EVCCID_SIM_STATE;
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
typedef struct CHARGE_CTRL_STRU
{
    uint8 chargeMode; /**< 充电方式(自动或手动)*/

    uint8 stopSrc;     /**< 停止充电发起方*/
    uint32 stopReason; /**< 停止原因 */

    uint32 ChargeStartTick; /**< 充电开始时Tick */

    uint32 imdTick;      /**< 绝缘监测计时器 */
    uint8 imdFinishFlag; /**< 绝缘监测完成标记*/
    uint8 imdStartFlag;  /**< 绝缘监测启动标记*/

    uint32 release01Timer;    /**< 泄放01计时器 */
    uint32 release01ActTimer; /**< 泄放01工作计时器 */
    uint8 release01SuccFlag;  /**< 泄放01完成标记*/

    uint16 BCP_VolFalseCnt; /**< BCP电压错误判断计数(配置)*/
    uint16 BCP_VolTrueCnt;  /**< BCP电压正确判断计数(配置)*/

    uint16 BCP_VolFalse1Cnt; /**< BCP电压错误判断计数(配置)*/
    uint16 BCP_VolFalse2Cnt; /**< BCP电压错误判断计数(配置)*/
    uint16 BCP_VolFalse3Cnt; /**< BCP电压错误判断计数(配置)*/

    uint8 preChargeSuccFlag; /**< 预充成功标记 */

    uint32 chargePauseTimer; /**< 充电暂停计时器 */

    uint32 chargeCurOverBCLCnt; /**< 充电中过流计数器 */

    uint32 chargeVolOverBCLCnt; /**< 充电中过流计数器 */
    uint32 chargeVolOverBCPCnt; /**< 充电中过流计数器 */
    uint32 chargeVolOverSetCnt; /**< 充电中过流计数器 */

    uint32 release02Delay;     /**< 泄放02延时 */
    uint32 release02Timer;     /**< 泄放02计时器 */
    uint32 release02ActTimer;  /**< 泄放02工作计时器 */
    uint8 release02FinishFlag; /**< 泄放02完成标记,不管成功还是失败*/
    uint8 k3k4OffFinishFlag;   /**< K3K4关闭完成*/

    uint32 k1k2OffDelay; /**< 断开K1K2倒计时 */
    uint32 k3k4OffDelay; /**< 断开K3K4倒计时 */

    uint32 stopFinishTimer; /**< 停止完成计时器 */
    uint32 stopFinishFlag;  /**< 停止完成标记 */

    uint32 clrDelay; /**< 停止完成后清状态延时 */

    uint32 chargeTick;    /**< 充电tick*/
    float chargePow;      /**< 充电功率 保留1位小数*/
    uint32 stopTick;      /**< 停止时钟*/
    uint32 chargeTime;    /**< 充电时长*/
    uint32 waitPcuWsTick; /**<PCU工作状态TICK*/

    uint8 K1K2Outside;     /**< 充电方式(自动或手动)*/
    uint8 ElecClock;       /**< 充电方式(自动或手动)*/
    uint16 K1K2OutsideVol; /**< 充电方式(自动或手动)*/
} CHARGE_CTRL;

typedef enum ENUM_CHARGE_STOP_SRC
{
    eChargeStopFlag_Auto = 0x01,   /**< 自动停止 */
    eChargeStopFlag_Manual = 0x02, /**< 手动停止 */
    eChargeStopFlag_Err = 0x03,    /**< 故障停止 */
    eChargeStopFlag_BMS = 0x04,    /**< BMS停止 */
} CHARGE_STOP_SRC;

typedef enum
{
    eLedCtrl_Close = 0x00,   /**<灯灭  */
    eLedCtrl_Twinkle = 0x01, /**< 灯闪烁 */
    eLedCtrl_Open = 0x02,    /**< 灯亮 */
} ENUM_LED_CTRL;

// 电磁锁操作
typedef enum
{
    sElecClock_Unlock = 0x00, /**< 电磁锁断开  */
    sElecClock_Lock = 0x01,   /**< 电磁锁闭合  */
} ElecClockOpr;

typedef enum
{
    // 华北地区
    BEIJING = 110000,        // 北京市
    TIANJIN = 120000,        // 天津市
    HEBEI = 130000,          // 河北省
    SHANXI = 140000,         // 山西省
    INNER_MONGOLIA = 150000, // 内蒙古自治区

    // 华中地区
    HENAN = 410000, // 河南省
    HUBEI = 420000, // 湖北省
    HUNAN = 430000, // 湖南省

    // 华南地区
    GUANGDONG = 440000, // 广东省
    GUANGXI = 450000,   // 广西壮族自治区
    HAINAN = 460000,    // 海南省

    // 西南地区
    CHONGQING = 500000, // 重庆市
    SICHUAN = 510000,   // 四川省
    GUIZHOU = 520000,   // 贵州省
    YUNNAN = 530000,    // 云南省

    // 西北地区
    SHANXI_61 = 610000, // 陕西省
    GANSU = 620000,     // 甘肃省
    QINGHAI = 630000,   // 青海省
} ProvinceCode;
/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
ACT_FLAG Get_ChargeActFlag(void);
void Set_ChargeActFlag(ACT_FLAG actFlag);
bool_e Get_Release01SuccFlag(void);
bool_e Get_Release02FinishFlag(void);
bool_e Get_K3K4FinishFlag(void);
bool_e Get_StopFinishFlag(void);
void Set_StopFinishFlag(uint8 flag);
bool_e Get_ImdSuccFlag(void);
void Set_ImdSuccFlag(bool_e flag);
bool_e Get_ImdStartFlag(void);
void Set_ImdStartFlag(bool_e flag);
bool_e Get_PreChargeSuccFlag(void);
uint8 Get_WorkState(void);
void Set_WorkState(uint8 workState);
void reset_chargeActFlag(uint8 step);
uint32 Get_DemondCur(void);
uint16 Get_DemondVol(void);
void Set_ManualCur(uint32 cur);
void Set_ManualVol(uint32 vol);
CHARGE_STOP_SRC Get_StopSrc(void);
void Set_StopSrc(CHARGE_STOP_SRC src);
void Set_StopReason(uint32 reason);
uint8 Get_ChargeMode(void);
void Set_ChargeMode(uint8 mode);
uint32 Get_ChargeStartTick(void);
void Charge_Task(void);
void Imd_Task(void);
uint16 Get_BatteryVol(void);
uint32 Get_ManualCur(void);
uint32 Get_ManualVol(void);
uint8 k1K2TestFlag(uint8 wr, uint8 flag);
uint8 k3K4TestFlag(uint8 wr, uint8 flag);
uint8 productTestFlag(uint8 wr, uint8 flag);
uint16 Get_ChargeTime(void);
uint16 Get_ChargeEnerge(void);
uint8 Get_ChargeMoudleStartUp(void);
uint8 Get_PcuErrInfo(uint32 pgn);
void Clr_PcuErrInfo(uint32 pgn);
void Set_Imd_Stage(uint8 stage);
uint8 Get_K1K2Outside(void);
void Set_K1K2Outside(uint8 K1K2Out);
uint8 Get_ElecClock(void);
void Set_ElecClock(uint8 Clock);

void Set_K1K2OutsideVol1(uint16 Vol);
uint16 Get_K1K2OutsideVol1(void);
void Set_ElecClockOperate(bool_e flag);
ElecClockOpr
Get_ElecClockOperate(void);

void Set_OutSideVol(uint8 mode);
uint8 Get_OutSideVol(void);

uint8 Get_ReverseConnect(void);
void Set_ReverseConnect(uint8 mode);

void Set_TimeOut(uint8 mode);
uint8 Get_TimeOut(void);
bool_e Check_VehParams(void);
uint8 Get_insulationCheckInterval(void);
void ConfigureLiquidCoolingTask(void);
void LiquidCoolingSendTask(void);
#endif
/*--------------------------End of ccuChargeMain.h----------------------------*/
