/**
 ******************************************************************************
 * @file       tcuMain.h
 * @brief      API include file of tcuMain.h.
 * @details    This file including all API functions's declare of tcuMain.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef __TCU_MAIN_H__
#define __TCU_MAIN_H__
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <types.h>
#include <can.h>
/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/
#define VEHICLE_VALIDATE_ENABLE 1
#define TCU_TIMEOUT_EN 1 /**<tcu超时判断*/
#define NW_VIN
#define TCU_STAGE_MATCH_VER 0       /**< 匹配-版本校验阶段 */
#define TCU_STAGE_MATCH_PARA 1      /**< 匹配-参数配置阶段 */
#define TCU_STAGE_RUN_FREE 2        /**< 运行-Free阶段 */
#define TCU_STAGE_RUN_STARTING 3    /**< 运行-启动中阶段 */
#define TCU_STAGE_RUN_CHARGING 4    /**< 运行-充电中阶段 */
#define TCU_STAGE_RUN_STOPPING 5    /**< 运行-停止中阶段 */
#define TCU_STAGE_RUN_STOP_FINISH 6 /**< 运行-停止完成阶段 */

#define PGN_CHARGE_START 0x000100      /**< 充电启动                  */
#define PGN_CHARGE_START_ACK 0x000200  /**< 启动应答                  */
#define PGN_CHARGE_STOP 0x000300       /**< 充电停止                  */
#define PGN_CHARGE_STOP_ACK 0x000400   /**< 停止应答                  */
#define PGN_TIME_SYN 0x000500          /**< 对时                         */
#define PGN_TIME_SYN_ACK 0x000600      /**< 对时应答 */
#define PGN_VER_CHECK 0x000700         /**< 版本校验 */
#define PGN_VER_CHECK_ACK 0x000800     /**< 版本校验应答 */
#define PGN_SET_PARA 0x000900          /**< 下发充电参数 */
#define PGN_SET_PARA_ACK 0x000A00      /**< 下发充电参数应答 */
#define PGN_SERVE_CTRL 0x000B00        /**< 服务启停控制 */
#define PGN_SERVE_CTRL_ACK 0x000C00    /**< 服务启停控制应答 */
#define PGN_ELECLOCK_CTRL 0x000D00     /**< 电子锁控制 */
#define PGN_ELECLOCK_CTRL_ACK 0x000E00 /**< 电子锁控制应答 */
#define PGN_POWER_CTRL 0x000F00        /**< 功率控制 */
#define PGN_POWER_CTRL_ACK 0x001000    /**< 功率控制应答 */

#define PGN_QUERY_CONFIG 0x006000     /**< 配置信息查询 */
#define PGN_QUERY_CONFIG_ACK 0x006100 /**< 配置信息查询应答 */

#define PGN_START_FINISH 0x001100     /**< 充电启动完成 */
#define PGN_START_FINISH_ACK 0x001200 /**< 充电启动完成应答 */
#define PGN_STOP_FINISH 0x001300      /**< 充电停止完成 */
#define PGN_STOP_FINISH_ACK 0x001400  /**< 充电停止完成应答 */
#define PGN_PILE_STATE 0x001500       /**< 充电桩状态帧 */

#define PGN_CCU_YC 0x002000 /**< CCU遥测帧                  */
#define PGN_TCU_YC 0x002100 /**< TCU遥测帧                  */

#define PGN_CCU_YX1 0x002200 /**< CCU遥信帧                  */
#define PGN_CCU_YX2 0x002300 /**< CCU遥信帧                  */

#define PGN_CAR_CHECK_INFO 0x002D00     /**< CCU发送车辆验证信息 */
#define PGN_CAR_CHECK_INFO_ACK 0x002E00 /**< TCU车辆验证信息应答 */

#define PGN_CAR_VIN_IDENTIFY 0x001700     /**< CCU发送车辆识别信息 */
#define PGN_CAR_VIN_IDENTIFY_ACK 0x001800 /**< TCU车辆识别信息应答 */
#define PGN_CAR_VIN_CONFIRM 0x001900      /**< TCU发送车辆鉴权帧 */
#define PGN_CAR_VIN_CONFIRM_ACK 0x001A00  /**< CCU发送车辆鉴权应答 */

#define PGN_TCU_HEART 0x004000 /**< TCU心跳帧                 */
#define PGN_CCU_HEART 0x004100 /**< CCU心跳帧                 */

#define PGN_TCU_FAULT 0x005100 /**< TCU错误帧                 */
#define PGN_CCU_FAULT 0x005200 /**< CCU错误帧                 */

#ifdef NW_VIN
#define PGN_TCU_VIN 0x007000             /**< VIN启动命令帧               */
#define PGN_TCU_VIN_ACK 0x007100         /**< VIN启动命令应答帧                  */
#define PGN_TCU_VIN_REP 0x001E00         /**< VIN上报命令帧                  */
#define PGN_TCU_VIN_REP_ACK 0x001F00     /**< VIN上报命令应答帧                  */
#define PGN_TCU_VIN_CONFIRM 0x007200     /**< VIN鉴权结果命令帧                  */
#define PGN_TCU_VIN_CONFIRM_ACK 0x007300 /**< VIN鉴权结果应答帧                  */
#endif

#define PGN_FIX_SET 0x008000       /**< 定值设置*/
#define PGN_FIX_SET_ACK 0x008100   /**< 定值设置应答帧*/
#define PGN_FIX_QUERY 0x008200     /**< 定值查询 */
#define PGN_FIX_QUERY_ACK 0x008300 /**< 定值查询应答帧 */

#define PGN_FIX_SET_MUL 0x008400     /**< 定值设置*/
#define PGN_FIX_SET_MUL_ACK 0x008500 /**< 定值设置应答帧*/

#define PGN_DEBUG_CMD 0x008E00     /**< 调试命令 */
#define PGN_DEBUG_CMD_ACK 0x008F00 /**< 调试命令应答 */

#define PGN_RST_CMD 0x009000     /**< 远程复位 */
#define PGN_RST_CMD_ACK 0x009100 /**< 远程复位应答 */

/**< TCU任务调用周期                 */
#define TCU_CALL_CYCLE 2

/**< TCU通信节点地址                 */
#define TCU_ADDR 0x8A
#define CCU_SINGLE_CHARGER_ADDR 0xF6                       /**<单充地址*/
#define CCU_MUL_CHARGER_ADDR 0xE0 + (Get_CcuCfgAddr() % 2) /**<多充地址*/
/**< CAN通道                              */
#define TCU_CHAN CAN_1

/**< TCU协议版本 */
#define TCU_PROTOCOL_VER 0x0110

#define TCU_PLUGANDPAY_SUPPORT_VER 0x0114

#define TCU_PROTOCOL_VER_0130 0x0130

#define TCU_PROTOCOL_VER_0121 0x0121

#define TCU_PROTOCOL_VER_0120 0x0120

#define TCU_PROTOCOL_VER_0114 0x0114

#if VEHICLE_VALIDATE_ENABLE

#ifdef NW_VIN
#define PGN_TCU_CNT 25 // 24//21//18         /**< 接收 TCU的PGN的数量        */
#define PGN_CCU_CNT 28 // 27//24//21         /**< 发送TCUPGN的数量            */
#else
#define PGN_TCU_CNT 21 // 18         /**< 接收 TCU的PGN的数量        */
#define PGN_CCU_CNT 24 // 21         /**< 发送TCUPGN的数量            */
#endif
#else

#define PGN_TCU_CNT 17 /**< 接收 TCU的PGN的数量        */
#define PGN_CCU_CNT 20 /**< 发送TCUPGN的数量            */

#endif

typedef enum
{
    eTcuMulFrame_NotFinish = 0, /***<TCU多帧未完成*/
    eTcuMulFrame_Check_ERR = 1, /***<TCU多帧校验错误 */
    eTcuMulFrame_Check_OK = 2,  /***<TCU多帧校验成功 */
} eTcuMulFrame_Check_State;
/**<即插即充状态枚举*/
typedef enum
{
    eTcuPlugAndPlay_NotEnable = 0x00, /***<即插即充不启用*/
    eTcuPlugAndPlay_Enable = 0x01,    /***<即插即充启用 */
} TcuPlugAndPlayStatus;
/**<启停服务状态枚举*/
typedef enum
{
    eTcuChargeServeCtrl_Stop = 0x01,             /***<TCU充电服务控制停止*/
    eTcuChargeServeCtrl_Enable = 0x02,           /***<TCU充电服务控制启用*/
    eTcuChargeServeCtrl_VehicleCalibrate = 0x03, /***<TCU充电服务车辆验证 */
} TcuChargeServeCtrlStatus;
/**<启停服务状态枚举*/
typedef enum
{
    eTcuVehicleCalibrate_OK = 0x00,        /***<TCU车辆验证成功*/
    eTcuVehicleCalibrate_VinErr = 0x01,    /***<TCU车辆VIN码不存在*/
    eTcuVehicleCalibrate_BindFail = 0x02,  /***<TCU车辆VIN码与用户验证绑定失败 */
    eTcuVehicleCalibrate_OtherFail = 0x03, /***<TCU车辆验证码不在范围 */
    eTcuVehicleCalibrate_AckFail = 0x04,   /***<TCU车辆应答失败*/
    eTcuVehicleCalibrate_Fail = 0x05,      /***<即插即充初始化状态:失败*/
} TcuVehicleCalibrateStatus;

/**<启停服务状态枚举*/
typedef enum
{
    Platform_protocol_Gw = 0x00,  /***<Tcu国网平台*/
    Platform_protocol_Ykc = 0x01, /***<TCU云快充平台*/
    Platform_protocol_Xj = 0x02,  /***<TCU小桔平台 */
    Platform_protocol_Hn = 0x03,  /***<海南平台 */
} PlatformProtocolType;

typedef enum
{
    RST_CCU = 0x01,    /***ccu 直流充电机*/
    RST_AC_CCU = 0x02, /***<交流充电机*/
    RST_PCU = 0x03,    /***<PCU */

    RST_MOD = 0x04,  /**充电模块**/
    RST_SW = 0x05,   /***<开关模块*/
    RST_EICU = 0x06, /***<环境信息模块 */
} ResetSourceType;

typedef enum
{
    VIN_NONE = 0,
    VIN_StartUp = 1,
    Card_Start = 2,
} VIN;
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/

typedef struct
{
    union
    {
        uint8 yx1State;
        struct
        {
            uint8 workState : 2;   /**< 工作状态 */
            uint8 allErr : 1;      /**< 总故障 */
            uint8 allAlarm : 1;    /**< 总告警*/
            uint8 jtErr : 1;       /**< 急停故障   */
            uint8 ygErr : 1;       /**< 烟感故障  */
            uint8 acDlqErr : 1;    /**< 断路器故障  */
            uint8 k1k2JdWdErr : 1; /**< K1K2据动/误动  */
        };
    };
    union
    {
        uint8 yx1ErrData2;
        struct
        {
            uint8 rdqErr : 1;      /**< 融断器故障 */
            uint8 eLockErr : 1;    /**< 电磁锁故障 */
            uint8 plieFanErr : 1;  /**< 充电桩风扇故障*/
            uint8 blqErr : 1;      /**< 避雷器故障   */
            uint8 imdErr : 1;      /**< 绝缘故障*/
            uint8 batRErr : 1;     /**< 电池反接故障  */
            uint8 junErr : 1;      /**< 导引故障 */
            uint8 pileTempErr : 1; /**< 桩过温 */
        };
    };
    union
    {
        uint8 yx1ErrData3;
        struct
        {
            uint8 junTmepErr : 1;     /**< 枪过温故障 */
            uint8 junNoHomeAlarm : 1; /**< 枪未归位 */
            uint8 bmsComErr : 1;      /**< BMS通讯故障*/
            uint8 inVolOverErr : 1;   /**< 输入过压  */
            uint8 inVolLessErr : 1;   /**< 输入欠压*/
            uint8 outVolOverErr : 1;  /**< 输出过压  */
            uint8 outVolLessErr : 1;  /**< 输出欠压  */
            uint8 outCurOverErr : 1;  /**< 输出过流 */
        };
    };
    union
    {
        uint8 yx1ErrData4;
        struct
        {
            uint8 moudleErr : 1;              /**< 充电模块故障 */
            uint8 moudleAcInAlarm : 1;        /**< 模块交流输入故障 */
            uint8 moudleAcInVolOverAlarm : 1; /**< 模块交流输入过压*/
            uint8 moudleAcInVolLessAlarm : 1; /**< 模块交流输入欠压*/
            uint8 moudleAcInPhaseAlarm : 1;   /**< 模块输入缺相*/
            uint8 moudleOutShortAlarm : 1;    /**< 模块输出短路*/
            uint8 moudleOutCurOverAlarm : 1;  /**< 模块输出过流*/
            uint8 moudleOutVolOverAlarm : 1;  /**< 模块输出过压*/
        };
    };
    union
    {
        uint8 yx1ErrData5;
        struct
        {
            uint8 moudleOutVolLessAlarm : 1;  /**< 模块输出欠压 */
            uint8 moudleOutTempOverAlarm : 1; /**< 充电模块输出过温 */
            uint8 moudleComAlarm : 1;         /**< 充电模块通讯告警*/
            uint8 moudleFanAlarm : 1;         /**< 充电模块风扇告警*/
            uint8 junState : 1;               /**< 枪状态*/
            uint8 junHomeState : 1;           /**< 枪座状态*/
            uint8 elockState : 1;             /**< 电子锁状态*/
            uint8 k1k2State : 1;              /**< 直流输出接触器状态*/
        };
    };
    uint8 errBackUpId;
    union
    {
        uint8 yx1ErrData7;
        struct
        {
            uint8 res1 : 1; /**< 备用 */
            uint8 res2 : 1; /**< 备用  */
            uint8 res3 : 1; /**< 备用 */
            uint8 res4 : 1; /**< 备用 */
            uint8 res5 : 1; /**< 备用 */
            uint8 res6 : 1; /**< 备用 */
            uint8 res7 : 1; /**< 备用 */
            uint8 res8 : 1; /**< 备用 */
        };
    };
    //	union
    //	{
    //		uint8 yx1ErrData7;
    //		struct
    //		{
    //			uint8  res9        				:   1;     /**< 备用 */
    //			uint8  res10        			:   1;     /**< 备用  */
    //			uint8  res11       				:   1;     /**< 备用 */
    //			uint8  res12        			:   1;     /**< 备用 */
    //			uint8  res13      				:   1;     /**< 备用 */
    //			uint8  res14    				:   1;     /**< 备用 */
    //			uint8  res15      				:   1;     /**< 备用 */
    //			uint8  res16    				:   1;     /**< 备用 */
    //		};
    //	};
} TCU_YX1_DATA;

typedef struct
{
    union
    {
        uint8 yx2ErrData1;
        struct
        {
            uint8 pileDoorErr : 1;       /**< 桩门禁故障*/
            uint8 k1k2NlErr : 1;         /**< 粘连故障 */
            uint8 imdAlarm : 1;          /**< 绝缘告警*/
            uint8 xfErr : 1;             /**< 泄放故障*/
            uint8 pileTempOverAlarm : 1; /**< 桩体过温告警   */
            uint8 junTempOverAlarm : 1;  /**< 枪过温告警*/
            uint8 acJcqJdWdErr : 1;      /**< 交流接触器据动/误动故障  */
            uint8 acJcqNlErr : 1;        /**< 交流接触器粘连故障  */
        };
    };
    union
    {
        uint8 yx2ErrData2;
        struct
        {
            uint8 fyErr : 1;               /**< 辅源故障*/
            uint8 qJcqJdWdErr : 1;         /**< 桥接接触器据动/误动故障 */
            uint8 qJcqNlErr : 1;           /**< 桥接触器粘连故障*/
            uint8 qJcqState : 1;           /**< 桥接触器状态*/
            uint8 ccuStopErr : 1;          /**< 充电控制器产生的停机   */
            uint8 pileWaterLoggingErr : 1; /**< 充电桩水浸故障*/
            uint8 cabDoorErr : 1;          /**< 充电柜门禁故障  */
            uint8 cabWaterLoggingErr : 1;  /**< 充电柜水浸故障  */
        };
    };
    union
    {
        uint8 yx2ErrData3;
        struct
        {
            uint8 cabYgErr : 1;    /**< 充电柜烟雾*/
            uint8 pcuComErr : 1;   /**< pcu通讯故障 */
            uint8 pcuComAlarm : 1; /**< PCU通讯告警*/
            uint8 pcuErr : 1;      /**< PCU故障*/
            uint8 pcuAlarm : 1;    /**< pcu告警   */
            uint8 swComErr : 1;    /**< 开关通讯故障*/
            uint8 swComAlarm : 1;  /**< 开关通讯告警  */
            uint8 swErr : 1;       /**< 开关模块故障  */
        };
    };
    union
    {
        uint8 yx2ErrData4;
        struct
        {
            uint8 swAlarm : 1;          /**< 开关告警*/
            uint8 bmsErr : 1;           /**< Bms故障 */
            uint8 bmsSendErr : 1;       /**< Bms发送故障*/
            uint8 moudelXfErrAlarm : 1; /**< 充电模块模块泄放故障*/
            uint8 jcjcReq : 1;          /**< 即插即充请求*/
            uint8 acJCQYX : 1;          /**< 交流接触器遥信状态*/
            uint8 acJCQYK : 1;          /**< 交流接触器遥控状态 */
            uint8 k1YK : 1;             /**< K1 遥控  */
        };
    };
    union
    {
        uint8 yx2Data5;
        struct
        {
            uint8 k2YK : 1;      /**< k2遥控*/
            uint8 res : 1;       /**< 预留*/
            uint8 res2 : 1;      /**< 预留*/
            uint8 fyYK : 1;      /**< 辅助电源遥控 */
            uint8 fy2YK : 1;     /**< 辅助电源遥控*/
            uint8 pileFanYK : 1; /**< 柜风机状态*/
            uint8 dzs : 1;       /**< 电子锁*/
            uint8 res3 : 1;      /**< 预留*/
        };
    };
    union
    {
        uint8 yx2Data6;
        struct
        {
            uint8 peErr : 1;           /**< PE断线*/
            uint8 guidErr : 1;         /**< 控制导引故障*/
            uint8 modStartErr : 1;     /**< 模块启动失败*/
            uint8 modCloseErr : 1;     /**< 模块关机失败*/
            uint8 modAdrExcept : 1;    /**< 模块地址异常*/
            uint8 hjErr : 1;           /**< 环境湿度故障*/
            uint8 pileOverTempAlm : 1; /**< 柜过温告警*/
            uint8 pileOverTempErr : 1; /**< 柜过温故障*/
        };
    };
    union
    {
        uint8 yx2Data7;
        struct
        {
            uint8 powAllocateFail : 1;     /**< 功率分配失败*/
            uint8 noMod : 1;               /**< 无可用模块*/
            uint8 preChargeAdjVolFail : 1; /**< 预充调压失败*/
            uint8 pcuComTimeout : 1;       /**< 功率控制模块通讯超时*/
            uint8 hjsdAlm : 1;             /**< 环境湿度告警*/
            uint8 res4 : 1;                /**< 备用*/
            uint8 res5 : 1;                /**< 备用*/
            uint8 res6 : 1;                /**< 备用*/
        };
    };
} TCU_YX2_DATA;

/**< TCU发送控制结构                         */
typedef struct TCU_SEND_CTRL_STRU
{
    uint32 sendRemainTimer; /**< 发送剩余时间 <0>停止发送  0xFFFF<永远发送> */
    uint32 sendStartTimer;  /**< 上次可以发送起始时间 */
    uint8 sendFlag;         /**< 已发送标记 */
    uint32 lastSendTimer;   /**< 上次发送时间 */
} TCU_SEND_CTRL;

/**< TCU接收控制结构                */
typedef struct TCU_RECV_CTRL_STRU
{
    int32 recvTimer;          /**< 接收计时器计时 */
    uint8 recvTimerEnableFlg; /**< 接收计时器使能标记 */
    uint8 recvFlag;           /**< 已接收标记 */
    uint8 recvCheckEnableFlg; /**< 接收计时器检测使能标记 */
} TCU_RECV_CTRL;

typedef struct TCU_CTRL_STRU
{
    uint8 paraSetResult;     /**< 参数下发结果 */
    uint8 paraSetFailReason; /**< 参数下发失败原因 */

    uint8 chargeServeState;          /**< 充电服务状态 */
    uint8 chargeServeCtrl;           /**< 充电服务控制命令 */
    uint8 chargeServeCtrlResult;     /**< 充电服务控制结果 */
    uint8 chargeServeCtrlFailReason; /**< 充电服务控制失败原因 */

    uint8 elecLockNO;         /**< 电磁锁编号 */
    uint8 elecCtrl;           /**< 电磁锁控制命令 */
    uint8 elecCtrlResult;     /**< 电磁锁控制结果 */
    uint8 elecCtrlFailReason; /**< 电磁锁控制失败原因 */

    uint8 powerCtrl;           /**< 功率调节类型 */
    uint16 powerValue;         /**< 功率调节参数 */
    uint32 powerValue_W;       /**< 功率调节参数单位W */
    uint8 powerCtrlResult;     /**< 功率调节结果 */
    uint8 powerCtrlFailReason; /**< 功率调节失败原因 */

    uint8 loadSwitch;            /**< 负荷控制开关 */
    uint8 fy_Switch;             /**< 辅助电源控制开关 */
    uint8 chargeStartResult;     /**< 启动充电结果 */
    uint8 chargeStartFailReason; /**< 启动失败原因 */

    uint8 chargeStartFinishResult;     /**< 启动完成结果 */
    uint8 chargeStartFinishFailReason; /**< 启动完成失败原因 */

    uint8 chargeStopFinishEn; /**< 停止完成帧使能 */

    uint8 chargeStopFinishResult; /**< 停止完成结果 */
    uint8 chargeStopFinishReason; /**< 停止完成原因 */

    uint8 stopFinishFlag; /**<停止完成 */

    uint8 chargeStopReason; /**< 停止原因 */

    uint8 TcuChargeStopReason; /**< TCU停止原因 */ // TODO -xg

    uint8 TcuRcvTimeOutFlag[8]; /**< TCU接收超时标志 */
#if VEHICLE_VALIDATE_ENABLE
    uint8 vehicleValidateFailReason; /**<车辆确认帧 失败原因*/
    uint8 plugAndPlay;               /**<车辆即插即充*/
#endif
    uint16 tcuProtocolVer; /**< TCU协议版本 */
    uint32 chargeTime;     /**< 充电时间 */
    uint32 chargeEnerge;   /**< 充电电量 */

    TCU_YX1_DATA tcuSendYx1; /**< 遥信1 */
    TCU_YX2_DATA tcuSendYx2; /**< 遥信2 */
    TCU_RECV_CTRL tcuRecvCtrl[PGN_TCU_CNT];
    TCU_SEND_CTRL tcuSendCtrl[PGN_CCU_CNT];

    uint8 ChargeGun; // 新增
} TCU_CTRL;

typedef struct
{
    uint8 port;    /**<接口标识，单枪时为0，多枪时从1开始*/
    uint8 devType; /**<设备类型*/
    uint8 comAdr;  /**<设备通讯地址*/
    uint8 cmd;
    uint16 index; /**<序号*/
    uint8 buf[128];
    uint8 len;
    uint8 sendEn;
} TCU_DEBUG;

typedef struct
{
    uint32 pgn;
    uint8 Class;
    uint8 Counter;
} Multiframe_Flag;

typedef enum
{
    FY_12V = 0,
    FY_24V = 1,
} FY_State;
/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */
TCU_DEBUG tcuDebug;
/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
uint8 Get_TcuStage(void);

void Set_TcuStage(uint8 stage);

void Set_CcuHeartState(uint8 state);

uint8 Get_CcuHeartState(void);

uint8 Get_TcuHeartState(void);

void Set_TcuHeartState(uint8 state);

uint16 Get_TcuChargeTime(void);
uint16 Get_TcuChargeEnerge(void);

bool_e Get_TcuStopFinishFlag(void);

void Get_Yx_Date(void);

void tcu_FixPara_Init();

uint8 Get_CcuToTcuAddr(void);

bool_e Tcu_GetVehicleValidate(void);

bool_e Get_TcuVerPlugAndPaySupport(void);

bool_e
Check_StartFinishResaon(void);

uint8 Get_StopFinishReason(void);

void MatchVer_Listen(void);

void Check_StopFinishReason(void);

uint16 Get_TcuProtocolVer(void);

void Set_MultiframeFlag(CAN_DATA *pCanData, uint8 flag);

uint8 Check_MultiframeFlag(CAN_DATA *pCanData);

void Clr_MultiframeFlag(void);

uint8 Get_MulFrameCnt(CAN_DATA *pCanData);

void Set_MulFrameCnt(CAN_DATA *pCanData, uint8 flag);
void Clr_MulFrameCnt(void);

uint8 Get_VIN_StartFlag(void);
void Set_VIN_StartFlag(uint8 flag);
uint8 Get_RstCmd(void);
uint8 Get_Fy_State(void);
void Set_RstCmd(uint8 state);

uint8 Get_TcuPlugAndPlay(void);
#endif //__TCU_MAIN_H__

/*--------------------------End of tcuMain.h----------------------------*/
