/**
 ******************************************************************************
 * @file      tcuSendCtrl.c
 * @brief     C Source file of tcuSendCtrl.c.
 * @details   This file including all API functions's
 *            implement of tcuSendCtrl.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <stddef.h>
#include <sxlib.h>
#include <string.h>
#include <tmLib.h>
#include <bcdLib.h>
#include <gpio.h>
#include <stdio.h>
#include <stdlib.h>
#include <taskLib.h>
#include <trace.h>
#include <maths.h>
#include <ccu/charge/ccuChargeMain.h>
#include <ccu/bsn/deviceState.h>
#include <ccu/bsn/io.h>
#include <ccu/bsn/sample.h>
#include <ccu\lib\ccuLib.h>
#include <ccu\para\para.h>
#include "tcuMain.h"
#include "tcuRecvCtrl.h"
#include "tcuSendCtrl.h"
#include <bms.h>
#include <bcdLib.h>
#include <inc/public.h>
#include <ccu/bms/bmsMain.h>
#include <ccu/pcu/pcuMain.h>
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
#define TCU_WORK_STATE_FREE 0   /**< 待机 */
#define TCU_WORK_STATE_WORK 1   /**< 工作 */
#define TCU_WORK_STATE_FINISH 2 /**< 充电完成 */
#define TCU_WORK_STATE_PAUSE 3  /**< 充电暂停 */

#define MAX_CUTFRAME_BUF_SIZE 128 /**< 分帧最大长度 */
#define MAX_CUTFRAME_BUF_CNT 5    /**< 分帧最多分帧种类 */
#define MAX_DIGITS 3              // 假设整数最多有10位数字（对于32位整数通常是足够的）
#define RESULT_SIZE (17)          // 假设最多有16个整数，每个整数最多MAX_DIGITS位
typedef uint16 (*PSendCycleFunc)(void);
typedef void (*PsendFunc)(uint8 *pOutBuf, uint8 *pOutLen);
typedef struct TCU_SEND_DEAL_STRU
{
    uint32 pgn;             /**< 参数组编号 */
    uint8 prio;             /**< 优先权 */
                            //    uint16          sendCycle;      /**< 发送周期 */
    PSendCycleFunc pSCycle; /**< 发送周期 */
    PsendFunc pSendFunc;    /**< 发送函数 */
} TCU_SEND_DEAL;

typedef struct UP_ERR_STRU
{
    uint16 errType;
    uint8 bit;
    uint32 pgn;
} UP_ERR;

typedef struct CUT_FRAME_CTRL_STRU
{
    CAN_ID canID;
    uint8 frameCnt;
    uint8 framePos;
    uint8 dataBuf[MAX_CUTFRAME_BUF_SIZE];
} CUT_FRAME_CTRL;

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
extern TCU_CTRL tcuCtrl;
extern uint8 tcuErroInfo[7];
static CUT_FRAME_CTRL tcuCutFrameCtrl[MAX_CUTFRAME_BUF_CNT];

/**
 *  TCU发送fifo
 */
#pragma pack(1)

typedef struct _TCU_FIFO
{
    uint8 port;
    uint32 canId;
    uint8 buf[8];
} TCU_FIFO;

#pragma pack()

typedef struct
{
    uint16 head;
    uint16 tail;
    TCU_FIFO tcufifo[256];
};

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/

static void Send_ChargeStartACK(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_ChargeStopACK(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_TimeSynACK(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_VerCheckACK(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_SetParaACK(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_ServeCtrlACK(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_ElecLockCtrlACK(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_PowerCtrlACK(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_QueryConfigACK(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_StartFinish(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_StopFinish(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_PileState(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YC(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YX1(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_YX2(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_Heart(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_Fault(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_TcuFixSetAck(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_TcuFixQueryAck(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_Vehicle_Validate(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_CarVinIdentify(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_CarVinConfirmAck(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_TcuDebugAck(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_TcuRstAck(uint8 *pOutBuf, uint8 *pOutLen);
static uint16 Tcu_Cycle_250ms(void);
static uint16 Tcu_Cycle_500ms(void);
static uint16 Tcu_Cycle_1000ms(void);
static uint16 Tcu_Cycle_YC(void);
static uint16 Tcu_Cycle_YX(void);

static void Send_VinRep(uint8 *pOutBuf, uint8 *pOutLen);
static void Send_VinConfirmAck(uint8 *pOutBuf, uint8 *pOutLen);
const TCU_SEND_DEAL TCU_SEND_DEAL_TABLE[] =
    {
        {PGN_CHARGE_START_ACK, 4, /*250*/ Tcu_Cycle_250ms, Send_ChargeStartACK},
        {PGN_TCU_VIN_ACK, 4, /*250*/ Tcu_Cycle_250ms, Send_ChargeStartACK},
        {PGN_CHARGE_STOP_ACK, 4, /*250*/ Tcu_Cycle_250ms, Send_ChargeStopACK},
        {PGN_TIME_SYN_ACK, 6, /*500*/ Tcu_Cycle_500ms, Send_TimeSynACK},
        {PGN_VER_CHECK_ACK, 6, /*500*/ Tcu_Cycle_500ms, Send_VerCheckACK},
        {PGN_SET_PARA_ACK, 6, /*500*/ Tcu_Cycle_500ms, Send_SetParaACK},
        {PGN_SERVE_CTRL_ACK, 4, /*250*/ Tcu_Cycle_250ms, Send_ServeCtrlACK},
        {PGN_ELECLOCK_CTRL_ACK, 4, /*250*/ Tcu_Cycle_250ms, Send_ElecLockCtrlACK},
        {PGN_POWER_CTRL_ACK, 4, /*250*/ Tcu_Cycle_250ms, Send_PowerCtrlACK},
        {PGN_QUERY_CONFIG_ACK, 6, /*500*/ Tcu_Cycle_500ms, Send_QueryConfigACK},
        {PGN_START_FINISH, 4, /*250*/ Tcu_Cycle_250ms, Send_StartFinish},
        {PGN_STOP_FINISH, 4, /*250*/ Tcu_Cycle_250ms, Send_StopFinish},
        {PGN_PILE_STATE, 6, /*500*/ Tcu_Cycle_500ms, Send_PileState},
        {PGN_CCU_YC, 6, /*1000*/ Tcu_Cycle_YC, Send_YC},
        {PGN_CCU_YX1, 4, /*500*/ Tcu_Cycle_YX, Send_YX1},
        {PGN_CCU_YX2, 4, /*500*/ Tcu_Cycle_YX, Send_YX2},
        {PGN_CCU_HEART, 6, /*1000*/ Tcu_Cycle_1000ms, Send_Heart},
        {PGN_CCU_FAULT, 4, /*250*/ Tcu_Cycle_250ms, Send_Fault},
        {PGN_FIX_SET_ACK, 6, /*250*/ Tcu_Cycle_250ms, Send_TcuFixSetAck},
        {PGN_FIX_QUERY_ACK, 6, /*250*/ Tcu_Cycle_250ms, Send_TcuFixQueryAck},
        {PGN_FIX_SET_MUL_ACK, 6, /*250*/ Tcu_Cycle_250ms, Send_TcuFixSetAck},
#if VEHICLE_VALIDATE_ENABLE
        {PGN_CAR_CHECK_INFO, 6, /*1000*/ Tcu_Cycle_1000ms, Send_Vehicle_Validate},
        {PGN_CAR_VIN_IDENTIFY, 6, /*1000*/ Tcu_Cycle_1000ms, Send_CarVinIdentify},
        {PGN_CAR_VIN_CONFIRM_ACK, 6, /*1000*/ Tcu_Cycle_1000ms, Send_CarVinConfirmAck},
        {PGN_TCU_VIN_REP, 4, /*1000*/ Tcu_Cycle_250ms, Send_VinRep},
        {PGN_TCU_VIN_CONFIRM_ACK, 4, /*1000*/ Tcu_Cycle_250ms, Send_VinConfirmAck},
#endif
        {PGN_DEBUG_CMD_ACK, 6, /*250*/ Tcu_Cycle_250ms, Send_TcuDebugAck},
        {PGN_RST_CMD_ACK, 6, /*250*/ Tcu_Cycle_1000ms, Send_TcuRstAck},
};

static uint16 Tcu_Cycle_250ms(void)
{
    return 250;
}

static uint16 Tcu_Cycle_500ms(void)
{
    return 500;
}

static uint16 Tcu_Cycle_1000ms(void)
{
    return 1000;
}

static uint16 Tcu_Cycle_YC(void)
{
    CONFIG_PARA strCfgPara;
    Get_PilePara((void *)&strCfgPara, eParaType_ConfigPara);
    return strCfgPara.ccuAndTcuYcCycle * 1000;
}

static uint16 Tcu_Cycle_YX(void)
{
    CONFIG_PARA strCfgPara;
    Get_PilePara((void *)&strCfgPara, eParaType_ConfigPara);
    return strCfgPara.ccuAndTcuYxCycle * 500;
}
bool convertNumbersToAsciiChars(int32_t *arr, uint32_t size, char *result, uint32_t *resultSize)
{
    *resultSize = 0;
    for (uint32_t i = 0; i < size; i++)
    {
        int32_t num = arr[i];
        char temp[MAX_DIGITS] = {0}; // +1 是为了内部方便处理（虽然最终不会用到）
        uint32_t index = 0;
        // 将整数转换为字符，逆序存储
        bool isNegative = false;
        if (num < 0)
        {
            isNegative = true;
            num = -num; // 转换为正数处理
        }
        do
        {
            temp[index++] = (num % 10) + '0';
            num /= 10;

        } while (num > 0);

        if ((index + (isNegative ? 1 : 0) > MAX_DIGITS))
        {
            continue;
        }
        // 如果需要，添加负号
        if (isNegative)
        {
            temp[index++] = '-';
        }
        // 检查是否会溢出结果缓冲区
        if (*resultSize + index + (isNegative ? 1 : 0) > RESULT_SIZE)
        {
            return false; // 缓冲区溢出，返回错误
        }

        // 将转换后的字符串（逆序）复制到结果缓冲区
        for (uint32_t j = 0; j < index; j++)
        {
            result[(*resultSize)++] = temp[index - j - 1];
        }
    }
    trace(TR_BMS_PROCESS, "<rx 02, convertNumbersToAsciiChars> : ");
    trace_buf(TR_BMS_PROCESS, result, *resultSize);
    return true; // 成功转换
}

BOOL hex2ascii(const uint8_t *pbyte, char *pstr, size_t len)
{
    // 预定义的十六进制字符数组
    static const char hexChars[] = "0123456789ABCDEF";
    if (2 * len > 12)
    {
        return FALSE;
    }
    for (size_t i = 0; i < len; i++)
    {
        pstr[i * 2] = hexChars[(pbyte[i] >> 4) & 0x0F];
        pstr[i * 2 + 1] = hexChars[pbyte[i] & 0x0F];
    }
    printf("=============================BRM:");
    for (uint8 i = 0; i < 2 * len; i++)
    {
        printf("%c", pstr[i]);
    }
    printf("\n");
    //    for (size_t j = 0; j < 17-2*len; j++)
    //    {
    //        pstr[len * 2 + j] = '0';
    //    }
    return TRUE;
    // 添加字符串结束符
    //    pstr[len * 2] = '\0';
}
/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
TCU_SEND_CTRL *Get_TcuSendCtrl(uint32 pgn)
{
    switch (pgn)
    {
    case PGN_CHARGE_START_ACK:
        return &tcuCtrl.tcuSendCtrl[0];

    case PGN_CHARGE_STOP_ACK:
        return &tcuCtrl.tcuSendCtrl[1];

    case PGN_TIME_SYN_ACK:
        return &tcuCtrl.tcuSendCtrl[2];

    case PGN_VER_CHECK_ACK:
        return &tcuCtrl.tcuSendCtrl[3];

    case PGN_SET_PARA_ACK:
        return &tcuCtrl.tcuSendCtrl[4];

    case PGN_SERVE_CTRL_ACK:
        return &tcuCtrl.tcuSendCtrl[5];

    case PGN_ELECLOCK_CTRL_ACK:
        return &tcuCtrl.tcuSendCtrl[6];

    case PGN_POWER_CTRL_ACK:
        return &tcuCtrl.tcuSendCtrl[7];

    case PGN_QUERY_CONFIG_ACK:
        return &tcuCtrl.tcuSendCtrl[8];

    case PGN_START_FINISH:
        return &tcuCtrl.tcuSendCtrl[9];

    case PGN_STOP_FINISH:
        return &tcuCtrl.tcuSendCtrl[10];

    case PGN_CCU_YC:
        return &tcuCtrl.tcuSendCtrl[11];

    case PGN_CCU_YX1:
        return &tcuCtrl.tcuSendCtrl[12];

    case PGN_CCU_YX2:
        return &tcuCtrl.tcuSendCtrl[13];

    case PGN_CCU_HEART:
        return &tcuCtrl.tcuSendCtrl[14];

    case PGN_CCU_FAULT:
        return &tcuCtrl.tcuSendCtrl[15];
    case PGN_PILE_STATE:
        return &tcuCtrl.tcuSendCtrl[16];
    case PGN_FIX_SET_ACK:
        return &tcuCtrl.tcuSendCtrl[17];
    case PGN_FIX_QUERY_ACK:
        return &tcuCtrl.tcuSendCtrl[18];

#if VEHICLE_VALIDATE_ENABLE
    case PGN_CAR_CHECK_INFO:
        return &tcuCtrl.tcuSendCtrl[19];
    case PGN_DEBUG_CMD_ACK:
        return &tcuCtrl.tcuSendCtrl[20];
    case PGN_CAR_VIN_IDENTIFY:
        return &tcuCtrl.tcuSendCtrl[21];
    case PGN_CAR_VIN_CONFIRM_ACK:
        return &tcuCtrl.tcuSendCtrl[22];
#else
    case PGN_DEBUG_CMD_ACK:
        return &tcuCtrl.tcuSendCtrl[19];
#endif
    case PGN_RST_CMD_ACK:
        return &tcuCtrl.tcuSendCtrl[23];

    case PGN_TCU_VIN_ACK:
        return &tcuCtrl.tcuSendCtrl[24];
    case PGN_TCU_VIN_REP:
        return &tcuCtrl.tcuSendCtrl[25];
    case PGN_TCU_VIN_CONFIRM_ACK:
        return &tcuCtrl.tcuSendCtrl[26];
    case PGN_FIX_SET_MUL_ACK:
        return &tcuCtrl.tcuSendCtrl[27];
    default:
        break;
    }

    return NULL;
}

/**
 ******************************************************************************
 * @brief       充电启动应答帧发送组帧
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void Send_ChargeStartACK(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    if (0xAA == pTcuCtrl->ChargeGun && Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
    {
        pBuf[index++] = 0xAA;
    }
    else
    {
        pBuf[index++] = Get_TcuJunctorId();
    }
    pBuf[index++] = pTcuCtrl->loadSwitch;
    pBuf[index++] = pTcuCtrl->chargeStartResult;
    pBuf[index++] = pTcuCtrl->chargeStartFailReason;

    if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120) && (Get_CcuCfgParaPlatform_convert() != Platform_protocol_Hn)) // 支持TCU协议版本[V1.14,V1.20)
    {
        if (TRUE == pTcuCtrl->plugAndPlay)
        {
            pBuf[index++] = 0x01; // 0x00-非即插即充  0x01-即插即充
        }
        else
        {
            pBuf[index++] = 0x00;
        }
    }
    else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120 && (Get_CcuCfgParaPlatform_convert() != Platform_protocol_Hn)) // 支持TCU协议版本[V1.20)
    {
        if (TRUE == pTcuCtrl->plugAndPlay)
        {
            pBuf[index++] = 0x02; // 0x01-非即插即充  0x02-即插即充
        }
        else
        {
            pBuf[index++] = 0x01;
        }
    }

    pOutLen[0] = index;
    if (Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
    {
        trace(TR_TCU_PROCESS, "<tx 02 71 , Send_ChargeStartACK> : ");
        trace_buf(TR_TCU_PROCESS, pBuf, index);
    }

    return;
}

static void Send_ChargeStopACK(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    if (0xAA == pTcuCtrl->ChargeGun && Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
    {
        pBuf[index++] = 0xAA;
    }
    else
    {
        pBuf[index++] = Get_TcuJunctorId();
    }
    pBuf[index++] = 0x00;

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief       心跳帧发送组帧
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void
Send_Heart(uint8 *pOutBuf, uint8 *pOutLen)
{
    static uint8 seq = 0;
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    pBuf[index++] = Get_TcuJunctorId();
    pBuf[index++] = seq++;
    pBuf[index++] = Get_CcuHeartState();
    pBuf[index++] = pTcuCtrl->chargeServeState;

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief       版本校验应答帧发送组帧
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void Send_VerCheckACK(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    pBuf[index++] = Get_TcuJunctorId();
    //    pBuf[index++] = (uint8)(TCU_PROTOCOL_VER);
    //    pBuf[index++] = (uint8)(TCU_PROTOCOL_VER >> 8);
    if (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER)
    {
        pTcuCtrl->tcuProtocolVer = TCU_PROTOCOL_VER;
    }
    pBuf[index++] = pTcuCtrl->tcuProtocolVer;
    pBuf[index++] = pTcuCtrl->tcuProtocolVer >> 8;
    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief       下发充电参数应答帧发送组帧
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void Send_SetParaACK(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    pBuf[index++] = Get_TcuJunctorId();

    pBuf[index++] = pTcuCtrl->paraSetResult;
    pBuf[index++] = pTcuCtrl->paraSetFailReason;

    pOutLen[0] = index;
    return;
}

/**
 ******************************************************************************
 * @brief       对时应答帧发送组帧
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void
Send_TimeSynACK(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    CP56TIME2A *pDate = NULL;

    pBuf[index++] = Get_TcuJunctorId();

    pDate = (CP56TIME2A *)(&pBuf[index]);

    Uint16ToTwoUint8(pDate->msec, time_of_now(0) * 1000);
    pDate->min = time_of_now(1);
    pDate->hour = time_of_now(2);
    pDate->mday = time_of_now(3);
    pDate->wday = time_of_now(6);
    pDate->month = time_of_now(4);
    pDate->year = time_of_now(5);
    index += 7;

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief       充电服务控制应答帧发送组帧
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void
Send_ServeCtrlACK(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    pBuf[index++] = Get_TcuJunctorId();

    pBuf[index++] = pTcuCtrl->chargeServeCtrl;
    pBuf[index++] = pTcuCtrl->chargeServeCtrlResult;
    pBuf[index++] = pTcuCtrl->chargeServeCtrlFailReason;

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief       电磁锁控制应答帧发送组帧
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void
Send_ElecLockCtrlACK(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    pBuf[index++] = Get_TcuJunctorId();

    pBuf[index++] = pTcuCtrl->elecLockNO;
    pBuf[index++] = pTcuCtrl->elecCtrl;
    pBuf[index++] = pTcuCtrl->elecCtrlResult;
    pBuf[index++] = pTcuCtrl->elecCtrlFailReason;

    pOutLen[0] = index;
    return;
}

static void Send_PowerCtrlACK(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    // 内容需外部参数支持
    pBuf[index++] = Get_TcuJunctorId();
    pBuf[index++] = pTcuCtrl->powerCtrl;

    if (pTcuCtrl->powerCtrl == 0x01) // 绝对值使用偏移量
    {
        Uint16ToTwoUint8(&pBuf[index], abs(pTcuCtrl->powerValue + 10000)); // 开普修改
    }
    else
    {
        Uint16ToTwoUint8(&pBuf[index], abs(pTcuCtrl->powerValue)); // 开普修改
    }

    index = index + 2;

    pBuf[index++] = pTcuCtrl->powerCtrlResult;
    pBuf[index++] = pTcuCtrl->powerCtrlFailReason;

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief       充电配置信息查询应答帧帧发送组帧
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void Send_QueryConfigACK(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    OPERATE_PARA strOperatePara;
    CHARGE_PARA strChargePara;

    Get_PilePara((void *)&strOperatePara, eParaType_OperatePara);
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    pBuf[index++] = Get_TcuJunctorId();
    uint8 dat[4];
    memcpy(&pBuf[index], strOperatePara.factoryCode,
           sizeof(strOperatePara.factoryCode));
    index += sizeof(strOperatePara.factoryCode);

    memcpy(&pBuf[index], strOperatePara.deviceType,
           sizeof(strOperatePara.deviceType));
    index += sizeof(strOperatePara.deviceType);

    pBuf[index++] = 0x02; /**2015国标*/

    *(uint32 *)dat = hex2bcd(FourUint8ToUint32(strOperatePara.factoryCode));
    memcpy(&pBuf[index], dat, sizeof(strOperatePara.factoryCode));
    index += sizeof(strOperatePara.factoryCode);

    *(uint16 *)dat = hex2bcd(TwoUint8ToUint16(strOperatePara.produceYear));
    memcpy(&pBuf[index], dat, sizeof(strOperatePara.produceYear));
    index += sizeof(strOperatePara.produceYear);

    *(uint16 *)dat = hex2bcd(TwoUint8ToUint16(strOperatePara.produceBatch));
    memcpy(&pBuf[index], dat, sizeof(strOperatePara.produceBatch));
    index += sizeof(strOperatePara.produceBatch);

    *(uint32 *)dat = hex2bcd(FourUint8ToUint32(strOperatePara.produceSerial));
    memcpy(&pBuf[index], dat, sizeof(strOperatePara.produceSerial));
    index += sizeof(strOperatePara.produceSerial);

#if 1
    pBuf[index++] = BREAK_UINT16(CCU_HARDWARE_VERSION, 0);
    pBuf[index++] = BREAK_UINT16(CCU_HARDWARE_VERSION, 1);

    pBuf[index++] = BREAK_UINT32(CCU_SOFTWARE_VERSION, 0);
    pBuf[index++] = BREAK_UINT32(CCU_SOFTWARE_VERSION, 1);
    pBuf[index++] = BREAK_UINT32(CCU_SOFTWARE_VERSION, 2);

    pBuf[index++] = BREAK_UINT32(EVC_SOFTWARE_DATE, 2);
    pBuf[index++] = BREAK_UINT32(EVC_SOFTWARE_DATE, 3);
    pBuf[index++] = BREAK_UINT32(EVC_SOFTWARE_DATE, 1);
    pBuf[index++] = BREAK_UINT32(EVC_SOFTWARE_DATE, 0);
#else
    pBuf[index++] = strOperatePara.hardVer[1];
    pBuf[index++] = strOperatePara.hardVer[0];

    pBuf[index++] = strOperatePara.softVer[2];
    pBuf[index++] = strOperatePara.hardVer[1];
    pBuf[index++] = strOperatePara.hardVer[0];

    pBuf[index++] = strOperatePara.softDate[2];
    pBuf[index++] = strOperatePara.softDate[3];
    pBuf[index++] = strOperatePara.softDate[1];
    pBuf[index++] = strOperatePara.softDate[0];
#endif

    memcpy(&pBuf[index], strChargePara.maxOutputVoltage,
           sizeof(strChargePara.maxOutputVoltage));
    index += sizeof(strChargePara.maxOutputVoltage);

    memcpy(&pBuf[index], strChargePara.minOutputVoltage,
           sizeof(strChargePara.minOutputVoltage));
    index += sizeof(strChargePara.minOutputVoltage);

    Uint16ToTwoUint8(&pBuf[index], 4000 - (FourUint8ToUint32(
                                               strChargePara.maxOutputCurrent) /
                                           100));
    index += 2;

    Uint16ToTwoUint8(&pBuf[index], 4000 - (FourUint8ToUint32(
                                               strChargePara.minOutputCurrent) /
                                           100));
    index += 2;

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief       发送车辆VIN验证
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */

static void Send_Vehicle_Validate(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    BRM_DATA strBrm;
    BCP_DATA strBcp;
    Get_BMS_Data(BMS_PGN_BRM, (void *)&strBrm);
    Get_BMS_Data(BMS_PGN_BCP, (void *)&strBcp);
    pBuf[index++] = Get_TcuJunctorId();
    if (Get_CcuCfgParaEuropeEnable())
    {
        uint32_t size = 6;
        uint8 VIN[17] = {0}; // 初始化 VIN 数组为全0
        if (hex2ascii(&strBrm.ec.evccId[1], VIN, size))
        {
            memcpy(&pBuf[index], VIN, 17); // 确保 index + 17 不超出 pBuf 的范围
        }
        else
        {
            memset(VIN, 0, 17);
            memcpy(&pBuf[index], VIN, 17); // 确保 index + 17 不超出 pBuf 的范围
        }
    }
    else
    {
        memcpy(&pBuf[index], strBrm.gbt.VIN, 17);
    }
    index += 17;
    memcpy(&pBuf[index], strBrm.gbt.chargeTimes, 3);
    index += 3;
    memcpy(&pBuf[index], strBcp.SOC, 2);
    index += 2;
    memcpy(&pBuf[index], strBcp.totalVoltage, 2);
    index += 2;

    pOutLen[0] = index;

    return;
}

/**
 ******************************************************************************
 * @brief      Send_CarVinIdentify.
 * @param[in]  None
 * @param[out] uint8 *pOutBuf   发送缓存区
 *             uint8 *pOutLen   组帧的数据长度
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Send_CarVinIdentify(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    BRM_DATA strBrm;
    BCP_DATA strBcp;

    Get_BMS_Data(BMS_PGN_BRM, (void *)&strBrm);
    Get_BMS_Data(BMS_PGN_BCP, (void *)&strBcp);
    pBuf[index++] = Get_TcuJunctorId();
    if (Get_CcuCfgParaEuropeEnable())
    {
        uint32_t size = 6;
        uint8 VIN[17] = {0}; // 初始化 VIN 数组为全0
        if (hex2ascii(&strBrm.ec.evccId[1], VIN, size))
        {
            memcpy(&pBuf[index], VIN, 17); // 确保 index + 17 不超出 pBuf 的范围
        }
        else
        {
            memset(VIN, 0, 17);
            memcpy(&pBuf[index], VIN, 17); // 确保 index + 17 不超出 pBuf 的范围
        }
    }
    else
    {
        memcpy(&pBuf[index], strBrm.gbt.VIN, 17);
    }
    index += 17;
    memcpy(&pBuf[index], strBrm.gbt.chargeTimes, 3);
    index += 3;
    memcpy(&pBuf[index], strBcp.SOC, 2);
    index += 2;
    memcpy(&pBuf[index], strBcp.totalVoltage, 2);
    index += 2;

    pOutLen[0] = index;

    trace(TR_TCU_PROCESS, "<tx 17, Send_CarVinIdentify> : ");
    trace_buf(TR_TCU_PROCESS, pBuf, index);
    return;
}

/**
 ******************************************************************************
 * @brief      Send_CarVinConfirmAck.
 * @param[in]  None
 * @param[out] uint8 *pOutBuf   发送缓存区
 *             uint8 *pOutLen   组帧的数据长度
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Send_CarVinConfirmAck(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    pBuf[index++] = Get_TcuJunctorId();

    pBuf[index++] = (0x00 == pTcuCtrl->vehicleValidateFailReason) ? 0 : 1;
    pBuf[index++] = pTcuCtrl->vehicleValidateFailReason;

    pOutLen[0] = index;

    return;
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Send_VinRep(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    BRM_DATA strBrm;
    uint8 TmpReason = 0;
    Get_BMS_Data(BMS_PGN_BRM, (void *)&strBrm);
    pBuf[index++] = Get_TcuJunctorId();
    if (Get_CcuCfgParaEuropeEnable())
    {
        uint32_t size = 6;
        uint8 VIN[17] = {0}; // 初始化 VIN 数组为全0
        if (hex2ascii(&strBrm.ec.evccId[1], VIN, size))
        {
            memcpy(&pBuf[index], VIN, 17); // 确保 index + 17 不超出 pBuf 的范围
        }
        else
        {
            memset(VIN, 0, 17);
            memcpy(&pBuf[index], VIN, 17); // 确保 index + 17 不超出 pBuf 的范围
        }
    }
    else
    {
        memcpy(&pBuf[index], strBrm.gbt.VIN, 17);
    }
    index += 17;
    pBuf[index++] = pTcuCtrl->chargeStartResult;
    pBuf[index++] = pTcuCtrl->chargeStartFinishFailReason;
    TmpReason = pTcuCtrl->chargeStartFinishFailReason;

    pOutLen[0] = index;

    trace(TR_TCU_PROCESS, "<tx 11/1E, reason %X> : ", TmpReason);
    trace_buf(TR_TCU_PROCESS, pBuf, index);
    return;
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Send_VinConfirmAck(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    pBuf[index++] = Get_TcuJunctorId();

    pBuf[index++] = (0x00 == pTcuCtrl->vehicleValidateFailReason) ? 0 : 1;

    pOutLen[0] = index;
    if (Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
    {
        trace(TR_TCU_PROCESS, "<tx 73 , Send_VinConfirmAck> : ");
        trace_buf(TR_TCU_PROCESS, pBuf, index);
    }
    return;
}
/**
 ******************************************************************************
 * @brief       充电启动完成组包
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void Send_StartFinish(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    uint8 TmpReason = 0;
    BCP_DATA strBcp;
    BRM_DATA strBrm;
    BRO_DATA strBro;
    CHARGE_PARA strChargePara;
    Get_BMS_Data(BMS_PGN_BCP, (void *)&strBcp);
    Get_BMS_Data(BMS_PGN_BRM, (void *)&strBrm);
    Get_BMS_Data(BMS_PGN_BRO, (void *)&strBro);
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);

    if (0xAA == pTcuCtrl->ChargeGun && Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
    {
        pBuf[index++] = 0xAA;
    }
    else
    {
        pBuf[index++] = Get_TcuJunctorId();
    }
    pBuf[index++] = pTcuCtrl->loadSwitch;
    pBuf[index++] = pTcuCtrl->chargeStartFinishFailReason != 0; // pTcuCtrl->chargeStartFinishResult;
    pBuf[index++] = pTcuCtrl->chargeStartFinishFailReason;
    TmpReason = pTcuCtrl->chargeStartFinishFailReason;

    pBuf[index++] = (uint8)(BMS_PROTOCOL_VER >> 16);
    pBuf[index++] = (uint8)(BMS_PROTOCOL_VER >> 8);
    pBuf[index++] = (uint8)(BMS_PROTOCOL_VER >> 0);

    pBuf[index++] = strBrm.gbt.bmsComProtocolVer[0];
    pBuf[index++] = strBrm.gbt.bmsComProtocolVer[1];
    pBuf[index++] = strBrm.gbt.bmsComProtocolVer[2];

    pBuf[index++] = (Get_Release01SuccFlag() == TRUE) ? 0x00 : 0x01;
    pBuf[index++] = strBrm.gbt.batteryType;
    pBuf[index++] = strBcp.highestTemperature;

    memcpy(&pBuf[index], strBcp.highestVoltage, 2);
    index += 2;

    memcpy(&pBuf[index], strBcp.singleHighestVoltage, 2);
    index += 2;
    /*欧标更改*/
    if (Get_CcuCfgParaEuropeEnable())
    {
        Uint16ToTwoUint8(&pBuf[index], Get_Bcp_HighestCurrent());
        index += 2;
    }
    else
    {
        memcpy(&pBuf[index], strBcp.highestCurrent, 2);
        index += 2;
    }
    memcpy(&pBuf[index], strBrm.gbt.ratedVoltage, 2);
    index += 2;

    memcpy(&pBuf[index], strBcp.totalVoltage, 2);
    index += 2;
    memcpy(&pBuf[index], strBrm.gbt.ratedCapacity, 2);
    index += 2;
    memcpy(&pBuf[index], strBcp.nominalEnergy, 2);
    index += 2;

    memcpy(&pBuf[index], strBcp.SOC, 2);
    index += 2;
    memcpy(&pBuf[index], strChargePara.maxOutputVoltage, 2);
    index += 2;
    memcpy(&pBuf[index], strChargePara.minOutputVoltage, 2);
    index += 2;

    Uint16ToTwoUint8(&pBuf[index], 4000 - (FourUint8ToUint32(strChargePara.maxOutputCurrent) / 100));
    index += 2;
    Uint16ToTwoUint8(&pBuf[index], 4000 - (FourUint8ToUint32(strChargePara.minOutputCurrent) / 100));
    index += 2;
    if (Get_CcuCfgParaEuropeEnable())
    {
        uint32_t size = 6;
        uint8 VIN[17] = {0}; // 初始化 VIN 数组为全0
        if (hex2ascii(&strBrm.ec.evccId[1], VIN, size))
        {
            memcpy(&pBuf[index], VIN, 17); // 确保 index + 17 不超出 pBuf 的范围
        }
        else
        {
            memset(VIN, 0, 17);
            memcpy(&pBuf[index], VIN, 17); // 确保 index + 17 不超出 pBuf 的范围
        }
    }
    else
    {
        memcpy(&pBuf[index], strBrm.gbt.VIN, 17);
    }
    index += 17;
    if (Get_CcuCfgParaPlatform_convert() != Platform_protocol_Hn)
    {
        /*新增电池组信息1203*/
        memcpy(&pBuf[index], strBrm.gbt.batteryFactory, 4);
        index += 4;
        memcpy(&pBuf[index], strBrm.gbt.batteryGroupNO, 4);
        index += 4;
        pBuf[index++] = strBrm.gbt.producedYear;
        pBuf[index++] = strBrm.gbt.producedMonth;
        pBuf[index++] = strBrm.gbt.producedMonth;
        memcpy(&pBuf[index], strBrm.gbt.chargeTimes, 3);
        index += 3;
        pBuf[index++] = strBrm.gbt.propertyFlag;
        memcpy(&pBuf[index], strBrm.gbt.Soft, 8);
        //    memset(&pBuf[index], 0, 8);
        index += 8;
        pBuf[index++] = strBrm.gbt.reserve;
        if (Get_CcuCfgParaPlatform_convert() == Platform_protocol_Xj)
        {
            pBuf[index++] = Get_SPN_2829(); /*xiaoju*/
            pBuf[index++] = Get_SPN_2830(); /*xiaoju*/
        }
    }

    pOutLen[0] = index;
    trace(TR_TCU_PROCESS, "<tx 11, reason %X> : ", TmpReason);
    trace_buf(TR_TCU_PROCESS, pBuf, index);

    return;
}
/**
 ******************************************************************************
 * @brief       充电停止完成组包
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  uint8 *pOutBuf   发送缓存区
 *              uint8 *pOutLen   组帧的数据长度
 * @retval      NONE
 *
 * @details
 * @note
 ******************************************************************************
 */
static void Send_StopFinish(uint8 *pOutBuf, uint8 *pOutLen)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    uint8 TmpReason = 0;
    BST_DATA strBst;
    BSD_DATA strBSD;
    BEM_DATA strBEM;
    Get_BMS_Data(BMS_PGN_BST, (void *)&strBst);
    Get_BMS_Data(BMS_PGN_BSD, (void *)&strBSD);
    Get_BMS_Data(BMS_PGN_BEM, (void *)&strBEM);

    //    Check_StopFinishReason();/**< 处理正常中止的情况*/

    if (0xAA == pTcuCtrl->ChargeGun && Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
    {
        pBuf[index++] = 0xAA;
    }
    else
    {
        pBuf[index++] = Get_TcuJunctorId();
    }
    pBuf[index++] = Get_StopFinishReason(); // pTcuCtrl->chargeStopFinishReason; //TODO-xg
    pBuf[index++] = 0;                      // TcuCtrl->chargeStopFinishResult;

    //    memcpy(&pBuf[index], (uint8 *)&strBst, sizeof(BST_DATA));
    //    pBuf[index+3] &= 0x0F;
    //    index += sizeof(BST_DATA);

    memcpy(&pBuf[index], (uint8 *)&strBst, MOFFSET(BST_DATA, plcEnergency) - MOFFSET(BST_DATA, stopCause));
    pBuf[index + 3] &= 0x0F;
    index += MOFFSET(BST_DATA, plcEnergency) - MOFFSET(BST_DATA, stopCause);

    //    memcpy(&pBuf[index], (uint8 *)&strBSD, sizeof(BSD_DATA));
    //    index += sizeof(BSD_DATA);
    memcpy(&pBuf[index], (uint8 *)&strBSD, MOFFSET(BSD_DATA, plcWeldDetectReq) - MOFFSET(BSD_DATA, stopSOC));
    index += MOFFSET(BSD_DATA, plcWeldDetectReq) - MOFFSET(BSD_DATA, stopSOC);

    //    memcpy(&pBuf[index], (uint8 *)&strBEM, sizeof(BEM_DATA));
    //    index += sizeof(BEM_DATA);

    memcpy(&pBuf[index], (uint8 *)&strBEM, MOFFSET(BEM_DATA, plcsequence));
    index += MOFFSET(BEM_DATA, plcsequence);

    if (BMS_PGN_BRM == Get_BMS_OverTimePgn())
    {
        Set_BitFlag(&pBuf[index], 0);
    }

    index++;

    if (BMS_PGN_BCP == Get_BMS_OverTimePgn())
    {
        Set_BitFlag(&pBuf[index], 0);
    }

    if (BMS_PGN_BRO == Get_BMS_OverTimePgn() || BMS_PGN_BRO_AA == Get_BMS_OverTimePgn())
    {
        Set_BitFlag(&pBuf[index], 2);
    }

    index++;

    if (BMS_PGN_BCS == Get_BMS_OverTimePgn())
    {
        Set_BitFlag(&pBuf[index], 0);
    }

    if (BMS_PGN_BCL == Get_BMS_OverTimePgn())
    {
        Set_BitFlag(&pBuf[index], 2);
    }

    if (BMS_PGN_BST == Get_BMS_OverTimePgn())
    {
        Set_BitFlag(&pBuf[index], 4);
    }

    index++;

    if (BMS_PGN_BSD == Get_BMS_OverTimePgn())
    {
        Set_BitFlag(&pBuf[index], 0);
    }

    index++;
    pOutLen[0] = index;

    trace(TR_TCU_PROCESS, "<tx 13, reason %X ,sizeof : %d> : ", pBuf[1], MOFFSET(BEM_DATA, plcsequence));
    trace_buf(TR_TCU_PROCESS, pBuf, index);

    return;
}

static void Send_PileState(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    uint8 workState = Get_WorkState();
    static uint8 tick = 0;
    pBuf[index++] = Get_TcuJunctorId();

    //    if(workState != CCU_WORK_STATE_STOP_FINISH )  //非充电完成状态，tick置0
    //    {
    //    	tick = 0;
    //    }

#if 1
    switch (workState)
    {
#if 1
    case CCU_WORK_STATE_FREE:
    {
        if (enumPhyConVol_4V == Get_PhyConVol())
        {
            workState = 1;
        }
        else
        {
            workState = 0;
        }
    }
    break;
    case CCU_WORK_STATE_READY:
    {
        workState = 1;
    }
    break;
#else
    case CCU_WORK_STATE_FREE:
    {
        workState = 0;
    }
    break;
    case CCU_WORK_STATE_READY:
    {
        workState = 1;
    }
    break;
#endif
    case CCU_WORK_STATE_SHAKE_HAND:
    {
        workState = 2;
    }
    break;
    case CCU_WORK_STATE_IMD:
    case CCU_WORK_STATE_RELEASE_01:
    {
        workState = 3;
    }
    break;
    case CCU_WORK_STATE_RECOGNIZE:
    {
        workState = 4;
    }
    break;
    case CCU_WORK_STATE_CONFIG:
    {
        workState = 5;
    }
    break;
    case CCU_WORK_STATE_PRE_CHARGE:
    {
        workState = 6;
    }
    break;
    case CCU_WORK_STATE_CHARGING:
    {
        workState = 7;
    }
    break;
    case CCU_WORK_STATE_CHARGE_PAUSE:
    {
        workState = 8;
    }
    break;
    case CCU_WORK_STATE_CHARGE_STOP:
    case CCU_WORK_STATE_RELEASE_02:
    {
        workState = 9;
    }
    break;
    case CCU_WORK_STATE_STOP_FINISH:
    {
        //        	abs(tickGet()- tick) >= 2 *sysClkRateGet()
        //        	if( tick <= 9 )  //先发9帧停止完成
        //        	{
        //        		tick++;
        //        		workState = 10;
        //        	}
        //        	else //第十帧发送充电完成帧
        //        	{
        //        		workState = 11;
        //        	}
        if (enumPhyConVol_4V == Get_PhyConVol())
        {
            workState = 10;
        }
        else
        {
            workState = 11;
        }
    }
    break;
    default:
        break;
    }
#else
    if (workState == CCU_WORK_STATE_FREE)
    {
        if (enumPhyConVol_4V == Get_PhyConVol())
        {
            workState = 1;
        }
        else
        {
            workState = 0;
        }
    }
    else if ((workState >= CCU_WORK_STATE_RECOGNIZE) && (workState <= CCU_WORK_STATE_STOP_FINISH))
    {
        workState -= 1;
    }

#endif
    pBuf[index++] = workState;
    memset(&pBuf[index], 0, 6);
    index += 6;
    pOutLen[0] = index;
    trace(TR_TCU_PROCESS, "<tx 15, reason YC> :%x ", workState);
    trace_buf(TR_TCU_PROCESS, pBuf, index);
    return;
}

static void Send_YC(uint8 *pOutBuf, uint8 *pOutLen)
{
    SAMPLE_PARA strSamplePara;
    CHARGE_PARA strChargePara;
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    BCL_DATA strBcl;
    BSM_DATA strBsm;
    BCS_DATA strBcs;

    Get_BMS_Data(BMS_PGN_BCL, (void *)&strBcl);
    Get_BMS_Data(BMS_PGN_BSM, (void *)&strBsm);
    Get_BMS_Data(BMS_PGN_BCS, (void *)&strBcs);
    Get_PilePara((void *)&strSamplePara, eParaType_SamplePara);
    Get_PilePara((void *)&strChargePara, eParaType_ChargePara);
    pBuf[index++] = Get_TcuJunctorId();
    Uint16ToTwoUint8(&pBuf[index], Get_K1K2InsideVol());
    index += 2;
    Uint16ToTwoUint8(&pBuf[index], 4000 - (Get_K1K2Current() / 100));
    index += 2;
    pBuf[index++] = strBcs.curSOC;
    if ((CCU_WORK_STATE_CHARGING == Get_WorkState()) || (CCU_WORK_STATE_CHARGE_PAUSE == Get_WorkState()))
    {
        pBuf[index++] = strBsm.lowestTemperature;
        pBuf[index++] = strBsm.highestTemperature;
    }
    else
    {
        pBuf[index++] = 50;
        pBuf[index++] = 50;
    }
    if (Platform_protocol_Xj == Get_CcuCfgParaPlatform_convert())
    {
        Uint16ToTwoUint8(&pBuf[index], TwoUint8ToUint16(strBcs.highestSingleVolAndNO));
        index += 2;
    }
    else
    {
        Uint16ToTwoUint8(&pBuf[index], TwoUint8ToUint16(strBcs.highestSingleVolAndNO) & 0x0FFF);
        index += 2;
    }
    Uint16ToTwoUint8(&pBuf[index], Get_LowestSigVol());
    index += 2;
    pBuf[index++] = 50 + 28;
    Uint16ToTwoUint8(&pBuf[index], (uint16)(Get_Cc1Vol() * 100));
    index += 2;
    memcpy(&pBuf[index], strBcl.voltageDemand, 2);
    index += 2;
    if ((CCU_WORK_STATE_CHARGING == Get_WorkState()) || (CCU_WORK_STATE_CHARGE_PAUSE == Get_WorkState()))
    {
        memcpy(&pBuf[index], strBcl.currentDemand, 2);
    }
    else
    {
        *(uint16 *)&pBuf[index] = 4000;
    }
    index += 2;
    pBuf[index++] = strBcl.bmsChargeMode;
    memcpy(&pBuf[index], strBcs.voltageMeasuredValue, 2);
    index += 2;
    if ((CCU_WORK_STATE_CHARGING == Get_WorkState()) || (CCU_WORK_STATE_CHARGE_PAUSE == Get_WorkState()))
    {
        memcpy(&pBuf[index], strBcs.currentMeasuredValue, 2);
    }
    else
    {
        *(uint16 *)&pBuf[index] = 4000;
    }
    index += 2;
    memcpy(&pBuf[index], strBcs.remainTime, 2);
    index += 2;
    pBuf[index++] = (int8)(Get_DCTemp(eADChannel_GunTemp1) / 10) + 50;
    pBuf[index++] = (int8)(Get_DCTemp(eADChannel_GunTemp2) / 10) + 50;
    pBuf[index++] = 50;
    pBuf[index++] = 50;
    if (Get_TcuProtocolVer() >= TCU_PROTOCOL_VER_0121)
    {
        if ((Get_WorkState() >= CCU_WORK_STATE_PRE_CHARGE) && (Get_WorkState() <= CCU_WORK_STATE_STOP_FINISH))
        {
            pBuf[index++] = Get_HighestSingleVoltageNO();
            pBuf[index++] = Get_HighestTemperatureNO();
            pBuf[index++] = Get_LowestTemperatureNO();
        }
        else
        {
            pBuf[index++] = 0;
            pBuf[index++] = 0;
            pBuf[index++] = 0;
        }
        pBuf[index++] = Get_InFanTemp();
        pBuf[index++] = Get_OutFanTemp();
        pBuf[index++] = Get_Humidity();
    }
    Uint16ToTwoUint8(&pBuf[index], 4000 - (FourUint8ToUint32(
                                               strChargePara.maxOutputCurrent) /
                                           100));
    index += 2;

    Uint16ToTwoUint8(&pBuf[index], 4000 - (FourUint8ToUint32(
                                               strChargePara.minOutputCurrent) /
                                           100));
    index += 2;
    pOutLen[0] = index;
    trace(TR_TCU_PROCESS, "<tx 20, reason YC> : ");
    trace_buf(TR_TCU_PROCESS, pBuf, index);
    return;
}

static uint8 Get_TcuWorkState(void)
{
    uint8 tcuStage = Get_TcuStage();

#if 0
    if (TCU_STAGE_RUN_STOP_FINISH == tcuStage)
    {
        if (eSwitchState_OFF == Get_SwitchState(SXIO_IN_DCS)
                || Check_ErrType(eErrType_ElecLockErr))
        {
            return TCU_WORK_STATE_FINISH;
        }
        else
        {
            return TCU_WORK_STATE_WORK;
        }
    }
    else if (0x00 == Get_ChargePauseFlg())
    {
        return TCU_WORK_STATE_PAUSE;
    }
    else if (TCU_STAGE_RUN_STARTING == tcuStage ||
             TCU_STAGE_RUN_CHARGING == tcuStage ||
             TCU_STAGE_RUN_STOPPING == tcuStage)
    {
        return TCU_WORK_STATE_WORK;
    }
    else
    {
        return TCU_WORK_STATE_FREE;
    }
#else
    uint8 workState = Get_WorkState();

    switch (workState)
    {
    case CCU_WORK_STATE_FREE:
    case CCU_WORK_STATE_READY:
    {
        return TCU_WORK_STATE_FREE;
    }
    break;
    case CCU_WORK_STATE_SHAKE_HAND:
    case CCU_WORK_STATE_IMD:
    case CCU_WORK_STATE_RELEASE_01:
    case CCU_WORK_STATE_RECOGNIZE:
    case CCU_WORK_STATE_CONFIG:
    case CCU_WORK_STATE_PRE_CHARGE:
    case CCU_WORK_STATE_CHARGING:
    {
        return TCU_WORK_STATE_WORK;
    }
    break;
    case CCU_WORK_STATE_CHARGE_PAUSE:
    {
        return TCU_WORK_STATE_PAUSE;
    }
    break;
    case CCU_WORK_STATE_CHARGE_STOP:
    case CCU_WORK_STATE_RELEASE_02:
    case CCU_WORK_STATE_STOP_FINISH:
    {
        if (eSendFlag_Yes == Get_TcuSendFlg(PGN_STOP_FINISH)) // TODO-开普修改
        {
            return TCU_WORK_STATE_FINISH;
        }
    }
    break;
    default:
        break;
    }
#endif
}
#if 0
const UP_ERR UP_ERR_YX1_MAP[] =
{
    { eErrType_EmergencyStop,                   12,       PGN_CCU_YX1 },
    { eErrType_SmokeErr,                        13,       PGN_CCU_YX1 },
    { eErrType_ACBreakerErr,                    14,       PGN_CCU_YX1 },
    { eErrType_K1Err,                         15,       PGN_CCU_YX1 },
    { eErrType_K2Err,                         15,       PGN_CCU_YX1 },
    { eErrType_FuseProtectorErr,                16,       PGN_CCU_YX1 },
    { eErrType_ElecLockErr,                     17,       PGN_CCU_YX1 },
    { eErrType_PileFanErr,                      18,       PGN_CCU_YX1 },
    { eErrType_BlqAlarm,                   		19,       PGN_CCU_YX1 },
    { eErrType_ImdErr,                          20,       PGN_CCU_YX1 },
    { eErrType_ComErrWithIMD,                   20,       PGN_CCU_YX1 },
    
    { eErrType_BatteryReverseConnect,           21,       PGN_CCU_YX1 },
    { eErrType_GunConnectErr,                   22,       PGN_CCU_YX1 },
    { eErrType_CabTempOverErr,                  23,       PGN_CCU_YX1 },
    { eErrType_PileTempOverLimitErr,            23,       PGN_CCU_YX1 },
    { eErrType_GunTempOverLimitErr,             24,       PGN_CCU_YX1 },
    { eErrType_GunNoHoming,                     25,       PGN_CCU_YX1 },
    { eErrType_ComErrWithBMS,                   26,       PGN_CCU_YX1 },
    { eErrType_InputVolOverLimit,               27,       PGN_CCU_YX1 },
    { eErrType_InputVolLessLimit,               28,       PGN_CCU_YX1 },
    { eErrType_OutputVolOverLimit,              29,       PGN_CCU_YX1 },
    { eErrType_OutputVolLessLimit,              30,       PGN_CCU_YX1 },
    { eErrType_OutputCurOverLimit,              31,       PGN_CCU_YX1 },
    { eErrType_PowerModuleErr,                  32,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleACInAlarm,            33,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleInVolOverAlarm,       34,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleInVolLessAlarm,       35,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleInOpenPhaseAlarm,     36,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleOutShortCutAlarm,     37,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleOutCurOverAlarm,   	38,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleOutVolOverAlarm,   	39,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleOutVolLessAlarm ,     40,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleTempAlarm,            41,       PGN_CCU_YX1 },
    { eErrType_PowerModuleComAlarm,    			42,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleFanAlarm,    			43,       PGN_CCU_YX1 },
//  { eErrType_PowerModuleInputOpenPhase,       36,       PGN_CCU_YX1 },
//  { eErrType_PowerModuleOutputShortCut,       37,       PGN_CCU_YX1 },
//  { eErrType_PowerModuleOutputCurOverLimit,   38,       PGN_CCU_YX1 },
//  { eErrType_PowerModuleOutputVolOverLimit,   39,       PGN_CCU_YX1 },
//  { eErrType_PowerModuleOutputVolLessLimit,   40,       PGN_CCU_YX1 },
//  { eErrType_PowerModuleTemp,                 41,       PGN_CCU_YX1 },
//  { eErrType_PowerModuleComAlarm,             42,       PGN_CCU_YX1 },
//  { eErrType_PowerModuleFanAlarm,             43,       PGN_CCU_YX1 },
//  {                                           44,       PGN_CCU_YX1 },
//  {                                           45,       PGN_CCU_YX1 },
//  {                                           46,       PGN_CCU_YX1 },
//  {                                           47,       PGN_CCU_YX1 },
//	{ eErrType_PcuForbid,                   	48,       PGN_CCU_YX1 },
	{ eErrType_PcuBusyTimeout,                  49,       PGN_CCU_YX1 },
    { eErrType_ComErrWithPCU,                   50,       PGN_CCU_YX1 },
    { eErrType_PcuStopCharge,                   51,       PGN_CCU_YX1 },
    { eErrType_PcuForbidCharge,                 52,       PGN_CCU_YX1 },
	{ eErrType_ModuleOnOffErr,                  52,       PGN_CCU_YX1 },
    { eErrType_SStartTimeOut,                   53,       PGN_CCU_YX1 },
	{ eErrType_CcuYxYcTimeoutErr,        		54,       PGN_CCU_YX1 },
	{ eErrType_QStartTimeOut ,                  55,       PGN_CCU_YX1 },
//  { eErrType_OutputCurOverLimitBSM,           52,       PGN_CCU_YX1 },
//  { eErrType_TempOverLimitBSM,                52,       PGN_CCU_YX1 },
//  { eErrType_SOCTooHighBSM,                   52,       PGN_CCU_YX1 },
//  { eErrType_SOCTooLowBSM,                    52,       PGN_CCU_YX1 },
//  { eErrType_ImdErrBSM,                       52,       PGN_CCU_YX1 },
//  { eErrType_PhyConErrBSM,                    52,       PGN_CCU_YX1 },
//  { eErrType_BRMErr,                          52,       PGN_CCU_YX1 },
//  { eErrType_BROErr,                          52,       PGN_CCU_YX1 },
//  { eErrType_PhySingleVoltooHighBSM,          52,       PGN_CCU_YX1 },
//  { eErrType_PhySingleVoltooLowBSM,           52,       PGN_CCU_YX1 },
//  { eErrType_BCPErr,                          52,       PGN_CCU_YX1 },
//  { eErrType_BatteryVolErr,                   53,       PGN_CCU_YX1 },
//  { eErrType_BCPVolErr1,                      53,       PGN_CCU_YX1 },
//    { eErrType_K1K2OutsideVolErr1,              54,       PGN_CCU_YX1 },
//  { eErrType_K1K2OutsideVolErr2,              54,       PGN_CCU_YX1 },
//  { eErrType_K1K2OutsideVolErr3,              54,       PGN_CCU_YX1 },
//  { eErrType_ChargePauseTimeout,              55,       PGN_CCU_YX1 },
//  { eErrType_VerCheckUnfinished,              56,       PGN_CCU_YX1 },
//  { eErrType_SetParaUnfinished,               57,       PGN_CCU_YX1 },
//    { eErrType_DoorOpenErr,                     72,       PGN_CCU_YX1 },
//    { eErrType_DCMainContactorSynechia,         73,       PGN_CCU_YX1 },
//    { eErrType_ImdAlarm,                        74,       PGN_CCU_YX1 },
//    { eErrType_ReleaseErr,                      75,       PGN_CCU_YX1 },
//    { eErrType_PileTempOverLimitAlarm,          76,       PGN_CCU_YX1 },
//    { eErrType_PcuTempOverLimitAlarm,           76,       PGN_CCU_YX1 },
//    { eErrType_GunTempOverLimitAlarm,           77,       PGN_CCU_YX1 },
//    { eErrType_AcJCQErr,                        78,       PGN_CCU_YX1 },
//    { eErrType_PcuForbidCharge,                 79,       PGN_CCU_YX1 },
//    { eErrType_AssistPowerErr,                  80,       PGN_CCU_YX1 },
//    { eErrType_MultipleContactorErr,            81,       PGN_CCU_YX1 },
//    { eErrType_MultipleContactorSynechia,       82,       PGN_CCU_YX1 },
};
#endif
// void Get_TcuReportYx1(uint8 *pOutData, const uint8 len)
//{
//     const UP_ERR *pErr = NULL;
//     uint8 index = 0;
//
//     if (len != 16)
//     {
//         return;
//     }
//
//     for (index = 0; index < FCNT(UP_ERR_YX1_MAP); index++)
//     {
//         pErr = &UP_ERR_YX1_MAP[index];
//
//         if (TRUE == Check_ErrType(pErr->errType))
//         {
//             Set_BitFlag(pOutData, pErr->bit);
//
//             if ((uint8)(pErr->errType >> 8) ==  DEVICE_STATE_FAULT)
//             {
//                 Set_BitFlag(pOutData, 10);
//             }
//
//             if ((uint8)(pErr->errType >> 8) ==  DEVICE_STATE_MAJOR_FAULT)
//             {
//                 Set_BitFlag(pOutData, 10);
//             }
//
//             if ((uint8)(pErr->errType >> 8) ==  DEVICE_STATE_ALARM)
//             {
//                 Set_BitFlag(pOutData, 11);
//             }
//         }
//     }
//
//     if (enumPhyConVol_4V == Get_PhyConVol() &&
//         FALSE == Check_ErrType(eErrType_EmergencyStop))
//     {
//          if (TRUE == Check_ErrType(eErrType_PowerModuleACInAlarm))
//           {
//               Set_BitFlag(pOutData, 33);
//               Set_BitFlag(pOutData, 11);
//           }
//
//           if (TRUE == Check_ErrType(eErrType_PowerModuleInVolOverAlarm))
//           {
//               Set_BitFlag(pOutData, 34);
//               Set_BitFlag(pOutData, 11);
//           }
//
//           if (TRUE == Check_ErrType(eErrType_PowerModuleInVolLessAlarm))
//           {
//               Set_BitFlag(pOutData, 35);
//               Set_BitFlag(pOutData, 11);
//           }
//
//           if (TRUE == Check_ErrType(eErrType_PowerModuleInOpenPhaseAlarm))
//           {
//               Set_BitFlag(pOutData, 36);
//               Set_BitFlag(pOutData, 11);
//           }
//
//           if (TRUE == Check_ErrType(eErrType_PowerModuleOutShortCutAlarm))
//           {
//               Set_BitFlag(pOutData, 37);
//               Set_BitFlag(pOutData, 11);
//           }
//
//           if (TRUE == Check_ErrType(eErrType_PowerModuleOutCurOverAlarm))
//           {
//               Set_BitFlag(pOutData, 38);
//               Set_BitFlag(pOutData, 11);
//           }
//
//           if (TRUE == Check_ErrType(eErrType_PowerModuleOutVolOverAlarm))
//           {
//               Set_BitFlag(pOutData, 39);
//               Set_BitFlag(pOutData, 11);
//           }
//
//           if (TRUE == Check_ErrType(eErrType_PowerModuleOutVolLessAlarm))
//           {
//               Set_BitFlag(pOutData, 40);
//               Set_BitFlag(pOutData, 11);
//           }
//
//           if (TRUE == Check_ErrType(eErrType_PowerModuleTempAlarm))
//           {
//               Set_BitFlag(pOutData, 41);
//               Set_BitFlag(pOutData, 11);
//           }
//
//           if (TRUE == Check_ErrType(eErrType_PowerModuleComAlarm))
//           {
//               Set_BitFlag(pOutData, 42);
//               Set_BitFlag(pOutData, 11);
//           }
//
//           if (TRUE == Check_ErrType(eErrType_PowerModuleFanAlarm))
//           {
//               Set_BitFlag(pOutData, 43);
//               Set_BitFlag(pOutData, 11);
//           }
//     }
//
//     if (enumPhyConVol_4V != Get_PhyConVol())
//     {
//         Set_BitFlag(pOutData, 44);
//     }
//
//     if (eSwitchState_OFF == Get_SwitchState(SXIO_IN_CDQ))
//     {
//         Set_BitFlag(pOutData, 45);
//     }
//
//     if (eSwitchState_ON == Get_SwitchState(SXIO_IN_DCS))
//     {
//         Set_BitFlag(pOutData, 46);
//     }
//
//     if (eSwitchState_ON == Get_SwitchState(SXIO_IN_K1) &&
//         eSwitchState_ON == Get_SwitchState(SXIO_IN_K2))
//     {
//         Set_BitFlag(pOutData, 47);
//     }
//
//     return;
// }

static uint8 s_locked_gun_state = 0;
static uint8 s_locked_gun_home_state = 0;
static uint8 s_locked_elock_state = 0;
static bool_e s_sim_state_lock_active = FALSE; /* 标志位，用于记住是否处于模拟充电锁定状态 */

void Get_Yx_Date(void)
{
    uint8 errFlag = 0, alarmFlag = 0;
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 workstate = Get_WorkState();
    CONFIG_PARA strCfgPara;
    Get_PilePara((void *)&strCfgPara, eParaType_ConfigPara);
    pTcuCtrl->tcuSendYx1.workState = Get_TcuWorkState();

    /*
     * 精准锁定遥信状态，仅针对模拟获取EVCCID的流程。
     * 1. 流程开始时(SIM_EVCCID_WAIT)，捕获并锁定状态。
     * 2. 当CCU进入下一次充电的准备状态时(READY)，才解锁。
     *    以此确保覆盖BMS复位时的信号跳变，且不会将锁带入正常充电流程。
     */

    /* 判断是否需要进入锁定状态 */
    if (Get_EvccidSimState() == SIM_EVCCID_WAIT && !s_sim_state_lock_active)
    {
        s_sim_state_lock_active = TRUE;
        trace(TR_ALWAYS, "TCU: Gun states LOCKING for EVCCID sim.\n");

        /* 捕获状态，区分欧标和国标 */
        if (Get_CcuCfgParaEuropeEnable())
        {
            s_locked_gun_state = (Get_EcPlcCpStatus() == eCpStatus_Unplug) ? 1 : 0;
            s_locked_gun_home_state = (Get_EcPlcCpStatus() == eCpStatus_Unplug) ? 0 : 1;
        }
        else
        {
            s_locked_gun_state = (enumPhyConVol_4V != Get_PhyConVol()) ? 1 : 0;
            s_locked_gun_home_state = (enumPhyConVol_4V != Get_PhyConVol()) ? 0 : 1;
        }
        s_locked_elock_state = (eSwitchState_ON == Get_SwitchState(SXIO_IN_DCS)) ? 1 : 0;
    }
    
    /* 判断是否需要解锁 */
    if (s_sim_state_lock_active && workstate == CCU_WORK_STATE_READY)
    {
        s_sim_state_lock_active = FALSE;
        trace(TR_ALWAYS, "TCU: Gun states UNLOCKED upon entering READY state.\n");
    }

    /* 根据锁定状态决定上报内容 */
    if (s_sim_state_lock_active)
    {
        /* 处于锁定状态，上报被锁定的值 */
        pTcuCtrl->tcuSendYx1.junState = s_locked_gun_state;
        pTcuCtrl->tcuSendYx1.junHomeState = s_locked_gun_home_state;
        pTcuCtrl->tcuSendYx1.elockState = s_locked_elock_state;
    }
    else
    {
        /* 不处于锁定状态，上报实时值 */
        if (Get_CcuCfgParaEuropeEnable())
        {
            pTcuCtrl->tcuSendYx1.junState = (Get_EcPlcCpStatus() == eCpStatus_Unplug) ? 1 : 0;
            pTcuCtrl->tcuSendYx1.junHomeState = (Get_EcPlcCpStatus() == eCpStatus_Unplug) ? 0 : 1;
        }
        else
        {
            pTcuCtrl->tcuSendYx1.junState = (enumPhyConVol_4V != Get_PhyConVol()) ? 1 : 0;
            pTcuCtrl->tcuSendYx1.junHomeState = (enumPhyConVol_4V != Get_PhyConVol()) ? 0 : 1;
        }
        pTcuCtrl->tcuSendYx1.elockState = (eSwitchState_ON == Get_SwitchState(SXIO_IN_DCS)) ? 1 : 0;
    }

    pTcuCtrl->tcuSendYx1.k1k2State =
        (eSwitchState_ON == Get_SwitchState(SXIO_IN_K1) && eSwitchState_ON == Get_SwitchState(SXIO_IN_K2)) ? 1 : 0;
    pTcuCtrl->tcuSendYx1.jtErr = Check_ErrType(eErrType_EmergencyStop) ||
                                 Check_ErrType(eErrType_PcuEmergencyStop);

    pTcuCtrl->tcuSendYx1.ygErr = Check_ErrType(eErrType_SmokeErr);

    pTcuCtrl->tcuSendYx1.acDlqErr = Check_ErrType(eErrType_ACBreakerErr);

    pTcuCtrl->tcuSendYx1.k1k2JdWdErr = (CCU_WORK_STATE_FREE == Get_WorkState()) ? 0 : (Check_ErrType(eErrType_K1Err) | Check_ErrType(eErrType_K2Err)); /*网外小桔新增*/
    pTcuCtrl->tcuSendYx1.rdqErr = Check_ErrType(eErrType_FuseProtectorErr);
    pTcuCtrl->tcuSendYx1.eLockErr = Check_ErrType(eErrType_ElecLockErr);
    pTcuCtrl->tcuSendYx1.plieFanErr = Check_ErrType(eErrType_PileFanErr);
    pTcuCtrl->tcuSendYx1.blqErr = Check_ErrType(eErrType_BlqErr);
    pTcuCtrl->tcuSendYx1.imdErr = Check_ErrType(eErrType_ImdErr) | Check_ErrType(eErrType_QStartTimeOut) | Check_ErrType(eErrType_ImdTimeOut) | Check_ErrType(eErrType_ComErrWithIMD); /**<因没有快速启动超时故障 所以报绝缘故障*/
    pTcuCtrl->tcuSendYx1.batRErr = Check_ErrType(eErrType_BatteryReverseConnect);
    pTcuCtrl->tcuSendYx1.junErr = Check_ErrType(eErrType_GunConnectErr);
    pTcuCtrl->tcuSendYx1.pileTempErr = Check_ErrType(eErrType_PileTempOverLimitErr);
    pTcuCtrl->tcuSendYx1.junTmepErr = Check_ErrType(eErrType_GunTempOverLimitErr);
    pTcuCtrl->tcuSendYx1.junNoHomeAlarm = Check_ErrType(eErrType_GunNoHoming);
    alarmFlag |= pTcuCtrl->tcuSendYx1.junNoHomeAlarm;
    pTcuCtrl->tcuSendYx1.bmsComErr = Check_ErrType(eErrType_ComErrWithBMS) | Check_ErrType(eErrType_ChargePauseTimeout);
    pTcuCtrl->tcuSendYx1.inVolOverErr = Check_ErrType(eErrType_InputVolOverLimit);
    pTcuCtrl->tcuSendYx1.inVolLessErr = Check_ErrType(eErrType_InputVolLessLimit);
    pTcuCtrl->tcuSendYx1.outVolOverErr = Check_ErrType(eErrType_OutputVolOverLimit);
    pTcuCtrl->tcuSendYx1.outVolLessErr = Check_ErrType(eErrType_OutputVolLessLimit);
    pTcuCtrl->tcuSendYx1.outCurOverErr = Check_ErrType(eErrType_OutputCurOverLimit);

    pTcuCtrl->tcuSendYx2.acJcqJdWdErr = Check_ErrType(eErrType_AcJCQErr); /**插枪时交流接触器故障表示据动/误动故障*/
    pTcuCtrl->tcuSendYx2.acJcqNlErr = 0;
    pTcuCtrl->tcuSendYx2.pcuComErr =
        (pTcuCtrl->tcuProtocolVer > TCU_PROTOCOL_VER) ? (Check_ErrType(eErrType_ComErrWithPCU) | Check_ErrType(eErrType_CcuYxYcTimeoutErr) | Check_ErrType(eErrType_PcuBusyTimeout)) : 0;
    pTcuCtrl->tcuSendYx1.moudleErr =
        (pTcuCtrl->tcuSendYx2.pcuComErr | pTcuCtrl->tcuSendYx1.acDlqErr | pTcuCtrl->tcuSendYx2.acJcqJdWdErr | pTcuCtrl->tcuSendYx2.acJcqNlErr) ? 0 : Check_ErrType(eErrType_PowerModuleErr) /*|Check_ErrType(eErrType_ModuleOnOffErr)*/;
#if 0 /*网外直流桩更改240126*/
	pTcuCtrl->tcuSendYx1.moudleAcInAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleACInAlarm);
	pTcuCtrl->tcuSendYx1.moudleAcInVolOverAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleInVolOverAlarm);
	pTcuCtrl->tcuSendYx1.moudleAcInVolLessAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleInVolLessAlarm);
	pTcuCtrl->tcuSendYx1.moudleAcInPhaseAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleInOpenPhaseAlarm);
	pTcuCtrl->tcuSendYx1.moudleOutShortAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleOutShortCutAlarm);
	pTcuCtrl->tcuSendYx1.moudleOutCurOverAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleOutCurOverAlarm);
	pTcuCtrl->tcuSendYx1.moudleOutVolOverAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleOutVolOverAlarm);
	pTcuCtrl->tcuSendYx1.moudleOutVolLessAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleOutVolLessAlarm);
	pTcuCtrl->tcuSendYx1.moudleOutTempOverAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleTempAlarm);
	pTcuCtrl->tcuSendYx1.moudleComAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleComAlarm);
	pTcuCtrl->tcuSendYx1.moudleFanAlarm =
			pTcuCtrl->tcuSendYx1.moudleErr ?
					0 : Check_ErrType(eErrType_PowerModuleFanAlarm);
#else
    // todo 是否需要考虑交流接触故障引发的模块告警?
    pTcuCtrl->tcuSendYx1.moudleAcInAlarm = Check_ErrType(eErrType_PowerModuleACInAlarm);
    pTcuCtrl->tcuSendYx1.moudleAcInVolOverAlarm = Check_ErrType(eErrType_PowerModuleInVolOverAlarm);
    pTcuCtrl->tcuSendYx1.moudleAcInVolLessAlarm = Check_ErrType(eErrType_PowerModuleInVolLessAlarm);
    pTcuCtrl->tcuSendYx1.moudleAcInPhaseAlarm = Check_ErrType(eErrType_PowerModuleInOpenPhaseAlarm);
    pTcuCtrl->tcuSendYx1.moudleOutShortAlarm = Check_ErrType(eErrType_PowerModuleOutShortCutAlarm);
    pTcuCtrl->tcuSendYx1.moudleOutCurOverAlarm = Check_ErrType(eErrType_PowerModuleOutCurOverAlarm);
    pTcuCtrl->tcuSendYx1.moudleOutVolOverAlarm = Check_ErrType(eErrType_PowerModuleOutVolOverAlarm);
    pTcuCtrl->tcuSendYx1.moudleOutVolLessAlarm = Check_ErrType(eErrType_PowerModuleOutVolLessAlarm);
    pTcuCtrl->tcuSendYx1.moudleOutTempOverAlarm = Check_ErrType(eErrType_PowerModuleTempAlarm);
    pTcuCtrl->tcuSendYx1.moudleComAlarm = Check_ErrType(eErrType_PowerModuleComAlarm);
    pTcuCtrl->tcuSendYx1.moudleFanAlarm = Check_ErrType(eErrType_PowerModuleFanAlarm);
#endif
    if (pTcuCtrl->tcuSendYx1.acDlqErr | pTcuCtrl->tcuSendYx2.acJcqJdWdErr | pTcuCtrl->tcuSendYx2.acJcqNlErr) /**清除其它故障*/
    {
        pTcuCtrl->tcuSendYx1.errBackUpId = 0;
        pTcuCtrl->tcuSendYx1.yx1ErrData7 = 0;
        Clear_ProcessFault(0);
    }
    else
    {
        pTcuCtrl->tcuSendYx1.res1 = Check_ErrType(eErrType_OutputCurOverLimit);
        pTcuCtrl->tcuSendYx1.res2 = Check_ErrType(eErrType_TCUOtherErr);
        pTcuCtrl->tcuSendYx1.res3 = Check_ErrType(eErrType_OutputShortCut);
        pTcuCtrl->tcuSendYx1.res4 =
            (pTcuCtrl->tcuProtocolVer > TCU_PROTOCOL_VER) ? 0 : Check_ErrType(eErrType_PcuBusyTimeout);
        pTcuCtrl->tcuSendYx1.res6 = Check_ErrType(eErrType_LoadCtrlSwitch);
        pTcuCtrl->tcuSendYx1.res7 = Check_ErrType(eErrType_CcuYxYcTimeoutErr);
        pTcuCtrl->tcuSendYx1.res8 =
            (pTcuCtrl->tcuProtocolVer > TCU_PROTOCOL_VER) ? 0 : Check_ErrType(eErrType_ComErrWithPCU);
    }
    pTcuCtrl->tcuSendYx2.pileDoorErr =
        (strCfgPara.fissionEnable && Check_ErrType(eErrType_CcuDoorOpenErr)) ? Check_ErrType(eErrType_CcuDoorOpenErr) : (Check_ErrType(eErrType_CcuDoorOpenErr) | Check_ErrType(eErrType_PcuDoorOpenErr));
    pTcuCtrl->tcuSendYx2.k1k2NlErr = (CCU_WORK_STATE_FREE == Get_WorkState()) ? (Check_ErrType(eErrType_K1Err) | Check_ErrType(eErrType_K2Err)) : 0; /*网外小桔新增*/
    pTcuCtrl->tcuSendYx2.imdAlarm = Check_ErrType(eErrType_ImdAlarm);
    alarmFlag |= pTcuCtrl->tcuSendYx2.imdAlarm;
    pTcuCtrl->tcuSendYx2.xfErr = Check_ErrType(eErrType_ReleaseErr);
    if (!Get_EnableFlag(eParaFmt_CfgFissionEnable))
    {
        pTcuCtrl->tcuSendYx2.pileTempOverAlarm = Check_ErrType(eErrType_PileTempOverLimitAlarm);
    }
    else
    {
        pTcuCtrl->tcuSendYx2.pileTempOverAlarm = 0;
    }
    alarmFlag |= pTcuCtrl->tcuSendYx2.pileTempOverAlarm;
    pTcuCtrl->tcuSendYx2.junTempOverAlarm = Check_ErrType(eErrType_GunTempOverLimitAlarm);
    alarmFlag |= pTcuCtrl->tcuSendYx2.junTempOverAlarm;
    pTcuCtrl->tcuSendYx2.fyErr = Check_ErrType(eErrType_AssistPowerErr);
    pTcuCtrl->tcuSendYx2.qJcqJdWdErr = 0; /**<未用到*/
    pTcuCtrl->tcuSendYx2.qJcqNlErr = 0;   /**<未用到*/
    pTcuCtrl->tcuSendYx2.qJcqState = 0;   /**<未用到*/
    pTcuCtrl->tcuSendYx2.ccuStopErr = 0;  /**<*/
    pTcuCtrl->tcuSendYx2.pileWaterLoggingErr = Check_ErrType(eErrType_WaterLoggingErr);
    pTcuCtrl->tcuSendYx2.cabDoorErr =
        (strCfgPara.fissionEnable && Check_ErrType(eErrType_CcuDoorOpenErr)) ? Check_ErrType(eErrType_PcuDoorOpenErr) : 0;
    pTcuCtrl->tcuSendYx2.cabWaterLoggingErr = Check_ErrType(eErrType_PcuWaterFault); // Check_ErrType(eErrType_WaterLoggingErr);//TODO -xg 北京检测修改 ，此处需要兼容分体桩.

    pTcuCtrl->tcuSendYx2.cabYgErr = 0; // Check_ErrType(eErrType_SmokeErr); //TODO-xg 北京入网检测修改
        pTcuCtrl->tcuSendYx2.pcuComAlarm = 0;

    pTcuCtrl->tcuSendYx2.pcuErr =
        (pTcuCtrl->tcuProtocolVer > TCU_PROTOCOL_VER) ? Check_ErrType(eErrType_PCUOtherErr) : 0;
    if (pTcuCtrl->tcuProtocolVer > TCU_PROTOCOL_VER)
    {
        pTcuCtrl->tcuSendYx2.pcuAlarm = 0;
        pTcuCtrl->tcuSendYx2.swComErr = 0;
        pTcuCtrl->tcuSendYx2.swComAlarm = 0;
        pTcuCtrl->tcuSendYx2.swErr = Check_ErrType(eErrType_SwErr);
        pTcuCtrl->tcuSendYx2.swAlarm = Check_ErrType(eErrType_SwAlarm);
        pTcuCtrl->tcuSendYx2.bmsErr = Check_ErrType(eErrType_BHMVolErr) | Check_ErrType(eErrType_BHMVolErr1) | Check_ErrType(eErrType_BCPVolErr1) | Check_ErrType(eErrType_K1K2OutsideVolErr2) | Check_ErrType(eErrType_K1K2OutsideVolErr3) | Check_ErrType(eErrType_BRMErr) | Check_ErrType(eErrType_BCPErr) | Check_ErrType(eErrType_SOCTooLowBSM) | Check_ErrType(eErrType_PhySingleVoltooLowBSM) | Check_ErrType(eErrType_BROErr) | Check_ErrType(eErrType_ChargeParaNoMatch) | Check_ErrType(eErrType_DemandLowMinAllowOutputVol);
        errFlag |= pTcuCtrl->tcuSendYx2.bmsErr;
        pTcuCtrl->tcuSendYx2.bmsSendErr = Check_ErrType(
                                              eErrType_OutputCurOverLimitBSM) |
                                          Check_ErrType(eErrType_TempOverLimitBSM) | Check_ErrType(eErrType_ImdErrBSM) | Check_ErrType(eErrType_PhyConErrBSM) | Check_ErrType(eErrType_PhySingleVoltooHighBSM);
        errFlag |= pTcuCtrl->tcuSendYx2.bmsSendErr;
        pTcuCtrl->tcuSendYx2.moudelXfErrAlarm = Check_ErrType(eErrType_PowerModuleXfAlarm);
    }
    if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120)
    {
        pTcuCtrl->tcuSendYx2.jcjcReq = 0;
        pTcuCtrl->tcuSendYx2.acJCQYX = Get_ACJCQState();
        pTcuCtrl->tcuSendYx2.acJCQYK = Get_ACJCQState();
        pTcuCtrl->tcuSendYx2.k1YK = get_out_K1();
        pTcuCtrl->tcuSendYx2.k2YK = get_out_K2();
        pTcuCtrl->tcuSendYx2.fyYK = get_out_K3();
        pTcuCtrl->tcuSendYx2.fy2YK = get_out_K4();
        pTcuCtrl->tcuSendYx2.pileFanYK = Get_fanState();
        pTcuCtrl->tcuSendYx2.dzs = get_out_ELEC_LOCK();
        pTcuCtrl->tcuSendYx2.peErr = Check_ErrType(eErrType_PEErr);
        pTcuCtrl->tcuSendYx2.guidErr = Check_ErrType(eErrType_GunConnectErr);
        pTcuCtrl->tcuSendYx2.modStartErr = Check_ErrType(eErrType_QStartTimeOut);
        pTcuCtrl->tcuSendYx2.modCloseErr = Check_ErrType(eErrType_ModuleOnOffErr);
        pTcuCtrl->tcuSendYx2.modAdrExcept = 0;
        pTcuCtrl->tcuSendYx2.hjErr = 0;
        if (Get_EnableFlag(eParaFmt_CfgFissionEnable))
        {
            pTcuCtrl->tcuSendYx2.pileOverTempAlm = Check_ErrType(eErrType_PileTempOverLimitAlarm);
        }
        else
        {
        pTcuCtrl->tcuSendYx2.pileOverTempAlm = 0;
        }
        pTcuCtrl->tcuSendYx2.pileOverTempErr = Check_ErrType(eErrType_CabTempOverErr);
        pTcuCtrl->tcuSendYx2.powAllocateFail = 0;
        pTcuCtrl->tcuSendYx2.noMod = Check_ErrType(eErrType_NoMod);
        pTcuCtrl->tcuSendYx2.preChargeAdjVolFail = Check_ErrType(eErrType_SStartTimeOut);
        pTcuCtrl->tcuSendYx2.pcuComTimeout = Check_ErrType(eErrType_ComErrWithPCU);
        pTcuCtrl->tcuSendYx2.hjsdAlm = Check_ErrType(eErrType_HjxxAlm);
        errFlag = 0x00;
        errFlag = (pTcuCtrl->tcuSendYx1.yx1State & 0xF0) |
                  (pTcuCtrl->tcuSendYx1.yx1ErrData2 & 0xFF) |
                  //	                (pTcuCtrl->tcuSendYx1.yx1ErrData3 & 0x01) |
                  (pTcuCtrl->tcuSendYx1.yx1ErrData3 & 0xFD) |
                  (pTcuCtrl->tcuSendYx1.yx1ErrData4 & 0x01) | // TODO -xg 北京入网检测增加
                  (pTcuCtrl->tcuSendYx2.yx2ErrData1 & 0xCB) |
                  (pTcuCtrl->tcuSendYx2.yx2ErrData2 & 0xE7) | // TODO -xg 北京入网检测修改
                  //	                (pTcuCtrl->tcuSendYx2.yx2ErrData3 & 0xAA) |
                  (pTcuCtrl->tcuSendYx2.yx2ErrData3 & 0xAB) |
                  (pTcuCtrl->tcuSendYx2.yx2ErrData4 & 0x06) |
                  (pTcuCtrl->tcuSendYx2.yx2Data6 & 0xBF) |
                  (pTcuCtrl->tcuSendYx2.yx2Data7 & 0x0F);
        alarmFlag = 0x00;
        //	      alarmFlag = (pTcuCtrl->tcuSendYx1.yx1ErrData3 & 0xFE) |
        alarmFlag = (pTcuCtrl->tcuSendYx1.yx1ErrData3 & 0x02) |
                    (pTcuCtrl->tcuSendYx1.yx1ErrData4 & 0xFE) | // TODO -xg 北京入网检测修改
                    (pTcuCtrl->tcuSendYx1.yx1ErrData5 & 0x0F) | // TODO -xg 北京入网检测修改
                    (pTcuCtrl->tcuSendYx2.yx2ErrData1 & 0x34) |
                    (pTcuCtrl->tcuSendYx2.yx2ErrData3 & 0x54) |
                    (pTcuCtrl->tcuSendYx2.yx2ErrData4 & 0x09) |
                    (pTcuCtrl->tcuSendYx2.yx2Data6 & 0x40) |
                    (pTcuCtrl->tcuSendYx2.yx2Data7 & 0x10);
    }
    else
    {
        errFlag = 0x00;
        errFlag = (pTcuCtrl->tcuSendYx1.yx1State & 0xF0) |
                  (pTcuCtrl->tcuSendYx1.yx1ErrData2 & 0xFF) |
                  //                  (pTcuCtrl->tcuSendYx1.yx1ErrData3 & 0x01) |
                  (pTcuCtrl->tcuSendYx1.yx1ErrData3 & 0xFD) |
                  (pTcuCtrl->tcuSendYx1.yx1ErrData4 & 0x01) | // TODO -xg 北京入网检测增加
                  (pTcuCtrl->tcuSendYx2.yx2ErrData1 & 0xCB) |
                  (pTcuCtrl->tcuSendYx2.yx2ErrData2 & 0xE7) | // TODO -xg 北京入网检测修改
                  //                  (pTcuCtrl->tcuSendYx2.yx2ErrData3 & 0xAA) |
                  (pTcuCtrl->tcuSendYx2.yx2ErrData3 & 0xAB) |
                  (pTcuCtrl->tcuSendYx2.yx2ErrData4 & 0x06);
        alarmFlag = 0x00;
        alarmFlag = (pTcuCtrl->tcuSendYx1.yx1ErrData3 & 0x02) |
                    (pTcuCtrl->tcuSendYx1.yx1ErrData4 & 0xFE) | // TODO -xg 北京入网检测修改
                    (pTcuCtrl->tcuSendYx1.yx1ErrData5 & 0x0F) | // TODO -xg 北京入网检测修改
                    (pTcuCtrl->tcuSendYx2.yx2ErrData1 & 0x34) |
                    (pTcuCtrl->tcuSendYx2.yx2ErrData3 & 0x54) |
                    (pTcuCtrl->tcuSendYx2.yx2ErrData4 & 0x01);
    }
    pTcuCtrl->tcuSendYx1.allErr = (errFlag != 0x00);
    pTcuCtrl->tcuSendYx1.allAlarm = (alarmFlag != 0x00);

    //    if (0x00 != pTcuCtrl->tcuSendYx1.allAlarm)
    //    {
    //        printf("pTcuCtrl->tcuSendYx1.yx1ErrData3 = %d \n", pTcuCtrl->tcuSendYx1.yx1ErrData3);
    //        printf("pTcuCtrl->tcuSendYx1.yx1ErrData4 = %d \n", pTcuCtrl->tcuSendYx1.yx1ErrData4);
    //        printf("pTcuCtrl->tcuSendYx1.yx1ErrData5 = %d \n", pTcuCtrl->tcuSendYx1.yx1ErrData5);
    //        printf("pTcuCtrl->tcuSendYx2.yx2ErrData1 = %d \n", pTcuCtrl->tcuSendYx2.yx2ErrData1);
    //        printf("pTcuCtrl->tcuSendYx2.yx2ErrData3 = %d \n", pTcuCtrl->tcuSendYx2.yx2ErrData3);
    //        printf("pTcuCtrl->tcuSendYx2.yx2ErrData4 = %d \n", pTcuCtrl->tcuSendYx2.yx2ErrData4);
    //    }
}

static void Send_YX1(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

#if 1
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    pBuf[index++] = Get_TcuJunctorId();
    pBuf[index++] = pTcuCtrl->tcuSendYx1.yx1State;
    pBuf[index++] = pTcuCtrl->tcuSendYx1.yx1ErrData2;
    pBuf[index++] = pTcuCtrl->tcuSendYx1.yx1ErrData3;
    pBuf[index++] = pTcuCtrl->tcuSendYx1.yx1ErrData4;
    pBuf[index++] = pTcuCtrl->tcuSendYx1.yx1ErrData5;
    pBuf[index++] = pTcuCtrl->tcuSendYx1.errBackUpId;
    pBuf[index++] = pTcuCtrl->tcuSendYx1.yx1ErrData7;

#else
    uint8 yxData[16] = {0};
    Get_TcuReportYx1(yxData, sizeof(yxData));

    pBuf[index] |= Get_TcuJunctorId();
    index++;

    pBuf[index] = Get_TcuWorkState();
    pBuf[index] |= yxData[index];
    index++;

    pBuf[index] |= yxData[index];
    index++;

    pBuf[index] |= yxData[index];
    index++;

    pBuf[index] |= yxData[index];
    index++;

    pBuf[index] |= yxData[index];
    index++;

    pBuf[index] |= yxData[index];
    index++;

    pBuf[index] |= yxData[index];
    index++;
#endif
    pOutLen[0] = index;

    return;
}
#if 0
const UP_ERR UP_ERR_YX2_MAP[] =
{
    { eErrType_CcuDoorOpenErr,                   	 8,       PGN_CCU_YX2 },
    { eErrType_K1Err,                          9,       PGN_CCU_YX2 },
    { eErrType_K2Err,                          9,       PGN_CCU_YX2 },
    { eErrType_ImdAlarm ,                       10,       PGN_CCU_YX2 },
    { eErrType_ReleaseErr,                      11,       PGN_CCU_YX2 },
    { eErrType_PileTempOverLimitAlarm ,         12,       PGN_CCU_YX1 },
    { eErrType_GunTempOverLimitAlarm,           13,       PGN_CCU_YX1 },
    { eErrType_AcJCQErr,                        14,       PGN_CCU_YX1 },
//    { eErrType_AcJCQErr,                   		14,       PGN_CCU_YX1 },
    { eErrType_AssistPowerErr,                  16,       PGN_CCU_YX1 },
//    { NULL,                   17,       PGN_CCU_YX1 },
//    { NULL,                   18,       PGN_CCU_YX1 },
//    { NULL,                   19,       PGN_CCU_YX1 },
//    { eErrType_BatteryReverseConnect,           20,       PGN_CCU_YX1 },
//    { eErrType_WaterLoggingErr,                 21,       PGN_CCU_YX1 },
    { eErrType_PcuDoorOpenErr,                     22,       PGN_CCU_YX1 },
    { eErrType_WaterLoggingErr,                 23,       PGN_CCU_YX1 },
    { eErrType_SmokeErr,                        24,       PGN_CCU_YX1 },
    { eErrType_ComErrWithPCU,                   25,       PGN_CCU_YX1 },
//    { eErrType_ComErrWithPCU,                   26,       PGN_CCU_YX1 },
    { eErrType_PCUOtherErr,                     27,       PGN_CCU_YX1 },
    { eErrType_PcuOtherAlarm,                   28,       PGN_CCU_YX1 },
//    { eErrType_SwComErr,                           29,       PGN_CCU_YX1 },
//    { eErrType_SwComAlarm,                           30,       PGN_CCU_YX1 },
    { eErrType_SwErr,              				31,       PGN_CCU_YX1 },
//    { eErrType_SwAlarm,                  32,       PGN_CCU_YX1 },
//	{ eErrType_BmsErr,            33,       PGN_CCU_YX1 },
//	{ eErrType_PowerModuleInVolOverAlarm,       34,       PGN_CCU_YX1 },
	{ eErrType_PowerModuleXfAlarm ,       		35,       PGN_CCU_YX1 },
};
#endif

static void Send_YX2(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

#if 1
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    // Get_Yx_Date();
    pBuf[index++] = Get_TcuJunctorId();
    pBuf[index++] = pTcuCtrl->tcuSendYx2.yx2ErrData1;
    pBuf[index++] = pTcuCtrl->tcuSendYx2.yx2ErrData2;
    pBuf[index++] = pTcuCtrl->tcuSendYx2.yx2ErrData3;
    pBuf[index++] = pTcuCtrl->tcuSendYx2.yx2ErrData4;
    pBuf[index++] = pTcuCtrl->tcuSendYx2.yx2Data5;
    pBuf[index++] = pTcuCtrl->tcuSendYx2.yx2Data6;
    pBuf[index++] = pTcuCtrl->tcuSendYx2.yx2Data7;

#else
    uint8 yxData[16] = {0};
    Get_TcuReportYx(yxData, sizeof(yxData));

    pBuf[index++] = Get_TcuJunctorId();

    pBuf[index] |= yxData[index + 8];
    index++;

    pBuf[index] |= yxData[index + 8];
    index++;
#endif
    pOutLen[0] = index;

    return;
}

static void Send_Fault(uint8 *pOutBuf, uint8 *pOutLen)
{
#if 1
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;

    pBuf[index++] = Get_TcuJunctorId();

    memcpy(&pBuf[index], tcuErroInfo, 7);
    index += 7;

    pOutLen[0] = index;
    return;
#else
    if (!Get_CcuCfgParaPlatform_convert())
    {
        uint8 index = pOutLen[0];
        uint8 *pBuf = pOutBuf;

        pBuf[index++] = Get_TcuJunctorId();

        memcpy(&pBuf[index], tcuErroInfo, 7);
        index += 7;

        pOutLen[0] = index;
        return;
    }
    else /*ykc*/
    {
        BEM_DATA strBEM;
        CEM_DATA strCEM;
        Get_BMS_Data(BMS_PGN_BEM, (void *)&strBEM);
        Get_BMS_Data(BMS_PGN_CEM, (void *)&strCEM);
        uint8 index = pOutLen[0];
        uint8 *pBuf = pOutBuf;
        pBuf[index++] = Get_TcuJunctorId();
        memcpy(&pBuf[index], (uint8 *)&strBEM, 4); /*取有用的4个字节*/
        index += 4;
        memcpy(&pBuf[index], (uint8 *)&strCEM, 4); /*取有用的4个字节*/
        index += 4;
        pOutLen[0] = index;
        return;
    }
#endif
}

static void Send_TcuFixSetAck(uint8 *pOutBuf, uint8 *pOutLen)
{
    //	pOutLen[0] = tcu_FixPara_SetAck(pOutBuf);
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    if (Platform_protocol_Xj == Get_CcuCfgParaPlatform_convert())
    {
        pBuf[index++] = Get_TcuJunctorId();
        memset(&pBuf[index], 0, 7);
        index += 7;
        pOutLen[0] = index;
        trace(TR_TCU_PROCESS, "<tx 81> : ");
        trace_buf(TR_TCU_PROCESS, pBuf, index);
    }
    else
    {
        pOutLen[0] = tcu_FixPara_SetAck(pBuf);
        trace(TR_TCU_PROCESS, "<tx 85> : ");
        trace_buf(TR_TCU_PROCESS, pBuf, pOutLen[0]);
    }
    return;
}

static void Send_TcuFixQueryAck(uint8 *pOutBuf, uint8 *pOutLen)
{
    pOutLen[0] = tcu_FixPara_QueryAck(pOutBuf);

    trace(TR_TCU_PROCESS, "<tx 83> : ");
    trace_buf(TR_TCU_PROCESS, pOutBuf, pOutLen[0]);

    return;
}

uint8 tcu_DebugAck(uint8 *buf)
{
    uint8 index = 0;
    TCU_DEBUG *pDebug = &tcuDebug;
    trace_buf(TR_TCU_PROCESS, &tcuDebug, sizeof(tcuDebug));
    if (pDebug->sendEn)
    {
        if (pDebug->cmd == 0x82) /**<  电压、电流校准命令应答*/
        {
            buf[index++] = pDebug->port;
            buf[index++] = pDebug->devType;
            buf[index++] = pDebug->comAdr;
            buf[index++] = pDebug->cmd;
            buf[index++] = pDebug->index & 0xFF;

            memcpy(&buf[index], pDebug->buf, pDebug->len - 5);

            index += pDebug->len - 5;

            printf("++++Tcu Adjust ACK :\n"); // TODO-xg 2020 0428
            print_buf(0, buf, index);
        }
        else /**<  参数读、写命令应答*/
        {
            buf[index++] = pDebug->port;
            buf[index++] = pDebug->devType;
            buf[index++] = pDebug->comAdr;
            buf[index++] = pDebug->cmd;
            *(uint16 *)&buf[index] = pDebug->index;
            index += 2;
            pDebug->len = pDebug->len < 6 ? 6 : pDebug->len;
            memcpy(&buf[index], pDebug->buf, pDebug->len - 6);
            index += pDebug->len - 6;
        }
    }
    pDebug->len = 0;
    pDebug->sendEn = 0;
    return index;
}

static void Send_TcuDebugAck(uint8 *pOutBuf, uint8 *pOutLen)
{
    if (eSendFlag_Yes == Get_TcuSendFlg(PGN_DEBUG_CMD_ACK))
    {
        if (0x00 != Get_TcuSendRemainTimer(PGN_DEBUG_CMD_ACK))
        {
            trace(TR_TCU_PROCESS, "关闭---TCU发送使能---%06X---调试应答帧\n", PGN_DEBUG_CMD_ACK);
            Set_TcuSendRemainTimer(PGN_DEBUG_CMD_ACK, 0x00);
        }
    }
    else
    {
        pOutLen[0] = tcu_DebugAck(pOutBuf);
    }
    return;
}
/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Send_TcuRstAck(uint8 *pOutBuf, uint8 *pOutLen)
{
    uint8 index = pOutLen[0];
    uint8 *pBuf = pOutBuf;
    pBuf[index++] = Get_TcuJunctorId();
    pBuf[index++] = 0x01;
    pBuf[index++] = Get_CcuToTcuAddr();
    pBuf[index++] = 0x01;
    pOutLen[0] = index;
    trace(TR_TCU_PROCESS, "<tx 91> : ");
    trace_buf(TR_TCU_PROCESS, pBuf, index);
    return;
    //    if ((CCU_WORK_STATE_FREE == Get_WorkState())
    //            || (CCU_WORK_STATE_STOP_FINISH == Get_WorkState()))
    //    {
    //        pOutBuf[index++] = 0x00;
    //    }
    //    else
    //    {
    //        pOutBuf[index++] = 0x01;
    //    }
}
// 重新打包
static uint8 Tcu_RePackCutFrame(uint8 *pBuf, uint8 *pLen)
{
    uint8 outBuf[256] = {0};
    uint8 dataLen = pLen[0];
    uint16 cs = 0;
    uint8 frameNo = 0, index = 0;
    uint8 outPos = 0, inPos = 0;
    uint8 lastFrameDataLen = 0;
    uint8 frameCnt = 2;
    // 先求总帧数
    //    if (9 == dataLen)
    if (dataLen <= 9)
    {
        frameCnt = 2;
    }
    else
    {
        frameCnt += (((dataLen - 9) % 7 == 0) ? 0 : 1) + (dataLen - 9) / 7;
    }
    // 获取校验码
    cs = Get_Cs16(pBuf, dataLen) + dataLen + frameCnt;
    // 填数据
    //    print_buf(0,pBuf,dataLen);
    //    printf("cs = %d,dataLen = %d,frameCnt = %d\n",cs,dataLen ,frameCnt);
    for (frameNo = 0; frameNo < frameCnt; frameNo++)
    {
        for (index = 0; index < 8; index++)
        {
            if (0 == frameNo) // 首帧
            {
                if (0 == index) // 帧序号
                {
                    outBuf[outPos++] = frameNo + 1;
                }
                else if (1 == index) // 总帧数
                {
                    outBuf[outPos++] = frameCnt;
                }
                else if (2 == index) // 有效数据长度低字节
                {
                    outBuf[outPos++] = (uint8)dataLen;
                }
                else if (3 == index) // 有效数据长度高字节
                {
                    outBuf[outPos++] = (uint8)(dataLen >> 8);
                }
                else // 填有效数据
                {
                    outBuf[outPos++] = pBuf[inPos++];
                }
            }
            else if ((frameCnt - 1) == frameNo) // 尾帧
            {
                if (0 == index) // 帧序号
                {
                    outBuf[outPos++] = frameNo + 1;
                    lastFrameDataLen = dataLen - inPos;

                    //                    printf("\n");
                    //                    printf("frameCnt = %d \n", frameCnt);
                    //                    printf("frameNo = %d \n", frameNo);
                    //                    printf("dataLen = %d \n", dataLen);
                    //                    printf("inPos = %d \n", inPos);
                    //                    printf("cs = %04x \n", cs);
                    //                    printf("lastFrameDataLen = %d \n", lastFrameDataLen);
                    //                    printf("\n");
                }
                else if ((lastFrameDataLen + 1) == index) // 校验码低字节
                {
                    outBuf[outPos++] = (uint8)cs;
                }
                else if ((lastFrameDataLen + 2) == index) // 校验码高字节
                {
                    outBuf[outPos++] = (uint8)(cs >> 8);
                }
                else // 填有效数据
                {
                    outBuf[outPos++] = pBuf[inPos++];
                }
                if ((((dataLen - 4 + 1) % 7) == 0) && (index == 1))
                {
                    outBuf[outPos - 1] = (uint8)(cs >> 8);
                }
            }
            else // 中间帧
            {
                if (0 == index) // 帧序号
                {
                    outBuf[outPos++] = frameNo + 1;
                }
                else // 填有效数据
                {
                    outBuf[outPos++] = pBuf[inPos++];
                }
                if ((((dataLen - 4 + 1) % 7) == 0) && ((frameCnt - 2) == frameNo) && (index == 7)) /**倒数二帧*/
                {
                    outBuf[outPos - 1] = (uint8)cs;
                }
            }

            if (outPos >= (sizeof(outBuf) - 1))
            {
                return 0;
            }
        }
    }

    memset(pBuf, 0x00, dataLen);
    memcpy(pBuf, outBuf, outPos);

    return frameCnt;
}

/**
 ******************************************************************************
 * @brief      Set_TcuSendFlg
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]   SEND_FLAG  enableFlg  已发送标记
 * @param[out]
 * @retval
 *
 * @details     0x55-已发送、0x00-未发送、0xFF-无效
 *
 * @note        设置发送标记
 ******************************************************************************
 */
void Set_TcuSendFlg(uint32 pgn, SEND_FLAG sendFlg)
{
    TCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_TcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->sendFlag = sendFlg;
    return;
}

/**
 ******************************************************************************
 * @brief      Get_TcuSendFlg
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval      SEND_FLAG  enableFlg  已发送标记
 *
 * @details     0x55-已发送、0x00-未发送、0xFF-无效
 *
 * @note        获取发送标记
 ******************************************************************************
 */
SEND_FLAG Get_TcuSendFlg(uint32 pgn)
{
    TCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_TcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return eSendFlag_Null;
    }

    return pSendCtrl->sendFlag;
}

/**
 ******************************************************************************
 * @brief      设置上次发送时间
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details     立即发送给countVal赋值FFFF
 *
 * @note        设置计时器
 ******************************************************************************
 */
void Set_TcuLastSendTimer(uint32 pgn, uint32 countVal)
{
    TCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_TcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->lastSendTimer = tickGet() + countVal;
}

/**
 ******************************************************************************
 * @brief      获取Tcu上次发送时间
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static uint32 Get_TcuLastSendTimer(uint32 pgn)
{
    TCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_TcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return tickGet();
    }

    return pSendCtrl->lastSendTimer;
}

/**
 ******************************************************************************
 * @brief      Set_TcuSendRemainTimer
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]   uint32 countValue          计时器初始值
 * @param[out]
 * @retval
 *
 * @details     0xFFFF表示永久发送
 *
 * @note        设置计时器
 ******************************************************************************
 */
void Set_TcuSendRemainTimer(uint32 pgn, uint32 countValue)
{
    TCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_TcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->sendRemainTimer = countValue;
    return;
}

/**
 ******************************************************************************
 * @brief      Get_TcuSendRemainTimer
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval      返回发送剩余时间
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
uint32 Get_TcuSendRemainTimer(uint32 pgn)
{
    TCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_TcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return 0;
    }

    return pSendCtrl->sendRemainTimer;
}

/**
 ******************************************************************************
 * @brief      设置TCU上次可以发送起始时间
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details
 *
 * @note        设置计时器
 ******************************************************************************
 */
void Set_TcuStartTimer(uint32 pgn)
{
    TCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_TcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return;
    }

    pSendCtrl->sendStartTimer = tickGet();
    return;
}

/**
 ******************************************************************************
 * @brief      获取刚给发送剩余时间赋值的时刻
 * @param[in]   uint32 Pgn                参数组编号
 * @param[in]
 * @param[out]
 * @retval
 *
 * @details     后面的程序周期检测当前tick与这个时间的差值，必须小于发送剩余时间
 *
 * @note
 ******************************************************************************
 */
uint32
Get_TcuStartTimer(uint32 pgn)
{
    TCU_SEND_CTRL *pSendCtrl = NULL;

    pSendCtrl = Get_TcuSendCtrl(pgn);

    if (NULL == pSendCtrl)
    {
        return 0;
    }

    return pSendCtrl->sendStartTimer;
}

/**
 ******************************************************************************
 * @brief       获取缓存中待发的分帧数据
 * @param[in]   NONE
 * @param[in]
 * @param[out]
 * @retval
 * @details
 *
 * @note
 ******************************************************************************
 */
static bool_e
Get_CutFrame(uint8 *pOutBuf, uint32 *pCanId, uint8 *dex)
{
    static uint8 index = 0;
    CUT_FRAME_CTRL *pCutFrameCtrl = NULL;

    dex[0] = index;

    for (; index < FCNT(tcuCutFrameCtrl);)
    {
        pCutFrameCtrl = &tcuCutFrameCtrl[index];
        index++;
        if (pCutFrameCtrl->frameCnt == 0)
        {
            continue;
        }
        pCanId[0] = pCutFrameCtrl->canID.canId;
        memcpy(pOutBuf, pCutFrameCtrl->dataBuf + pCutFrameCtrl->framePos * 8, 8);
        pCutFrameCtrl->framePos++;
        if (pCutFrameCtrl->framePos >= pCutFrameCtrl->frameCnt)
        {
            memset(pCutFrameCtrl, 0x00, sizeof(CUT_FRAME_CTRL));
        }
        return TRUE;
    }
    if (index == FCNT(tcuCutFrameCtrl))
    {
        index = 0;
    }
    return FALSE;
}

/**
 ******************************************************************************
 * @brief       将需要分帧发送的数据压入缓存里
 * @param[in]   uint8 *pInBuf    分帧数据
 *              uint32 canId     分帧ID
 *              uint8 frameCnt   分帧数量
 * @param[out]
 * @retval
 * @details
 *
 * @note
 ******************************************************************************
 */
static void Push_CutFrame(uint8 *pInBuf, uint32 canId, uint8 frameCnt)
{
    CUT_FRAME_CTRL *pCutFrameCtrl = NULL;

    for (uint8 index = 0; index < FCNT(tcuCutFrameCtrl); index++)
    {
        pCutFrameCtrl = &tcuCutFrameCtrl[index];

        if (pCutFrameCtrl->frameCnt != 0)
        {
            continue;
        }

        pCutFrameCtrl->frameCnt = frameCnt;
        pCutFrameCtrl->canID.canId = canId;

        if (frameCnt * 8 <= MAX_CUTFRAME_BUF_SIZE)
        {
            memcpy(pCutFrameCtrl->dataBuf, pInBuf, frameCnt * 8);
        }

        break;
    }
}

/**
 ******************************************************************************
 * @brief       初始化分帧相关变量
 * @param[in]   NONE
 * @param[in]
 * @param[out]
 * @retval
 * @details
 *
 * @note
 ******************************************************************************
 */
void Init_TcuCutFrame(void)
{
    memset(tcuCutFrameCtrl, 0x00, sizeof(tcuCutFrameCtrl));
}

extern void Clr_TcuErrInfo(void);
/**
 ******************************************************************************
 * @brief       Tcu发送服务
 * @param[in]   NONE
 * @param[in]
 * @param[out]
 * @retval
 * @details     1.分帧10ms发一帧
 *              2.循环检测各命令帧的发送剩余时间，发送周期
 * @note
 ******************************************************************************
 */
void Tcu_SendServer()
{
    const TCU_SEND_DEAL *pSendDeal = NULL;
    static uint32 s_Tick = 0;
    uint32 canId = 0;
    uint8 len = 0;
    uint8 buf[256] = {0};
    uint8 index = 0;
    uint8 frameCnt = 0;
#if 0
    //处理分帧
    if (abs(tickGet() - s_Tick) >= 1)
    {
        while (TRUE == Get_CutFrame(buf, &canId, &index))
        {
            can_send(TCU_CHAN,canId, buf, 8);
            s_Tick = tickGet();
            trace(TR_CH1, "  [S0: %08X]:  ", canId);
            trace_buf(TR_CH1, buf, 8);
            taskDelay(10);
        }
    }
#endif
    for (index = 0; index < FCNT(TCU_SEND_DEAL_TABLE); index++)
    {
        pSendDeal = &TCU_SEND_DEAL_TABLE[index];
        if (0xFFFF != Get_TcuSendRemainTimer(pSendDeal->pgn))
        {
            if (abs(tickGet() - Get_TcuStartTimer(pSendDeal->pgn)) > Get_TcuSendRemainTimer(pSendDeal->pgn))
            {
                Set_TcuSendRemainTimer(pSendDeal->pgn, 0x00);

                if (PGN_CCU_FAULT == pSendDeal->pgn)
                {
                    Clr_TcuErrInfo(); // TODO-开普修改
                }
            }
            if (0 == Get_TcuSendRemainTimer(pSendDeal->pgn))
            {
                continue;
            }
        }

        if (abs(tickGet() - Get_TcuLastSendTimer(pSendDeal->pgn)) < pSendDeal->pSCycle())
        {
            continue;
        }
        //        Set_TcuLastSendTimer(pSendDeal->pgn, 0);
        //        Set_TcuSendFlg(pSendDeal->pgn, eSendFlag_Yes); //放置此处，对调试命令有影响

        if (NULL == pSendDeal->pSendFunc)
        {
            continue;
        }

        len = 0;
        memset(buf, 0x00, sizeof(buf));
        pSendDeal->pSendFunc(buf, &len);
        Deal_TcuLogPackage(pSendDeal->pgn, buf, len); // 保存发送帧记录log
        canId = GetCanID(pSendDeal->pgn, pSendDeal->prio, Get_CcuToTcuAddr(), TCU_ADDR);
        if ((len > 8) || (PGN_FIX_QUERY_ACK == pSendDeal->pgn) || (PGN_FIX_SET_ACK == pSendDeal->pgn && (Get_CcuCfgParaPlatform_convert() != Platform_protocol_Xj)) || (PGN_DEBUG_CMD_ACK == pSendDeal->pgn && (Get_CcuCfgParaPlatform_convert() != Platform_protocol_Xj)) || (PGN_FIX_SET_MUL_ACK == pSendDeal->pgn))
        {
#if 0
            frameCnt = Tcu_RePackCutFrame(buf, &len);
            Push_CutFrame(buf + 8, canId, frameCnt - 1);
            can_send(TCU_CHAN, canId, buf, 8);
            trace(TR_CH1, "  开始分帧: %08X, 分帧数：%d, 帧长： %d\n", canId, frameCnt, len);
            trace(TR_CH1, "  [S1:%08X]: ", canId);
            trace_buf(TR_CH1, buf, len);
            s_Tick = tickGet();
            //taskDelay(10);

#else
            frameCnt = Tcu_RePackCutFrame(buf, &len);
            for (int i = 0; i < frameCnt; i++)
            {
                can_send(TCU_CHAN, canId, buf + i * 8, 8);
                trace(TR_CH1, "<S1: %X>  ", canId);
                trace_buf(TR_CH1, buf + i * 8, 8);
                taskDelay(0);
            }
#endif
        }
        else
        {
            can_send(TCU_CHAN, canId, buf, 8);
            trace(TR_CH1, "<S1: %X>  ", canId);
            trace_buf(TR_CH1, buf, len);
            // taskDelay(1);
        }

        Set_TcuLastSendTimer(pSendDeal->pgn, 0);
        Set_TcuSendFlg(pSendDeal->pgn, eSendFlag_Yes);

        //        Set_TcuLastSendTimer(pSendDeal->pgn, 0x00);
    }
}
/*----------------------------tcuSendCtrl.c--------------------------------*/
