/**
 ******************************************************************************
 * @file       publicPort.h
 * @brief      API include file of publicPort.h.
 * @details    This file including all API functions's declare of publicPort.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef _TRANSCEIVER_H
#define _TRANSCEIVER_H
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/
#define SEND_FOREVER (0xFFFFFFFF)
#define SEND_ONCE (0xFFFFFFFE)

#define RECEIVER_MAX_LENGTH (128) /**<  接收器最大数量*/
#define TRANSFER_MAX_LENGTH (128) /**<  发送器最大数量*/

#define CHANNEL_MAX_LENGTH (16) /**<  通道最大数量*/
#define DEVICE_MAX_LENGTH (64)  /**<  设备最大数量*/

#define FRAM_TRANSFER_TICK (50) /**<  分帧发送间隔*/
/*-----------------------------------------------------------------------------
 Section: Type Enumes
 ----------------------------------------------------------------------------*/
typedef enum
{
    eEnableFlag_Off,
    eEnableFlag_On,
} ENABLE_FLG_ONOFF;
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
typedef int (*RECEIVER_PROC_CALLBACK)(int dev, uint8 *pBuf, int rxLen);
typedef int (*RECEIVER_TIMEOUT_PROC_CALLBACK)(int dev);
typedef int (*TRANSFER_PROC_CALLBACK)(int channel, int dev, uint8 *pBuf, int bufSize);
typedef int (*RECEIVER_DEALY_CALLBACK)(void);
typedef int (*DEVICE_ADDR_CHECK_PROC)(void);

typedef int (*CHANNEL_RECEIVE_PROC)(int channel_id,
                                    int *dev_id,
                                    int *id,
                                    uint8 rxBuf[],
                                    int rxLen);
typedef int (*CHANNEL_TRANSFER_PROC)(int channel_id,
                                     int dev_id,
                                     int id,
                                     uint8 txBuf[],
                                     int txLen);
/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/**
 * 分帧数据包;
 */
#pragma pack(1)
typedef struct _RECV_FRAME_PACKAGE
{
    uint8 index;  /**< 序号*/
    uint8 buf[0]; /**< 数据缓存*/
} TRANSEIVER_FRAME_PACKAGE;
#pragma pack()

/**
 * 多帧数据;
 */
#pragma pack(1)
typedef struct _TRANSCEIVER_FRAME_STR
{
    uint8 toatal; /**< 总帧数*/
    uint16 len;   /**< 数据长度*/
    uint8 buf[0]; /**< 数据指针*/
} TRANSCEIVER_FRAME_STR;
#pragma pack()

typedef struct _TRANSFER_CTRL
{
    uint8 enable;      /**<  */
    uint8 frameEnable; /**<  */
    uint8 complete;    /**<  */
    uint8 flag;        /**<  */
    uint32 remainTime; /**<  */
    uint32 lastTime;   /**<  */
    uint32 startTime;  /**<  */
} TRANSFER_CTRL;

#define TRANSFER_FRAME_SIZE (7)   // 分帧数据单帧大小
#define TRANSFER_FRAME_FIFO (256) // 分帧数据缓存大小
typedef struct _FRAME_TRANSFER_CTRL
{
    uint8 enable;    /**<  */
    uint8 bussy;     /**<  */
    uint32 interval; /**<  */
    uint32 lastTime; /**<  */
    int id;
    int index;
    int total;
    int length;
    uint8 data[TRANSFER_FRAME_FIFO];
} FRAME_TRANSFER_CTRL;

typedef struct _RECEIVER_CTRL
{
    uint8 enable;        /**<  */
    uint8 flag;          /**<  */
    uint8 timeoutEnable; /**<  */
    uint8 timeoutFlag;   /**<  */
    uint32 lastTime;     /**<  */
    uint8 frameEnable;   /**<  */
    uint8 index;
    uint8 data[TRANSFER_FRAME_FIFO]; /**<  */
} RECEIVER_CTRL;

typedef struct _RECEIVER_STRUCT
{
    int id;                                          /**<  */
    int length;                                      /**<  */
    char *data;                                      /**<  */
    RECEIVER_CTRL *ctrl_reg;                         /**<  */
    RECEIVER_PROC_CALLBACK proc;                     /**<  */
                                                     //    RECEIVER_DEALY_CALLBACK timeout;       /**<  */
    int timeout_ms;                                  /**<  */
    RECEIVER_TIMEOUT_PROC_CALLBACK timeout_proc;     /**<  */
    RECEIVER_TIMEOUT_PROC_CALLBACK timeout_rst_proc; /**<  */
} RECEIVER_STRUCT;

typedef struct _TRANSFER_STRUCT
{
    int id;                      /**<  */
    int length;                  /**<  */
    char *data;                  /**<  */
    TRANSFER_CTRL *ctrl_reg;     /**<  */
    TRANSFER_PROC_CALLBACK proc; /**<  */
                                 //    RECEIVER_DEALY_CALLBACK period;      /**<  */
    int period_ms;               /**<  */
} TRANSFER_STRUCT;

typedef struct _DEVICE_STRUCT
{
    int deviceId;                                         /**<  */
    RECEIVER_STRUCT *receiver_array[RECEIVER_MAX_LENGTH]; /**<  */
    TRANSFER_STRUCT *transfer_array[TRANSFER_MAX_LENGTH]; /**<  */
                                                          //    DEVICE_RECEIVE_PROC   receive_proc;
                                                          //    DEVICE_TRANSFER_PROC  transfer_proc;
    DEVICE_ADDR_CHECK_PROC check_addr;
    FRAME_TRANSFER_CTRL frameTransfer;
} DEVICE_STRUCT;

typedef struct _CHANNEL_STRUCT
{
    int channelId;                                  /**<  */
    DEVICE_STRUCT *device_array[DEVICE_MAX_LENGTH]; /**<  */
    CHANNEL_RECEIVE_PROC receiv_proc;
    CHANNEL_TRANSFER_PROC transfer_proc;
} CHANNEL_STRUCT;
/*-------------------------------------------- ---------------------------------
 Section: Function Prototypes callback
 ----------------------------------------------------------------------------*/
// 设备通道收发函数注册
extern void
reg_channel(int channel,                      /**< 通道号*/
            CHANNEL_RECEIVE_PROC recvFunc,    /**< 通道接收函数*/
            CHANNEL_TRANSFER_PROC transFunc); /**< 通道发送函数*/

// 设备接收器注册
extern void
reg_receiver(int channel,                                    /**< 通道号*/
             int dev,                                        /**< 设备号*/
             int id,                                         /**< 功能码*/
             int length,                                     /**< 接收数据块长度*/
             RECEIVER_PROC_CALLBACK proc,                    /**< 接收处理函数*/
             int timeout_ms,                                 /**< 接收超时 */
             RECEIVER_TIMEOUT_PROC_CALLBACK timeout_proc,    /**< 接收超时处理函数*/
             RECEIVER_TIMEOUT_PROC_CALLBACK timeout_proc_rst /**< 超时复位处理函数*/
);

// 设备发送器注册
extern void
reg_transfer(int channel,                 /**< 通道号*/
             int dev,                     /**< 设备号*/
             int id,                      /**< 功能码*/
             int length,                  /**< 发送数据块长度*/
             TRANSFER_PROC_CALLBACK proc, /**< 发送处理函数*/
             int period_ms);              /**< 发送周期,单位:ms*/
/*-------------------------------------------- ---------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
// 收发器处理函数.
extern void
transceiver_init(void);

extern void
transceiver_handle(void);

extern void
dual_transceiver_handle(void);

extern int
get_receiver_data(int channel, int dev, int id, void **ptr);

extern int
get_transfer_data(int channel, int dev, int id, void **ptr);

extern void
set_send_enable(int channel, int dev, int id, uint8 value);

extern void
set_send_lastTime(int channel, int dev, int id, int time);

extern void
set_send_remainTime(int channel, int dev, int id, uint32 time);

extern uint8
get_sendFlag(int channel, int dev, int id);

extern uint8
get_sendCompleteFlag(int channel, int dev, int id);

extern void
set_recv_enable(int channel, int dev, int id, uint8 value);

extern void
set_recv_lastTime(int channel, int dev, int id, int time);

extern uint8
get_recvFlag(int channel, int dev, int id);

extern uint8
get_recvTimeoutFlag(int channel, int dev, int id);

extern void
set_recvTimeout_enable(int channel, int dev, int id, uint8 enable);

extern void
set_frame_send_enable(int channel, int dev, int id, uint8 value);

extern void
set_frame_recv_enable(int channel, int dev, int id, uint8 value);

extern void
set_send_startTime(int channel, int dev, int id, int time);

extern uint8
check_send_enable(int channel, int dev, int id);

uint8 check_recv_enable(int channel, int dev, int id);

void set_frame_recv_enable(int channel, int dev, int id, uint8 value);

void set_frame_send_enable(int channel, int dev, int id, uint8 value);

void set_sendCompleteFlag(int channel, int dev, int id, uint8 value);

uint8 get_sendCompleteFlag(int channel, int dev, int id);
#endif
/*--------------------------End of publicPort.h----------------------------*/
