#ifndef __MODBUS_RT_PLATFORM_THREAD_H_
#define __MODBUS_RT_PLATFORM_THREAD_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <signal.h>

#ifdef __cplusplus
extern "C"
{
#endif
#define MQ_SEM_MAX_MSGS 64
#include <taskLib.h>

    typedef struct modbus_rt_sem
    {
        SEM_ID sem;
    } modbus_rt_sem_t;

    typedef struct modbus_rt_mutex
    {
        SEM_ID mutex;
    } modbus_rt_mutex_t;

    typedef struct modbus_rt_thread
    {
        TASK_ID tid;
    } modbus_rt_thread_t;

    int modbus_rt_sem_init(modbus_rt_sem_t *m);
    int modbus_rt_sem_wait(modbus_rt_sem_t *m, uint32_t timeout);
    int modbus_rt_sem_post(modbus_rt_sem_t *m);
    void modbus_rt_sem_destroy(modbus_rt_sem_t *m);

    int modbus_rt_mutex_init(modbus_rt_mutex_t *m);
    int modbus_rt_mutex_lock(modbus_rt_mutex_t *m);
    int modbus_rt_mutex_unlock(modbus_rt_mutex_t *m);
    void modbus_rt_mutex_destroy(modbus_rt_mutex_t *m);

    modbus_rt_thread_t *modbus_rt_thread_init(const char *name, void (*entry)(void *parameter),
                                              void *parameter, uint32_t stack_size,
                                              uint32_t priority, uint32_t tick);
    void modbus_rt_thread_startup(modbus_rt_thread_t *thread);
    void modbus_rt_thread_destroy(modbus_rt_thread_t *thread);

    /* 新增：基于消息队列的计数信号量 (指针队列版本) */
    typedef struct modbus_rt_mq_sem
    {
        MSG_Q_ID qid;
    } modbus_rt_mq_sem_t;

    int modbus_rt_mq_sem_init(modbus_rt_mq_sem_t *m);
    int modbus_rt_mq_sem_wait(modbus_rt_mq_sem_t *m, uint32_t timeout);
    int modbus_rt_mq_sem_post(modbus_rt_mq_sem_t *m);
    void modbus_rt_mq_sem_destroy(modbus_rt_mq_sem_t *m);
    int modbus_rt_mq_sem_get_count(modbus_rt_mq_sem_t *m);
#ifdef __cplusplus
}
#endif

#endif /* __PKG_MODBUS_RT_PLATFORM_THREAD_H_ */
