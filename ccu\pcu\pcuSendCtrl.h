/**
 ******************************************************************************
 * @file       pcuSendCtrl.h
 * @brief      API include file of pcuSendCtrl.h.
 * @details    This file including all API functions's declare of pcuSendCtrl.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef __PCU_SEND_CTRL_H__
#define __PCU_SEND_CTRL_H__
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <types.h>
/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
void Pcu_SendServer(void);

uint32 Get_PcuSendRemainTimer(uint32 pgn);

void Set_PcuSendRemainTimer(uint32 pgn, uint32 countValue);

SEND_FLAG Get_PcuSendFlg(uint32 pgn);

void Set_PcuSendFlg(uint32 pgn, SEND_FLAG sendFlg);

void Set_PcuSendTimer(uint32 pgn, uint32 countValue);

void Get_PcuYxYc1Data(uint8 *pOutData);

#endif //__PCU_SEND_CTRL_H__

/*--------------------------End of pcuSendCtrl.h----------------------------*/
