/**
 ******************************************************************************
 * @file       bmsRecvCtrl.h
 * @brief      API include file of bmsRecvCtrl.h.
 * @details    This file including all API functions's declare of bmsRecvCtrl.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */

#ifndef __BMS_RECV_CTRL_H__
#define __BMS_RECV_CTRL_H__
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include "bmsMain.h"
#include "bmsDll.h"
/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
void Set_BmsRecvEnable(uint32 pgn, RECV_ENABLE enableFlg);

RECV_ENABLE Get_BmsRecvEnable(uint32 pgn);

void Set_BmsRecvFlag(uint32 pgn, RECV_FLAG RecvFlg);

RECV_FLAG Get_BmsRecvFlag(uint32 pgn);

TIMER_ENABLE Get_BmsRecvTimerEnable(uint32 pgn);

void Set_BmsRecvTimerEnable(uint32 pgn, TIMER_ENABLE enableFlg);

void Set_BmsRecvTimer(uint32 pgn, uint32 countValue);

void Bms_Decode(CAN_FRAME *pCanFrame);

void Bms_RecvTimerManage(void);

void Bms_OverTimeDeal(void);

BMS_RECV_CTRL *Get_BmsRecvCtrl(uint32 pgn);

uint32 Bms_OverTimeTick(uint8 flag, uint32 tick);

extern void Set_k1k2OffDelay(uint32 delay);
#endif //__BMS_RECV_CTRL_H__
/*--------------------------End of bmsRecvCtrl.h----------------------------*/
