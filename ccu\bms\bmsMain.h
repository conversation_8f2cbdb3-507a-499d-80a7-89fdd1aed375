/**
 ******************************************************************************
 * @file       bmsMain.h
 * @brief      API include file of bmsMain.h.
 * @details    This file including all API functions's declare of bmsMain.h.
 * @copy       Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef __BMS_MAIN_H__
#define __BMS_MAIN_H__

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <can.h>
#include <bms.h>
/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ---------------------------------------------------------------------------*/

/**< BMS与充电机节点地址 */
#define ADDR_CHARGER 0x56 /**< 充电机地址 */
#define ADDR_BMS 0xF4     /**< BMS地址 */

/**< CAN通道                         */
#define BMS_CHAN CAN_2

/**< BMS任务调用周期         */
#define BMS_CALL_CYCLE 1

#define BMS_PGN_CNT 15     // 14               /**< 接收BMS的PGN的数量  （新增BMS快充报文）*/
#define CHARGER_PGN_CNT 10 // 9                /**< 充电机发送PGN的数量 （新增充电机快充报文）*/
/*------------------------------------------------------------------------
 Section: Type Definitions
 ------------------------------------------------------------------------*/
/**< Bms发送控制结构                         */
typedef struct BMS_SEND_CTRL_STRU
{
    uint32 sendRemainTimer; /**< 发送剩余时间 */
    uint32 sendStartTimer;  /**< 上次可以发送起始时间 */
    uint8 sendFlg;          /**< 已发送标记 */
    uint32 lastSendTimer;   /**< 上次发送时间 */

} BMS_SEND_CTRL;

/**< Bms接收控制结构                         */
typedef struct BMS_RECV_CTRL_STRU
{
    uint8 recvEnableFlg;      /**< 接收使能标记 */
    uint8 recvFlg;            /**< 接收标记 */
    uint8 recvTimerEnableFlg; /**< 计时器使能标记 */
    uint32 recvTimer;         /**< 接收超时计时 */
} BMS_RECV_CTRL;

/**< bms数据                */
typedef struct BMS_DATA_STRU
{
    BHM_DATA strBHM;
    BRM_DATA strBRM;
    BCP_DATA strBCP;
    BRO_DATA strBRO;
    BCL_DATA strBCL;
    BCS_DATA strBCS;
    BSM_DATA strBSM;
    BMV_DATA strBMV;
    BMT_DATA strBMT;
    BSP_DATA strBSP;
    BST_DATA strBST;
    BSD_DATA strBSD;
    BEM_DATA strBEM;
    CSD_DATA strCSD;
    CEM_DATA strCEM;
    /************适配双枪400A以上大电流********************************/
    CFC_DATA strCFC;
    BFC_DATA strBFC;

} BMS_DATA;

/**< bms主控制结构   */
typedef struct BMS_CTRL_STRU
{
    uint8 bmsStage;            /**< bms阶段  */
    uint8 shakeHandResult;     /**< 握手结果 */
    uint8 spn_2560;            /**< 辨识结果 */
    uint8 Dual_Bfc;            /**< BFC应答 */
    uint8 Dual_Cfc;            /**< CFC应答 */
    uint8 Dual_current_offset; /**< 电流偏移 */
    uint8 spn_2830;            /**< 配置结果 */
    uint8 lastBROState;        /**< 上一次的bro状态 */
    uint8 evseIsolationStatus; /**< 欧标增加    0无效 1有效2警告3错误 */
    uint32 stopBmsDelay;       /**< bms服务停止延时 */

    uint8 tryConnectCnt;      /**< 超时重连次数 */
    uint32 overTimePgnforTcu; /**< 超时PGN(TCU用) */
    uint32 overTimePgnforBms; /**< 超时PGN(BMS用) */
    uint32 comState;          /**< 通信状态 */
    uint32 overTimeDealFlg;   /**< 超时处理标记（外部处理置标记，
                                                                              内部清)<0x55> 成功处理 */
    BMS_SEND_CTRL bmsSendCtrl[CHARGER_PGN_CNT];
    BMS_RECV_CTRL bmsRecvCtrl[BMS_PGN_CNT];
} BMS_CTRL;
/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
/* NONE */
void Stop_BMS(bool_e flg);
void Bms_Init(void);
void Bms_Task(void);
uint8 Get_BmsStage(void);

uint8 Get_HighestSingleVoltageNO(void);
uint8 Get_HighestTemperature(void);
uint8 Get_HighestTemperatureNO(void);
uint8 Get_LowestTemperature(void);
uint8 Get_LowestTemperatureNO(void);
BATTERY_STATE Get_StrBatteryState(void);
BATTERY_CTRL Get_StrBatteryCtrl(void);
uint16 Get_Bcp_HighestCurrent(void);
uint16 Get_HighestCurrentLimit(void);
bool IsBmsFastChargePrivateAgreement(void);
#endif
/*--------------------------End of bmsMain.h----------------------------*/
