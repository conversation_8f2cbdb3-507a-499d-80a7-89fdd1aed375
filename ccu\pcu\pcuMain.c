/**
 ******************************************************************************
 * @file      pcuMain.c
 * @brief     C Source file of pcuMain.c.
 * @details   This file including all API functions's
 *            implement of pcuMain.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <can.h>
#include <string.h>
#include <taskLib.h>
#include <dmnLib.h>
#include <stdlib.h>
#include <ttylib.h>
#include <board\SXDC_4_0_1\SXDC_4_0_1.h>
#include <ccu/bsn/sample.h>
#include <stdio.h>
#include <sxlib.h>
#include <trace.h>

#include <ccu\bsn\deviceState.h>
#include <ccu\charge\ccuChargeMain.h>
#include <ccu\lib\ccuLib.h>
#include <ccu\lib\ccuCanFrame.h>
#include <ccu\lib\ccuFix.h>
#include "pcuMain.h"
#include "pcuRecvCtrl.h"
#include "pcuSendCtrl.h"
#include <ccu\bsn\io.h>
#include <test.h>
#include "bms.h"
#include "maths.h"
#include <ccu\para\para.h>

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
// typedef void (*PdealFunc)(void);
// typedef struct PCU_STAGE_DEAL_STRU
//{
//     uint8       stage;
//     PdealFunc   func;
// }PCU_STAGE_DEAL;

#define MAX_PARA_MAP_SIZE 32

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
uint8 g_PcuTaskDelay = 0;

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
static int s_FdCan;                                          /**< CAN_3句柄 */
static uint8 s_PcuAddr;                                      /**< PCU地址 */
static bool_e s_PcuAddrRecvSuccFlag;                         /**< PCU地址接收成功标记 */
static RESULT s_SelfCheckStartSuccFlag;                      /**< 自检成功标记 */
static PARA_OPERATE s_CurParaOperate;                        /**< 定值操作对象 */
static PARA_OPERATE s_ParaNeedOperateMap[MAX_PARA_MAP_SIZE]; /**< 定值操作列表 */
static PCU_STATE s_PcuState = {0};
PCU_EXYC_DATA s_pcuExYcData;                             /**<PCU扩展数据*/
PCU_WS_DATA *pPcuWsData = &s_PcuState.pcuWs;             /**< pcu工作状态及故障信息帧数据指针 */
PCU_YXYC_DATA *pPcuYXYCData = &s_PcuState.pcuYxYc;       /**< PCU遥信遥测数据指针 */
PCU_ALARM_DATA *pPcuAlarmData = &s_PcuState.pcuAlarm;    /**< PCU告警数据指针 */
PCU_YK_ACK_DATA *pPcuYkCmdAck = &s_PcuState.pcuYkCmdAck; /**< PCU遥控应答指针 */
PCU_EXYC_DATA *pPcuExYcData = &s_pcuExYcData;            /**< PCU遥控应答指针 */
PCU_YC2_DATA *pPcuYc2Data = &s_PcuState.pcuYc2;          /**< PCU遥测 */
/**
 ******************************************************************************
 * @brief       PCU阶段设置
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note        设置 PCU操作阶段，同时清除超时标记
 * @note
 *
 ******************************************************************************
 */
static void pcuSetStage(PCU_STATE *pPcuState, pcu_ctrl_stage stage)
{
    pPcuState->pcuStage = stage;
    pPcuState->timeOutFlag = FALSE;
}

/**
 ******************************************************************************
 * @brief       获取PCU操作阶段
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note
 * @note
 *
 ******************************************************************************
 */
pcu_ctrl_stage Get_PcuStage(void)
{
    return s_PcuState.pcuStage;
}

/**
 ******************************************************************************
 * @brief      Clr_ChargeMoudleStartUp.
 * @param[in]  None
 * @param[out] None
 * @retval     	清startup标志。
 *
 * @details    //北京入网检测增加.
 *
 * @note
 ******************************************************************************
 */
void Clr_ChargeMoudleStartUp(void)
{
    s_PcuState.startUp = FALSE;
}

/**
 ******************************************************************************
 * @brief       充电模块启动完成状态
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details     内测电压达到需求电压（目标值或者目标值-5.5V）时为1，发送停机命令时为0
 * @note
 * @note
 *
 ******************************************************************************
 */
uint8 Get_ChargeMoudleStartUp(void)
{
    return s_PcuState.startUp;
}
/**
 ******************************************************************************
 * @brief       tick更新
 * @param[in]   flag   为真时开始保存tick并置位tick标记，为假时清除tick标记
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note
 * @note
 *
 ******************************************************************************
 */
void pcuSetStageTick(uint8 flag)
{
    PCU_STATE *pPcuState = &s_PcuState;
    if (flag)
    {
        if (FALSE == pPcuState->tickFlag)
        {
            pPcuState->tickFlag = TRUE;  /**置位tick标记*/
            pPcuState->tick = tickGet(); /**<更新tick 防止前面计时导致停止超时*/
            pPcuState->timeOutEn = TRUE; /**<超时使能*/
        }
    }
    else
    {
        pPcuState->tickFlag = FALSE;
        pPcuState->tick = tickGet(); /**<更新tick 防止前面计时导致停止超时*/
    }
}
/**
 ******************************************************************************
 * @brief       快速启动帧发送使能
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
static void pcuCmdPgnEn(uint32 cmdpgn)
{
    if (PGN_YK_QSTART == cmdpgn)
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_QSTART))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---快速开机启动帧\n", PGN_YK_QSTART);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
            Set_PcuSendTimer(PGN_YK_QSTART, 0xFFFF); /**<立即发送快速启动帧*/
            Set_PcuSendTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendTimer(PGN_YK_STOP, 0x00);
        }
    }
    else if (PGN_YK_CSTOP == cmdpgn)
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_CSTOP))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---保留模块停止帧\n", PGN_YK_CSTOP);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
            Set_PcuSendTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendTimer(PGN_YK_CSTOP, 0xFFFF); /**<立即发送保留模块停止帧*/
            Set_PcuSendTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendTimer(PGN_YK_STOP, 0x00);
        }
    }
    else if (PGN_YK_SSTART == cmdpgn)
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_SSTART))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---软启动帧\n", PGN_YK_SSTART);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
            Set_PcuSendTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendTimer(PGN_YK_SSTART, 0xFFFF); /**<立即发送软启动帧*/
            Set_PcuSendTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendTimer(PGN_YK_STOP, 0x00);
        }
    }
    else if (PGN_YK_SADDR == cmdpgn)
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_SADDR))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---显示地址帧\n", PGN_YK_SADDR);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
            Set_PcuSendTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendTimer(PGN_YK_SADDR, 0xFFFF); /**<立即发送显示地址帧*/
            Set_PcuSendTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendTimer(PGN_YK_STOP, 0x00);
        }
    }
    else if (PGN_YK_OPARA == cmdpgn)
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_OPARA))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---参数调整帧\n", PGN_YK_OPARA);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
            Set_PcuSendTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendTimer(PGN_YK_OPARA, 0xFFFF); /**<立即发送参数调整帧*/
            Set_PcuSendTimer(PGN_YK_STOP, 0x00);
        }
    }
    else if (PGN_YK_STOP == cmdpgn)
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_STOP))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---释放模块停止充电帧\n", PGN_YK_STOP);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0xFFFF);
            Set_PcuSendTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendTimer(PGN_YK_STOP, 0xFFFF); /**<立即发送释放模块停止充电帧*/
        }
    }
}

/**
 ******************************************************************************
 * @brief       命令帧停止发送
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
static void pcuCmdPgnDis(void)
{
    if (0 != Get_PcuSendRemainTimer(PGN_YK_QSTART))
    {
        trace(TR_PCU_PROCESS, "关闭---发送使能---%06X---快速启动帧\n", PGN_YK_QSTART);
        Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
        Set_PcuSendTimer(PGN_YK_QSTART, 0x00);
    }
    if (Get_PcuRecvEnable(PGN_YK_QSTART_ACK) != eRecvEnable_Off)
    {
        trace(TR_PCU_PROCESS, "关闭---接受使能---%06X---快速启动应答帧\n", PGN_YK_QSTART_ACK);
        Set_PcuRecvEnable(PGN_YK_QSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_QSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_QSTART_ACK, 0x00);
    }

    if (0 != Get_PcuSendRemainTimer(PGN_YK_CSTOP))
    {
        trace(TR_PCU_PROCESS, "关闭---发送使能---%06X---保留模块停止充电帧\n", PGN_YK_CSTOP);
        Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
        Set_PcuSendTimer(PGN_YK_CSTOP, 0x00);
    }
    if (Get_PcuRecvEnable(PGN_YK_CSTOP_ACK) != eRecvEnable_Off)
    {
        trace(TR_PCU_PROCESS, "关闭---接受使能---%06X---保留模块停止充电应答帧\n", PGN_YK_CSTOP_ACK);
        Set_PcuRecvEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_CSTOP_ACK, 0x00);
    }

    if (0 != Get_PcuSendRemainTimer(PGN_YK_SSTART))
    {
        trace(TR_PCU_PROCESS, "关闭---发送使能---%06X---软启动帧\n", PGN_YK_SSTART);
        Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
        Set_PcuSendTimer(PGN_YK_SSTART, 0x00);
    }
    if (Get_PcuRecvEnable(PGN_YK_SSTART_ACK) != eRecvEnable_Off)
    {
        trace(TR_PCU_PROCESS, "关闭---接受使能---%06X---软启动应答帧\n", PGN_YK_SSTART_ACK);
        Set_PcuRecvEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SSTART_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SSTART_ACK, 0x00);
    }

    if (0 != Get_PcuSendRemainTimer(PGN_YK_SADDR))
    {
        trace(TR_PCU_PROCESS, "关闭---发送使能---%06X---显示地址帧\n", PGN_YK_SADDR);
        Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
        Set_PcuSendTimer(PGN_YK_SADDR, 0x00);
    }
    if (Get_PcuRecvEnable(PGN_YK_SADDR_ACK) != eRecvEnable_Off)
    {
        trace(TR_PCU_PROCESS, "关闭---接受使能---%06X---显示地址应答帧\n", PGN_YK_SADDR_ACK);
        Set_PcuRecvEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_SADDR_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_SADDR_ACK, 0x00);
    }

    if (0 != Get_PcuSendRemainTimer(PGN_YK_OPARA))
    {
        trace(TR_PCU_PROCESS, "关闭---发送使能---%06X---参数调整帧\n", PGN_YK_OPARA);
        Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
        Set_PcuSendTimer(PGN_YK_OPARA, 0x00);
    }
    if (Get_PcuRecvEnable(PGN_YK_OPARA_ACK) != eRecvEnable_Off)
    {
        trace(TR_PCU_PROCESS, "关闭---接受使能---%06X---参数调整应答帧\n", PGN_YK_OPARA_ACK);
        Set_PcuRecvEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_OPARA_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_OPARA_ACK, 0x00);
    }

    if (0 != Get_PcuSendRemainTimer(PGN_YK_STOP))
    {
        trace(TR_PCU_PROCESS, "关闭---发送使能---%06X---释放模块停止充电帧\n", PGN_YK_STOP);
        Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
        Set_PcuSendTimer(PGN_YK_STOP, 0x00);
    }
    if (Get_PcuRecvEnable(PGN_YK_STOP_ACK) != eRecvEnable_Off)
    {
        trace(TR_PCU_PROCESS, "关闭---接受使能---%06X---释放模块停止充电帧\n", PGN_YK_STOP_ACK);
        Set_PcuRecvEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimerEnable(PGN_YK_STOP_ACK, eRecvEnable_Off);
        Set_PcuRecvTimer(PGN_YK_STOP_ACK, 0x00);
    }
}
/**
 ******************************************************************************
 * @brief       PCU设备故障查询
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuDevStateCheck(PCU_STATE *pPcuState)
{
    if ((DEVICE_STATE_FAULT == Get_DeviceState()) ||
        (DEVICE_STATE_MAJOR_FAULT == Get_DeviceState()))
    {
        trace(TR_PCU_PROCESS, "充电桩故障停止充电模块！\n");
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
    }
}
/**
 ******************************************************************************
 * @brief       PCU阶段管理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuFreeStageOperate(PCU_STATE *pPcuState)
{
    uint8 workState = Get_WorkState();
    uint16 insideVol = Get_K1K2InsideVol();
    memset(pPcuState + MOFFSET(PCU_STATE, timeOutFlag), 0, (sizeof(PCU_STATE) - MOFFSET(PCU_STATE, timeOutFlag)));
    //    memset(pPcuState ,0,sizeof(PCU_STATE));
    pPcuState->tick = tickGet();
    if (((CHARGE_MODE_MANUAL == Get_ChargeMode()) /**<手动模式时需在准备阶段发快速开机后直接到达充电阶段*/
         && (CCU_WORK_STATE_READY == workState)) ||
        ((Get_CcuCfgParaEuropeEnable() == 0) && (CCU_WORK_STATE_IMD == workState)) || ((Get_CcuCfgParaEuropeEnable() != 0) && (CCU_WORK_STATE_PRE_CHARGE == workState))) // 欧标在预充阶段做绝缘
    {
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_QStart);
        trace(TR_PCU_PROCESS, "快速启动！\n");
    }
#if PCU_ADDR_QUERY_SEND_EN
    else
    {
        pcuCmdPgnEn(PGN_YK_SADDR); /**<发送地址查询帧*/
    }
#else
    else if (insideVol > 150) /***/
    {
        pcuCmdPgnEn(PGN_YK_STOP); /**<防止误起机或者关机不成功*/
    }
    else
    {
        pcuCmdPgnDis(); /**<停止发送命令帧*/
    }
#endif
}

/**
 ******************************************************************************
 * @brief       快速启动等待内测电压达到目标-（1~10）V
 *
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuQsOperate(PCU_STATE *pPcuState)
{
    pPcuState->startUp = FALSE;
    if (FALSE == pPcuState->timeOutFlag)
    {
        pcuCmdPgnEn(PGN_YK_QSTART);
#if PCU_YK_CMD_ACK_EN
        if ((ePcuWorkState_Busy == pPcuState->pcuWs.workState) && (eYKList_QuickStart == pPcuState->pcuYkCmdAck.opCmd))
#else
        if (ePcuWorkState_Busy == pPcuState->pcuWs.workState)
#endif
        {
            pcuSetStage(pPcuState, ePcuCtrlStage_QStartWait);
            trace(TR_PCU_PROCESS, "快速启动工作中！\n");
            pcuSetStageTick(FALSE);
        }
    }
    else
    {
        Set_ErrType(eErrType_PcuBusyTimeout);
        trace(TR_PCU_PROCESS, "快速启动超时！workState = %d\n", pPcuState->pcuWs.workState);
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
    }
}

/**
 ******************************************************************************
 * @brief       快速启动等待模块工作
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuQsWaitOperate(PCU_STATE *pPcuState)
{
    uint16 insideVol = Get_K1K2InsideVol();
    pcuSetStageTick(TRUE);
    if (FALSE == pPcuState->timeOutFlag)
    {
        if (CHARGE_MODE_MANUAL == Get_ChargeMode())
        {
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_WaitK1K2On_1); /**需要更新TICK*/
            pPcuState->startUp = TRUE;
            trace(TR_PCU_PROCESS, "快速开机完成！内测电压 = %d,需求电压 = %d\n", insideVol, pPcuState->dmnVol);
        }
        else
        {
            if ((insideVol >= pPcuState->dmnVol - 100) && (insideVol <= pPcuState->dmnVol - 10)) /**<快速启动时内测电压达到目标值-(1~10)V*/
            {
                pcuSetStageTick(FALSE);
                pcuSetStage(pPcuState, ePcuCtrlStage_WaitK1K2On_1); /**需要更新TICK*/
                pPcuState->startUp = TRUE;
                trace(TR_PCU_PROCESS, "快速开机完成！内测电压 = %d,需求电压 = %d\n", insideVol, pPcuState->dmnVol);
            }
        }
    }
    else
    {
        Set_ErrType(eErrType_QStartTimeOut);
        trace(TR_PCU_PROCESS, "快速启动超时！insideVol = %d,dmnVol = %d\n", insideVol, pPcuState->dmnVol);
        pPcuState->startUp = FALSE;
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop); /**需要更新TICK*/
    }
}
/**
 ******************************************************************************
 * @brief       第一次等待K1K2ON
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuWaitK1K2On1Operate(PCU_STATE *pPcuState)
{
    uint16 insideVol = Get_K1K2InsideVol();
    uint16 outsideVol = Get_K1K2OutsideVol();
    pcuSetStageTick(TRUE);
    if (FALSE == pPcuState->timeOutFlag)
    {
        if (CHARGE_MODE_MANUAL == Get_ChargeMode())
        {
            if ((eSwitchState_ON == Get_SwitchState(SXIO_IN_K2)) && (eSwitchState_ON == Get_SwitchState(SXIO_IN_K1)))
            {
                pcuSetStageTick(FALSE);
                pcuSetStage(pPcuState, ePcuCtrlStage_AdjPara);
                trace(TR_PCU_PROCESS, "手动启动快速开机K1K2闭合完成！insideVol = %d,outsideVol =%d\n", insideVol, outsideVol);
            }
        }
        else
        {
            if ((eSwitchState_ON == Get_SwitchState(SXIO_IN_K2)) && (eSwitchState_ON == Get_SwitchState(SXIO_IN_K1)) && (abs(insideVol - outsideVol) < 100)) /**<压差小于10V认为闭合*/
            {
                pcuSetStageTick(FALSE);
                pcuSetStage(pPcuState, ePcuCtrlStage_ImdAdjPara);
                trace(TR_PCU_PROCESS, "快速开机K1K2闭合完成！insideVol = %d,outsideVol =%d\n", insideVol, outsideVol);
            }
        }
    }
    else
    {
        Set_ErrType(eErrType_K1Err); /**当作K1故障*/
        trace(TR_PCU_PROCESS, "快速启动时K1K2闭合超时!insideVol = %d,outsideVol =%d\n", insideVol, outsideVol);
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
    }
}
/**
 ******************************************************************************
 * @brief       绝缘参数调整到目标电压并且绝缘完成
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuImdAdjParaOperate(PCU_STATE *pPcuState)
{
    uint8 workState = Get_WorkState();
    pcuSetStageTick(TRUE);
    if (FALSE == pPcuState->timeOutFlag)
    {
        pcuCmdPgnEn(PGN_YK_OPARA);
        if (TRUE == Get_ImdSuccFlag())
        {
            Set_ImdStartFlag(FALSE);
            Set_ImdSuccFlag(FALSE);
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_MidStop);
            trace(TR_PCU_PROCESS, "绝缘完成停机！\n");
        }
        if (TRUE == Check_ErrType(eErrType_ImdErr))
        {
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
            trace(TR_PCU_PROCESS, "绝缘故障停机！\n");
        }
    }
    else
    {
        Set_ErrType(eErrType_ImdErr); // Set_ErrType(eErrType_ImdTimeOut );;  /**<因没有快速启动超时故障 所以报绝缘故障*/
        trace(TR_PCU_PROCESS, "绝缘检测超时！\n");
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
    }
}

/**
 ******************************************************************************
 * @brief       保留模块停机操作
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuMidStopOperate(PCU_STATE *pPcuState)
{
    uint8 workState = Get_WorkState();
    static uint8 midStopFlag = FALSE;
    if (FALSE == pPcuState->timeOutFlag)
    {
#if PCU_YK_CMD_ACK_EN
        if ((ePcuWorkState_Free == pPcuState->pcuWs.workState) && (eYKList_ChargeCStop == pPcuState->pcuYkCmdAck.opCmd) && (abs(tickGet() - pPcuState->tick) >= PCU_CSTOP_CMD_REMAIN_TIME))
#else
        if ((ePcuWorkState_Free == pPcuState->pcuWs.workState) && (abs(tickGet() - pPcuState->tick) >= PCU_CSTOP_CMD_REMAIN_TIME))
#endif
        {
            pPcuState->startUp = FALSE;
#if PCU_ADDR_QUERY_SEND_EN
            pcuCmdPgnEn(PGN_YK_SADDR); /**<发送地址查询帧*/
#else
            pcuCmdPgnDis(); /**<停止发送命令帧*/

            pPcuState->timeOutEn = FALSE; /**<应答成功并且工作状态变换过来不计超时*/
#endif
        }
        else
        {
            pcuCmdPgnEn(PGN_YK_CSTOP); /**<打开发送保留模块停止命令帧*/
        }
        if (Get_CcuCfgParaEuropeEnable())
        {
            if (Get_K1K2InsideVol() < 600 && (ePcuWorkState_Free == pPcuState->pcuWs.workState))
            {
                pPcuState->startUp = FALSE;
                pcuSetStageTick(FALSE);
                pcuSetStage(pPcuState, ePcuCtrlStage_SStart);
                trace(TR_PCU_PROCESS, "PCU进入软启动！\n");
            }
            else if (((workState >= CCU_WORK_STATE_CHARGE_STOP) && (workState <= CCU_WORK_STATE_STOP_FINISH)) || ((workState >= CCU_WORK_STATE_FREE) && (workState <= CCU_WORK_STATE_SHAKE_HAND)))
            {
                trace(TR_PCU_PROCESS, "运行阶段不匹配！PCU阶段%d,主流程阶段为%d\n", ePcuCtrlStage_MidStop, workState);
                pcuSetStageTick(FALSE);
                pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
            }
            if (eComState_TimeOut == Get_BmsComState()) /**超时状态时重新计时*/
            {
                if (FALSE == midStopFlag)
                {
                    midStopFlag = TRUE;
                    pcuSetStageTick(FALSE);
                }
            }
            else
            {
                midStopFlag = FALSE;
            }
        }
        else
        {
            if ((ePcuWorkState_Free == pPcuState->pcuWs.workState) && (CCU_WORK_STATE_PRE_CHARGE == workState)) /**主流程进入预充阶段时进入软起阶段*/
            {
                pPcuState->startUp = FALSE;
                pcuSetStageTick(FALSE);
                pcuSetStage(pPcuState, ePcuCtrlStage_SStart);
                trace(TR_PCU_PROCESS, "PCU进入软启动！\n");
            }
            else if (((workState >= CCU_WORK_STATE_CHARGE_STOP) && (workState <= CCU_WORK_STATE_STOP_FINISH)) || ((workState >= CCU_WORK_STATE_FREE) && (workState <= CCU_WORK_STATE_SHAKE_HAND)))
            {
                trace(TR_PCU_PROCESS, "运行阶段不匹配！PCU阶段%d,主流程阶段为%d\n", ePcuCtrlStage_MidStop, workState);
                pcuSetStageTick(FALSE);
                pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
            }
            if (eComState_TimeOut == Get_BmsComState()) /**超时状态时重新计时*/
            {
                if (FALSE == midStopFlag)
                {
                    midStopFlag = TRUE;
                    pcuSetStageTick(FALSE);
                }
            }
            else
            {
                midStopFlag = FALSE;
            }
        }
    }
    else
    {
        Set_ErrType(eErrType_PcuBusyTimeout);
        trace(TR_PCU_PROCESS, "绝缘检测完成后关机超时！\n");
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
    }
}
/**
 ******************************************************************************
 * @brief       软启动操作等待输出电压达到要求
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuSStartOperate(PCU_STATE *pPcuState)
{
    BCL_DATA strBCL;
    uint8 workState = Get_WorkState();
    uint16 insideVol = Get_K1K2InsideVol();
    uint16 outsideVol = Get_K1K2OutsideVol();
    Get_BMS_Data(BMS_PGN_BCL, (void *)&strBCL);
    if (FALSE == pPcuState->timeOutFlag)
    {
        if (!Get_CcuCfgParaEuropeEnable() || (eRecvFlag_Yes == Get_BmsRecvFlag(BMS_PGN_BCL) // 欧标需要先收到需求
                                              && TwoUint8ToUint16(strBCL.voltageDemand) >= Get_CcuChargeParaMinOutputVoltage()))
        {
            pcuCmdPgnEn(PGN_YK_SSTART);
        }
        if (ePcuWorkState_Busy == pPcuState->pcuWs.workState)
        {
            pcuSetStage(pPcuState, ePcuCtrlStage_SStartWs);
            trace(TR_PCU_PROCESS, "软启动工作中！insideVol = %d,outsideVol = %d\n", insideVol, outsideVol);
            pcuSetStageTick(FALSE);
        }
        if (((workState < CCU_WORK_STATE_PRE_CHARGE) || (workState > CCU_WORK_STATE_CHARGE_PAUSE)))
        {
            trace(TR_PCU_PROCESS, "运行阶段不匹配！PCU阶段%d,主流程阶段为%d\n", ePcuCtrlStage_MidStop, workState);
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
        }
    }
    else
    {
        Set_ErrType(eErrType_PcuBusyTimeout);
        trace(TR_PCU_PROCESS, "软启动超时！insideVol = %d,outsideVol = %d\n", insideVol, outsideVol);
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
    }
}
/**
 ******************************************************************************
 * @brief       软启动操作等待输出电压达到目标值
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuSStartWsOperate(PCU_STATE *pPcuState)
{
    uint16 insideVol = Get_K1K2InsideVol();
    uint16 outsideVol = Get_K1K2OutsideVol();
    pcuSetStageTick(TRUE);
    if (FALSE == pPcuState->timeOutFlag)
    {
        if (Get_TimeOut())
        {
            Set_ErrType(eErrType_SStartTimeOut);
            trace(TR_PCU_PROCESS, "软起开机超时！insideVol = %d,outsideVol = %d\n", insideVol, outsideVol);
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
        }
        else
        {
            if (Get_CcuCfgParaEuropeEnable())
            {
                CHARGE_PARA chargePara;

                Get_PilePara((void *)&chargePara, eParaType_ChargePara);
                if ((outsideVol >= 200) && (((insideVol >= outsideVol - 200) &&
                                             (insideVol <= outsideVol + 200)) ||
                                            (outsideVol >= (TwoUint8ToUint16(chargePara.minOutputVoltage)))))
                {
                    pcuSetStageTick(FALSE);
                    pcuSetStage(pPcuState, ePcuCtrlStage_WaitK1K2On_2);
                    pPcuState->startUp = TRUE;
                    trace(TR_PCU_PROCESS, "欧标软启动完成2！闭合 K1K2 tick = %d,insideVol = %d,outsideVol = %d\n", tickGet(), insideVol, outsideVol);
                }
            }
            else
            {
                if ((insideVol >= outsideVol - 100) && (insideVol <= outsideVol - 10)) /**<软启动时内测电压达到外侧-(1~10)V*/
                {
                    pcuSetStageTick(FALSE);
                    pcuSetStage(pPcuState, ePcuCtrlStage_WaitK1K2On_2);
                    pPcuState->startUp = TRUE;
                    trace(TR_PCU_PROCESS, "软启动完成！准备闭合 K1K2 tick = %d,insideVol = %d,outsideVol = %d\n", tickGet(), insideVol, outsideVol);
                }
            }
        }
    }
    else
    {
        Set_ErrType(eErrType_SStartTimeOut);
        trace(TR_PCU_PROCESS, "软起开机超时！insideVol = %d,outsideVol = %d\n", insideVol, outsideVol);
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
    }
}
/**
 ******************************************************************************
 * @brief       第二次等待K1K2ON
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *  1、软启动成功，发送CRO_AA后，收到BSM的充电暂停帧，导致pcu与ccu主流程不一致，导致无法正常充电
 ******************************************************************************
 */
void pcuWaitK1K2On2Operate(PCU_STATE *pPcuState)
{
    uint8 workState = Get_WorkState();
    uint16 insideVol = Get_K1K2InsideVol();
    uint16 outsideVol = Get_K1K2OutsideVol();
    pcuSetStageTick(TRUE);
    if (FALSE == pPcuState->timeOutFlag)
    {
        if (CCU_WORK_STATE_CHARGING == workState /**主流程进入充电阶段时阶段时进入参数调整阶段*/
            || workState == CCU_WORK_STATE_CHARGE_PAUSE)
        {
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_AdjPara);
            trace(TR_PCU_PROCESS, "软起K1K2闭合完成！tick = %d ,insideVol = %d,outsideVol = %d\n", tickGet(), insideVol, outsideVol);
        }
        else if ((workState >= CCU_WORK_STATE_RECOGNIZE) && (workState < CCU_WORK_STATE_PRE_CHARGE))
        {
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_MidStop);
            trace(TR_PCU_PROCESS, "PCU任务进入重连状态！insideVol = %d,outsideVol = %d\n", insideVol, outsideVol);
        }
        else if (((workState >= CCU_WORK_STATE_FREE) && (workState <= CCU_WORK_STATE_RELEASE_01)) || (workState > CCU_WORK_STATE_CHARGE_PAUSE))
        {
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
            trace(TR_PCU_PROCESS, "PCU任务工作流程与主流程不匹配！预充等待K1K2闭合 ,当前主阶段 = %d", workState);
            trace(TR_PCU_PROCESS, "insideVol = %d,outsideVol = %d\n", insideVol, outsideVol);
        }
    }
    else
    {
        Set_ErrType(eErrType_K1Err); /**当作K1故障*/
        trace(TR_PCU_PROCESS, "软起开机K1K2闭合超时！insideVol = %d,outsideVol = %d\n", insideVol, outsideVol);
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
    }
}
/**
 ******************************************************************************
 * @brief       充电中调整参数
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuAdjParaOperate(PCU_STATE *pPcuState)
{
    uint8 workState = Get_WorkState();
    pcuSetStageTick(TRUE);
    if (FALSE == pPcuState->timeOutFlag)
    {
        pcuCmdPgnEn(PGN_YK_OPARA);
        if ((workState >= CCU_WORK_STATE_CHARGE_STOP) && (workState <= CCU_WORK_STATE_STOP_FINISH))
        {
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
            trace(TR_PCU_PROCESS, "PCU任务进入停机！\n");
        }
        else if ((workState >= CCU_WORK_STATE_RECOGNIZE) && (workState <= CCU_WORK_STATE_PRE_CHARGE)) /**三次重连时用*/
        {
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_MidStop);
            trace(TR_PCU_PROCESS, "PCU任务进入重连状态！\n");
        }
        else if ((workState >= CCU_WORK_STATE_FREE) && (workState <= CCU_WORK_STATE_RELEASE_01))
        {
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
            trace(TR_PCU_PROCESS, "PCU任务工作流程与主流程不匹配！\n");
        }
        else
        {
            if (enumAllowFlag_Forbid == Get_ChargePauseFlg())
            {
                pcuSetStageTick(FALSE);
                pcuSetStage(pPcuState, ePcuCtrlStage_Pause);
                trace(TR_PCU_PROCESS, "充电暂停！\n");
            }
#if 1 // TODO-北京入网检测修改 2020-10-18 充电中工作状态错误不判
            if (pPcuState->pcuWs.workState != ePcuWorkState_Busy)
            {
                Set_ErrType(eErrType_PcuForbidCharge); /**工作状态不允许充电*/
                pcuSetStageTick(FALSE);
                pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
                trace(TR_PCU_PROCESS, "充电中PCU工作状态错误！\n");
            }
#endif
        }
    }
    else
    {
        trace(TR_PCU_PROCESS, "参数调整超时！\n");
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
    }
}
/**
 ******************************************************************************
 * @brief       暂停
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuPauseOperate(PCU_STATE *pPcuState)
{
    pcuSetStageTick(TRUE);
    if (FALSE == pPcuState->timeOutFlag)
    {
        if (enumAllowFlag_Allow == Get_ChargePauseFlg())
        {
            pcuSetStageTick(FALSE);
            pcuSetStage(pPcuState, ePcuCtrlStage_AdjPara);
            trace(TR_PCU_PROCESS, "重新进入充电中！\n");
        }
    }
    else
    {
        trace(TR_PCU_PROCESS, "充电暂停超时！\n");
        pcuSetStageTick(FALSE);
        pcuSetStage(pPcuState, ePcuCtrlStage_Stop);
    }
}

/**
 ******************************************************************************
 * @brief       停机
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void pcuStopOperate(PCU_STATE *pPcuState)
{
    pcuSetStageTick(TRUE);
    if (FALSE == pPcuState->timeOutFlag)
    {
//		if (FALSE == pPcuState->tickFlag)
//		{
//			pPcuState->tickFlag = TRUE;
//			pPcuState->tick = tickGet();
//		}
//		else
//		{
#if PCU_YK_CMD_ACK_EN
        if ((ePcuWorkState_Free == pPcuState->pcuWs.workState) && (eYKList_ChargeStop == pPcuState->pcuYkCmdAck.opCmd) && (abs(tickGet() - pPcuState->tick) >= PCU_STOP_CMD_REMAIN_TIME))
#else
        if (((ePcuWorkState_Free == pPcuState->pcuWs.workState) && (abs(tickGet() - pPcuState->tick) >= PCU_STOP_CMD_REMAIN_TIME)) /**<至少发送3S*/
            || (CCU_WORK_STATE_FREE == Get_WorkState()))
#endif
        {
            pPcuState->startUp = FALSE;
#if PCU_ADDR_QUERY_SEND_EN
            pcuCmdPgnEn(PGN_YK_SADDR); /**<发送地址查询帧*/
#else
            pcuCmdPgnDis(); /**<停止发送命令帧*/
#endif
            pcuSetStage(pPcuState, ePcuCtrlStage_Free);
            trace(TR_PCU_PROCESS, "PCU管理任务进入空闲状态！\n");
        }
        else
        {
            pcuCmdPgnEn(PGN_YK_STOP);
        }
        //		}
    }
    else
    {
        Set_ErrType(eErrType_PcuBusyTimeout);
        trace(TR_PCU_PROCESS, "充电结束关机超时！\n");
        pPcuState->startUp = FALSE;
#if PCU_ADDR_QUERY_SEND_EN
        pcuCmdPgnEn(PGN_YK_SADDR); /**<发送地址查询帧*/
#else
        pcuCmdPgnDis(); /**<停止发送命令帧*/
#endif
        pcuSetStage(pPcuState, ePcuCtrlStage_Free);
    }
}
/*-----------------------------------------------------------------------------
 Section: Local Function Prototypes
 ----------------------------------------------------------------------------*/

const PCU_WS_CONFIG PcuWsConfigTab[] =
    {
        {ePcuCtrlStage_Free, PCU_STAGE_NOT_TIMEOUT, NULL, pcuFreeStageOperate},
        {ePcuCtrlStage_QStart, 10000, pcuDevStateCheck, pcuQsOperate},
        {ePcuCtrlStage_QStartWait, 15000, pcuDevStateCheck, pcuQsWaitOperate},
        {ePcuCtrlStage_WaitK1K2On_1, 5000, pcuDevStateCheck, pcuWaitK1K2On1Operate},
        {ePcuCtrlStage_ImdAdjPara, 20000, pcuDevStateCheck, pcuImdAdjParaOperate},
        {ePcuCtrlStage_MidStop, 15000, pcuDevStateCheck, pcuMidStopOperate},
        {ePcuCtrlStage_SStart, 15000, pcuDevStateCheck, pcuSStartOperate},
        {ePcuCtrlStage_SStartWs, 10000, pcuDevStateCheck, pcuSStartWsOperate},
        {ePcuCtrlStage_WaitK1K2On_2, 15000, pcuDevStateCheck, pcuWaitK1K2On2Operate}, /**<10S是错开BMS中报文超时*/
        {ePcuCtrlStage_AdjPara, PCU_STAGE_NOT_TIMEOUT, pcuDevStateCheck, pcuAdjParaOperate},
        {ePcuCtrlStage_Pause, 600000, pcuDevStateCheck, pcuPauseOperate}, /**<暂停超时10分钟关机*/
        {ePcuCtrlStage_Stop, 15000, NULL, pcuStopOperate},                /**<暂停超时停止命令超时15s*/
};

/**
 ******************************************************************************
 * @brief       PCU阶段管理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
void Pcu_StageManage(void)
{
    const PCU_WS_CONFIG *pPcuConfig = NULL;
    PCU_STATE *pPcuState = &s_PcuState;
    for (int i = 0; i < FCNT(PcuWsConfigTab); i++)
    {
        pPcuConfig = &PcuWsConfigTab[i];
        if (pPcuConfig->pcuStage == pPcuState->pcuStage)
        {
            if (pPcuState->timeOutEn) /**阶段超时判断使能*/
            {
                if ((pPcuConfig->timeOut != PCU_STAGE_NOT_TIMEOUT) && (abs(tickGet() - pPcuState->tick) > pPcuConfig->timeOut))
                {
                    pPcuState->timeOutFlag = TRUE;
                }
                else
                {
                    pPcuState->timeOutFlag = FALSE;
                }
            }
            else
            {
                pPcuState->timeOutFlag = FALSE;
            }
            if (pPcuConfig->funState != NULL)
            {
                pPcuConfig->funState(pPcuState);
            }
            if (pPcuConfig->func != NULL)
            {
                pPcuConfig->func(pPcuState);
            }
        }
    }
}

///**
// ******************************************************************************
// * @brief       PCU异常处理
// * @param[in]   NONE
// * @param[out]  NONE
// * @retval      NONE
// * @details
// *
// * @note
// *
// ******************************************************************************
// */
// void Pcu_StageErrManage(void)
//{
//	PCU_STATE *pPcuState = &s_PcuState;
//	if((pPcuState->pcuStage >= ePcuCtrlStage_QStart)&&(pPcuState->pcuStage < ePcuCtrlStage_Stop))
//	{
//		if(ePcuServeState_Forbid == pPcuState->pcuWs.serveState)
//		{
//			Set_ErrType(eErrType_PcuForbidCharge);
//	    	trace(TR_PCU_PROCESS, "功率单元禁止输出！\n");
//	    	pcuSetStageTick(FALSE);
//	    	pcuSetStage(pPcuState,ePcuCtrlStage_Stop);
//		}
//
//	}
//}
/**
 ******************************************************************************
 * @brief       PCU阶段管理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */

static void Init_Pcu(void)
{
    //    memset(&g_StrPcuConstPara, 0x00, sizeof(PCU_CONST_PARA));
    pcu_FixPara_Init();
}

static void Check_Demond(void)
{
    static uint16 last_DemondVol = 0;
    static uint32 last_DemondCur = 0;
    PCU_STATE *pPcuState = &s_PcuState;
#if 0
    uint16 demondVol = Get_DemondVol();
    uint32 demondCur = Get_DemondCur();

    if(CCU_WORK_STATE_CHARGING == Get_WorkState())
    {
        if ((demondVol  != last_DemondVol) && (demondCur != last_DemondCur))
        {
            trace(TR_PCU_PROCESS, "当前需求电压   <%d>  上一次需求电压   <%d>  当前需求电流   <%d> 上一次需求电流   <%d>\n",
                   demondVol, last_DemondVol,  demondCur, last_DemondCur);
            Set_PcuSendTimer(PGN_YK_OPARA, 0xFFFF);   /**<有新需求立即发参数调整*/
        }
        last_DemondVol = demondVol;
        last_DemondCur = demondCur;
    }
#else
    pPcuState->dmnVol = Get_DemondVol();
    pPcuState->dmnCur = Get_DemondCur();
    if ((abs(pPcuState->dmnVol - last_DemondVol) > 5)       /**<需求电压变得0.5V*/
        || (abs(pPcuState->dmnCur - last_DemondCur) > 100)) /**<需求电流变动0.1A*/
    {
        trace(TR_PCU_PROCESS, "当前需求电压   <%d>  上一次需求电压   <%d>  当前需求电流   <%d> 上一次需求电流   <%d>\n",
              pPcuState->dmnVol, last_DemondVol, pPcuState->dmnCur, last_DemondCur);

        Set_PcuSendTimer(PGN_YK_OPARA, 0xFFFF); /**<有新需求立即发参数调整*/
    }
    last_DemondVol = pPcuState->dmnVol;
    last_DemondCur = pPcuState->dmnCur;
#endif
    //    if (eActFlag_On != Get_ChargeActFlag())
    //    {
    //        Set_PcuSendFlg(PGN_YK_QSTART, eSendFlag_No);
    //        Set_SelfcheckStartFlag(eResult_NULL);
    //
    //        Clr_PcuErrInfo(PGN_YK_QSTART_ACK);
    //        Clr_PcuErrInfo(PGN_YK_CSTOP_ACK);
    //        Clr_PcuErrInfo(PGN_YK_SSTART_ACK);
    //        Clr_PcuErrInfo(PGN_YK_SADDR_ACK);
    //        Clr_PcuErrInfo(PGN_YK_OPARA_ACK);
    //        Clr_PcuErrInfo(PGN_YK_STOP_ACK);
    //        Clr_PcuErrInfo(PGN_PARA_SET_ACK);
    //        Clr_PcuErrInfo(PGN_PARA_QUERY_ACK);
    //    }
    //
    //
    //
    //
    //
    //    if ((0 == last_DemondVol) && (0 == last_DemondCur))
    //    {
    //        if (eActFlag_On == Get_ChargeActFlag())
    //        {
    //            if (eSendFlag_Yes == Get_PcuSendFlg(PGN_YK_QSTART))
    //            {
    //                trace(TR_PCU_PROCESS, "打开---发送使能---%06X---软启动帧\n", PGN_YK_SSTART);
    //                Set_PcuSendRemainTimer(PGN_YK_SSTART, 5000 / PCU_CALL_CYCLE);
    //                Set_PcuSendTimer(PGN_YK_SSTART, 0xFFFF);
    //
    //                Set_PcuSendRemainTimer(PGN_YK_QSTART ,0x00);
    //                Set_PcuSendRemainTimer(PGN_YK_CSTOP ,0x00);
    //                Set_PcuSendRemainTimer(PGN_YK_SADDR,0x00);
    //                Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
    //                Set_PcuSendRemainTimer(PGN_YK_STOP,0x00);
    //            }
    //            else
    //            {
    //                trace(TR_PCU_PROCESS, "打开---发送使能---%06X---快速启动帧\n", PGN_YK_QSTART);
    //                Set_PcuSendRemainTimer(PGN_YK_QSTART, 5000 / PCU_CALL_CYCLE);
    //                Set_PcuSendTimer(PGN_YK_QSTART, 0xFFFF);
    //
    //                Set_PcuSendRemainTimer(PGN_YK_CSTOP ,0x00);
    //                Set_PcuSendRemainTimer(PGN_YK_SSTART ,0x00);
    //                Set_PcuSendRemainTimer(PGN_YK_SADDR,0x00);
    //                Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
    //                Set_PcuSendRemainTimer(PGN_YK_STOP,0x00);
    //            }
    //        }
    //    }
    //    else if ((0 == Get_DemondVol()) && (0 == Get_DemondCur()))
    //    {
    //        trace(TR_PCU_PROCESS, "打开---发送使能---%06X---充电停止帧\n", PGN_YK_CSTOP);
    //
    //        Set_PcuSendRemainTimer(PGN_YK_CSTOP, 5000 / PCU_CALL_CYCLE);
    //        Set_PcuSendTimer(PGN_YK_STOP, 0xFFFF);
    //
    //        Set_PcuSendRemainTimer(PGN_YK_QSTART ,0x00);
    //        Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
    //        Set_PcuSendRemainTimer(PGN_YK_SADDR,0x00);
    //        Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
    //        Set_PcuSendRemainTimer(PGN_YK_STOP,0x00);
    //    }
    //    else
    //    {
    //        trace(TR_PCU_PROCESS, "打开---发送使能---%06X---参数修改帧\n", PGN_YK_OPARA);
    //        Set_PcuSendRemainTimer(PGN_YK_OPARA, 5000 / PCU_CALL_CYCLE);
    //        Set_PcuSendTimer(PGN_YK_OPARA, 0xFFFF);
    //
    //        Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
    //        Set_PcuSendRemainTimer(PGN_YK_CSTOP ,0x00);
    //        Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
    //        Set_PcuSendRemainTimer(PGN_YK_SADDR,0x00);
    //        Set_PcuSendRemainTimer(PGN_YK_STOP,0x00);
    //    }
    //    last_DemondVol = Get_DemondVol();
    //    last_DemondCur = Get_DemondCur();
    //
    //
}
//
///**
// ******************************************************************************
// * @brief       快速启动阶段PCU控制
// * @param[in]   NONE
// * @param[out]  NONE
// * @retval      NONE
// * @details     打开对应的遥控命令帧
// *              处理PCU状态
// * @note        PCU阶段服务管理
// *
// ******************************************************************************
// */
// static void QstartPcuCtrl(void)
//{
//
//}
/**
 ******************************************************************************
 * @brief       PcuStateServer
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details     打开对应的遥控命令帧
 *              处理PCU状态
 * @note        PCU阶段服务管理
 *
 ******************************************************************************
 */
static void Pcu_StateServer(void)
{
    uint8 workState = Get_WorkState();
    if ((CHARGE_MODE_MANUAL == Get_ChargeMode()) /**<手动模式时需在准备阶段发快速开机后直接到达充电阶段*/
        && (CCU_WORK_STATE_READY == workState))
    {
        //        QstartPcuCtrl();
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_QSTART))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---快速开机启动帧\n", PGN_YK_QSTART);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
            Set_PcuSendTimer(PGN_YK_QSTART, 0xFFFF); /**<立即发送快速启动帧*/
        }
    }
    if (CCU_WORK_STATE_IMD == workState)
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_QSTART))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---快速开机启动帧\n", PGN_YK_QSTART);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
            Set_PcuSendTimer(PGN_YK_QSTART, 0xFFFF); /**<立即发送快速启动帧*/
        }
        //        else if(moudleQStartFlag(FALSE,FALSE))                    /**启动成功关闭启动帧*/
        //        {
        //            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);           /**启动成功关闭启动帧*/
        //            Set_PcuRecvEnable(PGN_YK_QSTART_ACK, eRecvEnable_Off);
        //            Set_PcuRecvTimer(PGN_YK_QSTART_ACK, 0x00);
        //        }
    }
    else if ((workState > CCU_WORK_STATE_IMD) && (workState <= CCU_WORK_STATE_CONFIG))
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_CSTOP))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---保留模块关机帧\n", PGN_YK_CSTOP);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
            Set_PcuSendTimer(PGN_YK_CSTOP, 0xFFFF); /**<立即发送保留模块关机帧*/
        }
        //        else if(moudleCStopFlag(FALSE,FALSE))
        //        {
        //            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);           /**启动成功关闭启动帧*/
        //            Set_PcuRecvEnable(PGN_YK_CSTOP_ACK, eRecvEnable_Off);
        //            Set_PcuRecvTimer(PGN_YK_CSTOP_ACK, 0x00);
        //        }
    }
    else if (CCU_WORK_STATE_PRE_CHARGE == workState)
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_SSTART))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---软启动帧\n", PGN_YK_SSTART);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
            Set_PcuSendTimer(PGN_YK_SSTART, 0xFFFF); /**<立即发送软启动帧*/
        }
    }
    else if (CCU_WORK_STATE_CHARGING == workState)
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_OPARA))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---参数调整帧\n", PGN_YK_OPARA);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
        }
    }
    else if (CCU_WORK_STATE_CHARGE_PAUSE == workState) /**<暂停分条件发停止帧或者软启动帧*/
    {
        if (enumAllowFlag_Allow == Get_ChargePauseFlg())
        {
            if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_SSTART))
            {
                trace(TR_PCU_PROCESS, "打开---发送使能---%06X---软启动帧\n", PGN_YK_SSTART);
                Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
                Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
                Set_PcuSendRemainTimer(PGN_YK_SSTART, 0xFFFF);
                Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
                Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
                Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
                Set_PcuSendTimer(PGN_YK_SSTART, 0xFFFF); /**<立即发送软启动帧*/
            }
        }
        else
        {
            if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_CSTOP))
            {
                trace(TR_PCU_PROCESS, "打开---发送使能---%06X---保留模块关机帧\n", PGN_YK_CSTOP);
                Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
                Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0xFFFF);
                Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
                Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
                Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
                Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
                Set_PcuSendTimer(PGN_YK_CSTOP, 0xFFFF); /**<立即发送保留模块关机帧*/
            }
        }
    }
    else if ((workState >= CCU_WORK_STATE_CHARGE_STOP) && (workState <= CCU_WORK_STATE_STOP_FINISH))
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_STOP))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---停止充电关机帧\n", PGN_YK_STOP);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0xFFFF);
            Set_PcuSendTimer(PGN_YK_STOP, 0xFFFF); /**<立即发送停止充电帧*/
        }
    }
    else
    {
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_YK_SADDR))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---显示地址帧\n", PGN_YK_SADDR);
            Set_PcuSendRemainTimer(PGN_YK_QSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_CSTOP, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SSTART, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_SADDR, 0xFFFF);
            Set_PcuSendRemainTimer(PGN_YK_OPARA, 0x00);
            Set_PcuSendRemainTimer(PGN_YK_STOP, 0x00);
        }
    }
    //    printf("workState = %d",workState);
}
static void Check_YxYcHeart(void)
{
    if (0xFFFF != Get_PcuSendRemainTimer(PGN_HEART_CCU))
    {
        trace(TR_PCU_PROCESS, "打开---发送使能---%06X---CCU心跳帧\n", PGN_HEART_CCU);
        Set_PcuSendRemainTimer(PGN_HEART_CCU, 0xFFFF);
        Set_PcuSendTimer(PGN_HEART_CCU, 0xFFFF);
    }
    if (eRecvEnable_On != Get_PcuRecvEnable(PGN_HEART_PCU))
    {
        trace(TR_PCU_PROCESS, "打开---接收使能---%06X---PCU心跳帧\n", PGN_HEART_PCU);
        Set_PcuRecvEnable(PGN_HEART_PCU, eRecvEnable_On);
    }
#if PCU_REC_HERT_SEND_YXYC_EN
    if (eRecvFlag_Yes == Get_PcuRecvFlag(PGN_HEART_PCU))
    {
#endif
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_CCU_YXYC1))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---CCU遥信遥测帧1\n", PGN_CCU_YXYC1);
            Set_PcuSendRemainTimer(PGN_CCU_YXYC1, 0xFFFF);
            Set_PcuSendTimer(PGN_CCU_YXYC1, 0xFFFF);
        }
        if (0xFFFF != Get_PcuSendRemainTimer(PGN_CCU_YXYC2))
        {
            trace(TR_PCU_PROCESS, "打开---发送使能---%06X---CCU遥信遥测帧2\n", PGN_CCU_YXYC2);
            Set_PcuSendRemainTimer(PGN_CCU_YXYC2, 0xFFFF);
            Set_PcuSendTimer(PGN_CCU_YXYC2, 0xFFFF);
        }

        if (eRecvEnable_On != Get_PcuRecvEnable(PGN_PCU_WS))
        {
            trace(TR_PCU_PROCESS, "打开---接收使能---%06X---PCU工作状态\n", PGN_PCU_WS);
            Set_PcuRecvEnable(PGN_PCU_WS, eRecvEnable_On);
        }
        if (eRecvEnable_On != Get_PcuRecvEnable(PGN_PCU_YXYC))
        {
            trace(TR_PCU_PROCESS, "打开---接收使能---%06X---PCU遥信遥测帧\n", PGN_PCU_YXYC);
            Set_PcuRecvEnable(PGN_PCU_YXYC, eRecvEnable_On);
        }
        if (eRecvEnable_On != Get_PcuRecvEnable(PGN_PCU_ALARM))
        {
            trace(TR_PCU_PROCESS, "打开---接收使能---%06X---PCU告警信息帧\n", PGN_PCU_ALARM);
            Set_PcuRecvEnable(PGN_PCU_ALARM, eRecvEnable_On);
        }
        if (eRecvEnable_On != Get_PcuRecvEnable(PGN_PCU_YC2))
        {
            trace(TR_PCU_PROCESS, "打开---接收使能---%06X---PCU遥测帧2\n", PGN_PCU_YC2);
            Set_PcuRecvEnable(PGN_PCU_YC2, eRecvEnable_On);
        }
        if (eRecvEnable_On != Get_PcuRecvEnable(PGN_PCU_EXYC))
        {
            trace(TR_PCU_PROCESS, "打开---接收使能---%06X---PCU扩展数据\n", PGN_PCU_EXYC);
            Set_PcuRecvEnable(PGN_PCU_EXYC, eRecvEnable_On);
        }
#if PCU_REC_HERT_SEND_YXYC_EN
    }
#endif
}

static void Check_ParaOperate(void)
{
    PARA_OPERATE Curtype;
    PARA_OPERATE *pOperateType;

    Curtype = Get_CurParaOperateType();

    if (0 == Curtype.paraType)
    {
        pOperateType = Get_ParaOperateType();

        if (NULL != pOperateType)
        {
            if (eOperateType_Set == pOperateType->operateType)
            {
                if (0 == Get_PcuSendRemainTimer(PGN_PCU_FIX_SET))
                {
                    trace(TR_PCU_PROCESS, "打开定值设置-设备类型<%02X> 参数类型 <%02X> \n",
                          pOperateType->devType, pOperateType->paraType);

                    Set_CurParaOperateType(pOperateType[0]);
                    //                    Del_ParaNeedOperateType();

                    Set_PcuSendRemainTimer(PGN_PCU_FIX_SET, 5000 / PCU_CALL_CYCLE);
                    Set_PcuSendTimer(PGN_PCU_FIX_SET, 0xFFFF);
                    return;
                }
            }

            if (eOperateType_Query == pOperateType->operateType)
            {
                if (0 == Get_PcuSendRemainTimer(PGN_PCU_FIX_QUERY))
                {
                    trace(TR_PCU_PROCESS, "打开定值查询-设备类型<%02X> 参数类型 <%02X> \n",
                          pOperateType->devType, pOperateType->paraType);

                    Set_CurParaOperateType(pOperateType[0]);
                    //                    Del_ParaNeedOperateType();

                    Set_PcuSendRemainTimer(PGN_PCU_FIX_QUERY,
                                           5000 / PCU_CALL_CYCLE);
                    Set_PcuSendTimer(PGN_PCU_FIX_QUERY, 0xFFFF);
                    return;
                }
            }
        }
    }
}

static void Check_YxYcChagnge(void)
{
    static uint8 Last_Data[8] = {0};
    uint8 Cur_Data[8] = {0};

    Get_PcuYxYc1Data(Cur_Data); /***/

    if (0 != memcmp(Cur_Data, Last_Data, sizeof(Cur_Data)))
    {
        memcpy(Last_Data, Cur_Data, sizeof(Cur_Data));
        //        trace(TR_PCU_PROCESS, "打开---发送使能---%06X---CCU遥信遥测帧\n", PGN_CCU_YXYC1);
        //        Set_PcuSendRemainTimer(PGN_CCU_YXYC1, 0xFFFF);
        Set_PcuSendTimer(PGN_CCU_YXYC1, 0xFFFF);
    }
}

static void Pcu_ProtocolDeal(void)
{
    Check_YxYcHeart();
    Check_Demond();
    Check_ParaOperate();
    Check_YxYcChagnge();
}

/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/

/**
 ******************************************************************************
 * @brief       获取当前定值操作类型
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      PARA_OPERATE type
 * @details
 * @note
 ******************************************************************************
 */
PARA_OPERATE Get_CurParaOperateType(void)
{
    return s_CurParaOperate;
}

/**
 ******************************************************************************
 * @brief       设置当前定值操作类型
 * @param[in]   PARA_OPERATE type
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note
 ******************************************************************************
 */
void Set_CurParaOperateType(PARA_OPERATE type)
{
    s_CurParaOperate = type;
}

/**
 ******************************************************************************
 * @brief       清除当前定值操作类型
 * @param[in]   PARA_OPERATE type
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 * @note
 ******************************************************************************
 */
void Clr_CurParaOperateType(void)
{
    memset(&s_CurParaOperate, 0x00, sizeof(s_CurParaOperate));
}

/**
 ******************************************************************************
 * @brief       获取当前定值待操作类型
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      PARA_OPERATE *  NULL-表示当前待操作列表没有操作任务
 * @details
 * @note
 ******************************************************************************
 */
PARA_OPERATE *Get_ParaOperateType(void)
{
    if (0 != s_ParaNeedOperateMap[0].paraType)
    {
        return &s_ParaNeedOperateMap[0];
    }
    else
    {
        return NULL;
    }
}

/**
 ******************************************************************************
 * @brief       清除定值待操作列表
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
void Del_ParaNeedOperateType(void)
{
    memmove(&s_ParaNeedOperateMap[0], &s_ParaNeedOperateMap[1],
            (MAX_PARA_MAP_SIZE - 1) * sizeof(PARA_OPERATE));

    memset(&s_ParaNeedOperateMap[MAX_PARA_MAP_SIZE - 1], 0x00,
           sizeof(PARA_OPERATE));
}

/**
 ******************************************************************************
 * @brief       添加待定值待设置列表
 * @param[in]   CONST_DEV_TYPE dev              设备类型
 * @param[in]   CONST_OPERATE_TYPE operateType  操作类型
 * @param[in]   uint8  paraType                 操作对象
 * @param[out]  NONE
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
void Add_ParaNeedSetType(CONST_DEV_TYPE dev, CONST_OPERATE_TYPE operateType, uint8 paraType)
{
    uint8 index = 0;

    for (index = 0; index < MAX_PARA_MAP_SIZE; index++)
    {
        if (s_ParaNeedOperateMap[index].devType == dev &&
            s_ParaNeedOperateMap[index].operateType == operateType &&
            s_ParaNeedOperateMap[index].paraType == paraType)
        {
            return;
        }
        else if (0 == s_ParaNeedOperateMap[index].paraType)
        {
            s_ParaNeedOperateMap[index].devType = dev;
            s_ParaNeedOperateMap[index].operateType = operateType;
            s_ParaNeedOperateMap[index].paraType = paraType;
            return;
        }
    }

    //    Del_ParaNeedOperateType();
}

/**
 ******************************************************************************
 * @brief       模块快速启动成功标识
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      TRUE-启动成功   FALSE-自检启动失败
 * @details
 * @note
 ******************************************************************************
 */
RESULT moudleQStartFlag(uint8 wr, uint8 flag)
{
    static RESULT qflag = eResult_NULL;
    if (wr)
    {
        qflag = flag;
    }
    return qflag;
}

/**
 ******************************************************************************
 * @brief       保留模块关机成功标识
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      TRUE-启动成功   FALSE-自检启动失败
 * @details
 * @note
 ******************************************************************************
 */
RESULT moudleCStopFlag(uint8 wr, uint8 flag)
{
    static RESULT cstopflag = eResult_NULL;
    if (wr)
    {
        cstopflag = flag;
    }
    return cstopflag;
}

/**
 ******************************************************************************
 * @brief       软启动成功标识
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      TRUE-启动成功   FALSE-自检启动失败
 * @details
 * @note
 ******************************************************************************
 */
RESULT moudleSStartFlag(uint8 wr, uint8 flag)
{
    static RESULT sstartflag = eResult_NULL;
    if (wr)
    {
        sstartflag = flag;
    }
    return sstartflag;
}

/**
 ******************************************************************************
 * @brief       模块关机成功标识
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      TRUE-启动成功   FALSE-自检启动失败
 * @details
 * @note
 ******************************************************************************
 */
RESULT moudleStopFlag(uint8 wr, uint8 flag)
{
    static RESULT stopflag = eResult_NULL;
    if (wr)
    {
        stopflag = flag;
    }
    return stopflag;
}

/**
 ******************************************************************************
 * @brief       模块关机成功标识
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      TRUE-启动成功   FALSE-自检启动失败
 * @details
 * @note
 ******************************************************************************
 */
RESULT moudleParaFlag(uint8 wr, uint8 flag)
{
    static RESULT stopflag = eResult_NULL;
    if (wr)
    {
        stopflag = flag;
    }
    return stopflag;
}
/**
 ******************************************************************************
 * @brief       获取自检启动成功标记
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      TRUE-启动成功   FALSE-自检启动失败
 * @details
 * @note
 ******************************************************************************
 */
RESULT
Get_SelfcheckStartFlag(void)
{
    return s_SelfCheckStartSuccFlag;
}

/**
 ******************************************************************************
 * @brief       设置自检启动成功标记
 * @param[in]   uint8 flag
 * @param[out]  NONE
 * @retval
 * @details
 * @note
 ******************************************************************************
 */
void Set_SelfcheckStartFlag(RESULT flag)
{
    s_SelfCheckStartSuccFlag = flag;
}

/**
 ******************************************************************************
 * @brief       获取PCU地址获取成功标记
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      TRUE-获取成功
 * @details
 * @note        在上电时未成功接收到PCU的非广播地址，则用广播地址与PCU通信
 *              接收到PCU地址后，变更目的地址
 ******************************************************************************
 */
bool_e Get_PcuAddrRecvSuccFlag(void)
{
    return s_PcuAddrRecvSuccFlag;
}

/**
 ******************************************************************************
 * @brief       设置PCU地址获取成功标记
 * @param[in]   bool_e flag   TRUE-获取成功
 * @param[out]  NONE
 * @retval      NONE
 * @details     在上电时未成功接收到PCU的非广播地址，则用广播地址与PCU通信
 *
 * @note        接收到PCU地址后，变更目的地址
 ******************************************************************************
 */
void Set_PcuAddrRecvSuccFlag(bool_e flag)
{
    s_PcuAddrRecvSuccFlag = flag;
}

/**
 ******************************************************************************
 * @brief       获取PCU地址
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      pcu通信地址
 * @details     在上电时未成功接收到PCU的非广播地址，则用广播地址与PCU通信
 * @note        接收到PCU地址后，变更目的地址
 ******************************************************************************
 */
uint8 Get_PcuAddr(void)
{
    return s_PcuAddr;
}

/**
 ******************************************************************************
 * @brief       获取CCU地址
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      ccu通信地址
 * @details     BASRADDR+MODE1
 * @note
 ******************************************************************************
 */
uint8 Get_CcuAddr(void)
{
    return ADDR_CCU + Get_CcuCfgAddr();
}

/**
******************************************************************************
* @brief       保存接收到的PCU地址
* @param[in]   uint8 addr
* @param[out]  NONE
* @retval      pcu通信地址
* @details     在上电时未成功接收到PCU的非广播地址，则用广播地址与PCU通信
* @note        接收到PCU地址后，变更目的地址
******************************************************************************
*/
void Set_PcuAddr(uint8 addr)
{
    s_PcuAddr = addr;
}

/**
 ******************************************************************************
 * @brief       句柄
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      ccu通信地址
 * @details     BASRADDR+MODE1
 * @note
 ******************************************************************************
 */
int32 Get_FdCan(void)
{
    return s_FdCan;
}

uint16 Get_PcuCurr(void)
{
    //    trace(TR_DEBUG, "pPcuExYcData->databuf: ");
    //    trace_buf(TR_DEBUG, pPcuExYcData->databuf, 8);
    //    trace(TR_DEBUG, "\n");
    return (pPcuExYcData->databuf[1] << 8) | pPcuExYcData->databuf[0];
}

/**
 ******************************************************************************
 * @brief       PCU任务管理
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details     1.PCU状态管理
 *              2.PCU发送服务
 *              3.PCU接收服务
 *              4.PCU接收超时管理
 * @note        10ms调用
 ******************************************************************************
 */
void Pcu_Task(void)
{
    uint32 dmnTick;
    Init_Pcu();
    /**< PCU_CHAN使用CAN3,为串口转CAN,这个设置的是串口的波特率 ,实际CAN通讯的波特率是125k*/
    memset(&s_pcuExYcData, 0xFF, sizeof(PCU_EXYC_DATA));
    if (MODE_CPCU != sysGetMode())
    {
        can_init(PCU_CHAN, CAN_BAD_125);
    }
    taskDelay(100);
    dmnTaskRegister();

    FOREVER
    {
        if (abs(tickGet() - dmnTick) > sysClkRateGet())
        {
            dmnTick = tickGet();
            dmnTaskSigned(); /**< 任务签到         */
        }
        taskDelay(5);

        g_PcuTaskDelay = (g_PcuTaskDelay >= PCU_CALL_CYCLE) ? PCU_CALL_CYCLE : g_PcuTaskDelay;
        taskDelay(PCU_CALL_CYCLE - g_PcuTaskDelay);
        g_PcuTaskDelay = 0;

        TESTLISTEN; /**< 板级测试模式监听 */

        Pcu_RecvServer();
        /**鲁能未周期发帧 暂时关闭接受超时*/
#if PCU_REC_TIMEOUT_EN
        Pcu_RecvTimerManage();
#endif
        Pcu_ProtocolDeal();

        //        Pcu_StateServer();

        Pcu_SendServer();

        Pcu_StageManage();
    }

    dmnTaskUnRegister();
    taskDelete(NULL);
}

/*----------------------------pcuMain.c--------------------------------*/
