
#ifndef __MODBUS_RTU_H_
#define __MODBUS_RTU_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include "modbus_config.h"

#if (MODBUS_RTU_SLAVE_ENABLE) || (MODBUS_RTU_MASTER_ENABLE)
#include "agile_modbus/agile_modbus.h"    //agile_modbus的头文件
#include "slave_util/modbus_slave_util.h" //agile_modbus的从机接口头文件
#include "platform/modbus_rt_platform_memory.h"
#include "platform/modbus_rt_platform_thread.h"
#include "platform/modbus_rt_platform_serial.h"
#include "list.h"
#include "../ccu/charge/lc_log_stat.h"

#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
#include "modbus_rt_platform_net_socket.h"
#endif

#define MODBUS_RT_WAIT_FOREVER ((uint32_t)-1) // 永久等待宏
#define LAST_VERSION 0
    /**
     * @brief   modbus rtu 串口参数结构体
     */
    struct modbus_rt_serial_info
    {
        char devname[MODBUS_RTU_NAME_MAX]; // 串口设备名称
        int baudrate;                      // 串口波特率
        int bytesize;                      // 串口数据位
        char parity;                       // 串口校验位
        int stopbits;                      // 串口停止位
        int xonxoff;                       // 串口流控
    };

    /**
     * @brief   modbus rtu over 模式选择,目前只选择OVER_NET表示TCP/UDP的承载模式
     */
    typedef enum
    {
        OVER_NONE = 0x00, // 不使用网络承载
        OVER_NET = 0x01,  // 使用网络承载 (TCP/UDP)
    } modbus_serial_over_type_t;

    /**
     * @brief Modbus 设备状态
     */
    typedef enum
    {
        MODBUS_DEV_STATE_ACTIVE,  // 设备活跃，可以处理命令
        MODBUS_DEV_STATE_CLOSING, // 设备正在关闭，不接受新命令
        MODBUS_DEV_STATE_CLOSED   // 设备已关闭
    } modbus_dev_state_t;
    /**
     * @brief   modbus rtu通信结构体
     */
    struct rtu_modbus_device
    {
        int serial;                               // 绑定的串口设备号
        struct modbus_rt_serial_info serial_info; // 串口信息
        int byte_timeout;                         // modbus字节超时时间，单位us，可以直通通过config配置
#if MODBUS_ASCII_SLAVE_ENABLE || MODBUS_ASCII_MASTER_ENABLE
        int ascii_flag; // 是否实现对modbus ascii的支持
#endif
        modbus_serial_over_type_t over_type; // 串口的承载模式
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        int sock;                     // TCP_Modbus对应的socket
        int type;                     // 类型：SOCK_STREAM(1)为TCP;SOCK_DGRAM(2):UDP
        char *ipaddr;                 // 设备的ip地址
        char ip_buf[INET_ADDRSTRLEN]; // 设备端的ip地址
        unsigned int port;            // 端口号
#endif
        int status;                 // 设备状态，是否已经打开
        modbus_mode_type mode;      // modbus rtu的运行模式：SLAVE/MASTER
        agile_modbus_rtu_t ctx_rtu; // agile_modbus_tcp_t结构体
        agile_modbus_t *ctx;        // agile_modbus的句柄
        int send_len;               // 发送缓存区能够容纳的数据的长度
        int read_len;               // 用于接收的缓存区容纳数据的长度
        uint8_t *ctx_send_buf;      // 存储发送缓存区的内存
        uint8_t *ctx_read_buf;      // 存储接收缓存区的内存
        modbus_rt_thread_t *thread; // 运行的线程标志
        modbus_rt_sem_t sem;        // 用于线程的启动和退出的信号量
        void *data;                 // modbus的运行的数据，根据SLAVE或者MASTER不同而不同
        int thread_owner;           // 线程所有权标志: 1 (默认) = 库创建和管理线程, 0 = 用户/应用层自己创建线程并调用poll函数

        // 新增字段
        modbus_dev_state_t dev_state;  // 设备状态
        int active_cmd_count;          // 活动命令计数
        modbus_rt_mutex_t state_mutex; // 状态互斥锁
        modbus_rt_sem_t all_cmd_done;  // 所有命令完成信号量
    };
    typedef struct rtu_modbus_device *rtu_modbus_device_t;

#if MODBUS_RTU_SLAVE_ENABLE
#include "modbus_slave.h"

    /**
     * @brief   modbus rtu slave结构体
     */
    struct rtu_slave_data
    {
        int addr;                       // modbus地址
        uint8_t slave_strict;           // 是否对modbus地址进行判断，由于modbus tcp是根据IP进行通信，所以该项可以不用对地址进行判断
        agile_modbus_slave_util_t util; // 用于把slave的寄存器等信息
    };
    typedef struct rtu_slave_data *rtu_slave_data_t;
#endif

#if MODBUS_RTU_MASTER_ENABLE
    /**
     * @brief Modbus 命令优先级
     */
    typedef enum
    {
        MODBUS_CMD_PRIO_HIGH = 1,   // 最高优先级，例如：紧急的遥调指令
        MODBUS_CMD_PRIO_NORMAL = 2, // 中等优先级，例如：用户手动下发的遥控指令
        MODBUS_CMD_PRIO_LOW = 3     // 最低优先级，例如：后台周期性轮询指令
    } modbus_cmd_prio_t;

    /**
     * @brief Modbus 命令状态
     */
    typedef enum
    {
        MODBUS_CMD_STATE_WAITING,  // 等待执行
        MODBUS_CMD_STATE_RUNNING,  // 正在执行
        MODBUS_CMD_STATE_COMPLETE, // 执行完毕
        MODBUS_CMD_STATE_DETACHED  // 调用者已超时放弃，所有权已转移给工作者
    } modbus_cmd_state_t;

    /**
     * @brief Modbus Master 命令结构体
     */
    typedef struct
    {
        /* 指令的基本参数 */
        int slave_addr;
        int function;
        int w_addr;
        int w_quantity;
        void *ptr_w_data;

        /* 仅为(0x17)AGILE_MODBUS_FC_WRITE_AND_READ_REGISTERS命令使用 */
        int r_addr;
        int r_quantity;
        void *ptr_r_data;

        /* 命令时间戳 */
        uint32_t timestamp;

        /* 执行与同步 */
        modbus_cmd_state_t status; // 命令状态，用于处理超时和取消

        modbus_rt_sem_t completion_sem;   // 用于通知调用者指令完成
        modbus_rt_sem_t caller_ready_sem; // 新增：用于调用者和工作者之间的握手
        int result;                       // 存储 excause 的返回值
        /* 新增：用于应用层传递上下文信息 */
        void *user_data;
        volatile int refcnt; /* 创建时 = 2(同步) / 1(异步) */
        /* 优先级队列节点 */
        plist_node list_node; // prio 字段在此结构体中
    } modbus_cmd_t;

    /**
     * @brief   modbus rtu master结构体
     */
    struct rtu_master_data
    {

#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
        char saddr[INET_ADDRSTRLEN]; // slave端的ip地址
        unsigned int sport;          // slave端的端口号
#endif
        /* 命令队列 */
        plist_head cmd_queue;          // 优先级命令队列
        modbus_rt_mutex_t cmd_mutex;   // 用于保护命令队列的互斥锁
        modbus_rt_sem_t cmd_sem;       // 用于通知执行线程有新命令的信号量
        modbus_rt_mq_sem_t cmd_mq_sem; // 命令通知执行线程信号量
        /* 结果回调 */
        void (*result_handler)(modbus_cmd_t *cmd, void *user_data);
        void *result_handler_data;

        /* 原始的指令字段，用于在工作线程中执行命令时存储当前指令信息 */
        int slave_addr; // modbus 从机地址
        int function;   // Modbus的功能码
        int data_addr;  // Modbus的操作地址
        int quantity;   // Modbus的操作地址数量
        int ret;        // 执行之后的返回值
        void *ptr_data; // Modbus的操作数据指针
        /*
         * 以下字段仅为AGILE_MODBUS_FC_WRITE_AND_READ_REGISTERS使用
         */
        int read_addr;
        int read_quantity;
        void *ptr_read_data; // Modbus的操作数据指针
    };
    typedef struct rtu_master_data *rtu_master_data_t;
#endif

    /**
     * @brief   modbus rtu API函数
     */
    rtu_modbus_device_t modbus_rtu(modbus_mode_type mode);
    int modbus_rtu_set_serial(rtu_modbus_device_t dev, const char *devname, int baudrate, int bytesize, char parity, int stopbits, int xonxoff);
    int modbus_rtu_set_thread_owner(rtu_modbus_device_t dev, int owner);

#if MODBUS_ASCII_SLAVE_ENABLE || MODBUS_ASCII_MASTER_ENABLE
    int modbus_rtu_set_ascii_flag(rtu_modbus_device_t dev, int flag); // 要想支持modbus ascii只能通过modbus rtu的该标志位来开启
#endif

    int modbus_rtu_open(rtu_modbus_device_t dev);
    int modbus_rtu_isopen(rtu_modbus_device_t dev);
    int modbus_rtu_close(rtu_modbus_device_t dev);
    int modbus_rtu_destroy(rtu_modbus_device_t *pos_dev);
    int modbus_rtu_excuse(rtu_modbus_device_t dev, int dir_slave, int type_function, int addr, int quantity, void *ptr_data);

#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
    int modbus_rtu_set_over_type(rtu_modbus_device_t dev, modbus_serial_over_type_t over_type);
    int modbus_rtu_set_net(rtu_modbus_device_t dev, char *ipaddr, unsigned int port, int type);
    int modbus_rtu_set_ip(rtu_modbus_device_t dev, char *ipaddr);
    int modbus_rtu_set_port(rtu_modbus_device_t dev, unsigned int port);
    int modbus_rtu_set_type(rtu_modbus_device_t dev, int type);
#endif

#if MODBUS_RTU_SLAVE_ENABLE
    int modbus_rtu_set_addr(rtu_modbus_device_t dev, int addr);
    int modbus_rtu_set_strict(rtu_modbus_device_t dev, uint8_t strict);
    int modbus_rtu_add_block(rtu_modbus_device_t dev, modbus_register_type_t type, int data_addr, void *data, int nums);
    int modbus_rtu_set_pre_ans_callback(rtu_modbus_device_t dev, int (*pre_ans)(agile_modbus_t *, int, int, int, int));
    int modbus_rtu_set_done_callback(rtu_modbus_device_t dev, int (*done)(agile_modbus_t *, int, int, int, int));
    int modbus_rtu_slave_poll(rtu_modbus_device_t dev);
#endif

#if MODBUS_TCP_MASTER_ENABLE
#if MODBUS_SERIAL_OVER_TCP_ENABLE || MODBUS_SERIAL_OVER_UDP_ENABLE
    int modbus_rtu_set_server(rtu_modbus_device_t dev, char *saddr, unsigned int sport);
    int modbus_rtu_get_saddr(rtu_modbus_device_t dev, char *saddr);
#endif
    int modbus_rtu_excuse_ex(rtu_modbus_device_t dev, int slave, int function, int w_addr, int w_quantity,
                             void *ptr_w_data, int r_addr, int r_quantity, void *ptr_r_data);
#endif
#if MODBUS_RTU_MASTER_ENABLE
    int modbus_rtu_master_send_cmd(rtu_modbus_device_t dev, modbus_cmd_t *cmd, int timeout);
    int modbus_rtu_master_poll(rtu_modbus_device_t dev);
    int modbus_rtu_set_master_result_handler(rtu_modbus_device_t dev, void (*handler)(modbus_cmd_t *cmd, void *user_data), void *user_data);
#endif
    void print_hex_data(const char *prompt, const uint8_t *data, int len);
    void cmd_cleanup(modbus_cmd_t *cmd);
    int ref_dec_and_fetch(volatile int *p, int delta);
#ifdef __cplusplus
}
#endif

#endif /* __MODBUS_RTU_H_ */
#endif
