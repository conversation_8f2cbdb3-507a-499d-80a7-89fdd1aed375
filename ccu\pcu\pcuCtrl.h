/**
 ******************************************************************************
 * @file       pcuCtrl.h
 * @brief      API include file of pcuCtrl.h.
 * @details    This file including all API functions's declare of pcuCtrl.h.
 * @copy       Copyright(C), 2008-2020,Sanxing Smart Electric Co.,Ltd.
 *
 ******************************************************************************
 */
#ifndef __PCU_CTRL_H__
#define __PCU_CTRL_H__
/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
/* NONE */
#include <types.h>
#include <ccu\lib\ccuLib.h>
#include <can.h>

/*-----------------------------------------------------------------------------
 Section: Macro Definitions
 ----------------------------------------------------------------------------*/
typedef enum
{
    RC = 0,        /***<遥控命令帧系数*/
    FIXED_SET,     /***<定值设定帧系数 */
    FIXED_GET,     /***<定值读帧系数 */
    RSRT1,         /***<遥信遥测1帧系数*/
    RSRT2,         /***<遥信遥测2帧系数*/
    SEND_HEART,    /***<心跳帧系数*/
    UP_HEART,      /***<升级_心跳帧系数*/
    UP_START,      /***<升级_启动更新系数*/
    UP_DEMAND,     /***<升级_索要区间系数*/
    UP_START_PACK, /***<升级_开始组包系数*/
    UP_DATA_SEND,  /***<升级_数据发送系数*/
    UP_END_PACK,   /***<升级_完成组包系数*/
    UP_CHECK,      /***<升级_校验系数*/
    UP_RESET,      /***<升级_立即复位系数*/
    SEND_FRAME_MAX,
} send_frame_index_e;

typedef enum
{
    RC_ACK = 0,        /***<遥控命令应答帧系数*/
    FIXED_SET_ACK,     /***<定值设定帧系数 */
    FIXED_GET_ACK,     /***<定值读帧系数 */
    WORK_ERR_STU,      /***<工作状态及故障信息帧系数*/
    RT,                /***<遥测帧系数*/
    ALARM,             /***<告警帧系数*/
    REC_HEART,         /***<心跳帧系数*/
    UP_HEART_ACK,      /***<升级_心跳帧系数*/
    UP_START_ACK,      /***<升级_启动更新系数*/
    UP_DEMAND_ACK1,    /***<升级_索要区间系数*/
    UP_DEMAND_ACK2,    /***<升级_索要区间系数*/
    UP_START_PACK_ACK, /***<升级_开始组包系数*/
    UP_END_PACK_ACK,   /***<升级_完成组包系数*/
    UP_CHECK_ACK,      /***<升级_校验系数*/
    UP_RESET_ACK,      /***<升级_立即复位系数*/
    REC_FRAME_MAX,
} receive_frame_index_e;

/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
typedef struct
{
    uint8 en;    /**<使能*/
    uint8 err;   /**<错误*/
    uint8 flag;  /**<接受完成标记或者发送完成标记*/
    uint8 res;   /**<预留*/
    uint32 tick; /**<记录帧tick*/
} frame_status_t;

typedef struct
{
    uint8 state;     /**<保存运行阶段*/
    uint32 run_tick; /**<运行tick*/
    frame_status_t send_frame[SEND_FRAME_MAX];
    frame_status_t rec_frame[REC_FRAME_MAX];
} pcu_ctrl_status_t;
/*-----------------------------------------------------------------------------
 Section: Globals
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Function Prototypes
 ----------------------------------------------------------------------------*/
/* NONE */
typedef bool_e (*pConditionFunc)(pcu_ctrl_status_t *);
typedef void (*pExecuteFunc)(pcu_ctrl_status_t *);
typedef struct
{
    uint8 state;
    pConditionFunc conditionFunc[3]; /**<条件*/
    pExecuteFunc executionFunc;      /**<执行函数*/
} pcu_state_mange_t;

#endif //__PCU_CTRL_H__
/*--------------------------End of pcuCtrl.h----------------------------*/
