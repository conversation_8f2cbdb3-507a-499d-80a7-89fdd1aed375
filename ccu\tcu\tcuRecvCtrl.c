/**
 ******************************************************************************
 * @file      tcuRecvCtrl.c
 * @brief     C Source file of tcuRecvCtrl.c.
 * @details   This file including all API functions's
 *            implement of tcuRecvCtrl.c.
 *
 * @copy      Copyrigth(C), 2008-2012,Sanxing Electric Co.,Ltd.
 */

/*-----------------------------------------------------------------------------
 Section: Includes
 ----------------------------------------------------------------------------*/
#include <stddef.h>
#include <sxlib.h>
#include <string.h>
#include <stdio.h>
#include <tmLib.h>
#include <clklib.h>
#include <time.h>
#include <trace.h>
#include <taskLib.h>
#include <stdLib.h>
#include "public.h"
#include <rtc.h>
#include <ccu\inc\bms.h>
#include <maths.h>
#include <ccu/bsn/sample.h>
#include <ccu\lib\ccuLib.h>
#include <ccu\lib\ccuFix.h>
#include "tcuMain.h"
#include "tcuRecvCtrl.h"
#include "tcuSendCtrl.h"
#include "ccu\bsn\io.h"
#include "ccu\bsn\deviceState.h"
#include "ccu\para\para.h"
#include "ccu\charge\ccuChargeMain.h"
#include "flash_rw.h"
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/
typedef uint32 (*pTimeOutFunc)(void);
typedef bool_e (*PDecodeFunc)(uint8 *pInBuf, uint8 len);
// typedef void (*POverTimeFunc)(void);

typedef struct BMS_RECV_DEAL_STRU
{
    uint32 pgn;
    uint8 prio;
    uint8 mulFrameFlag; /**< 多帧标记 多帧数据时为真，否则为0 */
                        //    uint32          overtime;
    pTimeOutFunc pOverTimeFunc;
    PDecodeFunc pDecodeFunc;
} TCU_RECV_DEAL;

typedef struct
{
    uint8 platform;
    TCU_RECV_DEAL Tcu_Frame;
} Multiframe_Adapt;

/*-----------------------------------------------------------------------------
 Section: Constant Definitions
 ----------------------------------------------------------------------------*/
/* NONE */

/*-----------------------------------------------------------------------------
 Section: Global Variables
 ----------------------------------------------------------------------------*/
extern TCU_CTRL tcuCtrl;
extern uint8 tcuErroInfo[7];

/*-----------------------------------------------------------------------------
 Section: Local Variables
 ----------------------------------------------------------------------------*/
static bool_e Recv_ChargeStart(uint8 *pInBuf, uint8 len);
static bool_e Recv_ChargeStop(uint8 *pInBuf, uint8 len);
static bool_e Recv_TimeSyn(uint8 *pInBuf, uint8 len);
static bool_e Recv_VerCheck(uint8 *pInBuf, uint8 len);
static bool_e Recv_SetPara(uint8 *pInBuf, uint8 len);
static bool_e Recv_ServeCtrl(uint8 *pInBuf, uint8 len);
static bool_e Recv_ElecLockCtrl(uint8 *pInBuf, uint8 len);
static bool_e Recv_PowerCtrl(uint8 *pInBuf, uint8 len);
static bool_e Recv_QueryConfig(uint8 *pInBuf, uint8 len);
static bool_e Recv_StartFinishACK(uint8 *pInBuf, uint8 len);
static bool_e Recv_StopFinishACK(uint8 *pInBuf, uint8 len);
static bool_e Recv_YC(uint8 *pInBuf, uint8 len);
static bool_e Recv_Heart(uint8 *pInBuf, uint8 len);
static bool_e Recv_Fault(uint8 *pInBuf, uint8 len);
static bool_e Recv_TcuFixSet(uint8 *pInBuf, uint8 len);
static bool_e Recv_TcuMulFixSet(uint8 *pInBuf, uint8 len);
static bool_e Recv_TcuFixQuery(uint8 *pInBuf, uint8 len);
static bool_e Recv_VehicleValidateAck(uint8 *pInBuf, uint8 len);
static bool_e Recv_TcuDebugCmd(uint8 *pInBuf, uint8 len);
static bool_e Recv_TcuRstCmd(uint8 *pInBuf, uint8 len);
static uint32 Get_TcuNoneTimeOut(void);
static uint32 Get_TcuTimeOut(void);
static uint32 Get_Tcu5STimeOut(void);
static uint32 Get_VehicleAckTimeOut(void);
static bool_e Recv_CarVinIdentifyAck(uint8 *pInBuf, uint8 len);
static uint32 Get_CarVinIdentifyAckTimeOut(void);
static uint32 Get_CarVinConfirmTimeOut(void);
static bool_e Recv_CarVinConfirm(uint8 *pInBuf, uint8 len);
static bool_e
Recv_VinRepAck(uint8 *pInBuf, uint8 len);
static bool_e
Recv_VinConfirm(uint8 *pInBuf, uint8 len);
static bool_e
Recv_CarVinConfirm(uint8 *pInBuf, uint8 len);
static uint32 Get_VinConfirmTimeOut(void);
static uint32 Get_CarVinRepAckTimeOut(void);
static uint32 Get_CarVinConfirmTimeOut(void);
const TCU_RECV_DEAL TCU_RECV_DEAL_TABLE[] =
    {
        {PGN_CHARGE_START, 4, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_ChargeStart},
        {PGN_TCU_VIN, 4, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_ChargeStart},
        {PGN_CHARGE_STOP, 4, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_ChargeStop},
        {PGN_TIME_SYN, 6, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_TimeSyn},
        {PGN_VER_CHECK, 6, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_VerCheck},
        {PGN_SET_PARA, 6, 0, Get_Tcu5STimeOut /*5000*/, Recv_SetPara},
        {PGN_SERVE_CTRL, 4, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_ServeCtrl},
        {PGN_ELECLOCK_CTRL, 4, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_ElecLockCtrl},
        {PGN_POWER_CTRL, 4, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_PowerCtrl},
        {PGN_QUERY_CONFIG, 6, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_QueryConfig},
        {PGN_START_FINISH_ACK, 4, 0, Get_Tcu5STimeOut /*5000*/, Recv_StartFinishACK},
        {PGN_STOP_FINISH_ACK, 4, 0, Get_Tcu5STimeOut /*5000*/, Recv_StopFinishACK},
        {PGN_TCU_YC, 6, 0, Get_TcuNoneTimeOut /*5000*/, Recv_YC},
        {PGN_TCU_HEART, 6, 0, Get_TcuTimeOut /*5000*/, Recv_Heart},
        {PGN_TCU_FAULT, 4, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_Fault},
        //    /*1*/     { PGN_FIX_SET,       	 	6,0,    Get_TcuNoneTimeOut/*0xFFFF*/,    	Recv_TcuFixSet          },
        {PGN_FIX_SET, 6, 1, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_TcuFixSet},
        {PGN_FIX_SET_MUL, 6, 1, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_TcuMulFixSet},
        {PGN_FIX_QUERY, 6, 1, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_TcuFixQuery},
#if VEHICLE_VALIDATE_ENABLE
        {PGN_CAR_CHECK_INFO_ACK, 6, 0, Get_VehicleAckTimeOut /*30000 */, Recv_VehicleValidateAck},
        {PGN_CAR_VIN_IDENTIFY_ACK, 6, 0, Get_CarVinIdentifyAckTimeOut, Recv_CarVinIdentifyAck},
        {PGN_CAR_VIN_CONFIRM, 6, 1, Get_CarVinConfirmTimeOut, Recv_CarVinConfirm},
        {PGN_TCU_VIN_REP_ACK, 4, 0, Get_CarVinRepAckTimeOut, Recv_VinRepAck},
        {PGN_TCU_VIN_CONFIRM, 4, 0, Get_VinConfirmTimeOut, Recv_VinConfirm},
#endif
        {PGN_DEBUG_CMD, 6, 1, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_TcuDebugCmd},
        //  /*1*/  {PGN_RST_CMD,               6,0,    Get_TcuNoneTimeOut/*0xFFFF*/,       Recv_TcuRstCmd          },
        {PGN_RST_CMD, 6, 1, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_TcuRstCmd},
};

const Multiframe_Adapt TCU_RECV_MUL_TABLE[] =
    {
        //    {0,{ PGN_FIX_SET, 6,1, Get_TcuNoneTimeOut/*0xFFFF*/,Recv_TcuFixSet}},
        //    {0,{ PGN_RST_CMD,  6,1, Get_TcuNoneTimeOut/*0xFFFF*/,Recv_TcuRstCmd}},
        {1, {PGN_FIX_SET, 6, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_TcuFixSet}},
        {1, {PGN_RST_CMD, 6, 0, Get_TcuNoneTimeOut /*0xFFFF*/, Recv_TcuRstCmd}},
};
static uint32 Get_TcuNoneTimeOut(void)
{
    return 0xFFFF;
}

static uint32 Get_TcuTimeOut(void)
{
    CONFIG_PARA strCfgPara;
    Get_PilePara((void *)&strCfgPara, eParaType_ConfigPara);
    return strCfgPara.ccuAndTcuOvertime * 1000;
}

static uint32 Get_Tcu5STimeOut(void)
{
    return 5000;
}

static uint32 Get_VehicleAckTimeOut(void)
{
    return 30000;
}

static uint32 Get_CarVinIdentifyAckTimeOut(void)
{
    return 60000;
}

static uint32 Get_CarVinConfirmTimeOut(void)
{
    return 30000;
}

static uint32 Get_CarVinRepAckTimeOut(void)
{
    return 5000;
}
static uint32 Get_VinConfirmTimeOut(void)
{
    return 30000;
}
/*-----------------------------------------------------------------------------
 Section: Function Definitions
 ----------------------------------------------------------------------------*/
/**
 ******************************************************************************
 * @brief      GetTcuSendCtrl
 * @param[in]   uint32 Pgn  参数组编号
 * @param[in]
 * @param[out]
 * @retval      TCU_SEND_CTRL *返回控制变量指针
 *
 * @details     获取对应PGN的控制变量
 *
 * @note
 ******************************************************************************
 */
TCU_RECV_CTRL *Get_TcuRecvCtrl(uint32 pgn)
{
    switch (pgn)
    {
    case PGN_CHARGE_START:
        return &tcuCtrl.tcuRecvCtrl[0];

    case PGN_CHARGE_STOP:
        return &tcuCtrl.tcuRecvCtrl[1];

    case PGN_TIME_SYN:
        return &tcuCtrl.tcuRecvCtrl[2];

    case PGN_VER_CHECK:
        return &tcuCtrl.tcuRecvCtrl[3];

    case PGN_SET_PARA:
        return &tcuCtrl.tcuRecvCtrl[4];

    case PGN_SERVE_CTRL:
        return &tcuCtrl.tcuRecvCtrl[5];

    case PGN_ELECLOCK_CTRL:
        return &tcuCtrl.tcuRecvCtrl[6];

    case PGN_POWER_CTRL:
        return &tcuCtrl.tcuRecvCtrl[7];

    case PGN_QUERY_CONFIG:
        return &tcuCtrl.tcuRecvCtrl[8];

    case PGN_START_FINISH_ACK:
        return &tcuCtrl.tcuRecvCtrl[9];

    case PGN_STOP_FINISH_ACK:
        return &tcuCtrl.tcuRecvCtrl[10];

    case PGN_TCU_YC:
        return &tcuCtrl.tcuRecvCtrl[11];

    case PGN_TCU_HEART:
        return &tcuCtrl.tcuRecvCtrl[12];

    case PGN_TCU_FAULT:
        return &tcuCtrl.tcuRecvCtrl[13];
    case PGN_FIX_SET:
        return &tcuCtrl.tcuRecvCtrl[14];
    case PGN_FIX_QUERY:
        return &tcuCtrl.tcuRecvCtrl[15];
#if VEHICLE_VALIDATE_ENABLE
    case PGN_CAR_CHECK_INFO_ACK:
        return &tcuCtrl.tcuRecvCtrl[16];
    case PGN_DEBUG_CMD:
        return &tcuCtrl.tcuRecvCtrl[17];
    case PGN_CAR_VIN_IDENTIFY_ACK:
        return &tcuCtrl.tcuRecvCtrl[18];
    case PGN_CAR_VIN_CONFIRM:
        return &tcuCtrl.tcuRecvCtrl[19];
#else
    case PGN_DEBUG_CMD:
        return &tcuCtrl.tcuRecvCtrl[16];
#endif
    case PGN_RST_CMD:
        return &tcuCtrl.tcuRecvCtrl[20];

    case PGN_TCU_VIN:
        return &tcuCtrl.tcuRecvCtrl[21];
    case PGN_TCU_VIN_REP_ACK:
        return &tcuCtrl.tcuRecvCtrl[22];
    case PGN_TCU_VIN_CONFIRM:
        return &tcuCtrl.tcuRecvCtrl[23];
    case PGN_FIX_SET_MUL:
        return &tcuCtrl.tcuRecvCtrl[24];
    default:
        break;
    }

    return NULL;
}

/**
 ******************************************************************************
 * @brief       初始化TCU充电过程中的控制变量和数据
 * @param[in]   NONE
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 *
 * @details     检测到启动标记后执行
 * @note
 ******************************************************************************
 */
static void Init_TcuCharge(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    TCU_RECV_CTRL *pRecvCtrl = NULL;
    TCU_SEND_CTRL *pSendCtrl = NULL;
    uint8 index = 0;

    uint32 CLR_SEND_PGN[] =
        {
            PGN_CHARGE_STOP_ACK,
            PGN_START_FINISH,
            PGN_STOP_FINISH,
            PGN_CAR_CHECK_INFO,   // TCU V1.14版本
            PGN_CAR_VIN_IDENTIFY, // TCU V1.21版本
            PGN_CAR_VIN_CONFIRM_ACK,
            PGN_TCU_VIN_ACK,
            PGN_TCU_VIN_REP,
            PGN_TCU_VIN_CONFIRM_ACK,

        };

    uint32 CLR_RECV_PGN[] =
        {
            PGN_CHARGE_STOP,
            PGN_START_FINISH_ACK,
            PGN_STOP_FINISH_ACK,
            PGN_TCU_VIN,
            PGN_TCU_VIN_REP_ACK,
            PGN_TCU_VIN_CONFIRM,
        };

    for (index = 0; index < FCNT(CLR_SEND_PGN); index++)
    {
        pSendCtrl = Get_TcuSendCtrl(CLR_SEND_PGN[index]);

        if (NULL != pSendCtrl)
        {
            memset(pSendCtrl, 0x00, sizeof(TCU_SEND_CTRL));
        }
    }

    for (index = 0; index < FCNT(CLR_RECV_PGN); index++)
    {
        pRecvCtrl = Get_TcuRecvCtrl(CLR_RECV_PGN[index]);

        if (NULL != pRecvCtrl)
        {
            memset(pRecvCtrl, 0x00, sizeof(TCU_RECV_CTRL));
        }
    }

    pTcuCtrl->chargeStopReason = 0x00;
    pTcuCtrl->TcuChargeStopReason = 0x00;
    pTcuCtrl->chargeEnerge = 0x00;
    pTcuCtrl->chargeTime = 0x00;

    pTcuCtrl->chargeStartFinishResult = 0x00;
    pTcuCtrl->chargeStartFinishFailReason = 0x00;

    pTcuCtrl->chargeStopFinishResult = 0x00;
    pTcuCtrl->chargeStopFinishReason = 0x00;

    pTcuCtrl->stopFinishFlag = FALSE;

    return;
}

extern void
Clr_TcuErrInfo(void);
/**
 ******************************************************************************
 * @brief       启动充电帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.判断
 *
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_ChargeStart(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 index = 0;
    uint8 stage = Get_TcuStage();
    uint8 workState = Get_WorkState();
    uint8 deviceState = Get_DeviceState();

    typedef struct
    {
        uint16 errType;
        uint8 reason;
    } START_FAIL_ERR;

    const START_FAIL_ERR START_FAIL_ERR_MAP[] =
        {
            {eErrType_ComErrWithTCU, 0x02},
            {eErrType_EmergencyStop, 0x09},
            {eErrType_CcuDoorOpenErr, 0x0A},
            //		 {eErrType_PcuDoorOpenErr,                     0x0A},
            {eErrType_BlqAlarm, 0x0B},
            {eErrType_SmokeErr, 0x0C},
            {eErrType_CabTempOverErr, 0x0D},
            {eErrType_PileTempOverLimitErr, 0x0D},
            {eErrType_GunTempOverLimitErr, 0x0E},
            {eErrType_ElecLockErr, 0x0F},
            {eErrType_ImdErr, 0x10},
            {eErrType_BatteryReverseConnect, 0x11},
            {eErrType_ComErrWithBMS, 0x12},
            {eErrType_ACBreakerErr, 0x13},
            {eErrType_AcJCQErr, 0x14},
            {eErrType_K1Err, 0x16},
            {eErrType_K2Err, 0x16},
            {eErrType_DCMainContactorSynechia, 0x17},
            {eErrType_FuseProtectorErr, 0x18},
            //         {eErrType_MultipleContactorErr,            0x19},
            {eErrType_MultipleContactorSynechia, 0x1A},
            {eErrType_ReleaseErr, 0x1B},
            {eErrType_AssistPowerErr, 0x1C},
            {eErrType_PowerModuleErr, 0x1D},
            {eErrType_InputVolOverLimit, 0x1E},
            {eErrType_InputVolLessLimit, 0x1E},
            {eErrType_InputOpenphase, 0x1E},
            //         {eErrType_InputCurOverLimit,               0x1E},
            {eErrType_OutputVolOverLimit, 0x1F},
            {eErrType_OutputVolLessLimit, 0x20},
            {eErrType_OutputCurOverLimit, 0x21},
            {eErrType_OutputShortCut, 0x22},
            {eErrType_PcuForbidCharge, 0x23},

            {eErrType_PcuAndMouileTimeout, 0x2D},
            {eErrType_PcuAndPcuTimeout, 0x2E},

            {eErrType_WaterLoggingErr, 0x27},
            {eErrType_ComErrWithPCU, 0x28},
            {eErrType_ReleaseErr, 0x34},
            {eErrType_CcuYxYcTimeoutErr, 0x37},
            {eErrType_PcuAndEcuTimeout, 0x38},
            {eErrType_PEErr, 0x39},
            //		 {eErrType_PcuDoorOpenErr,               	0x25},
            //		 {eErrType_WaterLoggingErr,               	0x26},
            //		 {eErrType_WaterLoggingErr,               	0x27},
            //		 {eErrType_ComErrWithPCU,               	0x28},
            //		 {eErrType_PowerModuleErr,               	0x2A},
        };
    pTcuCtrl->ChargeGun = pInBuf[0];
    pTcuCtrl->loadSwitch = pInBuf[1];
    pTcuCtrl->fy_Switch = (pInBuf[3] != 0x18) ? FY_12V : FY_24V;
#if VEHICLE_VALIDATE_ENABLE
    if (((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120)) && Get_CcuCfgParaPlatform_convert() != Platform_protocol_Hn)
    {
        pTcuCtrl->vehicleValidateFailReason = eTcuVehicleCalibrate_VinErr; // eTcuVehicleCalibrate_Fail;//TODO-xg
        pTcuCtrl->plugAndPlay =
            pInBuf[2] ? eTcuPlugAndPlay_Enable : eTcuPlugAndPlay_NotEnable; /**<车辆即插即充使能*/
    }
    else if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120) && Get_CcuCfgParaPlatform_convert() != Platform_protocol_Hn)
    {
        pTcuCtrl->vehicleValidateFailReason = eTcuVehicleCalibrate_VinErr; // eTcuVehicleCalibrate_Fail;
        pTcuCtrl->plugAndPlay =
            (pInBuf[2] == 2) ? eTcuPlugAndPlay_Enable : eTcuPlugAndPlay_NotEnable; /**<车辆即插即充使能*/
    }
#endif
    if (0x01 != pTcuCtrl->loadSwitch && 0x02 != pTcuCtrl->loadSwitch)
    {
        pTcuCtrl->chargeStartResult = 0x01;
        pTcuCtrl->chargeStartFailReason = 0x01; // 数据合法性校验失败
    }
    else if (eComState_Normal != Get_CcuHeartState() ||
             eComState_Normal != Get_TcuHeartState())
    {
        pTcuCtrl->chargeStartResult = 0x01;
        pTcuCtrl->chargeStartFailReason = 0x02; // 心跳超时
    }
    else if (TCU_STAGE_MATCH_VER == stage)
    {
        pTcuCtrl->chargeStartResult = 0x01;
        pTcuCtrl->chargeStartFailReason = 0x03; // 版本校验未完成
    }
    else if (TCU_STAGE_MATCH_PARA == stage)
    {
        pTcuCtrl->chargeStartResult = 0x01;
        pTcuCtrl->chargeStartFailReason = 0x04; // 参数下发未完成
    }
    else if (0x01 == pTcuCtrl->chargeServeState)
    {
        pTcuCtrl->chargeStartResult = 0x01;
        pTcuCtrl->chargeStartFailReason = 0x05; // 充电服务禁止
    }
    else if (TCU_STAGE_RUN_STARTING == stage ||
             TCU_STAGE_RUN_CHARGING == stage ||
             TCU_STAGE_RUN_STOPPING == stage)
    {
        if (CCU_WORK_STATE_CHARGE_PAUSE == workState)
        {
            pTcuCtrl->chargeStartResult = 0x01;
            pTcuCtrl->chargeStartFailReason = 0x07; // 充电暂停中
        }
        else
        {
            pTcuCtrl->chargeStartResult = 0x01;
            pTcuCtrl->chargeStartFailReason = 0x06; // 工作中
        }
    }
    else if (enumPhyConVol_4V != Get_PhyConVol())
    {
        pTcuCtrl->chargeStartResult = 0x01;
        pTcuCtrl->chargeStartFailReason = 0x08; // 充电枪未连接好
    }
    else
    {
        if (DEVICE_STATE_FAULT == deviceState ||
            DEVICE_STATE_MAJOR_FAULT == deviceState)
        {
            for (index = 0; index < FCNT(START_FAIL_ERR_MAP); index++)
            {
                if (TRUE == Check_ErrType(START_FAIL_ERR_MAP[index].errType))
                {
                    pTcuCtrl->chargeStartResult = 0x01;
                    pTcuCtrl->chargeStartFailReason = START_FAIL_ERR_MAP[index].reason;
                    break;
                }
            }

            if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120)) // 支持TCU协议版本[V1.14, V1.20)
            {
                if (0x00 == pTcuCtrl->chargeStartResult)
                {
                    pTcuCtrl->chargeStartResult = 0x01;
                    pTcuCtrl->chargeStartFailReason = 0x24; // 充电机其它故障
                }
            }
            else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120) // 支持TCU协议版本[V1.20)
            {
                if (TRUE == Check_ErrType(eErrType_PcuDoorOpenErr))
                {
                    pTcuCtrl->chargeStartResult = 0x01;
                    pTcuCtrl->chargeStartFailReason = 0x25; // 充电机柜门禁故障
                }
                else if (TRUE == Check_ErrType(eErrType_WaterLoggingErr) || TRUE == Check_ErrType(eErrType_PcuWaterFault))
                {
                    pTcuCtrl->chargeStartResult = 0x01;
                    pTcuCtrl->chargeStartFailReason = 0x26; // 水侵故障
                }
                else if (TRUE == Check_ErrType(eErrType_ComErrWithPCU))
                {
                    pTcuCtrl->chargeStartResult = 0x01;
                    pTcuCtrl->chargeStartFailReason = 0x28; // CCU与PCU通讯超时
                }
                else if (TRUE == Check_ErrType(eErrType_PowerModuleErr))
                {
                    pTcuCtrl->chargeStartResult = 0x01;
                    pTcuCtrl->chargeStartFailReason = 0x2A; // 开关模块故障
                }

                if (0x00 == pTcuCtrl->chargeStartResult) // 充电机其它故障
                {
                    pTcuCtrl->chargeStartResult = 0x01;
                    pTcuCtrl->chargeStartFailReason = 0xFF;
                }
            }
        } /* 测试过程中。tcu重启，主板没重启，然后主任务处在停止完成阶段，这时接收到启动
             命令无法启动，造成宕机故加这个判断 */
        else if (CCU_WORK_STATE_FREE != workState)
        {
            pTcuCtrl->chargeStartResult = 0x01;
            pTcuCtrl->chargeStartFailReason = 0x06; // 工作中
        }
        // 充电枪过温状态下不可以启动
        else if (TRUE == Check_ErrType(eErrType_GunTempOverLimitAlarm))
        {
            if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120)) // 支持TCU协议版本[V1.14, V1.20)
            {
                pTcuCtrl->chargeStartResult = 0x01;
                pTcuCtrl->chargeStartFailReason = 0x24; // 充电机其它故障
            }
            else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120) // 支持TCU协议版本[V1.20)
            {
                pTcuCtrl->chargeStartResult = 0x01;
                pTcuCtrl->chargeStartFailReason = 0xFF; // 充电机其它故障
            }
        }
        // 匹配阶段的状态上面已经判过了，所以如果不是空闲状态那就按工作中处理
        else if (TCU_STAGE_RUN_FREE != stage)
        {
            pTcuCtrl->chargeStartResult = 0x01;
            pTcuCtrl->chargeStartFailReason = 0x06; // 工作中
        }
        else
        {
            pTcuCtrl->chargeStartResult = 0x00;
            pTcuCtrl->chargeStartFailReason = 0x00; // 启动成功

            Set_ChargeMode(CHARGE_MODE_AUTO);
            Set_ChargeActFlag(eActFlag_On);

            Init_TcuCharge();
            Set_TcuStage(TCU_STAGE_RUN_STARTING);
        }
    }
    if (Card_Start == Get_VIN_StartFlag() && Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
    {
        if (0x00 == Get_TcuSendRemainTimer(PGN_CHARGE_START_ACK))
        {
            trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---启动应答帧\n", PGN_CHARGE_START_ACK, Get_TcuStage());

            // 如果启动成功，则应答一次即可
            if (0x00 == pTcuCtrl->chargeStartResult)
            {
                Set_TcuSendRemainTimer(PGN_CHARGE_START_ACK, 250);
                Set_TcuStartTimer(PGN_CHARGE_START_ACK);
            }
            else
            {
                Set_TcuSendRemainTimer(PGN_CHARGE_START_ACK, 2000);
                Set_TcuStartTimer(PGN_CHARGE_START_ACK);
            }

            Set_TcuLastSendTimer(PGN_CHARGE_START_ACK, 0xffff);
            Set_VIN_StartFlag(VIN_NONE);
        }
    }
    else if (VIN_StartUp == Get_VIN_StartFlag() && Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
    {
        if (0x00 == Get_TcuSendRemainTimer(PGN_TCU_VIN_ACK))
        {
            trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---VIN启动应答帧\n", PGN_TCU_VIN_ACK, Get_TcuStage());

            // 如果启动成功，则应答一次即可
            if (0x00 == pTcuCtrl->chargeStartResult)
            {
                Set_TcuSendRemainTimer(PGN_TCU_VIN_ACK, 250);
                Set_TcuStartTimer(PGN_TCU_VIN_ACK);
            }
            else
            {
                Set_TcuSendRemainTimer(PGN_TCU_VIN_ACK, 2000);
                Set_TcuStartTimer(PGN_TCU_VIN_ACK);
            }
            if ((pTcuCtrl->tcuProtocolVer == TCU_PROTOCOL_VER))
            {
                pTcuCtrl->plugAndPlay = eTcuPlugAndPlay_Enable; /**<车辆即插即充使能*/
            }
            Set_TcuLastSendTimer(PGN_TCU_VIN_ACK, 0xffff);
            Set_VIN_StartFlag(VIN_NONE);
        }
    }
    if (0x00 == Get_TcuSendRemainTimer(PGN_CHARGE_START_ACK) && Get_CcuCfgParaPlatform_convert() != Platform_protocol_Hn)
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---启动应答帧\n", PGN_CHARGE_START_ACK, Get_TcuStage());

        // 如果启动成功，则应答一次即可
        if (0x00 == pTcuCtrl->chargeStartResult)
        {
            Set_TcuSendRemainTimer(PGN_CHARGE_START_ACK, 250);
            Set_TcuStartTimer(PGN_CHARGE_START_ACK);
        }
        else
        {
            Set_TcuSendRemainTimer(PGN_CHARGE_START_ACK, 2000);
            Set_TcuStartTimer(PGN_CHARGE_START_ACK);
        }

        Set_TcuLastSendTimer(PGN_CHARGE_START_ACK, 0xffff);
    }

    //    Set_TcuStage(TCU_STAGE_RUN_STARTING);/**< TODO-xg 入网测试修改,在收到启动帧后进入启动阶段*/

    //    Clr_TcuErrInfo();//TODO-开普修改

    return TRUE;
}

/**
 ******************************************************************************
 * @brief       充电停止接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.保存TCU停止原因
 *              2.打开充电停止应答帧的发送使能
 *              3.设置当前阶段为运行-停止阶段
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_ChargeStop(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    //    pTcuCtrl->chargeStopReason = pInBuf[1];
    pTcuCtrl->ChargeGun = pInBuf[0];
    pTcuCtrl->TcuChargeStopReason = pInBuf[1];

    Set_ChargeActFlag(eActFlag_Off);
    Set_StopSrc(eChargeStopFlag_Manual);

    if (0x00 == Get_TcuSendRemainTimer(PGN_CHARGE_STOP_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---充电停止答帧\n", PGN_CHARGE_STOP_ACK, Get_TcuStage());
        Set_TcuSendRemainTimer(PGN_CHARGE_STOP_ACK, 2000);
        Set_TcuStartTimer(PGN_CHARGE_STOP_ACK);
        Set_TcuLastSendTimer(PGN_CHARGE_STOP_ACK, 0xffff);
    }

    if (TCU_STAGE_RUN_STARTING == Get_TcuStage() || TCU_STAGE_RUN_CHARGING == Get_TcuStage())
    {
        Set_TcuStage(TCU_STAGE_RUN_STOPPING);
    }

    return TRUE;
}

static void HeartOvertimeDeal(void)
{
    uint8 tcuStage = Get_TcuStage();
    TCU_RECV_CTRL *pRecvCtrl = NULL;
    TCU_SEND_CTRL *pSendCtrl = NULL;
    uint8 index = 0;
    uint32 CLR_SEND_PGN[] =
        {
            PGN_CHARGE_START_ACK, PGN_CHARGE_STOP_ACK, PGN_TIME_SYN_ACK,
            PGN_VER_CHECK_ACK, PGN_SET_PARA_ACK, PGN_SERVE_CTRL_ACK,
            PGN_ELECLOCK_CTRL_ACK, PGN_POWER_CTRL_ACK, PGN_QUERY_CONFIG_ACK,
            PGN_START_FINISH, PGN_STOP_FINISH, PGN_CCU_YC,
            PGN_CCU_YX1, PGN_CCU_YX2, PGN_CCU_FAULT, PGN_PILE_STATE, /**< TODO-xg 北京入网检测,增加状态帧*/
        };
    uint32 CLR_RECV_PGN[] =
        {
            PGN_CHARGE_START, PGN_CHARGE_STOP, PGN_TIME_SYN,
            PGN_VER_CHECK, PGN_SET_PARA, PGN_SERVE_CTRL,
            PGN_ELECLOCK_CTRL, PGN_POWER_CTRL, PGN_QUERY_CONFIG,
            PGN_START_FINISH_ACK, PGN_STOP_FINISH_ACK, PGN_TCU_YC, PGN_TCU_FAULT};

    //    if (TCU_STAGE_MATCH_VER == tcuStage || TCU_STAGE_MATCH_PARA == tcuStage
    //            || TCU_STAGE_RUN_FREE == tcuStage)
    {
        for (index = 0; index < FCNT(CLR_SEND_PGN); index++)
        {
            pSendCtrl = Get_TcuSendCtrl(CLR_SEND_PGN[index]);

            if (NULL != pSendCtrl)
            {
                memset(pSendCtrl, 0x00, sizeof(TCU_SEND_CTRL));
            }
        }

        for (index = 0; index < FCNT(CLR_RECV_PGN); index++)
        {
            pRecvCtrl = Get_TcuRecvCtrl(CLR_RECV_PGN[index]);

            if (NULL != pRecvCtrl)
            {
                memset(pRecvCtrl, 0x00, sizeof(TCU_RECV_CTRL));
            }
        }

        Set_TcuStage(TCU_STAGE_MATCH_VER);
    }
}

/**
 ******************************************************************************
 * @brief       心跳帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.检测TCU心跳是否异常，则进行异常处理

 * @note        防止ccu处在匹配阶段，而tcu处在运行阶段，所以在匹配阶段置心跳异常，
 *              强制把TCU拉到匹配阶段
 ******************************************************************************
 */
static bool_e Recv_Heart(uint8 *pInBuf, uint8 len)
{
    Set_CcuHeartState(eComState_Normal);

    if (eComState_Normal != pInBuf[2])
    {
        Set_TcuHeartState(eComState_TimeOut);
        HeartOvertimeDeal();
    }
    else
    {
        Set_TcuHeartState(eComState_Normal);
    }
    Clr_ErrType(eErrType_ComErrWithTCU);
    if (TCU_STAGE_RUN_FREE == Get_TcuStage())
    {
        Clr_ErrType(eErrType_StartFinishAckTimeOutTcu);
        Clr_ErrType(eErrType_StopFinishAckTimeOutTcu);
    }
    return TRUE;
}

/**
 ******************************************************************************
 * @brief       版本校验帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.先判断版本是否正确，如果错误认为没有接收
 *              2.关于版本校验应答帧，如果TCU持续发送那么我就持续应答
 *              3.只有当ccu处在匹配-版本校验阶段，收到此帧才能将自己置为匹配-参数配置阶段
 *
 * @note
 ******************************************************************************
 */
static bool_e
Recv_VerCheck(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    MatchVer_Listen(); /**< todo-20-05-16 增加 用于入网测试*/

    pTcuCtrl->tcuProtocolVer = *(uint16 *)&pInBuf[1];

    if (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER) /**<系统只支持110及其以上版本*/
    {
        trace(TR_TCU_PROCESS, "TCU通讯版本不支持---协议版本---<%x>\n", pTcuCtrl->tcuProtocolVer);
        return FALSE;
    }

    trace(TR_TCU_PROCESS, "接受---TCU协议版本号---协议版本---<%x>\n", pTcuCtrl->tcuProtocolVer);

    // 启动充电或者充电阶段,接收版本校验帧,停机. todo-xg 20-05-18 北京入网检测
    switch (Get_TcuStage())
    {
    case TCU_STAGE_MATCH_VER:
    case TCU_STAGE_MATCH_PARA:
    {
        Set_TcuStage(TCU_STAGE_MATCH_PARA);
    }
    break;
    case TCU_STAGE_RUN_STARTING:
    case TCU_STAGE_RUN_CHARGING:
    case TCU_STAGE_RUN_STOPPING:
    {
        Set_ChargeActFlag(eActFlag_Off);
        Set_StopSrc(eChargeStopFlag_Manual); /**< 停止原因:手动停止*/

        HeartOvertimeDeal(); /**< 清除TCU各种发送应答帧*/

        Set_TcuStage(TCU_STAGE_MATCH_PARA);
    }
    break;
    case TCU_STAGE_RUN_FREE:
    {
        HeartOvertimeDeal(); /**< 清除TCU各种发送应答帧*/

        Set_TcuStage(TCU_STAGE_MATCH_PARA);
    }
    break;
    case TCU_STAGE_RUN_STOP_FINISH:
    {
        HeartOvertimeDeal(); /**< 清除TCU各种发送应答帧*/

        if (TRUE == Get_StopFinishFlag())
        {
            Set_TcuStage(TCU_STAGE_MATCH_PARA);
        }
    }
    break;
    default:
        break;
    }

    if (0x00 == Get_TcuSendRemainTimer(PGN_VER_CHECK_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---版本校验应答帧\n", PGN_VER_CHECK_ACK, Get_TcuStage());
        Set_TcuSendRemainTimer(PGN_VER_CHECK_ACK, 2000);
        Set_TcuStartTimer(PGN_VER_CHECK_ACK);
        Set_TcuLastSendTimer(PGN_VER_CHECK_ACK, 0xFFFF);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief       下发充电参数帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.接收参数并保存参数
 *              2.当接收到下发充电参数帧，在确保已经发送版本校验应答帧的情况下，停止发送版本校验应答帧
 *              3.关闭下发充电参数帧的超时计时
 *              4.打开下发充电参数应答帧的发送使能
 *              5.如果当前处在匹配-参数配置阶段，置阶段为运行-空闲阶段
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_SetPara(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    OPERATE_PARA strOperatePara;
    OPERATE_PARA strOperateParNew;
    uint8 stage = Get_TcuStage();

    if (eComState_Normal != Get_CcuHeartState() ||
        eComState_Normal != Get_TcuHeartState())
    {
        pTcuCtrl->paraSetResult = 0x01;
        pTcuCtrl->paraSetFailReason = 0x02; // 心跳超时
    }
    else if (TCU_STAGE_MATCH_VER == stage)
    {
        pTcuCtrl->paraSetResult = 0x01;
        pTcuCtrl->paraSetFailReason = 0x03; // 版本校验未完成
    }
    else
    {
        pTcuCtrl->paraSetResult = 0x00;
        pTcuCtrl->paraSetFailReason = 0x00;

        Get_PilePara((void *)&strOperatePara, eParaType_OperatePara);
        memcpy(&strOperateParNew, &strOperatePara, sizeof(OPERATE_PARA));

#if 0
        memcpy(strOperatePara.chargerNO, &pInBuf[1], 7);
        memcpy(strOperatePara.areaCode, &pInBuf[1], 3);
#else
        memcpy(strOperateParNew.chargerNO, &pInBuf[1], 4); // TODO-开普检测
        memcpy(strOperateParNew.areaCode, &pInBuf[5], 3);
#endif
        if (0 != memcmp(&strOperateParNew, &strOperatePara, sizeof(OPERATE_PARA)))
        {
            Write_PilePara((void *)&strOperateParNew, eParaType_OperatePara, 0xFF);
        }
    }

    trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---版本校验应答帧\n", PGN_VER_CHECK_ACK, Get_TcuStage());

    Set_TcuSendRemainTimer(PGN_VER_CHECK_ACK, 0x00);

    trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---下发充电参数帧\n", PGN_SET_PARA, Get_TcuStage());

    Set_TcuRecvTimerEnable(PGN_SET_PARA, eTimerEnable_Off);

    if (0x00 == Get_TcuSendRemainTimer(PGN_SET_PARA_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---下发充电参数应答帧\n", PGN_SET_PARA_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_SET_PARA_ACK, 2000);
        Set_TcuStartTimer(PGN_SET_PARA_ACK);
        Set_TcuLastSendTimer(PGN_SET_PARA_ACK, 0xFFFF);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief       对时帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.校正系统软时钟
 *              2.打开对时应答帧的发送使能
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_TimeSyn(uint8 *pInBuf, uint8 len)
{
    CP56TIME2A *pDate = NULL;
    struct tm daytime;
    struct timespec ts;
    uint8 dtbyte[7] = {0};

    pDate = (CP56TIME2A *)(pInBuf + 1);

    daytime.tm_sec = TwoUint8ToUint16(pDate->msec) / 1000;
    daytime.tm_min = pDate->min;
    daytime.tm_hour = pDate->hour;
    daytime.tm_mday = pDate->mday;
    daytime.tm_mon = pDate->month - 1;
    daytime.tm_year = pDate->year > 100 ? pDate->year : pDate->year + 100;
    daytime.tm_isdst = 0;

    dtbyte[6] = hex_to_bcd(daytime.tm_year);
    dtbyte[5] = hex_to_bcd(daytime.tm_mon + 1);
    dtbyte[4] = hex_to_bcd(daytime.tm_mday);
    dtbyte[2] = hex_to_bcd(daytime.tm_hour);
    dtbyte[1] = hex_to_bcd(daytime.tm_min);
    dtbyte[0] = hex_to_bcd(daytime.tm_sec);

    if (RTCSTA_TIME_SET_OK != rtc_set_time(dtbyte))
    {
        ts.tv_sec = mktime(&daytime);
        ts.tv_nsec = 0;
        clockSet(CLOCK_REALTIME, &ts);
    }

    if (0x00 == Get_TcuSendRemainTimer(PGN_TIME_SYN_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---对时应答帧\n",
              PGN_TIME_SYN_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_TIME_SYN_ACK, 2000);
        Set_TcuStartTimer(PGN_TIME_SYN_ACK);
        Set_TcuLastSendTimer(PGN_TIME_SYN_ACK, 0xFFFF);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief       充电服务控制帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.当前心跳正常且处在空闲状态下可设置，其它均不可设置。
 *              2.打开充电服务控制应答帧的发送使能
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_ServeCtrl(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 state = Get_WorkState();
    uint8 stage = Get_TcuStage();

    pTcuCtrl->chargeServeCtrl = pInBuf[1];
#if VEHICLE_VALIDATE_ENABLE
    if ((eTcuChargeServeCtrl_Stop != pTcuCtrl->chargeServeCtrl) &&
        (eTcuChargeServeCtrl_Enable != pTcuCtrl->chargeServeCtrl) &&
        (eTcuChargeServeCtrl_VehicleCalibrate != pTcuCtrl->chargeServeCtrl))
#else
    if ((eTcuChargeServeCtrl_Stop != pTcuCtrl->chargeServeCtrl) &&
        (eTcuChargeServeCtrl_Enable != pTcuCtrl->chargeServeCtrl))
#endif
    {
        pTcuCtrl->chargeServeCtrlResult = 0x01;
        pTcuCtrl->chargeServeCtrlFailReason = 0x01; // 合法性校验失败
    }
    else if (eComState_Normal != Get_CcuHeartState() ||
             eComState_Normal != Get_TcuHeartState())
    {
        pTcuCtrl->chargeServeCtrlResult = 0x01;
        pTcuCtrl->chargeServeCtrlFailReason = 0x02; // 心跳超时
    }
    else if (TCU_STAGE_MATCH_VER == stage)
    {
        pTcuCtrl->chargeServeCtrlResult = 0x01;
        pTcuCtrl->chargeServeCtrlFailReason = 0x03; // 版本校验未完成
    }
    else if (TCU_STAGE_MATCH_PARA == stage)
    {
        pTcuCtrl->chargeServeCtrlResult = 0x01;
        pTcuCtrl->chargeServeCtrlFailReason = 0x04; // 参数下发未完成
    }
    else if (TCU_STAGE_RUN_STARTING == stage ||
             TCU_STAGE_RUN_CHARGING == stage ||
             TCU_STAGE_RUN_STOPPING == stage)
    {
        if (CCU_WORK_STATE_CHARGE_PAUSE == state)
        {
            pTcuCtrl->chargeServeCtrlResult = 0x01;
            pTcuCtrl->chargeServeCtrlFailReason = 0x06; // 充电暂停中
        }
        else
        {
            pTcuCtrl->chargeServeCtrlResult = 0x01;
            pTcuCtrl->chargeServeCtrlFailReason = 0x05; // 工作中
        }
    }
    else
    {
        pTcuCtrl->chargeServeState = pTcuCtrl->chargeServeCtrl;
        pTcuCtrl->chargeServeCtrlResult = 0x00;
        pTcuCtrl->chargeServeCtrlFailReason = 0x00; // 设置成功
    }

    if (0x00 == Get_TcuSendRemainTimer(PGN_SERVE_CTRL_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---充电服务控制应答帧\n",
              PGN_SERVE_CTRL_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_SERVE_CTRL_ACK, 2000);
        Set_TcuStartTimer(PGN_SERVE_CTRL_ACK);
        Set_TcuLastSendTimer(PGN_SERVE_CTRL_ACK, 0xFFFF);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief       电磁锁控制帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.当前心跳正常且处在空闲状态下可设置，其它均不可设置。
 *              2.打开电磁锁控制应答帧的发送使能
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_ElecLockCtrl(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    uint8 state = Get_WorkState();
    uint8 stage = Get_TcuStage();

    pTcuCtrl->elecLockNO = pInBuf[1];
    pTcuCtrl->elecCtrl = pInBuf[2];

    if (0x01 != pTcuCtrl->elecCtrl && 0x02 != pTcuCtrl->elecCtrl)
    {
        pTcuCtrl->elecCtrlResult = 0x01;
        pTcuCtrl->elecCtrlFailReason = 0x01; // 合法性校验失败
    }
    else if (eComState_Normal != Get_CcuHeartState() ||
             eComState_Normal != Get_TcuHeartState())
    {
        pTcuCtrl->elecCtrlResult = 0x01;
        pTcuCtrl->elecCtrlFailReason = 0x02; // 心跳超时
    }
    else if (TCU_STAGE_MATCH_VER == stage)
    {
        pTcuCtrl->elecCtrlResult = 0x01;
        pTcuCtrl->elecCtrlFailReason = 0x03; // 版本校验未完成
    }
    else if (TCU_STAGE_MATCH_PARA == stage)
    {
        pTcuCtrl->elecCtrlResult = 0x01;
        pTcuCtrl->elecCtrlFailReason = 0x04; // 参数下发未完成
    }
    else if (TCU_STAGE_RUN_STARTING == stage ||
             TCU_STAGE_RUN_CHARGING == stage ||
             TCU_STAGE_RUN_STOPPING == stage)
    {
        if (CCU_WORK_STATE_CHARGE_PAUSE == state)
        {
            pTcuCtrl->elecCtrlResult = 0x01;
            pTcuCtrl->elecCtrlFailReason = 0x06; // 充电暂停中
        }
        else
        {
            pTcuCtrl->elecCtrlResult = 0x01;
            pTcuCtrl->elecCtrlFailReason = 0x05; // 工作中
        }
    }
    else if (TRUE == Check_ErrType(eErrType_ElecLockErr))
    {
        pTcuCtrl->elecCtrlResult = 0x01;
        pTcuCtrl->elecCtrlFailReason = 0x07; // 电子锁故障
    }
    else
    {
        if (0x01 == pTcuCtrl->elecCtrl)
        {
#ifdef ELECK_CLOCK_CHECK_EN
            Set_ElecClockOperate(sElecClock_Lock); // TODO -开普测试修改
#else
            ELEC_LOCK_OFF;
#endif

            pTcuCtrl->elecCtrlResult = 0x00;
            pTcuCtrl->elecCtrlFailReason = 0x00; // 设置成功
        }
        else if (0x02 == pTcuCtrl->elecCtrl)
        {
#ifdef ELECK_CLOCK_CHECK_EN
            Set_ElecClockOperate(sElecClock_Unlock);
#else
            ELEC_LOCK_ON;
#endif

            pTcuCtrl->elecCtrlResult = 0x00;
            pTcuCtrl->elecCtrlFailReason = 0x00; // 设置成功
        }
    }

    if (0x00 == Get_TcuSendRemainTimer(PGN_ELECLOCK_CTRL_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---充电服务控制应答帧\n",
              PGN_ELECLOCK_CTRL_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_ELECLOCK_CTRL_ACK, 2000);
        Set_TcuStartTimer(PGN_ELECLOCK_CTRL_ACK);
        Set_TcuLastSendTimer(PGN_ELECLOCK_CTRL_ACK, 0xFFFF);
    }

    return TRUE;
}

/**
 ******************************************************************************
 * @brief       功率调节帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.当前心跳正常且处在空闲状态下可设置，其它均不可设置。
 *              2.打开电磁锁控制应答帧的发送使能
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_PowerCtrl(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    //    int sig = 1;

    pTcuCtrl->powerCtrl = pInBuf[1];

    if (0x10 == pTcuCtrl->powerCtrl)
    {
        pTcuCtrl->powerValue_W = FourUint8ToUint32(&pInBuf[2]);
        pTcuCtrl->powerValue = TwoUint8ToUint16(&pInBuf[6]);
    }
    else
    {
        pTcuCtrl->powerValue = TwoUint8ToUint16(&pInBuf[2]);
    }
    pTcuCtrl->powerCtrlResult = 0x01;     /**< 功率调节结果 */
    pTcuCtrl->powerCtrlFailReason = 0x01; /**< 功率调节失败原因 */

    if ((pInBuf[1] == 0x01))
    {
        // 判功率值[-1000.0  ~  1000.0]
        if (pTcuCtrl->powerValue <= 20000)
        {
            pTcuCtrl->powerCtrlResult = 0x00;
            /**< 功率调节结果 */
            pTcuCtrl->powerCtrlFailReason = 0x00; /**< 功率调节失败原因 */

            pTcuCtrl->powerValue -= 10000; // 偏移量是1000.0KW
        }
        //        if ((pTcuCtrl->powerValue >= -10000) && (pTcuCtrl->powerValue <= 10000))
        //        {
        //            pTcuCtrl->powerCtrlResult = 0x00;
        //            /**< 功率调节结果 */
        //            pTcuCtrl->powerCtrlFailReason = 0x00; /**< 功率调节失败原因 */
        //        }
    }
    else if ((pInBuf[1] == 0x02))
    {
        // 判功率比[0  ~  100]
        if ((pTcuCtrl->powerValue >= 0) && (pTcuCtrl->powerValue <= 100))
        {
            pTcuCtrl->powerCtrlResult = 0x00;     /**< 功率调节结果 */
            pTcuCtrl->powerCtrlFailReason = 0x00; /**< 功率调节失败原因 */
        }
    }
    else if ((pInBuf[1] == 0x10))
    {
        // 判功率比[0  ~  100]
        if (pTcuCtrl->powerValue_W <= 20000000)
        {
            pTcuCtrl->powerCtrlResult = 0x00;
            /**< 功率调节结果 */
            pTcuCtrl->powerCtrlFailReason = 0x00; /**< 功率调节失败原因 */

            pTcuCtrl->powerValue_W -= 10000000; // 偏移量是1000000.0W
        }
        if (pTcuCtrl->powerValue <= 20000)
        {
            pTcuCtrl->powerCtrlResult = 0x00;
            /**< 功率调节结果 */
            pTcuCtrl->powerCtrlFailReason = 0x00; /**< 功率调节失败原因 */

            pTcuCtrl->powerValue -= 10000; // 偏移量是1000.0KW
        }
    }

    if (0x00 == Get_TcuSendRemainTimer(PGN_POWER_CTRL_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---功率调节应答帧\n",
              PGN_POWER_CTRL_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_POWER_CTRL_ACK, 2000);
        Set_TcuStartTimer(PGN_POWER_CTRL_ACK);
        Set_TcuLastSendTimer(PGN_POWER_CTRL_ACK, 0xFFFF);
    }

    trace(TR_TCU_PROCESS, "<tx 0F> : ");
    trace_buf(TR_TCU_PROCESS, pInBuf, len);
    return TRUE;
}

/**
 ******************************************************************************
 * @brief       充电配置信息查询处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.打开充电配置信息查询应答帧发送使能
 *
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_QueryConfig(uint8 *pInBuf, uint8 len)
{
    if (0x00 == Get_TcuSendRemainTimer(PGN_QUERY_CONFIG_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---充电配置信息查询应答帧\n",
              PGN_QUERY_CONFIG_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_QUERY_CONFIG_ACK, 2000);
        Set_TcuStartTimer(PGN_QUERY_CONFIG_ACK);
        Set_TcuLastSendTimer(PGN_QUERY_CONFIG_ACK, 0xFFFF);
    }
    return TRUE;
}

/**
 ******************************************************************************
 * @brief       充电启动完成应答帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details     1.关闭充电启动完成发送使能
 *              2.打开电磁锁控制应答帧的发送使能
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_StartFinishACK(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    pTcuCtrl->ChargeGun = pInBuf[0];
    pTcuCtrl->loadSwitch = pInBuf[1];

    if (0x00 != Get_TcuSendRemainTimer(PGN_START_FINISH))
    {
        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---充电启动完成帧\n",
              PGN_START_FINISH, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_START_FINISH, 0x00);
    }

    if (eTimerEnable_Off != Get_TcuRecvTimerEnable(PGN_START_FINISH_ACK))
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---充电启动完成应答帧\n",
              PGN_START_FINISH_ACK, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_START_FINISH_ACK, eTimerEnable_Off);
    }

    if (0x00 == pInBuf[2])
    {
        if (TCU_STAGE_RUN_STARTING == Get_TcuStage())
        {
            Set_TcuStage(TCU_STAGE_RUN_CHARGING);
        }
    }

    return TRUE;
}

static bool_e Recv_StopFinishACK(uint8 *pInBuf, uint8 len)
{
    Set_TcuSendRemainTimer(PGN_STOP_FINISH, 0x00);
    Set_TcuRecvTimerEnable(PGN_STOP_FINISH_ACK, eTimerEnable_Off);

    // 暂时不管成功还是失败
    if (TCU_STAGE_RUN_STOPPING == Get_TcuStage())
    {
        Set_TcuStage(TCU_STAGE_RUN_STOP_FINISH);
    }

    return TRUE;
}

static bool_e Recv_YC(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    pTcuCtrl->chargeEnerge = TwoUint8ToUint16(&pInBuf[3]);
    pTcuCtrl->chargeTime = TwoUint8ToUint16(&pInBuf[1]);

    return TRUE;
}
/**
 ******************************************************************************
 * @brief       错误帧接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_Fault(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    memcpy(&pTcuCtrl->TcuRcvTimeOutFlag[0], &pInBuf[1], len - 1); // 保存TCU接收超时标志

    if (0x01 == Get_BitFlag(&pInBuf[1], 1)) /**< 下发充电参数应答超时*/
    {
        trace(TR_TCU_PROCESS, "接收---下发充电参数应答帧超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 2)) /**< 充电启动命令应答超时*/
    {
        trace(TR_TCU_PROCESS, "接收---充电启动命令应答帧超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 3)) /**< 等待充电启动完成状态*/
    {
        trace(TR_TCU_PROCESS, "等待---充电启动完成状态超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 4)) /**< 充电停止命令应答超时*/
    {
        trace(TR_TCU_PROCESS, "接收---充电停止命令应答帧超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 5)) /**< 等待充电停止完成状态超时*/
    {
        trace(TR_TCU_PROCESS, "等待---充电停止完成状态超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 6)) /**< 对时操作应答超时*/
    {
        trace(TR_TCU_PROCESS, "接收---对时操作应答帧超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 7)) /**< 充电服务启停应答超时*/
    {
        trace(TR_TCU_PROCESS, "接收---充电服务启停应答帧超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 8)) /**< 电子锁控制应答超时*/
    {
        trace(TR_TCU_PROCESS, "接收---电子锁控制应答帧超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 9)) /**< 充电功率调节应答超时*/
    {
        trace(TR_TCU_PROCESS, "接收---充电功率调节应答帧超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 10)) /**< 充电桩配置信息查询应答超时*/
    {
        trace(TR_TCU_PROCESS, "接收---充电桩配置信息查询应答帧超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 11)) /**< 遥信报文接收超时*/
    {
        trace(TR_TCU_PROCESS, "接收---遥信报文超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 12)) /**< 遥测报文接收超时*/
    {
        trace(TR_TCU_PROCESS, "接收---遥测报文超时---TCU\n");
    }
    if (0x01 == Get_BitFlag(&pInBuf[1], 13)) /**< 车辆验证报文接收超时*/
    {
        trace(TR_TCU_PROCESS, "接收---车辆验证报文超时---TCU\n");
    }

    return TRUE;
}
/**
 ******************************************************************************
 * @brief       定值设置接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_TcuFixSet(uint8 *pInBuf, uint8 len)
{

    if (pInBuf[0] == Get_TcuJunctorId() && RST_CCU == pInBuf[1] && (Get_CcuCfgParaPlatform_convert() == Platform_protocol_Xj))
    {
        if (27 == TwoUint8ToUint16(&pInBuf[3]))
        {
            //            printf("=======[%s]====[%d]=====[%d]====[%d]============\n",__FUNCTION__,__LINE__,Get_K1K2Outside(),Get_K1K2OutsideVol1());
            Set_K1K2Outside(pInBuf[5] & 0x02);
            Set_ElecClock(pInBuf[5] & 0x01);
            if (Get_ElecClock())
            {
                if (Get_EnableFlag(eErrType_ElecLockErr))
                {
                    Clr_CcuEnableFlag(eErrType_ElecLockErr);
                }
            }
            else
            {
                if (!Get_EnableFlag(eErrType_ElecLockErr))
                {
                    Set_CcuEnableFlag(eErrType_ElecLockErr);
                }
            }
            if (Get_K1K2Outside())
            {
                Set_K1K2OutsideVol1(TwoUint8ToUint16(&pInBuf[6]));
                //                printf("=======[%s]====[%d]=====[%d]================\n",__FUNCTION__,__LINE__,Get_K1K2OutsideVol1());
            }
        }
    }
    else
    {

        tcu_FixPara_Set(pInBuf, len);
    }

    if (0x00 == Get_TcuSendRemainTimer(PGN_FIX_SET_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---定值设置应答帧\n",
              PGN_FIX_SET_ACK, Get_TcuStage());
        if (eFixDevtype_DcChargeCon == pInBuf[1])
        {
            Set_TcuSendRemainTimer(PGN_FIX_SET_ACK, 250);
            Set_TcuStartTimer(PGN_FIX_SET_ACK);
            Set_TcuLastSendTimer(PGN_FIX_SET_ACK, 0xFFFF); /*立即发送*/
        }
        else
        {
            Set_TcuSendRemainTimer(PGN_FIX_SET_ACK, 5000);
            Set_TcuStartTimer(PGN_FIX_SET_ACK);
        }
    }

    trace(TR_TCU_PROCESS, "<tx 80> : ");
    trace_buf(TR_TCU_PROCESS, pInBuf, len);
    return TRUE;
}

/**
 ******************************************************************************
 * @brief       多帧定值设置接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_TcuMulFixSet(uint8 *pInBuf, uint8 len)
{
    tcu_FixPara_Set(pInBuf, len);
    if (0x00 == Get_TcuSendRemainTimer(PGN_FIX_SET_MUL_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---多帧定值设置应答帧\n",
              PGN_FIX_SET_MUL_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_FIX_SET_MUL_ACK, 250);
        Set_TcuStartTimer(PGN_FIX_SET_MUL_ACK);
        Set_TcuLastSendTimer(PGN_FIX_SET_MUL_ACK, 0xFFFF); /*立即发送*/
    }

    trace(TR_TCU_PROCESS, "<tx 84> : ");
    trace_buf(TR_TCU_PROCESS, pInBuf, len);
    return TRUE;
}
/**
 ******************************************************************************
 * @brief       定值查询接收处理
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_TcuFixQuery(uint8 *pInBuf, uint8 len)
{
    tcu_FixPara_Query(pInBuf, len);
    trace(TR_TCU_PROCESS, "<tx 82> : ");
    trace_buf(TR_TCU_PROCESS, pInBuf, len);
    if (0x00 == Get_TcuSendRemainTimer(PGN_FIX_QUERY_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---定值查询应答帧\n",
              PGN_FIX_QUERY_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_FIX_QUERY_ACK, 250);
        Set_TcuStartTimer(PGN_FIX_QUERY_ACK);
        Set_TcuLastSendTimer(PGN_FIX_QUERY_ACK, 0xFFFF); /*打开 1117*/
    }
    return TRUE;
}

#if VEHICLE_VALIDATE_ENABLE
static bool_e Recv_VehicleValidateAck(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    if (Get_TcuRecvTimerEnable(PGN_CAR_CHECK_INFO_ACK) == eTimerEnable_On)
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---车辆验证确认帧\n",
              PGN_CAR_CHECK_INFO_ACK, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_CAR_CHECK_INFO_ACK, eTimerEnable_Off);
        Set_TcuRecvTimer(PGN_CAR_CHECK_INFO_ACK, 0x00);
    }
    if (0 == pInBuf[1])
    {
        if ((pInBuf[2] >= eTcuVehicleCalibrate_OK) && (pInBuf[2] <= eTcuVehicleCalibrate_BindFail))
        {
            pTcuCtrl->vehicleValidateFailReason = pInBuf[2];
        }
        else
        {
            pTcuCtrl->vehicleValidateFailReason = eTcuVehicleCalibrate_OtherFail;
        }
    }
    else
    {
        pTcuCtrl->vehicleValidateFailReason = eTcuVehicleCalibrate_AckFail;
    }

    if (pTcuCtrl->vehicleValidateFailReason != eTcuVehicleCalibrate_OK)
    {
        Set_ErrType(eErrType_PlugAndPlayAckErr);
    }
}

static bool_e
Recv_CarVinIdentifyAck(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    if (Get_TcuRecvTimerEnable(PGN_CAR_VIN_IDENTIFY_ACK) == eTimerEnable_On)
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---车辆VIN识别应答帧\n",
              PGN_CAR_VIN_IDENTIFY_ACK, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_CAR_VIN_IDENTIFY_ACK, eTimerEnable_Off);
        Set_TcuRecvTimer(PGN_CAR_VIN_IDENTIFY_ACK, 0x00);
    }

    /**
     *  接收车辆识别应答帧标志成功，打开车辆鉴权帧接收计时
     */
    if (0x00 == pInBuf[1])
    {
        if (Get_TcuRecvTimerEnable(PGN_CAR_VIN_CONFIRM) == eTimerEnable_Off)
        {
            trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---车辆鉴权帧\n",
                  PGN_CAR_VIN_CONFIRM, Get_TcuStage());
            Set_TcuRecvTimerEnable(PGN_CAR_VIN_CONFIRM, eTimerEnable_On);
            Set_TcuRecvTimer(PGN_CAR_VIN_CONFIRM, 0x00);
        }
    }
    else if ((0x01 == pInBuf[1]) && (0x01 == pInBuf[2]))
    {
        Set_ErrType(eErrType_TcuCarVinIllegalityErr);
        return false;
    }

    return true;
}
static bool_e
Recv_VinRepAck(uint8 *pInBuf, uint8 len)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    if (Get_TcuRecvTimerEnable(PGN_TCU_VIN_REP_ACK) == eTimerEnable_On)
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---VIN上报应答帧\n",
              PGN_TCU_VIN_REP_ACK, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_TCU_VIN_REP_ACK, eTimerEnable_Off);
        Set_TcuRecvTimer(PGN_TCU_VIN_REP_ACK, 0x00);
    }

    if (0x00 != Get_TcuSendRemainTimer(PGN_TCU_VIN_REP))
    {
        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---VIN码上报帧\n",
              PGN_TCU_VIN_REP, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_TCU_VIN_REP, 0x00);
    }

    /**
     *  接收车辆识别应答帧标志成功，打开车辆鉴权帧接收计时
     */
    if (0x00 == pInBuf[1])
    {
        if (Get_TcuRecvTimerEnable(PGN_TCU_VIN_CONFIRM) == eTimerEnable_Off)
        {
            trace(TR_TCU_PROCESS, "打开---接收计时---%06X---<%d>---车辆鉴权帧\n",
                  PGN_TCU_VIN_CONFIRM, Get_TcuStage());
            Set_TcuRecvTimerEnable(PGN_TCU_VIN_CONFIRM, eTimerEnable_On);
            Set_TcuRecvTimer(PGN_TCU_VIN_CONFIRM, 0x00);
        }
    }
    else if ((0x01 == pInBuf[1]))
    {
        Set_ErrType(eErrType_TcuCarVinIllegalityErr);
        return false;
    }

    return true;
}
/**
 *  TCU发送鉴权帧
 */
#pragma pack(1)
typedef struct _CAR_VIN_COMFIRM_STR
{
    uint8 port;
    uint8 vin[17];
    uint8 resl;
    uint8 errcode;
} CAR_VIN_COMFIRM_STR;
#pragma pack()

static bool_e
Recv_CarVinConfirm(uint8 *pInBuf, uint8 len)
{
    int i = 0;
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    BRM_DATA strBrm;
    CAR_VIN_COMFIRM_STR *pDat = (CAR_VIN_COMFIRM_STR *)pInBuf;

    if (Get_TcuRecvTimerEnable(PGN_CAR_VIN_IDENTIFY_ACK) == eTimerEnable_On)
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---车辆识别应答帧2\n",
              PGN_CAR_VIN_IDENTIFY_ACK, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_CAR_VIN_IDENTIFY_ACK, eTimerEnable_Off);
        Set_TcuRecvTimer(PGN_CAR_VIN_IDENTIFY_ACK, 0x00);
    }

    if (Get_TcuRecvTimerEnable(PGN_CAR_VIN_CONFIRM) == eTimerEnable_On)
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---车辆VIN鉴权帧\n",
              PGN_CAR_VIN_CONFIRM, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_CAR_VIN_CONFIRM, eTimerEnable_Off);
        Set_TcuRecvTimer(PGN_CAR_VIN_CONFIRM, 0x00);
    }

    // 鉴权结果处理
    if (0x01 == pDat->resl)
    {
        if (0x01 == pDat->errcode) // 非法VIN处理
        {
            trace(TR_TCU_PROCESS, "鉴权结果---%06X---<%d>---非法VIN\n",
                  PGN_CAR_VIN_CONFIRM_ACK, Get_TcuStage());

            Set_ErrType(eErrType_TcuCarVinIllegalityErr);
        }
        else if (0x02 == pDat->errcode) // 平台判定鉴权失败
        {
            trace(TR_TCU_PROCESS, "鉴权结果---%06X---<%d>---平台判定鉴权失败\n",
                  PGN_CAR_VIN_CONFIRM_ACK, Get_TcuStage());
            Set_ErrType(eErrType_TcuCarVinConfirmFailErr);
        }
        else if (0x03 == pDat->errcode) // 平台判定鉴权超时
        {
            trace(TR_TCU_PROCESS, "鉴权结果---%06X---<%d>---平台判定鉴权超时\n",
                  PGN_CAR_VIN_CONFIRM_ACK, Get_TcuStage());
            Set_ErrType(eErrType_TcuCarVinConfirmTimeoutErr);
        }
        else // 其他原因
        {
            trace(TR_TCU_PROCESS, "鉴权结果---%06X---<%d>---其他原因\n",
                  PGN_CAR_VIN_CONFIRM_ACK, Get_TcuStage());
            Set_ErrType(eErrType_TcuCarVinOtherErr);
        }
    }
    else
    {
        Get_BMS_Data(BMS_PGN_BRM, (void *)&strBrm);
        if (Get_CcuCfgParaEuropeEnable())
        {
            uint32_t size = 6;
            uint8 VIN[17] = {0}; // 初始化 VIN 数组为全0
            if (hex2ascii(&strBrm.ec.evccId[1], VIN, size))
            {
                if (0 == memcmp(pDat->vin, VIN, 17))
                {
                    trace(TR_TCU_PROCESS, "鉴权结果---%06X---<%d>---鉴权成功!\n",
                          PGN_CAR_VIN_CONFIRM_ACK, Get_TcuStage());
                    pTcuCtrl->vehicleValidateFailReason = eTcuVehicleCalibrate_OK; // 即插即充检测结果:成功!!!
                }
                else
                {
                    trace(TR_TCU_PROCESS, "鉴权结果---%06X---<%d>---VIN码不匹配\n",
                          PGN_CAR_VIN_CONFIRM_ACK, Get_TcuStage());
                    trace(TR_TCU_PROCESS, "BRM--VIN \n");
                    trace_buf(TR_TCU_PROCESS, strBrm.gbt.VIN, sizeof(strBrm.gbt.VIN));
                    trace(TR_TCU_PROCESS, "TCU--VIN \n");
                    trace_buf(TR_TCU_PROCESS, pDat->vin, sizeof(pDat->vin));
                    Set_ErrType(eErrType_VinInconformityErr);
                }
            }
        }
        else
        {
            for (i = 0; i < sizeof(pDat->vin); i++)
            {
                if (strBrm.gbt.VIN[i] != pDat->vin[i])
                {
                    trace(TR_TCU_PROCESS, "鉴权结果---%06X---<%d>---VIN码不匹配\n",
                          PGN_CAR_VIN_CONFIRM_ACK, Get_TcuStage());
                    trace(TR_TCU_PROCESS, "BRM--VIN \n");
                    trace_buf(TR_TCU_PROCESS, strBrm.gbt.VIN, sizeof(strBrm.gbt.VIN));
                    trace(TR_TCU_PROCESS, "TCU--VIN \n");
                    trace_buf(TR_TCU_PROCESS, pDat->vin, sizeof(pDat->vin));
                    Set_ErrType(eErrType_VinInconformityErr);
                    break;
                }
            }

            if (i == sizeof(pDat->vin))
            {
                trace(TR_TCU_PROCESS, "鉴权结果---%06X---<%d>---鉴权成功!\n",
                      PGN_CAR_VIN_CONFIRM_ACK, Get_TcuStage());
                pTcuCtrl->vehicleValidateFailReason = eTcuVehicleCalibrate_OK; // 即插即充检测结果:成功!!!
            }
        }
    }

    if (0x00 == Get_TcuSendRemainTimer(PGN_CAR_VIN_CONFIRM_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---鉴权应答帧\n",
              PGN_CAR_VIN_CONFIRM_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_CAR_VIN_CONFIRM_ACK, 5000);
        Set_TcuStartTimer(PGN_CAR_VIN_CONFIRM_ACK);

        Set_TcuLastSendTimer(PGN_CAR_VIN_CONFIRM_ACK, 0xffff);
    }

    return FALSE;
}
#pragma pack(1)
typedef struct _CAR_VIN_COMFIRM
{
    uint8 port;
    uint8 resl;
} CAR_VIN_COMFIRM;
#pragma pack()
static bool_e
Recv_VinConfirm(uint8 *pInBuf, uint8 len)
{
    int i = 0;
    TCU_CTRL *pTcuCtrl = &tcuCtrl;
    BRM_DATA strBrm;
    CAR_VIN_COMFIRM *pDat = (CAR_VIN_COMFIRM *)pInBuf;

    if (Get_TcuRecvTimerEnable(PGN_TCU_VIN_REP_ACK) == eTimerEnable_On)
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---VIN上报应答帧\n",
              PGN_TCU_VIN_REP_ACK, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_TCU_VIN_REP_ACK, eTimerEnable_Off);
        Set_TcuRecvTimer(PGN_TCU_VIN_REP_ACK, 0x00);
    }

    if (Get_TcuRecvTimerEnable(PGN_TCU_VIN_CONFIRM) == eTimerEnable_On)
    {
        trace(TR_TCU_PROCESS, "关闭---接收计时---%06X---<%d>---VIN鉴权结果帧\n",
              PGN_TCU_VIN_CONFIRM, Get_TcuStage());

        Set_TcuRecvTimerEnable(PGN_TCU_VIN_CONFIRM, eTimerEnable_Off);
        Set_TcuRecvTimer(PGN_TCU_VIN_CONFIRM, 0x00);
    }

    // 鉴权结果处理
    if (0x01 == pDat->resl)
    {
        pTcuCtrl->vehicleValidateFailReason = eTcuVehicleCalibrate_Fail; // 即插即充检测结果:成功!!!
        Set_ErrType(eErrType_TcuCarVinIllegalityErr);
    }
    else
    {
        trace(TR_TCU_PROCESS, "鉴权结果---%06X---<%d>---鉴权成功!\n",
              PGN_CAR_VIN_CONFIRM_ACK, Get_TcuStage());
        pTcuCtrl->vehicleValidateFailReason = eTcuVehicleCalibrate_OK; // 即插即充检测结果:成功!!!
    }

    if (0x00 != Get_TcuSendRemainTimer(PGN_TCU_VIN_REP))
    {
        trace(TR_TCU_PROCESS, "关闭---发送使能---%06X---<%d>---VIN码上报帧\n",
              PGN_TCU_VIN_REP, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_TCU_VIN_REP, 0x00);
    }

    if (0x00 == Get_TcuSendRemainTimer(PGN_TCU_VIN_CONFIRM_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---鉴权应答帧\n",
              PGN_TCU_VIN_CONFIRM_ACK, Get_TcuStage());

        Set_TcuSendRemainTimer(PGN_TCU_VIN_CONFIRM_ACK, 2000);
        Set_TcuStartTimer(PGN_TCU_VIN_CONFIRM_ACK);

        Set_TcuLastSendTimer(PGN_TCU_VIN_CONFIRM_ACK, 0xffff);
    }

    return FALSE;
}
#endif

/**
 *  自动校准命令字.
 */
typedef enum
{
    CMD_ADJUST_PWRON,       /**<  开机*/
    CMD_ADJUST_PWROFF,      /**<  关机*/
    CMD_ADJUST_SETOUT,      /**<  设置输出*/
    CMD_ADJUST_OUTSIDE_VOL, /**<  校准外侧电压*/
    CMD_ADJUST_INSIDE_VOL,  /**<  校准内侧电压*/
    CMD_ADJUST_CUR,         /**<  校准电流*/
    CMD_LOOKUP_OUTSIDE_VOL, /**<  获取外侧电压*/
    CMD_LOOKUP_INSIDE_VOL,  /**<  获取内侧电压*/
    CMD_LOOKUP_CUR,         /**<  获取电流*/
} CMD_ADJUST_TYPE;

/**
 ******************************************************************************
 * @brief      Deal_DebugAdjust.
 * @param[in]  None
 * @param[out] None
 * @retval     处理自动校准帧.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static void
Deal_DebugAdjust(uint16 id, uint8 *pDat, uint16 Vol, uint16 Cur)
{
    uint8 index = 0;
    uint32 TmpVol = 0, TmpCur = 0;

    switch (id)
    {
    case CMD_ADJUST_PWRON:
    {
        if (eActFlag_On == Get_ChargeActFlag())
        {
            return;
        }

        printf("[debug]......接收到了手动开机命令,电压=%dV,电流=%dA\n", Vol / 10, Cur / 100);

        Set_ManualVol(Vol);      /**< 开机电压*/
        Set_ManualCur(Cur * 10); /**< 开机电流*/

        Set_ChargeMode(CHARGE_MODE_MANUAL);

        if (CCU_WORK_STATE_FREE == Get_WorkState())
        {
            if (DEVICE_STATE_NORMAL == Get_DeviceState())
            {
                Set_ChargeActFlag(eActFlag_On);
            }
            else if (DEVICE_STATE_ALARM == Get_DeviceState())
            {
                if (TRUE != Check_ErrType(eErrType_GunTempOverLimitAlarm))
                {
                    Set_ChargeActFlag(eActFlag_On);
                }
            }
        }

        pDat[index++] = BREAK_UINT16(Vol, 0);
        pDat[index++] = BREAK_UINT16(Vol, 1);
        pDat[index++] = BREAK_UINT16(Cur, 0);
        pDat[index++] = BREAK_UINT16(Cur, 1);
    }
    break;
    case CMD_ADJUST_PWROFF:
    {
        printf("[debug]......接收到了手动关机命令\n");
        Set_StopSrc(eChargeStopFlag_Manual);
        Set_ChargeActFlag(eActFlag_Off);

        pDat[index++] = BREAK_UINT16(Vol, 0);
        pDat[index++] = BREAK_UINT16(Vol, 1);
        pDat[index++] = BREAK_UINT16(Cur, 0);
        pDat[index++] = BREAK_UINT16(Cur, 1);
    }
    break;
    case CMD_ADJUST_SETOUT:
    {
        printf("[debug]......接收到了输出调整命令,电压=%dV,电流=%dA\n", Vol / 10, Cur / 100);

        Set_ManualVol(Vol);      /**< 输出电压*/
        Set_ManualCur(Cur * 10); /**< 输出电流*/

        pDat[index++] = BREAK_UINT16(Vol, 0);
        pDat[index++] = BREAK_UINT16(Vol, 1);
        pDat[index++] = BREAK_UINT16(Cur, 0);
        pDat[index++] = BREAK_UINT16(Cur, 1);
    }
    break;
    case CMD_ADJUST_OUTSIDE_VOL:
    {
        printf("[debug]......接收到了外侧电压校准命令,电压=%dV\n", Vol / 10);
        Adjust_RN8209(e8209_Voltage, eType_8209_Out, (float)Get_SigleSampleVal(e8209_Voltage, eType_8209_Out), Vol);

        pDat[index++] = BREAK_UINT16(Vol, 0);
        pDat[index++] = BREAK_UINT16(Vol, 1);
        pDat[index++] = BREAK_UINT16(Cur, 0);
        pDat[index++] = BREAK_UINT16(Cur, 1);
    }
    break;
    case CMD_ADJUST_INSIDE_VOL:
    {
        printf("[debug]......接收到了內侧电压校准命令,电压=%dV\n", Vol / 10);
        Adjust_RN8209(e8209_Voltage, eType_8209_In, (float)Get_SigleSampleVal(e8209_Voltage, eType_8209_In), Vol);

        pDat[index++] = BREAK_UINT16(Vol, 0);
        pDat[index++] = BREAK_UINT16(Vol, 1);
        pDat[index++] = BREAK_UINT16(Cur, 0);
        pDat[index++] = BREAK_UINT16(Cur, 1);
    }
    break;
    case CMD_ADJUST_CUR:
    {
        printf("[debug]......接收到了电流校准命令,电流=%dA\n", Cur / 100);
        Adjust_RN8209(e8209_Current_A, eType_8209_In, (float)Get_SigleSampleVal(e8209_Current_A, eType_8209_In), Cur * 10.0);

        pDat[index++] = BREAK_UINT16(Vol, 0);
        pDat[index++] = BREAK_UINT16(Vol, 1);
        pDat[index++] = BREAK_UINT16(Cur, 0);
        pDat[index++] = BREAK_UINT16(Cur, 1);
    }
    break;
    case CMD_LOOKUP_OUTSIDE_VOL:
    {
        TmpVol = Get_K1K2OutsideVol();

        printf("lookup outside vol= %d\n", TmpVol);

        pDat[index++] = BREAK_UINT16(TmpVol, 0);
        pDat[index++] = BREAK_UINT16(TmpVol, 1);
        pDat[index++] = 0;
        pDat[index++] = 0;
    }
    break;
    case CMD_LOOKUP_INSIDE_VOL:
    {
        TmpVol = Get_K1K2InsideVol(); /**< 单位:0.1V/bit*/

        printf("lookup inside vol= %d\n", TmpVol);

        pDat[index++] = BREAK_UINT16(TmpVol, 0);
        pDat[index++] = BREAK_UINT16(TmpVol, 1);
        pDat[index++] = 0;
        pDat[index++] = 0;
    }
    break;
    case CMD_LOOKUP_CUR:
    {
        TmpCur = Get_K1K2Current() / 10; /**< 单位:mA/bit*/

        printf("lookup  current= %d\n", TmpVol);

        pDat[index++] = 0;
        pDat[index++] = 0;
        pDat[index++] = BREAK_UINT16(TmpCur, 0);
        pDat[index++] = BREAK_UINT16(TmpCur, 1);
    }
    break;
    default:
        break;
    }
}

/**
 ******************************************************************************
 * @brief       TCU定值参数读
 * @param[in]   NONE
 * @param[out]  NONE
 * @retval      NONE
 * @details
 *
 * @note
 *
 ******************************************************************************
 */
bool_e
tcu_Debug_Recv(uint8 *buf, uint8 len)
{
    uint16 SetCur = 0, SetVol = 0;
    TCU_DEBUG *pDebug = &tcuDebug;
    pDebug->port = buf[0];
    pDebug->devType = buf[1];
    pDebug->comAdr = buf[2];
    pDebug->cmd = buf[3];
    pDebug->index = *(uint16 *)&buf[4];

    if (pDebug->port != Get_TcuJunctorId())
    {
        trace(TR_TCU_PROCESS, "调试命令---端口错误---要求端口<%d>---端口<%d>\n", Get_TcuJunctorId(), pDebug->port);
        return FALSE;
    }
    if (pDebug->devType != eFixDevtype_DcChargeCon)
    {
        trace(TR_TCU_PROCESS, "调试命令---设备类型错误---要求设备类型<%d>---实际设备类型<%d>\n", eFixDevtype_DcChargeCon, pDebug->devType);
        return FALSE;
    }
    if (pDebug->comAdr != Get_CcuToTcuAddr())
    {
        trace(TR_TCU_PROCESS, "调试命令---通讯地址错误---要求地址<%d>---实际地址<%d>\n", Get_CcuToTcuAddr(), pDebug->comAdr);
        return FALSE;
    }

    /**
     *  命令字处理.
     */
    switch (pDebug->cmd)
    {
    case 0: /**< 读参数命令*/
    {
        if (len == 6)
        {
            pDebug->len = Get_Para(pDebug->index, pDebug->buf) + 6;
            pDebug->cmd = 0x80;
            pDebug->sendEn = 1;
            return TRUE;
        }
        else
        {
            trace(TR_TCU_PROCESS, "调试命令---数据长度错误---要求长度大于等于6---实际长度<%d>\n", len);
            return FALSE;
        }
    }
    break;
    case 1: /**< 写参数命令*/
    {
        if (((pDebug->index > eparaFmt_SamplePara) && (pDebug->index < eparaFmt_SampleInsulatePara)) || ((pDebug->index > eparaFmt_CfgPara) && (pDebug->index < eParaFmt_CfgCTSSendEnable)))
        {
            trace(TR_TCU_PROCESS, "调试命令---该序号不支持写---序号<%d>\n", pDebug->index);
            return FALSE;
        }
        trace(TR_TCU_PROCESS, "调试命令---设参数---序号<%d>---长度<%d>\n", pDebug->index, len);
        //            trace_buf(TR_TCU_PROCESS, &buf[6],len);
        trace_buf(TR_TCU_PROCESS, &buf[0], len);
        Set_ParaOprateSrc(OporateType_AutoSet);
        Set_Para(pDebug->index, &buf[6]);
        pDebug->len = 6;
        pDebug->cmd = 0x81;
        pDebug->sendEn = 1;
        return TRUE;
    }
    break;
    case 2:
    {
        pDebug->index = buf[4];

        SetVol = BUILD_UINT16(buf[5], buf[6]); /**< 单位:0.1V*/
        SetCur = BUILD_UINT16(buf[7], buf[8]); /**< 单位:0.01A*/

        Deal_DebugAdjust(pDebug->index, pDebug->buf, SetVol, SetCur);

        pDebug->len = 9;
        pDebug->cmd = 0x82;
        pDebug->sendEn = 1;
        return TRUE;
    }
    break;
    default:
    {
        trace(TR_DEBUG, "调试命令---命令码错误---要求命令码0,1--实际命令码<%d>\n", pDebug->cmd);
        return FALSE;
    }
    break;
    }

    return FALSE;
}
/**
 ******************************************************************************
 * @brief       调试命令帧
 * @param[in]   uint8 *pInBuf          输入的数据
 * @param[in]   uint32 len             输入的数据长度
 * @param[out]  NONE
 * @retval      TRUE-成功接收                   置已接收标记，并清空超时计时
 *              FALSE-接收失败                  NOP
 *
 * @details
 *
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_TcuDebugCmd(uint8 *pInBuf, uint8 len)
{
    if (tcu_Debug_Recv(pInBuf, len))
    {
        if (0x00 == Get_TcuSendRemainTimer(PGN_DEBUG_CMD_ACK))
        {
            trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---TCU调试命令应答帧\n",
                  PGN_DEBUG_CMD_ACK, Get_TcuStage());
            Set_TcuSendFlg(PGN_DEBUG_CMD_ACK, eSendFlag_No);
            Set_TcuSendRemainTimer(PGN_DEBUG_CMD_ACK, 250);
            Set_TcuStartTimer(PGN_DEBUG_CMD_ACK);
        }
    }
    return TRUE;
}

/**
 ******************************************************************************
 * @brief      .
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
static bool_e Recv_TcuRstCmd(uint8 *pInBuf, uint8 len)
{
    if (0x00 == Get_TcuSendRemainTimer(PGN_RST_CMD_ACK))
    {
        trace(TR_TCU_PROCESS, "打开---发送使能---%06X---<%d>---远程复位应答帧\n",
              PGN_RST_CMD_ACK, Get_TcuStage());
        Set_TcuSendRemainTimer(PGN_RST_CMD_ACK, 2000);
        Set_TcuStartTimer(PGN_RST_CMD_ACK);
        Set_TcuLastSendTimer(PGN_RST_CMD_ACK, 0xFFFF);
    }
    Set_RstCmd(Rst_Normal);
    //    if ((CCU_WORK_STATE_FREE == Get_WorkState())
    //            || (CCU_WORK_STATE_STOP_FINISH == Get_WorkState()))
    //    if (0x00 == Get_TcuSendRemainTimer(PGN_RST_CMD_ACK))
    if (Get_CcuCfgParaPlatform_convert() == Platform_protocol_Xj)
    {
        if (pInBuf[0] == Get_TcuJunctorId() && 0x01 == pInBuf[1])
        {
            Set_RstCmd(Rst_Start);
            printf(" REST START!!!\n");
            //            DELAY_RST_ON;
        }
    }
    trace(TR_TCU_PROCESS, "<tx 90> : ");
    trace_buf(TR_TCU_PROCESS, pInBuf, len);
    return TRUE;
}

/**
******************************************************************************
* @brief       Set_TcuRecvFlag
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]   RECV_FLAG    RecvFlg    接收标记
* @param[out]
* @retval
*
* @details     0x55-已接收、0x00-未接收、 0xFF-无效
*
* @note        设置已接收标记
******************************************************************************
*/
void Set_TcuRecvFlag(uint32 pgn, RECV_FLAG RecvFlg)
{
    TCU_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_TcuRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return;
    }

    pRecvCtrl->recvFlag = RecvFlg;

    return;
}

/**
******************************************************************************
* @brief      Get_TcuRecvFlag
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]
* @param[out]
* @retval      RECV_FLAG    RecvFlg    接收标记
*
* @details     0x55-已接收、0x00-未接收、 0xFF-无效
*
* @note        获取已接收标记
******************************************************************************
*/
RECV_FLAG Get_TcuRecvFlag(uint32 pgn)
{
    TCU_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_TcuRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return eRecvFlag_No;
    }

    return pRecvCtrl->recvFlag;
}

/**
 ******************************************************************************
 * @brief      Set_TcuRecvOverTimerCheckEnable.
 * @param[in]  None
 * @param[out] None
 * @retval     设置Tcu帧接收超时检测是使能标志.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void Set_TcuRecvOverTimerCheckEnable(uint32 pgn, TIMER_ENABLE enableFlg)
{
    TCU_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_TcuRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return;
    }

    pRecvCtrl->recvCheckEnableFlg = enableFlg;

    return;
}

/**
 ******************************************************************************
 * @brief      Get_TcuRecvOverTimerCheckEnable.
 * @param[in]  None
 * @param[out] None
 * @retval     查询Tcu帧接收超时检测使能标志.
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
TIMER_ENABLE
Get_TcuRecvOverTimerCheckEnable(uint32 pgn, TIMER_ENABLE enableFlg)
{
    TCU_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_TcuRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return eTimerEnable_Null;
    }

    return pRecvCtrl->recvCheckEnableFlg;
}

/**
******************************************************************************
* @brief       Set_TcuRecvTimerEnable
* @param[in]   uint32 Pgn                     参数组编号
* @param[in]   TIMER_ENABLE enableFlg    接收计时使能标记
* @param[out]
* @retval
*
* @details     0x55-计时、0x00-不计时、 0xFF-无效
*
* @note        设置接收计时器使能
******************************************************************************
*/
void Set_TcuRecvTimerEnable(uint32 pgn, TIMER_ENABLE enableFlg)
{
    TCU_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_TcuRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return;
    }

    pRecvCtrl->recvTimerEnableFlg = enableFlg;

    return;
}

/**
******************************************************************************
* @brief       Get_TcuRecvTimerEnable
* @param[in]   uint32 Pgn                  参数组编号
* @param[in]
* @param[out]
* @retval      TIMER_ENABLE           接收计时器使能标记
*
* @details     0x55-计时、0x00-未接收、 0xFF-无效
*
* @note        获取接收计时器使能标记
******************************************************************************
*/
TIMER_ENABLE Get_TcuRecvTimerEnable(uint32 pgn)
{
    TCU_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_TcuRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return eTimerEnable_Null;
    }

    return pRecvCtrl->recvTimerEnableFlg;
}

/**
******************************************************************************
* @brief       Set_TcuRecvTimer
* @param[in]   uint32 Pgn                     参数组编号
* @param[in]   int32  countValue              计时器赋值
* @param[out]
* @retval
*
* @details
*
* @note        设置接收计时器
******************************************************************************
*/
void Set_TcuRecvTimer(uint32 pgn, int32 countValue)
{
    TCU_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_TcuRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return;
    }

    pRecvCtrl->recvTimer = tickGet() - countValue;

    return;
}

/**
******************************************************************************
* @brief       Get_TcuRecvTimer
* @param[in]   uint32 Pgn                     参数组编号
* @param[in]
* @param[out]
* @retval
*
* @details
*
* @note        获取接收计时器值
******************************************************************************
*/
int32 Get_TcuRecvTimer(uint32 pgn)
{
    TCU_RECV_CTRL *pRecvCtrl = NULL;

    pRecvCtrl = Get_TcuRecvCtrl(pgn);

    if (NULL == pRecvCtrl)
    {
        return 0;
    }

    return pRecvCtrl->recvTimer;
}
/**
******************************************************************************
* @brief       tcu多帧数据处理
* @param[in]   CAN_DATA *pCanData
* @param[in]   NONE
* @param[out]  NONE
* @retval      NONE
* @details
* @note
******************************************************************************
*/
static uint8 tcuMulFrame_Decode(CAN_DATA *pCanData, TCU_CAN_MULFRAME_RECV *pMulFrame)
{
    if (1 == pCanData->dataBuf[0])
    {
        trace_buf(TR_TCU_PROCESS, pCanData, sizeof(CAN_DATA));
        pMulFrame->canId = pCanData->canId;
        pMulFrame->frameCnt = pCanData->dataBuf[1];
        pMulFrame->dataLen = *(uint16 *)&pCanData->dataBuf[2];
        if (pMulFrame->dataLen <= 2) /**有效数据长度小于等于2时在一帧内完成*/
        {
            memcpy(pMulFrame->dataBuf, &pCanData->dataBuf[4], pMulFrame->dataLen);
            pMulFrame->sumCheck = *(uint16 *)&pCanData->dataBuf[4 + pMulFrame->dataLen];
            pMulFrame->dataCnt = pMulFrame->dataLen;
            pMulFrame->sumRecFlag = 2;
            trace(TR_TCU_PROCESS, "TCU分帧接受完成1 CAN_ID = %X\n", pMulFrame->canId);
        }
        else if (3 == pMulFrame->dataLen)
        {
            pMulFrame->dataCnt = pMulFrame->dataLen;
            memcpy(pMulFrame->dataBuf, &pCanData->dataBuf[4], 3);
            pMulFrame->sumCheck = pCanData->dataBuf[7];
            pMulFrame->sumRecFlag = 1;
            return eTcuMulFrame_NotFinish;
        }
        else
        {
            pMulFrame->dataCnt = 4;
            memcpy(pMulFrame->dataBuf, &pCanData->dataBuf[4], 4);
            pMulFrame->sumRecFlag = 0;
            return eTcuMulFrame_NotFinish;
        }
    }
    else
    {
        trace_buf(TR_TCU_PROCESS, pCanData, sizeof(CAN_DATA));
        if (pMulFrame->dataLen >= pMulFrame->dataCnt + 7)
        {
            memcpy(&pMulFrame->dataBuf[pMulFrame->dataCnt], &pCanData->dataBuf[1], 7);
            pMulFrame->dataCnt += 7;
            pMulFrame->sumRecFlag = 0;
            return eTcuMulFrame_NotFinish;
        }
        else if (pMulFrame->dataLen == pMulFrame->dataCnt + 6)
        {
            memcpy(&pMulFrame->dataBuf[pMulFrame->dataCnt], &pCanData->dataBuf[1], 6);
            pMulFrame->dataCnt += 6;
            pMulFrame->sumCheck = pCanData->dataBuf[7];
            pMulFrame->sumRecFlag = 1;
            trace(TR_TCU_PROCESS, "TCU分帧数据接受完成 校验码接受一半<%X>---接受长度<%d> \n", pMulFrame->sumCheck, pMulFrame->dataLen);
            return eTcuMulFrame_NotFinish;
        }
        else // if(pMulFrame->dataLen != pMulFrame->dataCnt)
        {
            if (pMulFrame->sumRecFlag == 0)
            {
                memcpy(&pMulFrame->dataBuf[pMulFrame->dataCnt], &pCanData->dataBuf[1], pMulFrame->dataLen - pMulFrame->dataCnt);
                pMulFrame->sumCheck = *(uint16 *)&pCanData->dataBuf[1 + pMulFrame->dataLen - pMulFrame->dataCnt];
                pMulFrame->dataCnt = pMulFrame->dataLen;
                pMulFrame->sumRecFlag = 2;
                trace(TR_TCU_PROCESS, "TCU分帧接受完成2 CAN_ID = %X,长度 = %d.\n", pMulFrame->canId, pMulFrame->dataLen);
            }
        }
        if (0 == pMulFrame->sumRecFlag)
        {
            pMulFrame->sumCheck = *(uint16 *)&pCanData->dataBuf[1];
            pMulFrame->sumRecFlag = 2;
            trace(TR_TCU_PROCESS, "TCU分帧接受完成3 CAN_ID = %X\n", pMulFrame->canId);
        }
        else if (1 == pMulFrame->sumRecFlag)
        {
            pMulFrame->sumCheck |= pCanData->dataBuf[1] << 8;
            pMulFrame->sumRecFlag = 2;
            trace(TR_TCU_PROCESS, "TCU分帧接受完成4 CAN_ID = %X\n", pMulFrame->canId);
        }
    }
    if ((pMulFrame->sumRecFlag == 2) && (pMulFrame->dataCnt == pMulFrame->dataLen))
    {
        if (pMulFrame->sumCheck == Get_Cs16(&pMulFrame->frameCnt, pMulFrame->dataLen + 3))
        {
            trace(TR_TCU_PROCESS, "TCU分帧校验合格 CAN_ID = %X\n", pMulFrame->canId);
            return eTcuMulFrame_Check_OK;
        }
        else
        {
            trace(TR_TCU_PROCESS, "TCU分帧校验错误--需要校验码<%X>--实际校验码<%X>",
                  pMulFrame->sumCheck,
                  Get_Cs16(&pMulFrame->frameCnt, pMulFrame->dataLen + 3));
            trace_buf(TR_TCU_PROCESS, pMulFrame, sizeof(TCU_CAN_MULFRAME_RECV));
            memset(pMulFrame, 0, sizeof(TCU_CAN_MULFRAME_RECV));
            return eTcuMulFrame_Check_ERR;
        }
    }
}

static void TCU_Decode(CAN_DATA *pCanData)
{
    const TCU_RECV_DEAL *pRecvDeal = NULL;
    const Multiframe_Adapt *pRecvMul = NULL;
    uint8 index = 0, tcuMulFrameRecState = eTcuMulFrame_NotFinish;
    static TCU_CAN_MULFRAME_RECV tcuMulFrame = {0};

    CAN_ID *pId = (CAN_ID *)&pCanData->canId;
    for (index = 0; index < FCNT(TCU_RECV_DEAL_TABLE); index++)
    {
        pRecvDeal = &TCU_RECV_DEAL_TABLE[index];

        if (TCU_ADDR != pId->sa || Get_CcuToTcuAddr() != pId->ps)
        {
            return;
        }

        if (pId->pf == (PGN_FIX_SET >> 8) || (PGN_RST_CMD >> 8) == pId->pf)
        {

            for (uint8 i = 0; i < FCNT(TCU_RECV_MUL_TABLE); i++)
            {
                pRecvMul = &TCU_RECV_MUL_TABLE[i];
                if (pId->pf == (pRecvMul->Tcu_Frame.pgn >> 8))
                {
                    if (pRecvMul->platform == (Get_CcuCfgParaPlatform_convert() == Platform_protocol_Xj))
                    {
                        pRecvDeal = &pRecvMul->Tcu_Frame;
                    }
                }
            }
        }

        if ((uint8)(pRecvDeal->pgn >> 8) != pId->pf)
        {
            continue;
        }

        if (pRecvDeal->prio != pId->prio)
        {
            return;
        }

        if (pRecvDeal->pgn == PGN_CHARGE_STOP)
        {
            trace(TR_TCU_PROCESS, ("[%06X]Recv tcu stop0:"), PGN_CHARGE_STOP);
            trace_buf(TR_TCU_PROCESS, pCanData->dataBuf, pCanData->dataLen);
        }

        if (NULL != pRecvDeal->pDecodeFunc)
        {
            if (pRecvDeal->mulFrameFlag)
            {
                tcuMulFrameRecState = tcuMulFrame_Decode(pCanData, &tcuMulFrame);
                if (tcuMulFrameRecState != eTcuMulFrame_NotFinish)
                {
                    if ((eTcuMulFrame_Check_OK == tcuMulFrameRecState) && (TRUE == pRecvDeal->pDecodeFunc(
                                                                                       tcuMulFrame.dataBuf,
                                                                                       tcuMulFrame.dataLen)))
                    {
                        Deal_TcuLogPackage(pRecvDeal->pgn, tcuMulFrame.dataBuf, tcuMulFrame.dataLen); // 保存接收帧记录log
                        Set_TcuRecvTimer(pRecvDeal->pgn, 0x00);
                        Set_TcuRecvFlag(pRecvDeal->pgn, eRecvFlag_Yes);
                    }
                    memset(&tcuMulFrame, 0, sizeof(tcuMulFrame));
                }
            }
            else
            {
                if (Check_TcuGun(pCanData))
                {
                    if (PGN_CHARGE_START == pRecvDeal->pgn && Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
                    {
                        Set_VIN_StartFlag(Card_Start);
                    }
                    else if (PGN_TCU_VIN == pRecvDeal->pgn && Get_CcuCfgParaPlatform_convert() == Platform_protocol_Hn)
                    {
                        Set_VIN_StartFlag(VIN_StartUp);
                    }
                    if (TRUE == pRecvDeal->pDecodeFunc(pCanData->dataBuf, pCanData->dataLen))
                    {
                        Deal_TcuLogPackage(pRecvDeal->pgn, pCanData->dataBuf, pCanData->dataLen); // 保存接收帧记录log
                        Set_TcuRecvTimer(pRecvDeal->pgn, 0x00);
                        Set_TcuRecvFlag(pRecvDeal->pgn, eRecvFlag_Yes);
                    }
                }
            }
        }
    }
}

static void Set_TcuErrInfo(uint32 pgn)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    if (PGN_START_FINISH_ACK == pgn)
    {
        Set_BitFlag(tcuErroInfo, 0);
    }
    else if (PGN_STOP_FINISH_ACK == pgn)
    {
        Set_BitFlag(tcuErroInfo, 1);
    }
    else if (PGN_TCU_YC == pgn)
    {
        Set_BitFlag(tcuErroInfo, 3);
    }
    else if (PGN_SET_PARA == pgn)
    {
        Set_BitFlag(tcuErroInfo, 4);
    }
    else
    {
        if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120)) // TODO-xg增加TCU V1.14和V1.20版本的错误帧信息.
        {
            if (PGN_CAR_CHECK_INFO_ACK == pgn)
            {
                Set_BitFlag(tcuErroInfo, 5);
            }
        }
        else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120)
        {
            if ((PGN_CAR_VIN_IDENTIFY_ACK == pgn) || (PGN_CAR_VIN_CONFIRM))
            {
                Set_BitFlag(tcuErroInfo, 9);
            }
        }
    }
}

void Clr_TcuErrInfo(void)
{
    TCU_CTRL *pTcuCtrl = &tcuCtrl;

    Clr_BitFlag(tcuErroInfo, 0);
    Clr_BitFlag(tcuErroInfo, 1);
    Clr_BitFlag(tcuErroInfo, 3);
    Clr_BitFlag(tcuErroInfo, 4);
    Clr_BitFlag(tcuErroInfo, 5);

    if ((pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0114) && (pTcuCtrl->tcuProtocolVer < TCU_PROTOCOL_VER_0120)) // TODO-xg增加TCU V1.14和V1.20版本的错误帧信息.
    {
        Clr_BitFlag(tcuErroInfo, 5);
    }
    else if (pTcuCtrl->tcuProtocolVer >= TCU_PROTOCOL_VER_0120)
    {
        Clr_BitFlag(tcuErroInfo, 5);
    }
}
/**
******************************************************************************
* @brief       TCU_RecvServer
* @param[in]
* @param[out]
* @retval
*
* @details     TCU数据接收
*
* @note
******************************************************************************
*/
void Tcu_RecvServer(void)
{
    CAN_DATA strCanData[10];
    CAN_DATA strData;
    uint8 validFrameCnt = 0;
    uint8 index = 0;
    uint16 readLen = 0;
    uint8 erro = 0;

    memset(strCanData, 0x00, sizeof(strCanData));
    readLen = can_recv(TCU_CHAN, (uint8 *)strCanData, sizeof(strCanData), &erro);
    validFrameCnt = readLen / sizeof(CAN_DATA);

    for (int index = 0; index < validFrameCnt; index++)
    {
        memset(&strData, 0x00, sizeof(strData));
        strData.canId = strCanData[index].canId;
        strData.dataLen = strCanData[index].dataLen;
        memcpy(strData.dataBuf, strCanData[index].dataBuf, sizeof(strData.dataBuf));

        trace(TR_CH1, "<R1: %X>  ", strData.canId);
        trace_buf(TR_CH1, strData.dataBuf, strData.dataLen);

        TCU_Decode(&strData);
        if (Get_CcuCfgParaPlatform_convert() != Platform_protocol_Hn)
        {
            UPD_Decode(&strData);
        }
        memset(&strCanData[index], 0x00, sizeof(CAN_DATA));
    }
}

/**
 ******************************************************************************
 * @brief      Tcu_RecvTimerManage.
 * @param[in]  None
 * @param[out] None
 * @retval
 *
 * @details
 *
 * @note
 ******************************************************************************
 */
void Tcu_RecvTimerManage(void)
{
    const TCU_RECV_DEAL *pRecvDeal = NULL;
    uint8 index = 0;
    uint32 curTimer = 0;

    for (index = 0; index < FCNT(TCU_RECV_DEAL_TABLE); index++)
    {
        pRecvDeal = &TCU_RECV_DEAL_TABLE[index];

        if (eTimerEnable_On != Get_TcuRecvTimerEnable(pRecvDeal->pgn))
        {
            //            if(PGN_TCU_HEART == pRecvDeal->pgn)
            //            {
            //                trace(TR_ALARM, "TCU心跳帧---%d---<%d>---超时不检测\n", pRecvDeal->pgn,Get_TcuStage());
            //            }

            continue;
        }

        if (0xFFFF == pRecvDeal->pOverTimeFunc())
        {
            continue;
        }

        curTimer = abs(tickGet() - Get_TcuRecvTimer(pRecvDeal->pgn));

        if (curTimer > pRecvDeal->pOverTimeFunc())
        {
            switch (pRecvDeal->pgn)
            {
            case PGN_TCU_HEART:
            {
#if TCU_TIMEOUT_EN
                if ((Get_TcuStage() == TCU_STAGE_RUN_STARTING) || /**在启动中或充电中报超时故障，因为需要发完启动完成帧*/
                    (Get_TcuStage() == TCU_STAGE_RUN_CHARGING))
                {
                    Set_ErrType(eErrType_ComErrWithTCU);
                }
#endif
                HeartOvertimeDeal();
                Set_CcuHeartState(eComState_TimeOut);
                Set_TcuRecvTimer(pRecvDeal->pgn, 0x00);
                continue; /**<心跳超时就不判其它故障*/
            }
            break;
            case PGN_SET_PARA:
            {
                HeartOvertimeDeal();
            }
            break;
            case PGN_CAR_CHECK_INFO_ACK: // TCU协议 V1.14 车辆识别应答帧
            {
                Set_TcuRecvTimerEnable(PGN_CAR_CHECK_INFO_ACK, eTimerEnable_Off);
                Set_ErrType(eErrType_PlugAndPlayAckTimeout);
            }
            break;
            case PGN_START_FINISH_ACK:
            {
                Set_TcuRecvTimerEnable(PGN_START_FINISH_ACK, eTimerEnable_Off);
                if (TRUE != Check_ErrType(eErrType_ComErrWithTCU))
                {
                    Set_ErrType(eErrType_StartFinishAckTimeOutTcu);
                }
            }
            break;
            case PGN_STOP_FINISH_ACK:
            {
                Set_TcuRecvTimerEnable(PGN_STOP_FINISH_ACK, eTimerEnable_Off);
                if (TRUE != Check_ErrType(eErrType_ComErrWithTCU))
                {
                    Set_ErrType(eErrType_StopFinishAckTimeOutTcu);
                }
            }
            break;
            case PGN_CAR_VIN_IDENTIFY_ACK: // TCU协议 V1.20 车辆识别应答帧
            {
                Set_TcuRecvTimerEnable(PGN_CAR_VIN_IDENTIFY_ACK, eTimerEnable_Off);
                if (TRUE != Check_ErrType(eErrType_TcuCarVinOtherErr))
                {
                    Set_ErrType(eErrType_TcuCarVinOtherErr);
                }
            }
            break;
            case PGN_CAR_VIN_CONFIRM: // TCU协议 V1.20    车辆鉴权帧
            {
                Set_TcuRecvTimerEnable(PGN_CAR_VIN_CONFIRM, eTimerEnable_Off);
                if (TRUE != Check_ErrType(eErrType_CarVinConfirmTimeoutErr))
                {
                    Set_ErrType(eErrType_CarVinConfirmTimeoutErr);
                }
            }
            break;
            default:
                break;
            }

            if (eComState_Normal == Get_CcuHeartState() &&
                eComState_Normal == Get_TcuHeartState())
            {
                if (PGN_START_FINISH_ACK == pRecvDeal->pgn)
                {
                    if (TRUE != Check_ErrType(eErrType_ComErrWithTCU))
                    {
                        Set_ErrType(eErrType_StartFinishAckTimeOutTcu);
                    }
                }
                else if (PGN_STOP_FINISH_ACK == pRecvDeal->pgn)
                {
                    if (TRUE != Check_ErrType(eErrType_ComErrWithTCU))
                    {
                        Set_ErrType(eErrType_StopFinishAckTimeOutTcu);
                    }
                }
                else
                {
                    Set_ErrType(eErrType_ComErrWithTCU);
                }

                Set_TcuErrInfo(pRecvDeal->pgn);

                if (0 == Get_TcuSendRemainTimer(PGN_CCU_FAULT))
                {
                    Set_TcuSendRemainTimer(PGN_CCU_FAULT, 5000);
                    Set_TcuStartTimer(PGN_CCU_FAULT);
                    Set_TcuLastSendTimer(PGN_CCU_FAULT, 0xFFFF);
                }
            }
        }
    }
    return;
}

/*----------------------------tcuRecvCtrl.c--------------------------------*/
