
#include "../led_pwm.h"

#include <stdio.h>

#include <string.h>

#include <maths.h>

#include <stm32f2xx.h>

#include <stm32f2xxgpio.h>

#include <stm32f2xxrcc.h>

#include <stm32f2xxtim.h>

#include "adc.h"

#include "gpio.h"

#include "trace.h"

/* 硬件定义 */

#define LED_GPIO_PORT GPIOF

#define RED_PIN GPIO_Pin_3 // TIM5_CH1

#define YELLOW_PIN GPIO_Pin_4 // TIM5_CH2

#define GREEN_PIN GPIO_Pin_8 // TIM13_CH1

/* PWM参数 */

#define PWM_FREQ 1000 // 1kHz

#define DUTY_MAX 95 // 最大占空比95%

#define DUTY_MIN 5 // 最小占空比5%

#define BREATH_STEPS 360 // 呼吸周期步数(对应360度)

/* 时钟频率 - TIM5和TIM13都在APB1总线 */

#define TIM_CLK_FREQ 24000000UL // APB1: 12MHz * 2 = 24MHz

#define TIM_ARR (TIM_CLK_FREQ / PWM_FREQ - 1) // 统一ARR值

/* 数学常数 */

#define PI 3.14159265359f

#define TWO_PI (2.0f * PI)

/* 私有变量 */

static bool pwm_ready = false;

static LED_Control_t led_ctrl[LED_COUNT];

static uint8_t fade_speed = 2; // 淡入淡出速度

/* 优先级队列 */

#define LED_QUEUE_SIZE 8

static LED_Request_t request_queue[LED_QUEUE_SIZE];

static uint8_t queue_head = 0, queue_tail = 0;

/* 反相呼吸控制 */

static CrossBreath_Control_t cross_breath = {0};

/* 防抖参数 */

#define DEBOUNCE_TIME_MS 50

static uint32_t last_request_time[LED_COUNT] = {0};

static void Process_LED_Fade(LED_Color_t color);

static void Process_LED_Breath(LED_Color_t color);

/* 优先级队列管理 */

static bool Queue_IsFull(void);

static bool Queue_IsEmpty(void);

static bool Queue_AddRequest(const LED_Request_t *request);

static LED_Request_t *Queue_GetHighestPriority(void);

static void Queue_RemoveProcessed(void);

static bool Should_Ignore_Request(LED_Color_t color, LED_Priority_t priority);

/* 反相呼吸处理 */

static void Process_Cross_Breath(void);

static uint8_t Calculate_Cross_Breath_Duty(bool is_primary, uint16_t step);

/* 队列请求处理 */

static void Process_Queue_Request(LED_Request_t *request);

static void Process_LED_Transition(LED_Color_t color);

/* ==================== 私有函数 ==================== */

/**



 * @brief GPIO初始化 (仅配置为复用模式，不设置复用功能)



 */

static void GPIO_Init_Config(void)

{

    GPIO_InitTypeDef GPIO_InitStructure;

    /* 使能GPIOF时钟 */

    rCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOF, ENABLE);

    /* 配置GPIO为复用功能 */

    GPIO_InitStructure.GPIO_Pin = RED_PIN | YELLOW_PIN | GREEN_PIN;

    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;

    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;

    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;

    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;

    GPIO_Init(LED_GPIO_PORT, &GPIO_InitStructure);
}

/**



 * @brief GPIO回退模式(PWM初始化失败时使用)



 */

static void GPIO_Fallback_Config(void)

{

    GPIO_InitTypeDef GPIO_InitStructure;

    /* 使能GPIOF时钟 */

    rCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOF, ENABLE);

    GPIO_InitStructure.GPIO_Pin = RED_PIN | YELLOW_PIN | GREEN_PIN;

    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;

    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;

    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;

    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;

    GPIO_Init(LED_GPIO_PORT, &GPIO_InitStructure);

    /* 默认设置为高电平(95%亮度模拟) */

    GPIO_SetBits(LED_GPIO_PORT, RED_PIN | YELLOW_PIN | GREEN_PIN);
}

/**
 * @brief 定时器初始化 (TIM5和TIM13都在APB1总线，合并配置)

 */

static bool Timer_Init_Config(void)

{

    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;

    TIM_OCInitTypeDef TIM_OCInitStructure;

    /* 1. 使能定时器时钟 */

    rCC_AHB1PeriphClockCmd(RCC_APB1Periph_TIM5 | RCC_APB1Periph_TIM13, ENABLE);

    /* 2. 配置GPIO复用功能 */

    GPIO_PinAFConfig(LED_GPIO_PORT, GPIO_PinSource3, GPIO_AF_TIM5); // PF3->TIM5_CH1

    GPIO_PinAFConfig(LED_GPIO_PORT, GPIO_PinSource4, GPIO_AF_TIM5); // PF4->TIM5_CH2

    GPIO_PinAFConfig(LED_GPIO_PORT, GPIO_PinSource8, GPIO_AF_TIM13); // PF8->TIM13_CH1

    /* 3. 时基初始化 */

    TIM_TimeBaseStructure.TIM_Period = TIM_ARR;

    TIM_TimeBaseStructure.TIM_Prescaler = 0;

    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;

    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;

    TIM_TimeBaseInit(TIM5, &TIM_TimeBaseStructure);

    TIM_TimeBaseInit(TIM13, &TIM_TimeBaseStructure);

    /* 4. PWM输出配置 */

    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;

    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;

    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High; // 高有效

    TIM_OCInitStructure.TIM_OCIdleState = TIM_OCIdleState_Reset;

    /* 修正：初始脉冲设为95%占空比 */

    TIM_OCInitStructure.TIM_Pulse = ((uint16_t)(TIM_ARR + 1) * DUTY_MAX) / 100;

    /* 5. 配置TIM5及其通道 */

    TIM_OC1Init(TIM5, &TIM_OCInitStructure);

    TIM_OC1PreloadConfig(TIM5, TIM_OCPreload_Enable);

    TIM_OC2Init(TIM5, &TIM_OCInitStructure);

    TIM_OC2PreloadConfig(TIM5, TIM_OCPreload_Enable);

    TIM_ARRPreloadConfig(TIM5, ENABLE);

    TIM_Cmd(TIM5, ENABLE);

    /* 6. 配置TIM13及其通道 */

    TIM_OC1Init(TIM13, &TIM_OCInitStructure);

    TIM_OC1PreloadConfig(TIM13, TIM_OCPreload_Enable);

    TIM_ARRPreloadConfig(TIM13, ENABLE);

    TIM_Cmd(TIM13, ENABLE);

    return true;
}

/**



 * @brief 初始化LED控制结构



 */

static void LED_Control_Init(void)

{

    for (int i = 0; i < LED_COUNT; i++)

    {

        led_ctrl[i].state = LED_STATE_STATIC;

        led_ctrl[i].breath_mode = BREATH_SINE;

        led_ctrl[i].breath_step = 0;

        led_ctrl[i].target_duty = DUTY_MAX; // 初始目标95%

        led_ctrl[i].current_duty = DUTY_MAX; // 初始当前95%

        led_ctrl[i].is_active = true; // 初始为激活状态
    }
}

/**



 * @brief 更新单个LED的PWM输出



 */

static void Update_LED_PWM(LED_Color_t color)

{

    if (color >= LED_COUNT)

    {

        return;
    }

    uint8_t duty = led_ctrl[color].current_duty;

    if (duty > 100)

        duty = 100;

    uint16_t pulse = ((uint16_t)(TIM_ARR + 1) * duty) / 100;

    switch (color)

    {

    case LED_RED:

        TIM_SetCompare1(TIM5, pulse);

        break;

    case LED_YELLOW:

        TIM_SetCompare2(TIM5, pulse);

        break;

    case LED_GREEN:

        TIM_SetCompare1(TIM13, pulse);

        break;

    default:

        break;
    }
}

/**



 * @brief 计算呼吸占空比 - 确保从95%开始，从高到低变化



 * @note 修正：step=0时返回DUTY_MAX(95%)，然后逐渐降低到DUTY_MIN(5%)，再回到DUTY_MAX



 */

static uint8_t Calculate_Breath_Duty(Breath_Mode_t mode, uint16_t step)

{

    float ratio;

    float angle = (float)step * TWO_PI / BREATH_STEPS;

    switch (mode)

    {

    case BREATH_SINE:

        /* 使用余弦函数：cos(0)=1, cos(π)=-1, cos(2π)=1 */

        /* (cos(angle)+1)/2: 从1开始，降到0，再回到1 */

        ratio = (cosf(angle) + 1.0f) * 0.5f;

        break;

    case BREATH_LINEAR:

        /* 线性变化：从1开始，降到0，再回到1 */

        if (step < BREATH_STEPS / 2)

        {

            /* 前半段：从1降到0 */

            ratio = 1.0f - (float)step / (BREATH_STEPS / 2);
        }

        else

        {

            /* 后半段：从0升到1 */

            ratio = (float)(step - BREATH_STEPS / 2) / (BREATH_STEPS / 2);
        }

        break;

    case BREATH_EXPONENTIAL:

        /* 指数变化：基于余弦函数，但变化更柔和 */

        ratio = (cosf(angle) + 1.0f) * 0.5f;

        ratio = ratio * ratio; // 平方使变化更柔和

        break;

    default:

        ratio = 1.0f;

        break;
    }

    /* 确保ratio在0-1范围内 */

    if (ratio > 1.0f)

        ratio = 1.0f;

    if (ratio < 0.0f)

        ratio = 0.0f;

    /* 映射到DUTY_MIN到DUTY_MAX范围 */

    return (uint8_t)(DUTY_MIN + ratio * (DUTY_MAX - DUTY_MIN));
}

/**



 * @brief 处理单个LED的呼吸效果



 */

static void Process_LED_Breath(LED_Color_t color)

{

    if (color >= LED_COUNT || led_ctrl[color].state != LED_STATE_BREATHING)

    {

        return;
    }

    LED_Control_t *ctrl = &led_ctrl[color];

    ctrl->breath_step++;

    if (ctrl->breath_step >= BREATH_STEPS)

    {

        ctrl->breath_step = 0;
    }

    ctrl->current_duty = Calculate_Breath_Duty(ctrl->breath_mode, ctrl->breath_step);

    Update_LED_PWM(color);
}

/**



 * @brief 处理LED淡入淡出



 */

static void Process_LED_Fade(LED_Color_t color)

{

    if (color >= LED_COUNT)

    {

        return;
    }

    LED_Control_t *ctrl = &led_ctrl[color];

    bool updated = false;

    switch (ctrl->state)

    {

    case LED_STATE_FADING_IN:

        if (ctrl->current_duty < ctrl->target_duty)

        {

            if (ctrl->current_duty + fade_speed <= ctrl->target_duty)

            {

                ctrl->current_duty += fade_speed;
            }

            else

            {

                ctrl->current_duty = ctrl->target_duty;

                ctrl->state = LED_STATE_STATIC;
            }

            updated = true;
        }

        break;

    case LED_STATE_FADING_OUT:

        if (ctrl->current_duty > ctrl->target_duty)

        {

            if (ctrl->current_duty >= fade_speed + ctrl->target_duty)

            {

                ctrl->current_duty -= fade_speed;
            }

            else

            {

                ctrl->current_duty = ctrl->target_duty;

                ctrl->state = (ctrl->target_duty == 0) ? LED_STATE_OFF : LED_STATE_STATIC;

                if (ctrl->target_duty == 0)

                {

                    ctrl->is_active = false;
                }
            }

            updated = true;
        }

        break;

    default:

        break;
    }

    if (updated)

    {

        Update_LED_PWM(color);
    }
}

/* ==================== 公共函数 ==================== */

/**



 * @brief LED PWM初始化



 */

bool LED_PWM_Init(void)

{

    /* 1. GPIO配置 */

    GPIO_Init_Config();

    /* 2. 定时器配置 */

    if (!Timer_Init_Config())

    {

        GPIO_Fallback_Config();

        return false;
    }

    /* 3. 初始化LED控制结构 */

    LED_Control_Init();

    /* 4. 确保所有LED初始为95%亮度 */

    for (LED_Color_t color = 0; color < LED_COUNT; color++)

    {

        Update_LED_PWM(color);
    }

    pwm_ready = true;

    return true;
}

/**



 * @brief 设置单个LED占空比



 */

void LED_PWM_SetDuty(LED_Color_t color, uint8_t duty)

{

    if (!pwm_ready || color >= LED_COUNT)

    {

        return;
    }

    if (duty > 100)

    {

        duty = 100;
    }

    led_ctrl[color].current_duty = duty;

    led_ctrl[color].target_duty = duty;

    if (duty == 0)

    {

        led_ctrl[color].state = LED_STATE_OFF;

        led_ctrl[color].is_active = false;
    }

    else

    {

        led_ctrl[color].state = LED_STATE_STATIC;

        led_ctrl[color].is_active = true;
    }

    /* 重置呼吸和淡入淡出状态 */

    led_ctrl[color].breath_step = 0;

    Update_LED_PWM(color);
}

/**



 * @brief 设置所有LED占空比



 */

void LED_PWM_SetAllDuty(uint8_t red, uint8_t yellow, uint8_t green)

{

    LED_PWM_SetDuty(LED_RED, red);

    LED_PWM_SetDuty(LED_YELLOW, yellow);

    LED_PWM_SetDuty(LED_GREEN, green);
}

/**



 * @brief 设置LED状态



 */

void LED_PWM_SetLEDState(LED_Color_t color, LED_State_t state)

{

    if (!pwm_ready || color >= LED_COUNT)

    {

        return;
    }

    led_ctrl[color].state = state;

    led_ctrl[color].is_active = (state != LED_STATE_OFF);
}

/**



 * @brief 启动单个LED呼吸模式



 */

void LED_PWM_StartSingleBreath(LED_Color_t color, Breath_Mode_t mode)

{

    if (!pwm_ready || color >= LED_COUNT)

    {

        return;
    }

    LED_Control_t *ctrl = &led_ctrl[color];

    /* 确保从95%开始，从高到低变化 */

    ctrl->state = LED_STATE_BREATHING;

    ctrl->breath_mode = mode;

    ctrl->breath_step = 0; // 从0开始，Calculate_Breath_Duty会返回95%

    ctrl->is_active = true;

    ctrl->current_duty = DUTY_MAX; // 立即设置为95%

    Update_LED_PWM(color);
}

/**



 * @brief 停止单个LED呼吸模式



 */

void LED_PWM_StopSingleBreath(LED_Color_t color)

{

    if (!pwm_ready || color >= LED_COUNT)

    {

        return;
    }

    led_ctrl[color].state = LED_STATE_STATIC;

    /* 保持当前亮度或设置为95% */

    led_ctrl[color].current_duty = DUTY_MAX;

    led_ctrl[color].target_duty = DUTY_MAX;

    Update_LED_PWM(color);
}

/**



 * @brief LED切换 (支持多种切换模式)



 */

void LED_PWM_SwitchLED(LED_Color_t from_color, LED_Color_t to_color, Switch_Mode_t mode)

{

    if (!pwm_ready || from_color >= LED_COUNT || to_color >= LED_COUNT)

        return;

    switch (mode)

    {

    case SWITCH_MODE_IMMEDIATE:

        /* 立即关闭from_color，立即开启to_color */

        LED_PWM_SetDuty(from_color, 0);

        LED_PWM_SetDuty(to_color, DUTY_MAX);

        break;

    case SWITCH_MODE_FADE:

        /* 淡出from_color，淡入to_color */

        if (led_ctrl[from_color].current_duty > 0)

        {

            led_ctrl[from_color].state = LED_STATE_FADING_OUT;

            led_ctrl[from_color].target_duty = 0;
        }

        /* 确保目标LED从0开始淡入到95% */

        led_ctrl[to_color].current_duty = 0;

        led_ctrl[to_color].state = LED_STATE_FADING_IN;

        led_ctrl[to_color].target_duty = DUTY_MAX;

        led_ctrl[to_color].is_active = true;

        Update_LED_PWM(to_color);

        break;

    case SWITCH_MODE_INHERIT:

        /* 继承当前亮度和呼吸步长 */

        if (led_ctrl[from_color].state == LED_STATE_BREATHING)

        {

            led_ctrl[to_color].state = LED_STATE_BREATHING;

            led_ctrl[to_color].breath_mode = led_ctrl[from_color].breath_mode;

            led_ctrl[to_color].breath_step = led_ctrl[from_color].breath_step;

            led_ctrl[to_color].current_duty = led_ctrl[from_color].current_duty;
        }

        else

        {

            led_ctrl[to_color].current_duty = DUTY_MAX; // 继承95%亮度

            led_ctrl[to_color].state = LED_STATE_STATIC;
        }

        led_ctrl[to_color].is_active = true;

        led_ctrl[to_color].target_duty = led_ctrl[to_color].current_duty;

        /* 关闭from_color */

        LED_PWM_SetDuty(from_color, 0);

        Update_LED_PWM(to_color);

        break;
    }
}

/**



 * @brief 主更新函数 - 处理队列和呼吸效果



 */

void LED_PWM_BreathUpdate(void)

{

    if (!pwm_ready)

        return;

    // 1. 处理优先级队列

    LED_Request_t *request = Queue_GetHighestPriority();

    if (request != NULL)

    {

        Process_Queue_Request(request);

        request->is_valid = false; // 标记为已处理

        Queue_RemoveProcessed();
    }

    // 2. 处理反相呼吸

    Process_Cross_Breath();

    // 3. 处理常规LED状态

    for (LED_Color_t color = 0; color < LED_COUNT; color++)

    {

        if (led_ctrl[color].is_active)

        {

            switch (led_ctrl[color].state)

            {

            case LED_STATE_BREATHING:

                Process_LED_Breath(color);

                break;

            case LED_STATE_FADING_IN:

            case LED_STATE_FADING_OUT:

                Process_LED_Fade(color);

                break;

            case LED_STATE_TRANSITIONING:

                Process_LED_Transition(color);

                break;

            default:

                break;
            }
        }
    }
}

/**



 * @brief 获取指定LED的状态



 */

LED_State_t LED_PWM_GetLEDState(LED_Color_t color)

{

    if (!pwm_ready || color >= LED_COUNT)

    {

        return LED_STATE_OFF;
    }

    return led_ctrl[color].state;
}

/**



 * @brief 获取指定LED的占空比



 */

uint8_t LED_PWM_GetDuty(LED_Color_t color)

{

    if (!pwm_ready || color >= LED_COUNT)

    {

        return 0;
    }

    return led_ctrl[color].current_duty;
}

/* ==================== 优先级队列实现 ==================== */

/**

 * @brief 检查队列是否已满

 */

static bool Queue_IsFull(void)

{

    return ((queue_tail + 1) % LED_QUEUE_SIZE) == queue_head;
}

/**

 * @brief 检查队列是否为空

 */

static bool Queue_IsEmpty(void)

{

    return queue_head == queue_tail;
}

/**

 * @brief 防抖检查

 */

static bool Should_Ignore_Request(LED_Color_t color, LED_Priority_t priority)

{

    uint32_t now = tickGet();

    // 高优先级请求不防抖

    if (priority >= LED_PRIORITY_HIGH)

    {

        return false;
    }

    // 检查是否在防抖时间内

    if (now - last_request_time[color] < DEBOUNCE_TIME_MS)

    {

        return true;
    }

    last_request_time[color] = now;

    return false;
}

/**

 * @brief 添加请求到队列

 */

static bool Queue_AddRequest(const LED_Request_t *request)

{

    if (Queue_IsFull())

    {

        return false;
    }

    // 检查是否应该合并相同目标的请求

    for (uint8_t i = queue_head; i != queue_tail; i = (i + 1) % LED_QUEUE_SIZE)

    {

        if (request_queue[i].target_color == request->target_color &&

            request_queue[i].is_valid)

        {

            // 如果新请求优先级更高，则替换

            if (request->priority >= request_queue[i].priority)

            {

                request_queue[i] = *request;

                return true;
            }

            return false; // 忽略低优先级的重复请求
        }
    }

    // 添加新请求

    request_queue[queue_tail] = *request;

    queue_tail = (queue_tail + 1) % LED_QUEUE_SIZE;

    return true;
}

/**

 * @brief 获取最高优先级的请求

 */

static LED_Request_t *Queue_GetHighestPriority(void)

{

    if (Queue_IsEmpty())

    {

        return NULL;
    }

    LED_Request_t *highest = NULL;

    LED_Priority_t max_priority = LED_PRIORITY_LOW;

    for (uint8_t i = queue_head; i != queue_tail; i = (i + 1) % LED_QUEUE_SIZE)

    {

        if (request_queue[i].is_valid && request_queue[i].priority >= max_priority)

        {

            max_priority = request_queue[i].priority;

            highest = &request_queue[i];
        }
    }

    return highest;
}

/**

 * @brief 移除已处理的请求

 */

static void Queue_RemoveProcessed(void)

{

    while (!Queue_IsEmpty() && !request_queue[queue_head].is_valid)

    {

        queue_head = (queue_head + 1) % LED_QUEUE_SIZE;
    }
}

/* ==================== 反相呼吸实现 ==================== */

/**

 * @brief 计算反相呼吸占空比

 */

static uint8_t Calculate_Cross_Breath_Duty(bool is_primary, uint16_t step)

{

    float angle = (float)step * TWO_PI / BREATH_STEPS;

    float intensity = (cosf(angle) + 1.0f) * 0.5f; // 0-1范围

    if (!is_primary)

    {

        intensity = 1.0f - intensity; // 反相
    }

    uint8_t available_range = cross_breath.total_brightness - 2 * cross_breath.min_brightness;

    return cross_breath.min_brightness + (uint8_t)(intensity * available_range);
}

/**

 * @brief 处理反相呼吸

 */

static void Process_Cross_Breath(void)

{

    if (!cross_breath.is_active)

    {

        return;
    }

    cross_breath.breath_step++;

    if (cross_breath.breath_step >= BREATH_STEPS)

    {

        cross_breath.breath_step = 0;
    }

    uint8_t duty1 = Calculate_Cross_Breath_Duty(true, cross_breath.breath_step);

    uint8_t duty2 = Calculate_Cross_Breath_Duty(false, cross_breath.breath_step);

    // 确保总亮度恒定

    if (duty1 + duty2 != cross_breath.total_brightness)

    {

        duty2 = cross_breath.total_brightness - duty1;
    }

    led_ctrl[cross_breath.color1].current_duty = duty1;

    led_ctrl[cross_breath.color2].current_duty = duty2;

    Update_LED_PWM(cross_breath.color1);

    Update_LED_PWM(cross_breath.color2);
}

/* ==================== 新增公共API ==================== */

/**

 * @brief 请求设置LED颜色

 */

bool LED_PWM_RequestColor(LED_Color_t color, LED_Priority_t priority, uint8_t duty)

{

    if (!pwm_ready || color >= LED_COUNT)

    {

        return false;
    }

    if (Should_Ignore_Request(color, priority))

    {

        return false;
    }

    LED_Request_t request = {

        .target_color = color,

        .target_state = (duty == 0) ? LED_STATE_OFF : LED_STATE_STATIC,

        .switch_mode = SWITCH_MODE_FADE,

        .priority = priority,

        .request_time = tickGet(),

        .duration_ms = 200,

        .target_duty = duty,

        .is_valid = true};

    return Queue_AddRequest(&request);
}

/**

 * @brief 请求LED颜色切换

 */

bool LED_PWM_RequestSwitch(LED_Color_t from, LED_Color_t to, Switch_Mode_t mode, LED_Priority_t priority)

{

    if (!pwm_ready || from >= LED_COUNT || to >= LED_COUNT)

    {

        return false;
    }

    if (Should_Ignore_Request(to, priority))

    {

        return false;
    }

    LED_Request_t request = {

        .target_color = to,

        .target_state = LED_STATE_STATIC,

        .switch_mode = mode,

        .priority = priority,

        .request_time = tickGet(),

        .duration_ms = (mode == SWITCH_MODE_SEQUENTIAL) ? 400 : 200,

        .target_duty = DUTY_MAX,

        .is_valid = true};

    return Queue_AddRequest(&request);
}

/**

 * @brief 请求反相呼吸

 */

bool LED_PWM_RequestCrossBreath(LED_Color_t color1, LED_Color_t color2, LED_Priority_t priority)

{

    if (!pwm_ready || color1 >= LED_COUNT || color2 >= LED_COUNT)

    {

        return false;
    }

    LED_Request_t request = {

        .target_color = color1, // 主色作为目标

        .target_state = LED_STATE_CROSS_BREATHING,

        .switch_mode = SWITCH_MODE_CROSS_BREATH,

        .priority = priority,

        .request_time = tickGet(),

        .duration_ms = 0, // 无限持续

        .target_duty = DUTY_MAX,

        .is_valid = true};

    return Queue_AddRequest(&request);
}

/**

 * @brief 清空队列

 */

void LED_PWM_ClearQueue(LED_Priority_t min_priority)

{

    for (uint8_t i = queue_head; i != queue_tail; i = (i + 1) % LED_QUEUE_SIZE)

    {

        if (request_queue[i].priority < min_priority)

        {

            request_queue[i].is_valid = false;
        }
    }

    Queue_RemoveProcessed();
}

/**

 * @brief 处理队列请求

 */

static void Process_Queue_Request(LED_Request_t *request)

{

    LED_Color_t color = request->target_color;

    switch (request->switch_mode)

    {

    case SWITCH_MODE_IMMEDIATE:

        // 立即切换

        LED_PWM_SetDuty(color, request->target_duty);

        break;

    case SWITCH_MODE_FADE:

        // 淡入淡出切换

        led_ctrl[color].target_duty = request->target_duty;

        led_ctrl[color].state = (request->target_duty > led_ctrl[color].current_duty) ? LED_STATE_FADING_IN : LED_STATE_FADING_OUT;

        led_ctrl[color].state_start_time = tickGet();

        led_ctrl[color].transition_duration = request->duration_ms;

        led_ctrl[color].is_active = true;

        break;

    case SWITCH_MODE_SEQUENTIAL:

        // 顺序切换 - 先关闭其他LED

        for (LED_Color_t i = 0; i < LED_COUNT; i++)

        {

            if (i != color && led_ctrl[i].current_duty > 0)

            {

                led_ctrl[i].target_duty = 0;

                led_ctrl[i].state = LED_STATE_FADING_OUT;

                led_ctrl[i].state_start_time = tickGet();

                led_ctrl[i].transition_duration = request->duration_ms / 2;
            }
        }

        // 延迟启动目标LED

        led_ctrl[color].target_duty = request->target_duty;

        led_ctrl[color].state = LED_STATE_TRANSITIONING;

        led_ctrl[color].state_start_time = tickGet() + request->duration_ms / 2;

        led_ctrl[color].transition_duration = request->duration_ms / 2;

        led_ctrl[color].is_active = true;

        break;

    case SWITCH_MODE_CROSS_BREATH:

        // 启动反相呼吸

        cross_breath.color1 = color;

        cross_breath.color2 = (color + 1) % LED_COUNT; // 简单选择下一个颜色

        cross_breath.breath_step = 0;

        cross_breath.total_brightness = 90; // 总亮度90%

        cross_breath.min_brightness = 10; // 最小亮度10%

        cross_breath.is_active = true;

        // 设置LED状态

        led_ctrl[cross_breath.color1].state = LED_STATE_CROSS_BREATHING;

        led_ctrl[cross_breath.color2].state = LED_STATE_CROSS_BREATHING;

        led_ctrl[cross_breath.color1].is_active = true;

        led_ctrl[cross_breath.color2].is_active = true;

        break;

    default:

        break;
    }
}

/**

 * @brief 处理LED状态转换

 */

static void Process_LED_Transition(LED_Color_t color)

{

    LED_Control_t *ctrl = &led_ctrl[color];

    uint32_t now = tickGet();

    if (now >= ctrl->state_start_time)

    {

        // 开始转换

        ctrl->state = (ctrl->target_duty > ctrl->current_duty) ? LED_STATE_FADING_IN : LED_STATE_FADING_OUT;
    }
}
