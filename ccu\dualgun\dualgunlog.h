/**
 * @file dualgun_log.h
 * @brief 双枪充电增强日志系统头文件
 * @details 提供日志等级、颜色、过滤和节流功能
 */

#ifndef DUALGUN_LOG_H
#define DUALGUN_LOG_H

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <trace.h> // 假设这是原始trace函数的头文件

//==============================================================================
// 日志级别定义
//==============================================================================
typedef enum
{
    LOG_LEVEL_ERROR = 0, // 错误 - 红色
    LOG_LEVEL_WARN = 1,  // 警告 - 黄色
    LOG_LEVEL_INFO = 2,  // 信息 - 绿色
    LOG_LEVEL_DEBUG = 3, // 调试 - 青色
    LOG_LEVEL_TRACE = 4  // 跟踪 - 白色
} DualgunLogLevel;

//==============================================================================
// 全局变量声明
//==============================================================================
#define DUAL_CHARGE_CHANNEL_LOG_NUM (0x01) ///< 双通道配置 - Number of Dual Charge Channels
// 当前允许的最大日志级别 (0-4)
extern DualgunLogLevel g_dualgun_log_level;

#define MAX_CAN_ID_CACHE 6 // 支持缓存的最大CAN ID数量
// CAN帧缓存结构体
typedef struct
{
    uint32 canId;         // CAN标识符
    uint8 data[8];        // 数据内容(标准CAN帧最多8字节)
    uint8 dataLen;        // 数据长度
    uint32 lastPrintTime; // 上次打印时间
    uint32 repeatCount;   // 重复次数计数
} CanFrameCache;

// 节流控制结构
typedef struct
{
    uint32 error_interval;   // 错误日志间隔
    uint32 warn_interval;    // 警告日志间隔
    uint32 info_interval;    // 信息日志间隔
    uint32 debug_interval;   // 调试日志间隔
    uint32 trace_interval;   // 跟踪日志间隔
    uint32 voltage_interval; // 电压相关日志间隔
    uint32 current_interval; // 电流相关日志间隔
    uint32 state_interval;   // 状态变化日志间隔
    uint32 can_interval;     // CAN日志间隔
} DualgunThrottleConfig;

// 上次打印时间记录结构
typedef struct
{
    uint32 error_time;                                // 上次错误日志时间
    uint32 warn_time;                                 // 上次警告日志时间
    uint32 info_time;                                 // 上次信息日志时间
    uint32 debug_time;                                // 上次调试日志时间
    uint32 trace_time;                                // 上次跟踪日志时间
    uint32 voltage_time[DUAL_CHARGE_CHANNEL_LOG_NUM]; // 各通道上次电压日志时间
    uint32 current_time[DUAL_CHARGE_CHANNEL_LOG_NUM]; // 各通道上次电流日志时间
    uint32 state_time[DUAL_CHARGE_CHANNEL_LOG_NUM];   // 各通道上次状态日志时间
    uint32 can_time;                                  // 上次CAN日志时间
} DualgunLogTime;

// 上次输出的值记录结构 - 用于变化检测
typedef struct
{
    uint16 voltage[DUAL_CHARGE_CHANNEL_LOG_NUM];         // 各通道上次输出的电压值
    uint16 current[DUAL_CHARGE_CHANNEL_LOG_NUM];         // 各通道上次输出的电流值
    uint16 battery_voltage[DUAL_CHARGE_CHANNEL_LOG_NUM]; // 各通道上次输出的电池电压值
    uint8 mode[DUAL_CHARGE_CHANNEL_LOG_NUM];             // 各通道上次输出的模式
    uint8 status[DUAL_CHARGE_CHANNEL_LOG_NUM];           // 各通道上次输出的状态
    uint8 work_status[DUAL_CHARGE_CHANNEL_LOG_NUM];      // 各通道上次输出的工作状态
    uint8 service[DUAL_CHARGE_CHANNEL_LOG_NUM];          // 各通道上次输出的服务状态
    // CAN相关统计
    CanFrameCache canCache[MAX_CAN_ID_CACHE]; // CAN帧缓存
    uint32 can_total_count;                   // CAN消息总计数
    uint32 can_logged_count;                  // 已记录的CAN消息计数
} DualgunLoggedValue;

// 变化阈值设置结构
typedef struct
{
    uint16 voltage_threshold; // 电压变化阈值 (0.1V)
    uint16 current_threshold; // 电流变化阈值 (0.01A)
} DualgunChangeThreshold;

// 在DualgunLoggedValue结构体中添加CAN帧缓存

// 外部全局变量
extern DualgunThrottleConfig g_log_throttle;
extern DualgunLogTime g_last_log_time;
extern DualgunLoggedValue g_last_logged_value;
extern DualgunChangeThreshold g_change_threshold;

//==============================================================================
// ANSI颜色定义
//==============================================================================
#define LOG_COLOR_RED "\033[1;31m"     // 粗体红
#define LOG_COLOR_YELLOW "\033[1;33m"  // 粗体黄
#define LOG_COLOR_GREEN "\033[1;32m"   // 粗体绿
#define LOG_COLOR_CYAN "\033[1;36m"    // 粗体青
#define LOG_COLOR_WHITE "\033[1;37m"   // 粗体白
#define LOG_COLOR_BLUE "\033[1;34m"    // 粗体蓝
#define LOG_COLOR_MAGENTA "\033[1;35m" // 粗体紫
#define LOG_COLOR_RESET "\033[0m"      // 重置所有属性

// 日志级别前缀和颜色映射
extern const char *LOG_LEVEL_PREFIX[];

//==============================================================================
// 函数声明
//==============================================================================

/**
 * @brief 获取当前系统tick (毫秒)
 * @return 当前系统时间(毫秒)
 */
uint32 dualgun_tickGet(void);

/**
 * @brief 内部日志处理函数
 * @param level 日志级别
 * @param channel 通道号
 * @param throttle_time 节流时间(ms)
 * @param last_time 上次输出时间指针
 * @return 是否应该输出日志
 */
bool dualgun_should_log(DualgunLogLevel level, uint8 channel, uint32 throttle_time, uint32 *last_time);

/**
 * @brief 设置日志级别
 * @param level 0-ERROR, 1-WARN, 2-INFO, 3-DEBUG, 4-TRACE
 */
void dualgun_set_log_level(int level);

/**
 * @brief 设置节流间隔
 * @param type 0-error, 1-warn, 2-info, 3-debug, 4-trace, 5-voltage, 6-current, 7-state, 8-can
 * @param ms 间隔时间(毫秒), 0表示不限制
 */
void dualgun_set_throttle(int type, int ms);

/**
 * @brief 设置变化阈值
 * @param type 0-error, 1-warn, 2-info, 3-debug, 4-trace, 5-voltage, 6-current, 7-state, 8-can
 * @param value 阈值值
 */
void dualgun_set_threshold(int type, int value);

/**
 * @brief 显示当前日志设置
 */
void dualgun_show_log_config(void);

/**
 * @brief 注册Shell命令
 * 在系统初始化时调用
 */
void dualgun_register_commands(void);

/**
 * @brief 初始化双枪充电日志系统
 */
void dualgun_log_init(void);

/**
 * @brief 检查CAN帧内容是否变化
 * @param canId CAN标识符
 * @param data 数据内容
 * @param dataLen 数据长度
 * @return 如果内容变化或需要周期性打印返回true，否则返回false
 */
bool is_can_frame_changed(uint32 canId, uint8 *data, uint8 dataLen);

/**
 * @brief 显示CAN日志统计信息
 */
void dualgun_show_can_stats(void);

//==============================================================================
// 日志宏定义
//==============================================================================

// 基础日志宏 - 按级别带颜色输出
#define DUALGUN_LOG(level, tid, fmt, ...)                                                 \
    do                                                                                    \
    {                                                                                     \
        uint32 *time_ptr = NULL;                                                          \
        uint32 interval = 0;                                                              \
                                                                                          \
        /* 根据级别选择对应的时间指针和间隔设置 */                                        \
        switch (level)                                                                    \
        {                                                                                 \
        case LOG_LEVEL_ERROR:                                                             \
            time_ptr = &g_last_log_time.error_time;                                       \
            interval = g_log_throttle.error_interval;                                     \
            break;                                                                        \
        case LOG_LEVEL_WARN:                                                              \
            time_ptr = &g_last_log_time.warn_time;                                        \
            interval = g_log_throttle.warn_interval;                                      \
            break;                                                                        \
        case LOG_LEVEL_INFO:                                                              \
            time_ptr = &g_last_log_time.info_time;                                        \
            interval = g_log_throttle.info_interval;                                      \
            break;                                                                        \
        case LOG_LEVEL_DEBUG:                                                             \
            time_ptr = &g_last_log_time.debug_time;                                       \
            interval = g_log_throttle.debug_interval;                                     \
            break;                                                                        \
        case LOG_LEVEL_TRACE:                                                             \
            time_ptr = &g_last_log_time.trace_time;                                       \
            interval = g_log_throttle.trace_interval;                                     \
            break;                                                                        \
        default:                                                                          \
            break;                                                                        \
        }                                                                                 \
                                                                                          \
        if (time_ptr && dualgun_should_log(level, 0, interval, time_ptr))                 \
        {                                                                                 \
            trace(tid, "%s" fmt LOG_COLOR_RESET, LOG_LEVEL_PREFIX[level], ##__VA_ARGS__); \
        }                                                                                 \
    } while (0)

// CAN日志宏 - 内容变化检测，防止重复打印
#define DUALGUN_CAN_LOG(tid, can_Id, data, dataLen, fmt, ...)                   \
    do                                                                          \
    {                                                                           \
        g_last_logged_value.can_total_count++;                                  \
                                                                                \
        /* 检查日志级别和内容变化 */                                            \
        if (LOG_LEVEL_INFO <= g_dualgun_log_level &&                            \
            is_can_frame_changed(can_Id, data, dataLen))                        \
        {                                                                       \
            g_last_logged_value.can_logged_count++;                             \
                                                                                \
            /* 查找对应的缓存项索引 */                                          \
            int idx;                                                            \
            for (idx = 0; idx < MAX_CAN_ID_CACHE; idx++)                        \
            {                                                                   \
                if (g_last_logged_value.canCache[idx].canId == can_Id)          \
                    break;                                                      \
            }                                                                   \
                                                                                \
            /* 提示重复次数 */                                                  \
            uint32 repeat = g_last_logged_value.canCache[idx].repeatCount;      \
            if (repeat > 0)                                                     \
            {                                                                   \
                trace(tid, "%s" fmt " [相同内容重复:%d次]\n" LOG_COLOR_RESET,   \
                      LOG_LEVEL_PREFIX[LOG_LEVEL_INFO], ##__VA_ARGS__, repeat); \
                trace_buf(tid, data, dataLen);                                  \
            }                                                                   \
            else                                                                \
            {                                                                   \
                trace(tid, "%s" fmt LOG_COLOR_RESET,                            \
                      LOG_LEVEL_PREFIX[LOG_LEVEL_INFO], ##__VA_ARGS__);         \
                trace_buf(tid, data, dataLen);                                  \
            }                                                                   \
                                                                                \
            /* 周期性显示统计信息 */                                            \
            static uint32 last_stats_time = 0;                                  \
            uint32 now = dualgun_tickGet();                                     \
            if (now - last_stats_time > 30000)                                  \
            { /* 30秒显示一次统计 */                                            \
                last_stats_time = now;                                          \
            }                                                                   \
        }                                                                       \
    } while (0)
// 各级别简便宏
#define DUALGUN_ERROR(tid, fmt, ...) DUALGUN_LOG(LOG_LEVEL_ERROR, tid, fmt, ##__VA_ARGS__)
#define DUALGUN_WARN(tid, fmt, ...) DUALGUN_LOG(LOG_LEVEL_WARN, tid, fmt, ##__VA_ARGS__)
#define DUALGUN_INFO(tid, fmt, ...) DUALGUN_LOG(LOG_LEVEL_INFO, tid, fmt, ##__VA_ARGS__)
#define DUALGUN_DEBUG(tid, fmt, ...) DUALGUN_LOG(LOG_LEVEL_DEBUG, tid, fmt, ##__VA_ARGS__)
#define DUALGUN_TRACE(tid, fmt, ...) DUALGUN_LOG(LOG_LEVEL_TRACE, tid, fmt, ##__VA_ARGS__)

// 电压变化日志 - 结合变化检测和节流
#define DUALGUN_VOLTAGE_LOG(level, tid, channel, voltage, fmt, ...)                                                                           \
    do                                                                                                                                        \
    {                                                                                                                                         \
        if ((level) <= g_dualgun_log_level && channel < DUAL_CHARGE_CHANNEL_LOG_NUM)                                                          \
        {                                                                                                                                     \
            bool value_changed = (abs(voltage - g_last_logged_value.voltage[channel]) >= g_change_threshold.voltage_threshold);               \
            if (value_changed || dualgun_should_log(level, channel, g_log_throttle.voltage_interval, &g_last_log_time.voltage_time[channel])) \
            {                                                                                                                                 \
                trace(tid, "%s" fmt LOG_COLOR_RESET, LOG_LEVEL_PREFIX[level], ##__VA_ARGS__);                                                 \
                g_last_logged_value.voltage[channel] = voltage;                                                                               \
            }                                                                                                                                 \
        }                                                                                                                                     \
    } while (0)

// 电流变化日志 - 结合变化检测和节流
#define DUALGUN_CURRENT_LOG(level, tid, channel, current, fmt, ...)                                                                           \
    do                                                                                                                                        \
    {                                                                                                                                         \
        if ((level) <= g_dualgun_log_level && channel < DUAL_CHARGE_CHANNEL_LOG_NUM)                                                          \
        {                                                                                                                                     \
            bool value_changed = (abs(current - g_last_logged_value.current[channel]) >= g_change_threshold.current_threshold);               \
            if (value_changed || dualgun_should_log(level, channel, g_log_throttle.current_interval, &g_last_log_time.current_time[channel])) \
            {                                                                                                                                 \
                trace(tid, "%s" fmt LOG_COLOR_RESET, LOG_LEVEL_PREFIX[level], ##__VA_ARGS__);                                                 \
                g_last_logged_value.current[channel] = current;                                                                               \
            }                                                                                                                                 \
        }                                                                                                                                     \
    } while (0)

// 电压电流组合日志 - 同时监控电压和电流变化
#define DUALGUN_POWER_LOG(level, tid, channel, vol, cur, fmt, ...)                                                            \
    do                                                                                                                        \
    {                                                                                                                         \
        if ((level) <= g_dualgun_log_level && channel < DUAL_CHARGE_CHANNEL_NUM)                                              \
        {                                                                                                                     \
            bool voltage_changed = (abs(vol - g_last_logged_value.voltage[channel]) >= g_change_threshold.voltage_threshold); \
            bool current_changed = (abs(cur - g_last_logged_value.current[channel]) >= g_change_threshold.current_threshold); \
            uint32 *time_ptr = &g_last_log_time.voltage_time[channel]; /* 使用电压时间作为组合时间 */                         \
                                                                                                                              \
            if (voltage_changed || current_changed ||                                                                         \
                dualgun_should_log(level, channel, g_log_throttle.voltage_interval, time_ptr))                                \
            {                                                                                                                 \
                trace(tid, "%s" fmt LOG_COLOR_RESET, LOG_LEVEL_PREFIX[level], ##__VA_ARGS__);                                 \
                g_last_logged_value.voltage[channel] = vol;                                                                   \
                g_last_logged_value.current[channel] = cur;                                                                   \
            }                                                                                                                 \
        }                                                                                                                     \
    } while (0)

// 状态变化日志 - 只在状态变化时输出
#define DUALGUN_STATE_LOG(level, tid, channel, state, state_type, fmt, ...)                                                                                      \
    do                                                                                                                                                           \
    {                                                                                                                                                            \
        if ((level) <= g_dualgun_log_level && channel < DUAL_CHARGE_CHANNEL_LOG_NUM)                                                                             \
        {                                                                                                                                                        \
            uint8 *last_state = NULL;                                                                                                                            \
            switch (state_type)                                                                                                                                  \
            {                                                                                                                                                    \
            case 0:                                                                                                                                              \
                last_state = &g_last_logged_value.mode[channel];                                                                                                 \
                break;                                                                                                                                           \
            case 1:                                                                                                                                              \
                last_state = &g_last_logged_value.status[channel];                                                                                               \
                break;                                                                                                                                           \
            case 2:                                                                                                                                              \
                last_state = &g_last_logged_value.work_status[channel];                                                                                          \
                break;                                                                                                                                           \
            case 3:                                                                                                                                              \
                last_state = &g_last_logged_value.service[channel];                                                                                              \
                break;                                                                                                                                           \
            default:                                                                                                                                             \
                break;                                                                                                                                           \
            }                                                                                                                                                    \
            if (last_state && (*last_state != state || dualgun_should_log(level, channel, g_log_throttle.state_interval, &g_last_log_time.state_time[channel]))) \
            {                                                                                                                                                    \
                trace(tid, "%s" fmt LOG_COLOR_RESET, LOG_LEVEL_PREFIX[level], ##__VA_ARGS__);                                                                    \
                *last_state = state;                                                                                                                             \
            }                                                                                                                                                    \
        }                                                                                                                                                        \
    } while (0)

// 简便状态宏 - 按类型
#define DUALGUN_MODE_LOG(level, tid, channel, mode, fmt, ...) \
    DUALGUN_STATE_LOG(level, tid, channel, mode, 0, fmt, ##__VA_ARGS__)

#define DUALGUN_STATUS_LOG(level, tid, channel, status, fmt, ...) \
    DUALGUN_STATE_LOG(level, tid, channel, status, 1, fmt, ##__VA_ARGS__)

#define DUALGUN_WORK_LOG(level, tid, channel, work_status, fmt, ...) \
    DUALGUN_STATE_LOG(level, tid, channel, work_status, 2, fmt, ##__VA_ARGS__)

#define DUALGUN_SERVICE_LOG(level, tid, channel, service, fmt, ...) \
    DUALGUN_STATE_LOG(level, tid, channel, service, 3, fmt, ##__VA_ARGS__)

#endif // DUALGUN_LOG_H
