#include "dualgun.h"
#include <message.h>
#include <pcu/eicu/inc/port.h>
#include "transceiver.h"
#include "../pcu/pcuMain.h"
#include "shell.h"
#include "trace.h"
// #include "sample.h"
/*-----------------------------------------------------------------------------
 Section: Type Definitions
 ----------------------------------------------------------------------------*/

void CcuChargeCtrlSend(uint8 channel,
                       uint8 ctrlCmd,
                       uint16 voltage,
                       uint16 current,
                       uint16 batVoltage)
{
    //    int dev = (DEV_CCU_ADDR_A == Get_CcuAddr()) ? DEV_CCU_A : DEV_CCU_B;
    int dev = Get_CCU();
    RemoteControlCmd *pData = NULL;
    DUALCHARGE_CTRL *pCtrl = Get_DualchargeCtrl(channel);

    // 双重有效性检查
    if (!pCtrl)
        return; // LCOV_EXCL_LINE

    get_transfer_data(channel, dev, ID_DUALCHARGE_CTRL, (void **)&pData);
    if (pData)
    {
        pData->reconnect_status = 0;         // 状态同步
        pData->spn_flag = 0;                 // SPN标志
        pData->result_status = 0;            // 结果状态
        pData->cmd = ctrlCmd;                // 控制命令
        pData->reserved1 = 0;                // 保留字段初始化
        pData->set_voltage = voltage;        // 设定电压(mV)
        pData->set_current = current;        // 设定电流(mA)
        pData->battery_voltage = batVoltage; // 电池电压(mV)
    }
    if (eEnableFlag_Off == check_send_enable(channel, dev, ID_DUALCHARGE_CTRL))
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d,CcuChargeCtrlSend open ctrl send\n", channel);
        set_send_startTime(channel, dev, ID_DUALCHARGE_CTRL, 0);
        set_send_remainTime(channel, dev, ID_DUALCHARGE_CTRL, 10 * sysClkRateGet());
        set_send_enable(channel, dev, ID_DUALCHARGE_CTRL, eEnableFlag_On);
    }
    DUALGUN_INFO(TR_CCU_DEBUG, "ch:%d,CcuChargeCtrlSend open ctrl send\n", channel);
    DUALGUN_POWER_LOG(LOG_LEVEL_INFO, TR_CCU_DEBUG, channel, voltage, current,
                      "cmd:%d,voltage=%d,current=%d,batVoltage=%d\n",
                      pData->cmd, pData->set_voltage, pData->set_current, pData->battery_voltage);
}

void CcuChargeTele(int channel, int dev)
{
    RemoteControlAck *pData = NULL;
    DUALCHARGE_CTRL *pCtrl = Get_DualchargeCtrl(channel);

    // 双重有效性检查
    if (!pCtrl)
        return;

    get_transfer_data(channel, dev, ID_DUALCHARGE_TELE, (void **)&pData);
    if (pData)
    {

        // 使用单个布尔变量跟踪变化
        bool has_changes = (pData->success_flag != pCtrl->success_flag) ||
                           (pData->connected != pCtrl->connected) ||
                           (pData->reconnect != pCtrl->reconnect_status) ||
                           (pData->mode != pCtrl->mode) ||
                           (pData->spn_id != pCtrl->spn) ||
                           (pData->service != pCtrl->service) ||
                           (pData->status != pCtrl->status) ||
                           (pData->actual_voltage != pCtrl->actual_voltage) ||
                           (pData->actual_current != pCtrl->actual_current) ||
                           (pData->battery_voltage != pCtrl->battery_voltage);

        if (has_changes)
        {
            printf("状态更新: 模式=%d, 服务=%d, spn:%d,状态=%d, 电压=%d, 电流=%d, 电池电压=%d\n",
                   pData->mode, pData->service, pCtrl->spn, pData->status,
                   pData->actual_voltage, pData->actual_current, pData->battery_voltage);
        }
        pData->success_flag = pCtrl->success_flag; // 操作结果
        pData->connected = pCtrl->connected;       // 连接状态

        // 只有A板（发起方）设置重连状态
        if (Is_DualCharge_Initiator(channel))
        {
            pData->reconnect = pCtrl->reconnect_status; // 重连状态
        }

        pData->mode = pCtrl->mode;                       // 模式同步
        pData->spn_id = pCtrl->spn;                      // 命令同步
        pData->service = pCtrl->service;                 // 命令同步
        pData->status = pCtrl->status;                   // 当前状态码
        pData->actual_voltage = pCtrl->actual_voltage;   // 实际电压(0.1V)
        pData->actual_current = pCtrl->actual_current;   // 实际电流(0.1A)
        pData->battery_voltage = pCtrl->battery_voltage; // 电池电压(0.1V)
    }
}
/**
 ******************************************************************************
 * @brief      发送控制器命令帧
 * @param[in]  channel - 通道号
 * @param[in]  dev     - 设备标识符
 * @param[out] pBuf    - 输出数据缓冲区指针
 * @param[in]  bufSize - 缓冲区大小
 * @retval     实际写入缓冲区的数据长度（字节），失败返回0
 * @details    获取指定通道的控制命令数据，并拷贝到发送缓冲区。
 *             若缓冲区空间不足则报错。
 * @note       依赖get_transfer_data函数获取RemoteControlCmd结构数据
 ******************************************************************************
 */
static int
Send_CcuTele(int channel, int dev, uint8 *pBuf, int bufSize)
{
    int resl = 0;
    RemoteControlAck *pCtrl = NULL;

    // 获取通道对应的控制命令数据结构
    CcuChargeTele(channel, dev);
    get_transfer_data(channel, dev, ID_DUALCHARGE_TELE, (void **)&pCtrl);
    if (NULL != pCtrl)
    {
        // 检查缓冲区大小是否足够
        if (bufSize < sizeof(RemoteControlAck))
        {
            DUALGUN_ERROR(TR_CCU_DEBUG, "Send_CcuCtrl()...error!\n");
            return resl;
        }
        // 拷贝控制命令数据到发送缓冲区
        memcpy(pBuf, pCtrl, sizeof(RemoteControlAck));
        resl = sizeof(RemoteControlAck);
    }
    return resl;
}
/**
 ******************************************************************************
 * @brief      发送控制器命令帧
 * @param[in]  channel - 通道号
 * @param[in]  dev     - 设备标识符
 * @param[out] pBuf    - 输出数据缓冲区指针
 * @param[in]  bufSize - 缓冲区大小
 * @retval     实际写入缓冲区的数据长度（字节），失败返回0
 * @details    获取指定通道的控制命令数据，并拷贝到发送缓冲区。
 *             若缓冲区空间不足则报错。
 * @note       依赖get_transfer_data函数获取RemoteControlCmd结构数据
 ******************************************************************************
 */
static int
Send_CcuCtrl(int channel, int dev, uint8 *pBuf, int bufSize)
{
    int resl = 0;
    RemoteControlCmd *pCtrl = NULL;

    // 获取通道对应的控制命令数据结构
    get_transfer_data(channel, dev, ID_DUALCHARGE_CTRL, (void **)&pCtrl);
    if (NULL != pCtrl)
    {
        // 检查缓冲区大小是否足够
        if (bufSize < sizeof(RemoteControlCmd))
        {
            DUALGUN_ERROR(TR_CCU_DEBUG, "Send_CcuCtrl()...error!\n");
            return resl;
        }
        // 拷贝控制命令数据到发送缓冲区
        memcpy(pBuf, pCtrl, sizeof(RemoteControlCmd));
        resl = sizeof(RemoteControlCmd);
    }
    return resl;
}

/**
 ******************************************************************************
 * @brief      CAN总线数据发送函数
 * @param[in]  channel - 通道号
 * @param[in]  dev     - 设备标识符
 * @param[in]  id      - 消息ID
 * @param[in]  txBuf   - 待发送数据缓冲区
 * @param[in]  txLen   - 数据长度
 * @retval     固定返回0
 * @details    构造CAN数据帧，填充消息ID和数据内容，通过CAN3总线发送。
 * @note       数据长度超过CAN帧大小时会被截断（依赖CAN_DATA定义）
 ******************************************************************************
 */
static int
Ccu_Send(int channel, int dev, int id, uint8 txBuf[], int txLen)
{
    CAN_DATA the_data;                     /* CAN数据帧结构体 */
    if (txLen <= sizeof(the_data.dataBuf)) // 检查数据长度是否合法
    {
        memset(&the_data, 0x00, sizeof(CAN_DATA));
        // 生成CAN ID（依赖设备地址和消息类型）
        Get_CcuCanId(&the_data.canId, dev, id);

        // 填充数据内容
        memcpy(the_data.dataBuf, txBuf, txLen);
        the_data.dataLen = txLen;

        // 调试跟踪：记录CAN ID和原始数据，使用内容变化检测
        DUALGUN_CAN_LOG(TR_CCU_DEBUG, the_data.canId, the_data.dataBuf, the_data.dataLen,
                        "<channel:%d>-----<Ccu tx3: %X>",
                        channel, the_data.canId);

        // 通过CAN3总线发送数据
        can_send(PCU_CHAN, the_data.canId, the_data.dataBuf, the_data.dataLen);
    }
    return 0;
}

/**
 ******************************************************************************
 * @brief      CCU发送模块初始化
 * @param[in]  None
 * @retval     None
 * @details    注册所有通道的发送回调函数，并初始化控制命令和应答的传输参数。
 * @note       设备地址由Get_CcuAddr()动态获取，区分主备设备
 ******************************************************************************
 */
void Ccu_SendInit(void)
{
    int dev = 0;
    // 根据设备地址选择设备标识符
    //    dev = (DEV_CCU_ADDR_A == Get_CcuAddr()) ? DEV_CCU_A : DEV_CCU_B;
    dev = Get_CCU();

    // 遍历所有通道进行初始化
    for (int i = DUAL_CHARGE_CHANNEL_01; i < DUAL_CHARGE_CHANNEL_NUM; i++)
    {
        DUALGUN_INFO(TR_CCU_DEBUG, "Ccu_SendInit %d[dev : %x][%x]\n", i, dev, Get_CcuAddr());
        // 注册通道发送函数
        reg_channel(i, NULL, Ccu_Send);

        // 注册控制命令传输参数（250ms周期）
        reg_transfer(i, dev, ID_DUALCHARGE_CTRL, sizeof(RemoteControlCmd),
                     Send_CcuCtrl, 250);

        // 注册应答传输参数（250ms周期）
        reg_transfer(i, dev, ID_DUALCHARGE_TELE, sizeof(RemoteControlAck),
                     Send_CcuTele, 250);
    }
}

/**
 ******************************************************************************
 * @brief      CCU发送测试命令
 * @param[in]  cmdtp - 命令表指针
 * @param[in]  argc  - 参数个数
 * @param[in]  argv  - 参数值数组
 * @retval     固定返回0
 * @details    通过Shell命令手动激活通道1的控制命令发送，设置发送周期为5秒。
 * @note       仅用于开发调试，正式版本应移除
 ******************************************************************************
 */
static uint32
Test_CcuSend(cmd_tbl_t *cmdtp, uint32 argc, const uint8 *argv[])
{
    int dev = 0;
    //    dev = (DEV_CCU_ADDR_A == Get_CcuAddr()) ? DEV_CCU_A : DEV_CCU_B;
    dev = Get_CCU();

    // 仅测试通道1
    if (eEnableFlag_Off == check_send_enable(DUAL_CHARGE_CHANNEL_01, dev, ID_DUALCHARGE_CTRL))
    {
        // 启用发送功能
        set_send_enable(DUAL_CHARGE_CHANNEL_01, dev, ID_DUALCHARGE_CTRL, eEnableFlag_On);
        // 设置立即发送
        set_send_startTime(DUAL_CHARGE_CHANNEL_01, dev, ID_DUALCHARGE_CTRL, 0);
        // 设置发送周期为5秒（依赖系统时钟频率）
        set_send_remainTime(DUAL_CHARGE_CHANNEL_01, dev, ID_DUALCHARGE_CTRL, 5 * sysClkRateGet());
    }
    return 0;
}

/* 注册Shell命令 */
SHELL_CMD(ccu_send,                             /* 命令名 */
          CFG_MAXARGS,                          /* 最大参数数 */
          (SHELL_CMD_FUNCPTR)Test_CcuSend,      /* 命令处理函数 */
          "ccu_send \r\t\t\t\t CCU发送测试\n"); /* 帮助信息 */

/*----------------------------pdcuSend.c--------------------------------*/
